pipeline {
    agent any

    environment {
            remote_dir = "/mnt/db/pages"
            // 部署远程主机ip地址,需要通过密钥的方式设置免密登录
            remote_ip = "***************"
            remote_user = "root"
            projectPath = "/usr/local/jenkins/vue_remote_deploy.sh"
    }

    stages{
        stage("pull code"){
            steps{
                sh "ls ${env.JENKINS_HOME}/workspace/${env.JOB_NAME}"
                deleteDir()
                sh "ls ${env.JENKINS_HOME}/workspace/${env.JOB_NAME}"
                checkout([$class: 'GitSCM', branches: [[name: '*/demo']], extensions: [], userRemoteConfigs: [[credentialsId: '1a52f9fb-04fd-42b9-bd99-b2e9abf4741b', url: 'http://www.jingruiit.com:7654/jrzh/jrzh_bladex_-supply_chain_vue.git']]])
            }
        }
        stage('build'){
            steps{
                sh "cnpm install"
                sh "cnpm run build"
            }
        }
        stage('deploy'){
            steps{
                sh "sh ${projectPath} ${env.JOB_NAME} ${env.JENKINS_HOME} ${remote_ip} ${remote_user} ${remote_dir}"
            }
        }
    }
}

