"use strict";var t=function(){let r,t=!1;const a="data-font-name";function c(){for(const e of document.querySelectorAll(`span[${a}]`))e.className="debuggerHideText"}function p(e,t){for(const n of document.querySelectorAll(`span[${a}=${e}]`))n.className=t?"debuggerShowText":"debuggerHideText"}function n(e){if(e.target.dataset.fontName&&"SPAN"===e.target.tagName.toUpperCase()){var t=e.target.dataset.fontName,n=document.getElementsByTagName("input");for(let e=0;e<n.length;++e){const a=n[e];a.dataset.fontName===t&&(a.checked=!a.checked,p(t,a.checked),a.scrollIntoView())}}}return{id:"FontInspector",name:"<PERSON><PERSON> <PERSON>",panel:null,manager:null,init:function(e){const t=this.panel,n=document.createElement("button");n.addEventListener("click",c),n.textContent="Refresh",t.appendChild(n),r=document.createElement("div"),t.appendChild(r)},cleanup:function(){r.textContent=""},enabled:!1,get active(){return t},set active(e){t=e,t?(document.body.addEventListener("click",n,!0),c()):(document.body.removeEventListener("click",n,!0),function(){for(const e of document.querySelectorAll(`span[${a}]`))e.className=""}())},fontAdded:function(t,e){var n=function(t,n){const a=document.createElement("table");for(let e=0;e<n.length;e++){const i=document.createElement("tr"),o=document.createElement("td");o.textContent=n[e],i.appendChild(o);const d=document.createElement("td");d.textContent=t[n[e]].toString(),i.appendChild(d),a.appendChild(i)}return a}(t,["name","type"]);const a=t.loadedName,i=document.createElement("div"),o=document.createElement("span");o.textContent=a;const d=document.createElement("a");e?(e=/url\(['"]?([^)"']+)/.exec(e),d.href=e[1]):t.data&&(d.href=URL.createObjectURL(new Blob([t.data],{type:t.mimeType}))),d.textContent="Download";const l=document.createElement("a");l.href="",l.textContent="Log",l.addEventListener("click",function(e){e.preventDefault(),console.log(t)});const s=document.createElement("input");s.setAttribute("type","checkbox"),s.dataset.fontName=a,s.addEventListener("click",function(){p(a,s.checked)}),i.appendChild(s),i.appendChild(o),i.appendChild(document.createTextNode(" ")),i.appendChild(d),i.appendChild(document.createTextNode(" ")),i.appendChild(l),i.appendChild(n),r.appendChild(i),setTimeout(()=>{this.active&&c()},2e3)}}}();let b;var x=function(){let d=[],i=null,a=null,l=null,o=Object.create(null);return{id:"Stepper",name:"Stepper",panel:null,manager:null,init:function(e){const t=this;a=document.createElement("div"),l=document.createElement("select"),l.addEventListener("change",function(e){t.selectStepper(this.value)}),a.appendChild(l),i=document.createElement("div"),this.panel.appendChild(a),this.panel.appendChild(i),sessionStorage.getItem("pdfjsBreakPoints")&&(o=JSON.parse(sessionStorage.getItem("pdfjsBreakPoints"))),b=Object.create(null);for(const n in e.OPS)b[e.OPS[n]]=n},cleanup:function(){l.textContent="",i.textContent="",d=[]},enabled:!1,active:!1,create:function(e){const t=document.createElement("div");t.id="stepper"+e,t.hidden=!0,t.className="stepper",i.appendChild(t);const n=document.createElement("option");n.textContent="Page "+(e+1),n.value=e,l.appendChild(n);var a=o[e]||[],a=new s(t,e,a);return d.push(a),1===d.length&&this.selectStepper(e,!1),a},selectStepper:function(e,t){let n;for(e|=0,t&&this.manager.selectPanel(this),n=0;n<d.length;++n){const i=d[n];i.panel.hidden=i.pageIndex!==e}var a=l.options;for(n=0;n<a.length;++n){const o=a[n];o.selected=(0|o.value)===e}},saveBreakPoints:function(e,t){o[e]=t,sessionStorage.setItem("pdfjsBreakPoints",JSON.stringify(o))}}}();const s=function(){function v(e,t){const n=document.createElement(e);return t&&(n.textContent=t),n}return class{constructor(e,t,n){this.panel=e,this.breakPoint=0,this.nextBreakPoint=null,this.pageIndex=t,this.breakPoints=n,this.currentIdx=-1,this.operatorListIdx=0}init(e){const t=this.panel,n=v("div","c=continue, s=step"),a=v("table");n.appendChild(a),a.cellSpacing=0;const i=v("tr");a.appendChild(i),i.appendChild(v("th","Break")),i.appendChild(v("th","Idx")),i.appendChild(v("th","fn")),i.appendChild(v("th","args")),t.appendChild(n),this.table=a,this.updateOperatorList(e)}updateOperatorList(n){const t=this;function a(){var e=+this.dataset.idx;this.checked?t.breakPoints.push(e):t.breakPoints.splice(t.breakPoints.indexOf(e),1),x.saveBreakPoints(t.pageIndex,t.breakPoints)}if(!(15e3<this.operatorListIdx)){const s=document.createDocumentFragment();var e=Math.min(15e3,n.fnArray.length);for(let t=this.operatorListIdx;t<e;t++){const r=v("tr");r.className="line",r.dataset.idx=t,s.appendChild(r);var i=this.breakPoints.includes(t),o=n.argsArray[t]||[];const c=v("td"),p=v("input");p.type="checkbox",p.className="points",p.checked=i,p.dataset.idx=t,p.onclick=a,c.appendChild(p),r.appendChild(c),r.appendChild(v("td",t.toString()));i=b[n.fnArray[t]];let e=o;if("showText"===i){var d=o[0];const h=v("tr"),u=v("tr"),m=v("tr");for(let e=0;e<d.length;e++){var l=d[e];if("object"==typeof l&&null!==l)h.appendChild(v("td",l.originalCharCode)),u.appendChild(v("td",l.fontChar)),m.appendChild(v("td",l.unicode));else{const g=v("td",l);g.classList.add("advance"),h.appendChild(g),u.appendChild(v("td")),m.appendChild(v("td"))}}e=v("td");const f=v("table");f.classList.add("showText"),e.appendChild(f),f.appendChild(h),f.appendChild(u),f.appendChild(m)}r.appendChild(v("td",i)),e instanceof HTMLElement?r.appendChild(e):r.appendChild(v("td",JSON.stringify(function n(a){if("string"==typeof a)return a.length<=75?a:a.substring(0,75)+"...";if("object"!=typeof a||null===a)return a;if("length"in a){const i=[];let e,t;for(e=0,t=Math.min(10,a.length);e<t;e++)i.push(n(a[e]));return e<a.length&&i.push("..."),i}const e={};for(const t in a)e[t]=n(a[t]);return e}(e))))}if(e<n.fnArray.length){const C=v("td","...");C.colspan=4,s.appendChild(C)}this.operatorListIdx=n.fnArray.length,this.table.appendChild(s)}}getNextBreakPoint(){this.breakPoints.sort(function(e,t){return e-t});for(let e=0;e<this.breakPoints.length;e++)if(this.breakPoints[e]>this.currentIdx)return this.breakPoints[e];return null}breakIt(e,t){x.selectStepper(this.pageIndex,!0),this.currentIdx=e;const n=e=>{switch(e.keyCode){case 83:document.removeEventListener("keydown",n),this.nextBreakPoint=this.currentIdx+1,this.goTo(-1),t();break;case 67:document.removeEventListener("keydown",n),this.nextBreakPoint=this.getNextBreakPoint(),this.goTo(-1),t()}};document.addEventListener("keydown",n),this.goTo(e)}goTo(n){var a=this.panel.getElementsByClassName("line");for(let e=0,t=a.length;e<t;++e){const i=a[e];(0|i.dataset.idx)===n?(i.style.backgroundColor="rgb(251,250,207)",i.scrollIntoView()):i.style.backgroundColor=null}}}}();var n=function(){let l=[];function s(e){for(;e.hasChildNodes();)e.removeChild(e.lastChild)}return{id:"Stats",name:"Stats",panel:null,manager:null,init(e){},enabled:!1,active:!1,add(e,t){if(t){var n,a=function(n){for(let e=0,t=l.length;e<t;++e)if(l[e].pageNumber===n)return e;return!1}(e);!1!==a&&(n=l[a],this.panel.removeChild(n.div),l.splice(a,1));const i=document.createElement("div");i.className="stats";const o=document.createElement("div");o.className="title",o.textContent="Page: "+e;const d=document.createElement("div");d.textContent=t.toString(),i.appendChild(o),i.appendChild(d),l.push({pageNumber:e,div:i}),l.sort(function(e,t){return e.pageNumber-t.pageNumber}),s(this.panel);for(let e=0,t=l.length;e<t;++e)this.panel.appendChild(l[e].div)}},cleanup(){l=[],s(this.panel)}}}();window.PDFBug=function(){const p=[];let e=null;return{tools:[t,x,n],enable(i){var t=1===i.length&&"all"===i[0];const o=this.tools;for(let e=0;e<o.length;++e){const n=o[e];(t||i.includes(n.id))&&(n.enabled=!0)}t||o.sort(function(e,t){let n=i.indexOf(e.id);n=n<0?o.length:n;let a=i.indexOf(t.id);return a=a<0?o.length:a,n-a})},init(t,e,n){this.enable(n);const a=document.createElement("div");a.id="PDFBug";const i=document.createElement("div");i.setAttribute("class","controls"),a.appendChild(i);const o=document.createElement("div");o.setAttribute("class","panels"),a.appendChild(o),e.appendChild(a),e.style.right="300px";var d=this.tools;const l=this;for(let e=0;e<d.length;++e){const s=d[e],r=document.createElement("div"),c=document.createElement("button");c.textContent=s.name,c.addEventListener("click",function(t){return function(e){e.preventDefault(),l.selectPanel(t)}}(e)),i.appendChild(c),o.appendChild(r),s.panel=r,s.manager=this,s.enabled?s.init(t):r.textContent=`${s.name} is disabled. To enable add "${s.id}" to `+"the pdfBug parameter and refresh (separate multiple by commas).",p.push(c)}this.selectPanel(0)},cleanup(){for(let e=0,t=this.tools.length;e<t;e++)this.tools[e].enabled&&this.tools[e].cleanup()},selectPanel(t){if((t="number"!=typeof t?this.tools.indexOf(t):t)!==e){e=t;const a=this.tools;for(let e=0;e<a.length;++e){var n=e===t;p[e].classList.toggle("active",n),a[e].active=n,a[e].panel.hidden=!n}}}}}();