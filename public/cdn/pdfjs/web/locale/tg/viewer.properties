# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ð¡Ð°Ò³Ð¸ÑÐ°Ð¸ ÒÐ°Ð±Ð»Ó£

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=Ð¥ÑÑÐ´ ÐºÐ°ÑÐ´Ð°Ð½
zoom_out_label=Ð¥ÑÑÐ´ ÐºÐ°ÑÐ´Ð°Ð½
zoom_in.title=ÐÐ°Ð»Ð¾Ð½ ÐºÐ°ÑÐ´Ð°Ð½
zoom_in_label=ÐÐ°Ð»Ð¾Ð½ ÐºÐ°ÑÐ´Ð°Ð½
zoom.title=Ð¢Ð°Ð½Ð·Ð¸Ð¼Ð¸ Ð°Ð½Ð´Ð¾Ð·Ð°
open_file.title=ÐÑÑÐ¾Ð´Ð°Ð½Ð¸ ÑÐ°Ð¹Ð»
open_file_label=ÐÑÑÐ¾Ð´Ð°Ð½
print.title=Ð§Ð¾Ð¿ ÐºÐ°ÑÐ´Ð°Ð½
print_label=Ð§Ð¾Ð¿ ÐºÐ°ÑÐ´Ð°Ð½
download.title=ÐÐ¾ÑÐ³Ð¸ÑÓ£ ÐºÐ°ÑÐ´Ð°Ð½
download_label=ÐÐ¾ÑÐ³Ð¸ÑÓ£ ÐºÐ°ÑÐ´Ð°Ð½
bookmark.title=ÐÐ°Ð¼ÑÐ´Ð¸ Ò·Ð¾ÑÓ£ (Ð½ÑÑÑÐ° Ð±Ð°ÑÐ´Ð¾ÑÑÐ°Ð½ Ñ ÐºÑÑÐ¾Ð´Ð°Ð½ Ð´Ð°Ñ ÑÐ°Ð²Ð·Ð°Ð½Ð°Ð¸ Ð½Ð°Ð²)
bookmark_label=ÐÐ°Ð¼ÑÐ´Ð¸ Ò·Ð¾ÑÓ£

# Secondary toolbar and context menu
tools.title=ÐÐ±Ð·Ð¾ÑÒ³Ð¾




# Document properties dialog box
document_properties_file_name=ÐÐ¾Ð¼Ð¸ ÑÐ°Ð¹Ð»:
document_properties_file_size=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ ÑÐ°Ð¹Ð»:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÐÐ°ÐºÑÑÐ±
document_properties_page_size_name_legal=Ò²ÑÒÑÒÓ£
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.

print_progress_message=ÐÐ¼Ð¾Ð´Ð°ÑÐ¾Ð·Ð¸Ð¸ Ò³ÑÒ·Ò·Ð°Ñ Ð±Ð°ÑÐ¾Ð¸ ÑÐ¾Ð¿â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÐÐµÐºÐ¾Ñ ÐºÐ°ÑÐ´Ð°Ð½

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ð½Ð°Ð²Ð¾ÑÐ¸ Ò·Ð¾Ð½Ð¸Ð±Ó£

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit[zero]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[one]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[two]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[few]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[many]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[other]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ

# Error panel labels
error_more_info=ÐÐ°ÑÐ»ÑÐ¼Ð¾ÑÐ¸ Ð±ÐµÑÑÐ°Ñ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number

# Predefined zoom values
page_scale_width=ÐÐ· ÑÓ¯Ð¸ Ð¿Ð°Ò³Ð½Ð¾Ð¸ ÑÐ°Ò³Ð¸ÑÐ°
page_scale_auto=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ ÑÑÐ´ÐºÐ¾Ñ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages

# Loading indicator messages
invalid_file_error=Ð¤Ð°Ð¹Ð»Ð¸ PDF Ð½Ð¾Ð´ÑÑÑÑÑ Ñ Ð²Ð°Ð¹ÑÐ¾Ð½ÑÑÐ´Ð° Ð¼ÐµÐ±Ð¾ÑÐ°Ð´.
missing_file_error=Ð¤Ð°Ð¹Ð»Ð¸ PDF ÒÐ¾Ð¸Ð± Ð°ÑÑ.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
password_label=ÐÐ°ÑÐ¾Ð¸ ÐºÑÑÐ¾Ð´Ð°Ð½Ð¸ Ð¸Ð½ ÑÐ°Ð¹Ð»Ð¸ PDF Ð½Ð¸Ò³Ð¾Ð½Ð²Ð¾Ð¶Ð°ÑÐ¾ Ð²Ð¾ÑÐ¸Ð´ ÐºÑÐ½ÐµÐ´.
password_cancel=ÐÐµÐºÐ¾Ñ ÐºÐ°ÑÐ´Ð°Ð½

# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.

