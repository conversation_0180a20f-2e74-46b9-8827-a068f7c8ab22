# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Jun kan ruxaq
previous_label=Jun kan
next.title=Jun chik ruxaq
next_label=Jun chik

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ruxaq
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=richin {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} richin {{pagesCount}})

zoom_out.title=Tich'utinirisÃ¤x
zoom_out_label=Tich'utinirisÃ¤x
zoom_in.title=TinimirisÃ¤x
zoom_in_label=TinimirisÃ¤x
zoom.title=Sum
presentation_mode.title=Tijal ri rub'anikil niwachin
presentation_mode_label=Pa rub'eyal niwachin
open_file.title=Tijaq Yakb'Ã¤l
open_file_label=Tijaq
print.title=Titz'ajb'Ã¤x
print_label=Titz'ajb'Ã¤x
download.title=TiqasÃ¤x
download_label=TiqasÃ¤x
bookmark.title=Rutz'etik wakami (tiwachib'Ã«x o tijaq pa jun k'ak'a' tzuwÃ¤ch)
bookmark_label=Rutzub'al wakami

# Secondary toolbar and context menu
tools.title=Samajib'Ã¤l
tools_label=Samajib'Ã¤l
first_page.title=Tib'e pa nab'ey ruxaq
first_page_label=Tib'e pa nab'ey ruxaq
last_page.title=Tib'e pa ruk'isib'Ã¤l ruxaq
last_page_label=Tib'e pa ruk'isib'Ã¤l ruxaq
page_rotate_cw.title=TisutÃ¯x pan ajkiq'a'
page_rotate_cw_label=TisutÃ¯x pan ajkiq'a'
page_rotate_ccw.title=TisutÃ¯x pan ajxokon
page_rotate_ccw_label=TisutÃ¯x pan ajxokon

cursor_text_select_tool.title=Titzij ri rusamajib'al Rucha'ik Rucholajem Tzij
cursor_text_select_tool_label=Rusamajib'al Rucha'ik Rucholajem Tzij
cursor_hand_tool.title=Titzij ri q'ab'aj samajib'Ã¤l
cursor_hand_tool_label=Q'ab'aj Samajib'Ã¤l

scroll_vertical.title=TokisÃ¤x Pa'Ã¤l Q'axanem
scroll_vertical_label=Pa'Ã¤l Q'axanem
scroll_horizontal.title=TokisÃ¤x Kotz'Ã¶l Q'axanem
scroll_horizontal_label=Kotz'Ã¶l Q'axanem
scroll_wrapped.title=TokisÃ¤x Tzub'aj Q'axanem
scroll_wrapped_label=Tzub'aj Q'axanem

spread_none.title=Man ketun taq ruxaq pa rub'eyal wuj
spread_none_label=Majun Rub'eyal
spread_odd.title=Ke'atunu' ri taq ruxaq rik'in natikirisaj rik'in jun man k'ulaj ta rajilab'al
spread_odd_label=Man K'ulaj Ta Rub'eyal
spread_even.title=Ke'atunu' ri taq ruxaq rik'in natikirisaj rik'in jun k'ulaj rajilab'al
spread_even_label=K'ulaj Rub'eyal

# Document properties dialog box
document_properties.title=Taq richinil wujâ¦
document_properties_label=Taq richinil wujâ¦
document_properties_file_name=Rub'i' yakb'Ã¤l:
document_properties_file_size=Runimilem yakb'Ã¤l:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=B'i'aj:
document_properties_author=B'anel:
document_properties_subject=Taqikil:
document_properties_keywords=Kixe'el taq tzij:
document_properties_creation_date=Ruq'ijul xtz'uk:
document_properties_modification_date=Ruq'ijul xjalwachÃ¯x:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Q'inonel:
document_properties_producer=PDF b'anÃ¶y:
document_properties_version=PDF ruwÃ¤ch:
document_properties_page_count=Jarupe' ruxaq:
document_properties_page_size=Runimilem ri Ruxaq:
document_properties_page_size_unit_inches=pa
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=rupalem
document_properties_page_size_orientation_landscape=rukotz'olem
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Loman wuj
document_properties_page_size_name_legal=Taqanel tzijol
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Anin Rutz'etik Ajk'amaya'l:
document_properties_linearized_yes=Ja'
document_properties_linearized_no=Mani
document_properties_close=Titz'apÃ¯x

print_progress_message=Ruchojmirisaxik wuj richin nitz'ajb'Ã¤xâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Tiq'at

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Tijal ri ajxikin kajtz'ik
toggle_sidebar_notification2.title=Tik'ex ri ajxikin yuqkajtz'ik (ri wuj eruk'wan taq ruchi'/taqo/kuchuj)
toggle_sidebar_label=Tijal ri ajxikin kajtz'ik
document_outline.title=Tik'ut pe ruch'akulal wuj (kamul-pitz'oj richin nirik'/nich'utinirisÃ¤x ronojel ruch'akulal)
document_outline_label=Ruch'akulal wuj
attachments.title=Kek'ut pe ri taq taqoj
attachments_label=Taq taqoj
layers.title=Kek'ut taq Kuchuj (ka'i'-pitz' richin yetzolÃ¯x ronojel ri taq kuchuj e k'o wi)
layers_label=Taq kuchuj
thumbs.title=Kek'ut pe taq ch'utiq
thumbs_label=KokÃ¶j
current_outline_item.title=KekanÃ¶x  Taq Ch'akulal Kik'wan Chib'Ã¤l
current_outline_item_label=Taq Ch'akulal Kik'wan Chib'Ã¤l
findbar.title=TikanÃ¶x chupam ri wuj
findbar_label=TikanÃ¶x

additional_layers=Tz'aqat ta Kuchuj
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Ruxaq {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ruxaq {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ruch'utinirisaxik ruxaq {{page}}

# Find panel button title and messages
find_input.title=TikanÃ¶x
find_input.placeholder=TikanÃ¶x pa wujâ¦
find_previous.title=Tib'an b'enam pa ri jun kan q'aptzij xilitÃ¤j
find_previous_label=Jun kan
find_next.title=Tib'e pa ri jun chik pajtzij xilitÃ¤j
find_next_label=Jun chik
find_highlight=Tiya' retal ronojel
find_match_case_label=Tuk'Ã¤m ri' kik'in taq nimatz'ib' chuqa' taq ch'utitz'ib'
find_entire_word_label=Tz'aqÃ¤t taq tzij
find_reached_top=Xb'eq'i' ri rutikirib'al wuj, xtikanÃ¶x k'a pa ruk'isib'Ã¤l
find_reached_bottom=Xb'eq'i' ri ruk'isib'Ã¤l wuj, xtikanÃ¶x pa rutikirib'al
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} richin {{total}} nuk'Ã¤m ri'
find_match_count[two]={{current}} richin {{total}} nikik'Ã¤m ki'
find_match_count[few]={{current}} richin {{total}} nikik'Ã¤m ki'
find_match_count[many]={{current}} richin {{total}} nikik'Ã¤m ki'
find_match_count[other]={{current}} richin {{total}} nikik'Ã¤m ki'
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=K'Ã¯y chi re {{limit}} nikik'Ã¤m ki'
find_match_count_limit[one]=K'Ã¯y chi re {{limit}} nuk'Ã¤m ri'
find_match_count_limit[two]=K'Ã¯y chi re {{limit}} nikik'Ã¤m ki'
find_match_count_limit[few]=K'Ã¯y chi re {{limit}} nikik'Ã¤m ki'
find_match_count_limit[many]=K'Ã¯y chi re {{limit}} nikik'Ã¤m ki'
find_match_count_limit[other]=K'Ã¯y chi re {{limit}} nikik'Ã¤m ki'
find_not_found=Man xilitÃ¤j ta ri pajtzij

# Error panel labels
error_more_info=Ch'aqa' chik rutzijol
error_less_info=Jub'a' ok rutzijol
error_close=Titz'apÃ¯x
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Uqxa'n: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Tzub'aj: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Yakb'Ã¤l: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=B'ey: {{line}}
rendering_error=Xk'ulwachitÃ¤j jun sachoj toq ninuk'wachij ri ruxaq.

# Predefined zoom values
page_scale_width=Ruwa ruxaq
page_scale_fit=Tinuk' ruxaq
page_scale_auto=Yonil chi nimilem
page_scale_actual=Runimilem Wakami
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=NisamÃ¤jâ¦
loading_error=\u0020Xk'ulwachitÃ¤j jun sach'oj toq xnuk'ux ri PDF .
invalid_file_error=Man oke ta o yujtajinÃ¤q ri PDF yakb'Ã¤l.
missing_file_error=Man xilitÃ¤j ta ri PDF yakb'Ã¤l.
unexpected_response_error=Man oyob'en ta tz'olin rutzij ruk'u'x samaj.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Tz'ib'anÃ¯k]
password_label=Tatz'ib'aj ri ewan tzij richin najÃ¤q re yakb'Ã¤l re' pa PDF.
password_invalid=Man okel ta ri ewan tzij: Tatojtob'ej chik.
password_ok=Ãtz
password_cancel=Tiq'at

printing_not_supported=Rutzijol k'ayewal: Ri rutz'ajb'axik man koch'el ta ronojel pa re okik'amaya'l re'.
printing_not_ready=Rutzijol k'ayewal: Ri PDF man xusamajij ta ronojel richin nitz'ajb'Ã¤x.
web_fonts_disabled=E chupÃ¼l ri taq ajk'amaya'l tz'ib': man tikirel ta nokisÃ¤x ri taq tz'ib' PDF pa ch'ikenÃ¯k
