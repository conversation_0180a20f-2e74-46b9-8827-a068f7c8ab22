# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÑÐµÑÑÐ¾Ð´Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°
previous_label=ÐÑÐµÑÑÐ¾Ð´Ð½Ð°
next.title=Ð¡Ð»ÐµÐ´ÐµÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ°
next_label=Ð¡Ð»ÐµÐ´ÐµÑÐ°

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ°
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Ð¾Ð´ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ð¾Ð´ {{pagesCount}})

zoom_out.title=Ð£Ð¼Ð°ÑÐ¸
zoom_out_label=Ð£Ð¼Ð°ÑÐ¸
zoom_in.title=Ð£Ð²ÐµÐ»Ð¸ÑÐ°Ñ
zoom_in_label=Ð£Ð²ÐµÐ»Ð¸ÑÐ°Ñ
zoom.title=Ð£Ð²ÐµÐ»Ð¸ÑÐ°Ð²Ð°ÑÐµ
presentation_mode.title=ÐÑÐ¾Ð¼ÐµÐ½Ð¸ Ð½Ð° Ð¿ÑÐ¸ÐºÐ°Ð· Ñ ÑÐµÐ¶Ð¸Ð¼Ñ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸ÑÐµ
presentation_mode_label=Ð ÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸ÑÐµ
open_file.title=ÐÑÐ²Ð¾ÑÐ¸ Ð´Ð°ÑÐ¾ÑÐµÐºÑ
open_file_label=ÐÑÐ²Ð¾ÑÐ¸
print.title=Ð¨ÑÐ°Ð¼Ð¿Ð°Ñ
print_label=Ð¨ÑÐ°Ð¼Ð¿Ð°Ñ
download.title=ÐÑÐµÑÐ·Ð¼Ð¸
download_label=ÐÑÐµÑÐ·Ð¼Ð¸
bookmark.title=Ð¢ÑÐµÐ½ÑÑÐ½Ð¸ Ð¿ÑÐ¸ÐºÐ°Ð· (ÐºÐ¾Ð¿Ð¸ÑÐ°Ñ Ð¸Ð»Ð¸ Ð¾ÑÐ²Ð¾ÑÐ¸ Ñ Ð½Ð¾Ð²Ð¾Ð¼ Ð¿ÑÐ¾Ð·Ð¾ÑÑ)
bookmark_label=Ð¢ÑÐµÐ½ÑÑÐ½Ð¸ Ð¿ÑÐ¸ÐºÐ°Ð·

# Secondary toolbar and context menu
tools.title=ÐÐ»Ð°ÑÐºÐµ
tools_label=ÐÐ»Ð°ÑÐºÐµ
first_page.title=ÐÐ´Ð¸ Ð½Ð° Ð¿ÑÐ²Ñ ÑÑÑÐ°Ð½Ð¸ÑÑ
first_page_label=ÐÐ´Ð¸ Ð½Ð° Ð¿ÑÐ²Ñ ÑÑÑÐ°Ð½Ð¸ÑÑ
last_page.title=ÐÐ´Ð¸ Ð½Ð° Ð¿Ð¾ÑÐ»ÐµÐ´ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
last_page_label=ÐÐ´Ð¸ Ð½Ð° Ð¿Ð¾ÑÐ»ÐµÐ´ÑÑ ÑÑÑÐ°Ð½Ð¸ÑÑ
page_rotate_cw.title=Ð Ð¾ÑÐ¸ÑÐ°Ñ Ñ ÑÐ¼ÐµÑÑ ÐºÐ°Ð·Ð°ÑÐºÐµ Ð½Ð° ÑÐ°ÑÑ
page_rotate_cw_label=Ð Ð¾ÑÐ¸ÑÐ°Ñ Ñ ÑÐ¼ÐµÑÑ ÐºÐ°Ð·Ð°ÑÐºÐµ Ð½Ð° ÑÐ°ÑÑ
page_rotate_ccw.title=Ð Ð¾ÑÐ¸ÑÐ°Ñ Ñ ÑÐ¼ÐµÑÑ ÑÑÐ¿ÑÐ¾ÑÐ½Ð¾Ð¼ Ð¾Ð´ ÐºÐ°Ð·Ð°ÑÐºÐµ Ð½Ð° ÑÐ°ÑÑ
page_rotate_ccw_label=Ð Ð¾ÑÐ¸ÑÐ°Ñ Ñ ÑÐ¼ÐµÑÑ ÑÑÐ¿ÑÐ¾ÑÐ½Ð¾Ð¼ Ð¾Ð´ ÐºÐ°Ð·Ð°ÑÐºÐµ Ð½Ð° ÑÐ°ÑÑ

cursor_text_select_tool.title=ÐÐ¼Ð¾Ð³ÑÑÐ¸ Ð°Ð»Ð°Ñ Ð·Ð° ÑÐµÐ»ÐµÐºÑÐ¾Ð²Ð°ÑÐµ ÑÐµÐºÑÑÐ°
cursor_text_select_tool_label=ÐÐ»Ð°Ñ Ð·Ð° ÑÐµÐ»ÐµÐºÑÐ¾Ð²Ð°ÑÐµ ÑÐµÐºÑÑÐ°
cursor_hand_tool.title=ÐÐ¼Ð¾Ð³ÑÑÐ¸ Ð°Ð»Ð°Ñ Ð·Ð° Ð¿Ð¾Ð¼ÐµÑÐ°ÑÐµ
cursor_hand_tool_label=ÐÐ»Ð°Ñ Ð·Ð° Ð¿Ð¾Ð¼ÐµÑÐ°ÑÐµ

scroll_vertical.title=ÐÐ¾ÑÐ¸ÑÑÐ¸ Ð²ÐµÑÑÐ¸ÐºÐ°Ð»Ð½Ð¾ ÑÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ
scroll_vertical_label=ÐÐµÑÑÐ¸ÐºÐ°Ð»Ð½Ð¾ ÑÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ
scroll_horizontal.title=ÐÐ¾ÑÐ¸ÑÑÐ¸ ÑÐ¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»Ð½Ð¾ ÑÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ
scroll_horizontal_label=Ð¥Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»Ð½Ð¾ ÑÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ
scroll_wrapped.title=ÐÐ¾ÑÐ¸ÑÑÐ¸ ÑÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ Ð¿Ð¾ Ð¾Ð¼Ð¾ÑÑ
scroll_wrapped_label=Ð¡ÐºÑÐ¾Ð»Ð¾Ð²Ð°ÑÐµ Ð¿Ð¾ Ð¾Ð¼Ð¾ÑÑ

spread_none.title=ÐÐµÐ¼Ð¾Ñ ÑÐ¿Ð°ÑÐ°ÑÐ¸ ÑÐ¸ÑÐµÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ°
spread_none_label=ÐÐµÐ· ÑÐ°ÑÐ¿ÑÐ¾ÑÑÐ¸ÑÐ°ÑÐ°
spread_odd.title=Ð¡Ð¿Ð¾ÑÐ¸ ÑÐ¸ÑÐµÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ° ÐºÐ¾ÑÐµ Ð¿Ð¾ÑÐ¸ÑÑ Ð½ÐµÐ¿Ð°ÑÐ½Ð¸Ð¼ Ð±ÑÐ¾ÑÐµÐ¼
spread_odd_label=ÐÐµÐ¿Ð°ÑÐ½Ð° ÑÐ°ÑÐ¿ÑÐ¾ÑÑÐ¸ÑÐ°ÑÐ°
spread_even.title=Ð¡Ð¿Ð¾ÑÐ¸ ÑÐ¸ÑÐµÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ° ÐºÐ¾ÑÐµ Ð¿Ð¾ÑÐ¸ÑÑ Ð¿Ð°ÑÐ½Ð¸Ð¼ Ð±ÑÐ¾ÑÐµÐ¼
spread_even_label=ÐÐ°ÑÐ½Ð° ÑÐ°ÑÐ¿ÑÐ¾ÑÑÐ¸ÑÐ°ÑÐ°

# Document properties dialog box
document_properties.title=ÐÐ°ÑÐ°Ð¼ÐµÑÑÐ¸ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_label=ÐÐ°ÑÐ°Ð¼ÐµÑÑÐ¸ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_file_name=ÐÐ¼Ðµ Ð´Ð°ÑÐ¾ÑÐµÐºÐµ:
document_properties_file_size=ÐÐµÐ»Ð¸ÑÐ¸Ð½Ð° Ð´Ð°ÑÐ¾ÑÐµÐºÐµ:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} B)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} B)
document_properties_title=ÐÐ°ÑÐ»Ð¾Ð²:
document_properties_author=ÐÑÑÐ¾Ñ:
document_properties_subject=Ð¢ÐµÐ¼Ð°:
document_properties_keywords=ÐÑÑÑÐ½Ðµ ÑÐµÑÐ¸:
document_properties_creation_date=ÐÐ°ÑÑÐ¼ ÐºÑÐµÐ¸ÑÐ°ÑÐ°:
document_properties_modification_date=ÐÐ°ÑÑÐ¼ Ð¼Ð¾Ð´Ð¸ÑÐ¸ÐºÐ°ÑÐ¸ÑÐµ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Ð¡ÑÐ²Ð°ÑÐ°Ð»Ð°Ñ:
document_properties_producer=PDF Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾ÑÐ°Ñ:
document_properties_version=PDF Ð²ÐµÑÐ·Ð¸ÑÐ°:
document_properties_page_count=ÐÑÐ¾Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°:
document_properties_page_size=ÐÐµÐ»Ð¸ÑÐ¸Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐµ:
document_properties_page_size_unit_inches=Ð¸Ð½
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=ÑÑÐ¿ÑÐ°Ð²Ð½Ð¾
document_properties_page_size_orientation_landscape=Ð²Ð¾Ð´Ð¾ÑÐ°Ð²Ð½Ð¾
document_properties_page_size_name_a3=Ð3
document_properties_page_size_name_a4=Ð4
document_properties_page_size_name_letter=Ð¡Ð»Ð¾Ð²Ð¾
document_properties_page_size_name_legal=ÐÑÐ°Ð²Ð°
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ÐÑÐ· Ð²ÐµÐ± Ð¿ÑÐ¸ÐºÐ°Ð·:
document_properties_linearized_yes=ÐÐ°
document_properties_linearized_no=ÐÐµ
document_properties_close=ÐÐ°ÑÐ²Ð¾ÑÐ¸

print_progress_message=ÐÑÐ¸Ð¿ÑÐµÐ¼Ð°Ð¼ Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ñ Ð·Ð° ÑÑÐ°Ð¼Ð¿Ð°ÑÐµâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÐÑÐºÐ°Ð¶Ð¸

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ Ð´Ð¾Ð´Ð°ÑÐ½Ñ Ð¿Ð°Ð»ÐµÑÑ
toggle_sidebar_notification2.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸/ÑÐ°ÐºÑÐ¸Ñ Ð±Ð¾ÑÐ½Ñ ÑÑÐ°ÐºÑ (Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ñ ÑÐ°Ð´ÑÐ¶Ð¸ ÐºÐ¾Ð½ÑÑÑÑ/Ð¿ÑÐ¸Ð»Ð¾Ð³Ðµ/ÑÐ»Ð¾ÑÐµÐ²Ðµ)
toggle_sidebar_label=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ Ð´Ð¾Ð´Ð°ÑÐ½Ñ Ð¿Ð°Ð»ÐµÑÑ
document_outline.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ ÑÑÑÑÐºÑÑÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ° (Ð´Ð²Ð¾ÑÑÑÑÐºÐ¸Ð¼ ÐºÐ»Ð¸ÐºÐ¾Ð¼ Ð¿ÑÐ¾ÑÐ¸ÑÑÑÐµÑÐµ/ÑÐºÑÐ¿ÑÐ°ÑÐµ ÑÐ²Ðµ ÑÑÐ°Ð²ÐºÐµ)
document_outline_label=ÐÐ¾Ð½ÑÑÑÐ° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°
attachments.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ Ð¿ÑÐ¸Ð»Ð¾Ð³Ðµ
attachments_label=ÐÑÐ¸Ð»Ð¾Ð·Ð¸
layers.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ ÑÐ»Ð¾ÑÐµÐ²Ðµ (Ð´ÑÐ¿Ð»Ð¸ ÐºÐ»Ð¸Ðº Ð·Ð° Ð²ÑÐ°ÑÐ°ÑÐµ ÑÐ²Ð¸Ñ ÑÐ»Ð¾ÑÐµÐ²Ð° Ñ Ð¿Ð¾Ð´ÑÐ°Ð·ÑÐ¼ÐµÐ²Ð°Ð½Ð¾ ÑÑÐ°ÑÐµ)
layers_label=Ð¡Ð»Ð¾ÑÐµÐ²Ð¸
thumbs.title=ÐÑÐ¸ÐºÐ°Ð¶Ð¸ ÑÐ»Ð¸ÑÐ¸ÑÐµ
thumbs_label=Ð¡Ð»Ð¸ÑÐ¸ÑÐµ
current_outline_item.title=ÐÑÐ¾Ð½Ð°ÑÐ¸ÑÐµ ÑÑÐµÐ½ÑÑÐ½Ð¸ ÐµÐ»ÐµÐ¼ÐµÐ½Ñ ÑÑÑÑÐºÑÑÑÐµ
current_outline_item_label=Ð¢ÑÐµÐ½ÑÑÐ½Ð° ÐºÐ¾Ð½ÑÑÑÐ°
findbar.title=ÐÑÐ¾Ð½Ð°ÑÐ¸ Ñ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ
findbar_label=ÐÑÐ¾Ð½Ð°ÑÐ¸

additional_layers=ÐÐ¾Ð´Ð°ÑÐ½Ð¸ ÑÐ»Ð¾ÑÐµÐ²Ð¸
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ð¡Ð»Ð¸ÑÐ¸ÑÐ° Ð¾Ð´ ÑÑÑÐ°Ð½Ð¸ÑÐµ {{page}}

# Find panel button title and messages
find_input.title=ÐÑÐ¾Ð½Ð°ÑÐ¸
find_input.placeholder=ÐÑÐ¾Ð½Ð°ÑÐ¸ Ñ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑâ¦
find_previous.title=ÐÑÐ¾Ð½Ð°ÑÐ¸ Ð¿ÑÐµÑÑÐ¾Ð´Ð½Ð¾ Ð¿Ð¾ÑÐ°Ð²ÑÐ¸Ð²Ð°ÑÐµ ÑÑÐ°Ð·Ðµ
find_previous_label=ÐÑÐµÑÑÐ¾Ð´Ð½Ð°
find_next.title=ÐÑÐ¾Ð½Ð°ÑÐ¸ ÑÐ»ÐµÐ´ÐµÑÐµ Ð¿Ð¾ÑÐ°Ð²ÑÐ¸Ð²Ð°ÑÐµ ÑÑÐ°Ð·Ðµ
find_next_label=Ð¡Ð»ÐµÐ´ÐµÑÐ°
find_highlight=ÐÑÑÐ°ÐºÐ½ÑÑÐ¸ ÑÐ²Ðµ
find_match_case_label=ÐÐ¾Ð´ÑÐ´Ð°ÑÐ°ÑÐ°
find_entire_word_label=Ð¦ÐµÐ»Ðµ ÑÐµÑÐ¸
find_reached_top=ÐÐ¾ÑÑÐ¸Ð³Ð½ÑÑ Ð²ÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð½Ð°ÑÑÐ°Ð²Ð¸Ð¾ ÑÐ° Ð´Ð½Ð°
find_reached_bottom=ÐÐ¾ÑÑÐ¸Ð³Ð½ÑÑÐ¾ Ð´Ð½Ð¾ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð½Ð°ÑÑÐ°Ð²Ð¸Ð¾ ÑÐ° Ð²ÑÑÐ°
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Ð¾Ð´ {{total}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count[two]={{current}} Ð¾Ð´ {{total}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count[few]={{current}} Ð¾Ð´ {{total}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count[many]={{current}} Ð¾Ð´ {{total}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count[other]={{current}} Ð¾Ð´ {{total}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count_limit[one]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count_limit[two]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count_limit[few]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count_limit[many]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_match_count_limit[other]=ÐÐ¸ÑÐµ Ð¾Ð´ {{limit}} Ð¾Ð´Ð³Ð¾Ð²Ð°ÑÐ°
find_not_found=Ð¤ÑÐ°Ð·Ð° Ð½Ð¸ÑÐµ Ð¿ÑÐ¾Ð½Ð°ÑÐµÐ½Ð°

# Error panel labels
error_more_info=ÐÐ¸ÑÐµ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸ÑÐ°
error_less_info=ÐÐ°ÑÐµ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸ÑÐ°
error_close=ÐÐ°ÑÐ²Ð¾ÑÐ¸
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÐÐ¾ÑÑÐºÐ°: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ð¡ÑÐµÐº: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ÐÐ°ÑÐ¾ÑÐµÐºÐ°: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÐÐ¸Ð½Ð¸ÑÐ°: {{line}}
rendering_error=ÐÐ¾ÑÐ»Ð¾ ÑÐµ Ð´Ð¾ Ð³ÑÐµÑÐºÐµ Ð¿ÑÐ¸Ð»Ð¸ÐºÐ¾Ð¼ ÑÐµÐ½Ð´ÐµÑÐ¾Ð²Ð°ÑÐ° Ð¾Ð²Ðµ ÑÑÑÐ°Ð½Ð¸ÑÐµ.

# Predefined zoom values
page_scale_width=Ð¨Ð¸ÑÐ¸Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐµ
page_scale_fit=ÐÑÐ¸Ð»Ð°Ð³Ð¾Ð´Ð¸ ÑÑÑÐ°Ð½Ð¸ÑÑ
page_scale_auto=ÐÑÑÐ¾Ð¼Ð°ÑÑÐºÐ¾ ÑÐ²ÐµÐ»Ð¸ÑÐ°Ð²Ð°ÑÐµ
page_scale_actual=Ð¡ÑÐ²Ð°ÑÐ½Ð° Ð²ÐµÐ»Ð¸ÑÐ¸Ð½Ð°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=Ð£ÑÐ¸ÑÐ°Ð²Ð°ÑÐµâ¦
loading_error=ÐÐ¾ÑÐ»Ð¾ ÑÐµ Ð´Ð¾ Ð³ÑÐµÑÐºÐµ Ð¿ÑÐ¸Ð»Ð¸ÐºÐ¾Ð¼ ÑÑÐ¸ÑÐ°Ð²Ð°ÑÐ° PDF-Ð°.
invalid_file_error=PDF Ð´Ð°ÑÐ¾ÑÐµÐºÐ° ÑÐµ Ð½ÐµÐ²Ð°Ð¶ÐµÑÐ° Ð¸Ð»Ð¸ ÑÐµ Ð¾ÑÑÐµÑÐµÐ½Ð°.
missing_file_error=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐµ PDF Ð´Ð°ÑÐ¾ÑÐµÐºÐ°.
unexpected_response_error=ÐÐµÐ¾ÑÐµÐºÐ¸Ð²Ð°Ð½ Ð¾Ð´Ð³Ð¾Ð²Ð¾Ñ Ð¾Ð´ ÑÐµÑÐ²ÐµÑÐ°.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ÐºÐ¾Ð¼ÐµÐ½ÑÐ°Ñ]
password_label=Ð£Ð½ÐµÑÐ¸ÑÐµ Ð»Ð¾Ð·Ð¸Ð½ÐºÑ Ð´Ð° Ð±Ð¸ÑÑÐµ Ð¾ÑÐ²Ð¾ÑÐ¸Ð»Ð¸ Ð¾Ð²Ð°Ñ PDF Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ð°Ñ.
password_invalid=ÐÐµÐ¸ÑÐ¿ÑÐ°Ð²Ð½Ð° Ð»Ð¾Ð·Ð¸Ð½ÐºÐ°. ÐÐ¾ÐºÑÑÐ°ÑÑÐµ Ð¿Ð¾Ð½Ð¾Ð²Ð¾.
password_ok=Ð£ ÑÐµÐ´Ñ
password_cancel=ÐÑÐºÐ°Ð¶Ð¸

printing_not_supported=Ð£Ð¿Ð¾Ð·Ð¾ÑÐµÑÐµ: Ð¨ÑÐ°Ð¼Ð¿Ð°ÑÐµ Ð½Ð¸ÑÐµ Ñ Ð¿Ð¾ÑÐ¿ÑÐ½Ð¾ÑÑÐ¸ Ð¿Ð¾Ð´ÑÐ¶Ð°Ð½Ð¾ Ñ Ð¾Ð²Ð¾Ð¼ Ð¿ÑÐµÐ³Ð»ÐµÐ´Ð°ÑÑ.
printing_not_ready=Ð£Ð¿Ð¾Ð·Ð¾ÑÐµÑÐµ: PDF Ð½Ð¸ÑÐµ Ñ Ð¿Ð¾ÑÐ¿ÑÐ½Ð¾ÑÑÐ¸ ÑÑÐ¸ÑÐ°Ð½ Ð·Ð° ÑÑÐ°Ð¼Ð¿Ñ.
web_fonts_disabled=ÐÐµÐ± ÑÐ¾Ð½ÑÐ¾Ð²Ð¸ ÑÑ Ð¾Ð½ÐµÐ¼Ð¾Ð³ÑÑÐµÐ½Ð¸: Ð½Ðµ Ð¼Ð¾Ð³Ñ ÐºÐ¾ÑÐ¸ÑÑÐ¸ÑÐ¸ ÑÐ³ÑÐ°ÑÐµÐ½Ðµ PDF ÑÐ¾Ð½ÑÐ¾Ð²Ðµ.
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
