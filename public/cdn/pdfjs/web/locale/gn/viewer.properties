# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Kuatiarogue mboyvegua
previous_label=Mboyvegua
next.title=Kuatiarogue upeigua
next_label=Upeigua

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Kuatiarogue
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} gui
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=MomichÄ©
zoom_out_label=MomichÄ©
zoom_in.title=Mbotuicha
zoom_in_label=Mbotuicha
zoom.title=Tuichakue
presentation_mode.title=Jehechauka reko moambue
presentation_mode_label=Jehechauka reko
open_file.title=MarandurendÃ¡pe jeike
open_file_label=Jeike
print.title=Monguatia
print_label=Monguatia
download.title=Mboguejy
download_label=Mboguejy
bookmark.title=AgÌagua jehecha (mbohasarÃ£ tÃ©rÃ£ eike peteÄ© ovetÃ£ pyahÃºpe)
bookmark_label=AgÌagua jehecha

# Secondary toolbar and context menu
tools.title=Tembipuru
tools_label=Tembipuru
first_page.title=Kuatiarogue Ã±epyrÅ©me jeho
first_page_label=Kuatiarogue Ã±epyrÅ©me jeho
last_page.title=Kuatiarogue pahÃ¡pe jeho
last_page_label=Kuatiarogue pahÃ¡pe jeho
page_rotate_cw.title=AravÃ³icha mbojere
page_rotate_cw_label=AravÃ³icha mbojere
page_rotate_ccw.title=Aravo rapykue gotyo mbojere
page_rotate_ccw_label=Aravo rapykue gotyo mbojere

cursor_text_select_tool.title=Emyandy moÃ±eâáº½rÃ£ jeporavo rembipuru
cursor_text_select_tool_label=MoÃ±eâáº½rÃ£ jeporavo rembipuru
cursor_hand_tool.title=Tembipuru po pegua myandy
cursor_hand_tool_label=Tembipuru po pegua

scroll_vertical.title=Eipuru jekuâe ykeguÃ¡va
scroll_vertical_label=Jekuâe ykeguÃ¡va
scroll_horizontal.title=Eipuru jekuâe yvate gotyo
scroll_horizontal_label=Jekuâe yvate gotyo
scroll_wrapped.title=Eipuru jekuâe mbohyrupyre
scroll_wrapped_label=Jekuâe mbohyrupyre

spread_none.title=Ani ejuaju spreads kuatiarogue ndive
spread_none_label=Spreads á»¹re
spread_odd.title=Embojuaju kuatiarogue jepysokue eÃ±epyrÅ©vo kuatiarogue impar-vagui
spread_odd_label=Spreads impar
spread_even.title=Embojuaju kuatiarogue jepysokue eÃ±epyrÅ©vo kuatiarogue par-vagui
spread_even_label=Ipukuve uvei

# Document properties dialog box
document_properties.title=Kuatia mbaâeteeâ¦
document_properties_label=Kuatia mbaâeteeâ¦
document_properties_file_name=Marandurenda rÃ©ra:
document_properties_file_size=Marandurenda tuichakue:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Teratee:
document_properties_author=ApohÃ¡ra:
document_properties_subject=Mbaâegua:
document_properties_keywords=Jehero:
document_properties_creation_date=TeÃ±oihague arange:
document_properties_modification_date=IÃ±ambue hague arange:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Apoâypyha:
document_properties_producer=PDF mbosakoâiha:
document_properties_version=PDF mbojuehegua:
document_properties_page_count=Kuatiarogue papapy:
document_properties_page_size=Kuatiarogue tuichakue:
document_properties_page_size_unit_inches=Amo
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=OÄ©hÃ¡icha
document_properties_page_size_orientation_landscape=apaisado
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=KuatiaÃ±eâáº½
document_properties_page_size_name_legal=Tee
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ãanduti jahecha pyaâe:
document_properties_linearized_yes=AÃ±ete
document_properties_linearized_no=AhÃ¡niri
document_properties_close=Mboty

print_progress_message=Embosakoâi kuatia emonguatia hagÌuaâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Heja

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Tenda yke moambue
toggle_sidebar_notification2.title=Embojopyru tenda ykegua (kuatia oguereko kuaakaha/moirÅ©ha/Ã±uÃ£ha)
toggle_sidebar_label=Tenda yke moambue
document_outline.title=Ehechauka kuatia rape (eikutu mokÃµi jey embotuicha/emomichÄ© hagÌua opavavete mbaâepuru)
document_outline_label=Kuatia apopyre
attachments.title=MoirÅ©ha jehechauka
attachments_label=MoirÅ©ha
layers.title=Ehechauka Ã±uÃ£ha (eikutu joâa emombaâapo hagÌua opaite Ã±uÃ£ha tekoypÃ½pe)
layers_label=ÃuÃ£ha
thumbs.title=MbaâemirÄ© jehechauka
thumbs_label=MbaâemirÄ©
current_outline_item.title=Eheka mbaâepuru agÌaguaitÃ©va
current_outline_item_label=Mbaâepuru agÌaguaitÃ©va
findbar.title=KuatiÃ¡pe jeheka
findbar_label=Juhu

additional_layers=ÃuÃ£ha moirÅ©guÃ¡va
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Kuatiarogue {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Kuatiarogue {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Kuatiarogue mbaâemirÄ© {{page}}

# Find panel button title and messages
find_input.title=Juhu
find_input.placeholder=KuatiÃ¡pe jejuhuâ¦
find_previous.title=Ejuhu Ã±eâáº½rysÃ½i osáº½âypy hague
find_previous_label=Mboyvegua
find_next.title=Eho Ã±eâáº½ juhupyre upeiguÃ¡vape
find_next_label=Upeigua
find_highlight=Embojekuaavepa
find_match_case_label=Ejesareko taiguasu/taimichÄ©re
find_entire_word_label=Ãeâáº½ oÄ©mbÃ¡va 
find_reached_top=Ojehupyty kuatia Ã±epyrÅ©, okuâejeÃ½ta kuatia paha guive
find_reached_bottom=Ojehupyty kuatia paha, okuâejeÃ½ta kuatia Ã±epyrÅ© guive
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} {{total}} ojojoguÃ¡va
find_match_count[two]={{current}} {{total}} ojojoguÃ¡va
find_match_count[few]={{current}} {{total}} ojojoguÃ¡va
find_match_count[many]={{current}} {{total}} ojojoguÃ¡va
find_match_count[other]={{current}} {{total}} ojojoguÃ¡va
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Hetave {{limit}} ojojoguÃ¡va
find_match_count_limit[one]=Hetave {{limit}} ojojogua
find_match_count_limit[two]=Hetave {{limit}} ojojoguÃ¡va
find_match_count_limit[few]=Hetave {{limit}} ojojoguÃ¡va
find_match_count_limit[many]=Hetave {{limit}} ojojoguÃ¡va
find_match_count_limit[other]=Hetave {{limit}} ojojoguÃ¡va
find_not_found=Ãeâáº½rysÃ½i ojejuhuâá»¹va

# Error panel labels
error_more_info=Maranduve
error_less_info=Saâive marandu
error_close=Mboty
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ãeâáº½mondo: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Mbojoâapy: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Marandurenda: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Tairenda: {{line}}
rendering_error=Oiko jejavy ehechaukasÃ©vo kuatiarogue.

# Predefined zoom values
page_scale_width=Kuatiarogue pekue
page_scale_fit=Kuatiarogue Ã±emoÄ©porÃ£
page_scale_auto=Tuichakue ijeheguÃ­va
page_scale_actual=Tuichakue agÌagua
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=Henyháº½hÃ­naâ¦
loading_error=Oiko jejavy PDF oÃ±emyeÃ±yháº½nguÃ©vo.
invalid_file_error=PDF marandurenda ndoikÃ³iva tÃ©rÃ£ ivaipyrÃ©va.
missing_file_error=NdaipÃ³ri PDF marandurenda
unexpected_response_error=Mohendahavusu mbohovÃ¡i Ã±ehaâarÃµâá»¹va.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Jehaipy {{type}}]
password_label=Emoinge Ã±eâáº½Ã±emi eipeâa hagÌua ko marandurenda PDF.
password_invalid=Ãeâáº½Ã±emi ndoikÃ³iva. EhaâÃ£ jey.
password_ok=MONEÄ¨
password_cancel=Heja

printing_not_supported=KyhyjerÃ£: Ãembokuatia ndojokupytypÃ¡i ko kundahÃ¡ra ndive.
printing_not_ready=KyhyjerÃ£: Ko PDF nahenyháº½mbÃ¡i oÃ±embokuatia hagÌuÃ¡icha.
web_fonts_disabled=Ãanduti taity oÃ±emongÃ©ma: ndaikatumoâÃ£i eipuru PDF jehaiâÃ­va taity.
