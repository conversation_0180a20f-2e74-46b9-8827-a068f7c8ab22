# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¸«à¸à¹à¸²à¸à¹à¸­à¸à¸«à¸à¹à¸²
previous_label=à¸à¹à¸­à¸à¸«à¸à¹à¸²
next.title=à¸«à¸à¹à¸²à¸à¸±à¸à¹à¸
next_label=à¸à¸±à¸à¹à¸

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=à¸«à¸à¹à¸²
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=à¸à¸²à¸ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} à¸à¸²à¸ {{pagesCount}})

zoom_out.title=à¸à¸¹à¸¡à¸­à¸­à¸
zoom_out_label=à¸à¸¹à¸¡à¸­à¸­à¸
zoom_in.title=à¸à¸¹à¸¡à¹à¸à¹à¸²
zoom_in_label=à¸à¸¹à¸¡à¹à¸à¹à¸²
zoom.title=à¸à¸¹à¸¡
presentation_mode.title=à¸ªà¸¥à¸±à¸à¹à¸à¹à¸à¹à¸«à¸¡à¸à¸à¸²à¸£à¸à¸³à¹à¸ªà¸à¸­
presentation_mode_label=à¹à¸«à¸¡à¸à¸à¸²à¸£à¸à¸³à¹à¸ªà¸à¸­
open_file.title=à¹à¸à¸´à¸à¹à¸à¸¥à¹
open_file_label=à¹à¸à¸´à¸
print.title=à¸à¸´à¸¡à¸à¹
print_label=à¸à¸´à¸¡à¸à¹
download.title=à¸à¸²à¸§à¸à¹à¹à¸«à¸¥à¸
download_label=à¸à¸²à¸§à¸à¹à¹à¸«à¸¥à¸
bookmark.title=à¸¡à¸¸à¸¡à¸¡à¸­à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸ (à¸à¸±à¸à¸¥à¸­à¸à¸«à¸£à¸·à¸­à¹à¸à¸´à¸à¹à¸à¸«à¸à¹à¸²à¸à¹à¸²à¸à¹à¸«à¸¡à¹)
bookmark_label=à¸¡à¸¸à¸¡à¸¡à¸­à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸

# Secondary toolbar and context menu
tools.title=à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­
tools_label=à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­
first_page.title=à¹à¸à¸¢à¸±à¸à¸«à¸à¹à¸²à¹à¸£à¸
first_page_label=à¹à¸à¸¢à¸±à¸à¸«à¸à¹à¸²à¹à¸£à¸
last_page.title=à¹à¸à¸¢à¸±à¸à¸«à¸à¹à¸²à¸ªà¸¸à¸à¸à¹à¸²à¸¢
last_page_label=à¹à¸à¸¢à¸±à¸à¸«à¸à¹à¸²à¸ªà¸¸à¸à¸à¹à¸²à¸¢
page_rotate_cw.title=à¸«à¸¡à¸¸à¸à¸à¸²à¸¡à¹à¸à¹à¸¡à¸à¸²à¸¬à¸´à¸à¸²
page_rotate_cw_label=à¸«à¸¡à¸¸à¸à¸à¸²à¸¡à¹à¸à¹à¸¡à¸à¸²à¸¬à¸´à¸à¸²
page_rotate_ccw.title=à¸«à¸¡à¸¸à¸à¸à¸§à¸à¹à¸à¹à¸¡à¸à¸²à¸¬à¸´à¸à¸²
page_rotate_ccw_label=à¸«à¸¡à¸¸à¸à¸à¸§à¸à¹à¸à¹à¸¡à¸à¸²à¸¬à¸´à¸à¸²

cursor_text_select_tool.title=à¹à¸à¸´à¸à¹à¸à¹à¸à¸²à¸à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­à¸à¸²à¸£à¹à¸¥à¸·à¸­à¸à¸à¹à¸­à¸à¸§à¸²à¸¡
cursor_text_select_tool_label=à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­à¸à¸²à¸£à¹à¸¥à¸·à¸­à¸à¸à¹à¸­à¸à¸§à¸²à¸¡
cursor_hand_tool.title=à¹à¸à¸´à¸à¹à¸à¹à¸à¸²à¸à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­à¸¡à¸·à¸­
cursor_hand_tool_label=à¹à¸à¸£à¸·à¹à¸­à¸à¸¡à¸·à¸­à¸¡à¸·à¸­

scroll_vertical.title=à¹à¸à¹à¸à¸²à¸£à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸§à¸à¸±à¹à¸
scroll_vertical_label=à¸à¸²à¸£à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸§à¸à¸±à¹à¸
scroll_horizontal.title=à¹à¸à¹à¸à¸²à¸£à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸§à¸à¸­à¸
scroll_horizontal_label=à¸à¸²à¸£à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸§à¸à¸­à¸
scroll_wrapped.title=à¹à¸à¹à¸à¸²à¸£à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸à¸à¸¥à¸¸à¸¡
scroll_wrapped_label=à¹à¸¥à¸·à¹à¸­à¸à¹à¸à¸à¸à¸¥à¸¸à¸¡

spread_none.title=à¹à¸¡à¹à¸à¹à¸­à¸à¸£à¸§à¸¡à¸à¸²à¸£à¸à¸£à¸°à¸à¸²à¸¢à¸«à¸à¹à¸²
spread_none_label=à¹à¸¡à¹à¸à¸£à¸°à¸à¸²à¸¢
spread_odd.title=à¸£à¸§à¸¡à¸à¸²à¸£à¸à¸£à¸°à¸à¸²à¸¢à¸«à¸à¹à¸²à¹à¸£à¸´à¹à¸¡à¸à¸²à¸à¸«à¸à¹à¸²à¸à¸µà¹
spread_odd_label=à¸à¸£à¸°à¸à¸²à¸¢à¸­à¸¢à¹à¸²à¸à¹à¸«à¸¥à¸·à¸­à¹à¸¨à¸©
spread_even.title=à¸£à¸§à¸¡à¸à¸²à¸£à¸à¸£à¸°à¸à¸²à¸¢à¸«à¸à¹à¸²à¹à¸£à¸´à¹à¸¡à¸à¸²à¸à¸«à¸à¹à¸²à¸à¸¹à¹
spread_even_label=à¸à¸£à¸°à¸à¸²à¸¢à¸­à¸¢à¹à¸²à¸à¹à¸à¹à¸²à¹à¸à¸µà¸¢à¸¡

# Document properties dialog box
document_properties.title=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´à¹à¸­à¸à¸ªà¸²à¸£â¦
document_properties_label=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´à¹à¸­à¸à¸ªà¸²à¸£â¦
document_properties_file_name=à¸à¸·à¹à¸­à¹à¸à¸¥à¹:
document_properties_file_size=à¸à¸à¸²à¸à¹à¸à¸¥à¹:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} à¹à¸à¸à¹)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} à¹à¸à¸à¹)
document_properties_title=à¸à¸·à¹à¸­à¹à¸£à¸·à¹à¸­à¸:
document_properties_author=à¸à¸¹à¹à¸ªà¸£à¹à¸²à¸:
document_properties_subject=à¸à¸·à¹à¸­à¹à¸£à¸·à¹à¸­à¸:
document_properties_keywords=à¸à¸³à¸ªà¸³à¸à¸±à¸:
document_properties_creation_date=à¸§à¸±à¸à¸à¸µà¹à¸ªà¸£à¹à¸²à¸:
document_properties_modification_date=à¸§à¸±à¸à¸à¸µà¹à¹à¸à¹à¹à¸:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¸à¸¹à¹à¸ªà¸£à¹à¸²à¸:
document_properties_producer=à¸à¸¹à¹à¸à¸¥à¸´à¸ PDF:
document_properties_version=à¸£à¸¸à¹à¸ PDF:
document_properties_page_count=à¸à¸³à¸à¸§à¸à¸«à¸à¹à¸²:
document_properties_page_size=à¸à¸à¸²à¸à¸«à¸à¹à¸²:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=à¹à¸à¸§à¸à¸±à¹à¸
document_properties_page_size_orientation_landscape=à¹à¸à¸§à¸à¸­à¸
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=à¸à¸à¸«à¸¡à¸²à¸¢
document_properties_page_size_name_legal=à¸à¹à¸­à¸à¸à¸«à¸¡à¸²à¸¢
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=à¸¡à¸¸à¸¡à¸¡à¸­à¸à¹à¸§à¹à¸à¹à¸à¸à¸£à¸§à¸à¹à¸£à¹à¸§:
document_properties_linearized_yes=à¹à¸à¹
document_properties_linearized_no=à¹à¸¡à¹
document_properties_close=à¸à¸´à¸

print_progress_message=à¸à¸³à¸¥à¸±à¸à¹à¸à¸£à¸µà¸¢à¸¡à¹à¸­à¸à¸ªà¸²à¸£à¸ªà¸³à¸«à¸£à¸±à¸à¸à¸²à¸£à¸à¸´à¸¡à¸à¹â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=à¸¢à¸à¹à¸¥à¸´à¸

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à¹à¸à¸´à¸/à¸à¸´à¸à¹à¸à¸à¸à¹à¸²à¸
toggle_sidebar_notification2.title=à¹à¸à¸´à¸/à¸à¸´à¸à¹à¸à¸à¸à¹à¸²à¸ (à¹à¸­à¸à¸ªà¸²à¸£à¸¡à¸µà¹à¸à¹à¸²à¸£à¹à¸²à¸/à¹à¸à¸¥à¹à¹à¸à¸/à¹à¸¥à¹à¸¢à¸­à¸£à¹)
toggle_sidebar_label=à¹à¸à¸´à¸/à¸à¸´à¸à¹à¸à¸à¸à¹à¸²à¸
document_outline.title=à¹à¸ªà¸à¸à¹à¸à¹à¸²à¸£à¹à¸²à¸à¹à¸­à¸à¸ªà¸²à¸£ (à¸à¸¥à¸´à¸à¸ªà¸­à¸à¸à¸£à¸±à¹à¸à¹à¸à¸·à¹à¸­à¸à¸¢à¸²à¸¢/à¸¢à¸¸à¸à¸£à¸²à¸¢à¸à¸²à¸£à¸à¸±à¹à¸à¸«à¸¡à¸)
document_outline_label=à¹à¸à¹à¸²à¸£à¹à¸²à¸à¹à¸­à¸à¸ªà¸²à¸£
attachments.title=à¹à¸ªà¸à¸à¹à¸à¸¥à¹à¹à¸à¸
attachments_label=à¹à¸à¸¥à¹à¹à¸à¸
layers.title=à¹à¸ªà¸à¸à¹à¸¥à¹à¸¢à¸­à¸£à¹ (à¸à¸¥à¸´à¸à¸ªà¸­à¸à¸à¸£à¸±à¹à¸à¹à¸à¸·à¹à¸­à¸£à¸µà¹à¸à¹à¸à¹à¸¥à¹à¸¢à¸­à¸£à¹à¸à¸±à¹à¸à¸«à¸¡à¸à¹à¸à¹à¸à¸ªà¸à¸²à¸à¸°à¹à¸£à¸´à¹à¸¡à¸à¹à¸)
layers_label=à¹à¸¥à¹à¸¢à¸­à¸£à¹
thumbs.title=à¹à¸ªà¸à¸à¸ à¸²à¸à¸à¸à¸²à¸à¸¢à¹à¸­
thumbs_label=à¸ à¸²à¸à¸à¸à¸²à¸à¸¢à¹à¸­
current_outline_item.title=à¸à¹à¸à¸«à¸²à¸£à¸²à¸¢à¸à¸²à¸£à¹à¸à¹à¸²à¸£à¹à¸²à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸
current_outline_item_label=à¸£à¸²à¸¢à¸à¸²à¸£à¹à¸à¹à¸²à¸£à¹à¸²à¸à¸à¸±à¸à¸à¸¸à¸à¸±à¸
findbar.title=à¸à¹à¸à¸«à¸²à¹à¸à¹à¸­à¸à¸ªà¸²à¸£
findbar_label=à¸à¹à¸à¸«à¸²

additional_layers=à¹à¸¥à¹à¸¢à¸­à¸£à¹à¹à¸à¸´à¹à¸¡à¹à¸à¸´à¸¡
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=à¸«à¸à¹à¸² {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¸«à¸à¹à¸² {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¸ à¸²à¸à¸à¸à¸²à¸à¸¢à¹à¸­à¸à¸­à¸à¸«à¸à¹à¸² {{page}}

# Find panel button title and messages
find_input.title=à¸à¹à¸à¸«à¸²
find_input.placeholder=à¸à¹à¸à¸«à¸²à¹à¸à¹à¸­à¸à¸ªà¸²à¸£â¦
find_previous.title=à¸«à¸²à¸à¸³à¹à¸«à¸à¹à¸à¸à¹à¸­à¸à¸«à¸à¹à¸²à¸à¸­à¸à¸§à¸¥à¸µ
find_previous_label=à¸à¹à¸­à¸à¸«à¸à¹à¸²
find_next.title=à¸«à¸²à¸à¸³à¹à¸«à¸à¹à¸à¸à¸±à¸à¹à¸à¸à¸­à¸à¸§à¸¥à¸µ
find_next_label=à¸à¸±à¸à¹à¸
find_highlight=à¹à¸à¹à¸à¸ªà¸µà¸à¸±à¹à¸à¸«à¸¡à¸
find_match_case_label=à¸à¸±à¸§à¸à¸´à¸¡à¸à¹à¹à¸«à¸à¹à¹à¸¥à¹à¸à¸à¸£à¸à¸à¸±à¸
find_entire_word_label=à¸à¸±à¹à¸à¸à¸³
find_reached_top=à¸à¹à¸à¸«à¸²à¸à¸¶à¸à¸à¸¸à¸à¹à¸£à¸´à¹à¸¡à¸à¹à¸à¸à¸­à¸à¸«à¸à¹à¸² à¹à¸£à¸´à¹à¸¡à¸à¹à¸à¸à¹à¸­à¸à¸²à¸à¸à¹à¸²à¸à¸¥à¹à¸²à¸
find_reached_bottom=à¸à¹à¸à¸«à¸²à¸à¸¶à¸à¸à¸¸à¸à¸ªà¸´à¹à¸à¸ªà¸¸à¸à¸«à¸à¹à¸² à¹à¸£à¸´à¹à¸¡à¸à¹à¸à¸à¹à¸­à¸à¸²à¸à¸à¹à¸²à¸à¸à¸
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} à¸à¸²à¸ {{total}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count[two]={{current}} à¸à¸²à¸ {{total}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count[few]={{current}} à¸à¸²à¸ {{total}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count[many]={{current}} à¸à¸²à¸ {{total}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count[other]={{current}} à¸à¸²à¸ {{total}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count_limit[one]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count_limit[two]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count_limit[few]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count_limit[many]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_match_count_limit[other]=à¸¡à¸²à¸à¸à¸§à¹à¸² {{limit}} à¸à¸µà¹à¸à¸£à¸à¸à¸±à¸
find_not_found=à¹à¸¡à¹à¸à¸à¸§à¸¥à¸µ

# Error panel labels
error_more_info=à¸à¹à¸­à¸¡à¸¹à¸¥à¹à¸à¸´à¹à¸¡à¹à¸à¸´à¸¡
error_less_info=à¸à¹à¸­à¸¡à¸¹à¸¥à¸à¹à¸­à¸¢à¸¥à¸
error_close=à¸à¸´à¸
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=à¸à¹à¸­à¸à¸§à¸²à¸¡: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=à¸ªà¹à¸à¸: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¹à¸à¸¥à¹: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¸à¸£à¸£à¸à¸±à¸: {{line}}
rendering_error=à¹à¸à¸´à¸à¸à¹à¸­à¸à¸´à¸à¸à¸¥à¸²à¸à¸à¸à¸°à¹à¸£à¸à¹à¸à¸­à¸£à¹à¸«à¸à¹à¸²

# Predefined zoom values
page_scale_width=à¸à¸§à¸²à¸¡à¸à¸§à¹à¸²à¸à¸«à¸à¹à¸²
page_scale_fit=à¸à¸­à¸à¸µà¸«à¸à¹à¸²
page_scale_auto=à¸à¸¹à¸¡à¸­à¸±à¸à¹à¸à¸¡à¸±à¸à¸´
page_scale_actual=à¸à¸à¸²à¸à¸à¸£à¸´à¸
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=à¸à¸³à¸¥à¸±à¸à¹à¸«à¸¥à¸â¦
loading_error=à¹à¸à¸´à¸à¸à¹à¸­à¸à¸´à¸à¸à¸¥à¸²à¸à¸à¸à¸°à¹à¸«à¸¥à¸ PDF
invalid_file_error=à¹à¸à¸¥à¹ PDF à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸à¸«à¸£à¸·à¸­à¹à¸ªà¸µà¸¢à¸«à¸²à¸¢
missing_file_error=à¹à¸à¸¥à¹ PDF à¸«à¸²à¸¢à¹à¸
unexpected_response_error=à¸à¸²à¸£à¸à¸­à¸à¸ªà¸à¸­à¸à¸à¸­à¸à¹à¸à¸´à¸£à¹à¸à¹à¸§à¸­à¸£à¹à¸à¸µà¹à¹à¸¡à¹à¸à¸²à¸à¸à¸´à¸

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[à¸à¸³à¸­à¸à¸´à¸à¸²à¸¢à¸à¸£à¸°à¸à¸­à¸ {{type}}]
password_label=à¸à¹à¸­à¸à¸£à¸«à¸±à¸ªà¸à¹à¸²à¸à¹à¸à¸·à¹à¸­à¹à¸à¸´à¸à¹à¸à¸¥à¹ PDF à¸à¸µà¹
password_invalid=à¸£à¸«à¸±à¸ªà¸à¹à¸²à¸à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸ à¹à¸à¸£à¸à¸¥à¸­à¸à¸­à¸µà¸à¸à¸£à¸±à¹à¸
password_ok=à¸à¸à¸¥à¸
password_cancel=à¸¢à¸à¹à¸¥à¸´à¸

printing_not_supported=à¸à¸³à¹à¸à¸·à¸­à¸: à¹à¸à¸£à¸²à¸§à¹à¹à¸à¸­à¸£à¹à¸à¸µà¹à¹à¸¡à¹à¹à¸à¹à¸ªà¸à¸±à¸à¸ªà¸à¸¸à¸à¸à¸²à¸£à¸à¸´à¸¡à¸à¹à¸­à¸¢à¹à¸²à¸à¹à¸à¹à¸¡à¸à¸µà¹
printing_not_ready=à¸à¸³à¹à¸à¸·à¸­à¸: PDF à¹à¸¡à¹à¹à¸à¹à¸£à¸±à¸à¸à¸²à¸£à¹à¸«à¸¥à¸à¸­à¸¢à¹à¸²à¸à¹à¸à¹à¸¡à¸à¸µà¹à¸ªà¸³à¸«à¸£à¸±à¸à¸à¸²à¸£à¸à¸´à¸¡à¸à¹
web_fonts_disabled=à¹à¸à¸à¸­à¸±à¸à¸©à¸£à¹à¸§à¹à¸à¸à¸¹à¸à¸à¸´à¸à¹à¸à¹à¸à¸²à¸: à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¹à¸à¹à¹à¸à¸à¸­à¸±à¸à¸©à¸£ PDF à¸à¸±à¸à¸à¸±à¸§
