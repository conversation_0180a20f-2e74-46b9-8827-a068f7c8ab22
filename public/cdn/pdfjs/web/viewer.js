(()=>{"use strict";var i=[,(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OptionKind=t.compatibilityParams=t.AppOptions=void 0;const i=Object.create(null);t.compatibilityParams=i;{var n="undefined"!=typeof navigator&&navigator.userAgent||"",s="undefined"!=typeof navigator&&navigator.platform||"",a="undefined"!=typeof navigator&&navigator.maxTouchPoints||1;const h=/Android/.test(n),d=/\b(iPad|iPhone|iPod)(?=;)/.test(n)||"MacIntel"===s&&1<a,c=/CriOS/.test(n);c&&(i.disableCreateObjectURL=!0),(d||h)&&(i.maxCanvasPixels=5242880)}const r={VIEWER:2,API:4,WORKER:8,PREFERENCE:128},o={annotationMode:{value:2,kind:(t.OptionKind=r).VIEWER+r.PREFERENCE},cursorToolOnLoad:{value:0,kind:r.VIEWER+r.PREFERENCE},defaultUrl:{value:"compressed.tracemonkey-pldi-09.pdf",kind:r.VIEWER},defaultZoomValue:{value:"",kind:r.VIEWER+r.PREFERENCE},disableHistory:{value:!1,kind:r.VIEWER},disablePageLabels:{value:!1,kind:r.VIEWER+r.PREFERENCE},enablePermissions:{value:!1,kind:r.VIEWER+r.PREFERENCE},enablePrintAutoRotate:{value:!0,kind:r.VIEWER+r.PREFERENCE},enableScripting:{value:!0,kind:r.VIEWER+r.PREFERENCE},externalLinkRel:{value:"noopener noreferrer nofollow",kind:r.VIEWER},externalLinkTarget:{value:0,kind:r.VIEWER+r.PREFERENCE},historyUpdateUrl:{value:!1,kind:r.VIEWER+r.PREFERENCE},ignoreDestinationZoom:{value:!1,kind:r.VIEWER+r.PREFERENCE},imageResourcesPath:{value:"./images/",kind:r.VIEWER},maxCanvasPixels:{value:16777216,compatibility:i.maxCanvasPixels,kind:r.VIEWER},pdfBugEnabled:{value:!1,kind:r.VIEWER+r.PREFERENCE},printResolution:{value:150,kind:r.VIEWER},renderer:{value:"canvas",kind:r.VIEWER},sidebarViewOnLoad:{value:-1,kind:r.VIEWER+r.PREFERENCE},scrollModeOnLoad:{value:-1,kind:r.VIEWER+r.PREFERENCE},spreadModeOnLoad:{value:-1,kind:r.VIEWER+r.PREFERENCE},textLayerMode:{value:1,kind:r.VIEWER+r.PREFERENCE},useOnlyCssZoom:{value:!1,kind:r.VIEWER+r.PREFERENCE},viewerCssTheme:{value:0,kind:r.VIEWER+r.PREFERENCE},viewOnLoad:{value:0,kind:r.VIEWER+r.PREFERENCE},cMapPacked:{value:!0,kind:r.API},cMapUrl:{value:"../web/cmaps/",kind:r.API},disableAutoFetch:{value:!1,kind:r.API+r.PREFERENCE},disableFontFace:{value:!1,kind:r.API+r.PREFERENCE},disableRange:{value:!1,kind:r.API+r.PREFERENCE},disableStream:{value:!1,kind:r.API+r.PREFERENCE},docBaseUrl:{value:"",kind:r.API},enableXfa:{value:!0,kind:r.API+r.PREFERENCE},fontExtraProperties:{value:!1,kind:r.API},isEvalSupported:{value:!0,kind:r.API},maxImageSize:{value:-1,kind:r.API},pdfBug:{value:!1,kind:r.API},standardFontDataUrl:{value:"../web/standard_fonts/",kind:r.API},verbosity:{value:1,kind:r.API},workerPort:{value:null,kind:r.WORKER},workerSrc:{value:"../build/pdf.worker.js",kind:r.WORKER}};o.disablePreferences={value:!1,kind:r.VIEWER},o.locale={value:"undefined"!=typeof navigator?navigator.language:"en-US",kind:r.VIEWER},o.sandboxBundleSrc={value:"../build/pdf.sandbox.js",kind:r.VIEWER},o.renderer.kind+=r.PREFERENCE;const l=Object.create(null);t.AppOptions=class{constructor(){throw new Error("Cannot initialize AppOptions.")}static get(e){var t=l[e];if(void 0!==t)return t;e=o[e];return void 0!==e?e.compatibility??e.value:void 0}static getAll(e=null){const t=Object.create(null);for(const a in o){var i=o[a];if(e){if(0==(e&i.kind))continue;if(e===r.PREFERENCE){var n=i.value,s=typeof n;if("boolean"==s||"string"==s||"number"==s&&Number.isInteger(n)){t[a]=n;continue}throw new Error(`Invalid type for preference: ${a}`)}}n=l[a];t[a]=void 0!==n?n:i.compatibility??i.value}return t}static set(e,t){l[e]=t}static setAll(e){for(const t in e)l[t]=e[t]}static remove(e){delete l[e]}static _hasUserOptions(){return 0<Object.keys(l).length}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewerApplication=t.PDFPrintServiceFactory=t.DefaultExternalServices=void 0;var g=i(3),m=i(1),c=i(4),d=i(5),u=i(7),p=i(8),f=i(9),v=i(10),_=i(12),w=i(13),b=i(14),y=i(16),P=i(17),S=i(18),E=i(19),L=i(20),C=i(21),T=i(22),I=i(23),D=i(24),M=i(26),B=i(35),N=i(37),o=i(38);const n=1e3,s="enablePermissions",x={UNKNOWN:-1,PREVIOUS:0,INITIAL:1},l={AUTOMATIC:0,LIGHT:1,DARK:2},V=["1.0","1.1","1.2","1.3","1.4","1.5","1.6","1.7","1.8","1.9","2.0","2.1","2.2","2.3"],k=["acrobat distiller","acrobat pdfwriter","adobe livecycle","adobe pdf library","adobe photoshop","ghostscript","tcpdf","cairo","dvipdfm","dvips","pdftex","pdfkit","itext","prince","quarkxpress","mac os x","microsoft","openoffice","oracle","luradocument","pdf-xchange","antenna house","aspose.cells","fpdf"];class a{constructor(){throw new Error("Cannot initialize DefaultExternalServices.")}static updateFindControlState(e){}static updateFindMatchesCount(e){}static initPassiveLoading(e){}static async fallback(e){}static reportTelemetry(e){}static createDownloadManager(e){throw new Error("Not implemented: createDownloadManager")}static createPreferences(){throw new Error("Not implemented: createPreferences")}static createL10n(e){throw new Error("Not implemented: createL10n")}static createScripting(e){throw new Error("Not implemented: createScripting")}static get supportsIntegratedFind(){return(0,c.shadow)(this,"supportsIntegratedFind",!1)}static get supportsDocumentFonts(){return(0,c.shadow)(this,"supportsDocumentFonts",!0)}static get supportedMouseWheelZoomModifierKeys(){return(0,c.shadow)(this,"supportedMouseWheelZoomModifierKeys",{ctrlKey:!0,metaKey:!0})}static get isInAutomation(){return(0,c.shadow)(this,"isInAutomation",!1)}}t.DefaultExternalServices=a;const A={initialBookmark:document.location.hash.substring(1),_initializedCapability:(0,c.createPromiseCapability)(),_fellback:!1,appConfig:null,pdfDocument:null,pdfLoadingTask:null,printService:null,pdfViewer:null,pdfThumbnailViewer:null,pdfRenderingQueue:null,pdfPresentationMode:null,pdfDocumentProperties:null,pdfLinkService:null,pdfHistory:null,pdfSidebar:null,pdfSidebarResizer:null,pdfOutlineViewer:null,pdfAttachmentViewer:null,pdfLayerViewer:null,pdfCursorTools:null,pdfScriptingManager:null,store:null,downloadManager:null,overlayManager:null,preferences:null,toolbar:null,secondaryToolbar:null,eventBus:null,l10n:null,isInitialViewSet:!1,downloadComplete:!1,isViewerEmbedded:window.parent!==window,url:"",baseUrl:"",_downloadUrl:"",externalServices:a,_boundEvents:Object.create(null),documentInfo:null,metadata:null,_contentDispositionFilename:null,_contentLength:null,_saveInProgress:!1,_wheelUnusedTicks:0,_idleCallbacks:new Set,async initialize(e){this.preferences=this.externalServices.createPreferences(),this.appConfig=e,await this._readPreferences(),await this._parseHashParameters(),this._forceCssTheme(),await this._initializeL10n(),this.isViewerEmbedded&&m.AppOptions.get("externalLinkTarget")===c.LinkTarget.NONE&&m.AppOptions.set("externalLinkTarget",c.LinkTarget.TOP),await this._initializeViewerComponents(),this.bindEvents(),this.bindWindowEvents();e=e.appContainer||document.documentElement;this.l10n.translate(e).then(()=>{this.eventBus.dispatch("localized",{source:this})}),this._initializedCapability.resolve()},async _readPreferences(){if(!m.AppOptions.get("disablePreferences")){m.AppOptions._hasUserOptions()&&console.warn('_readPreferences: The Preferences may override manually set AppOptions; please use the "disablePreferences"-option in order to prevent that.');try{m.AppOptions.setAll(await this.preferences.getAll())}catch(e){console.error(`_readPreferences: "${e?.message}".`)}}},async _parseHashParameters(){if(m.AppOptions.get("pdfBugEnabled")){var e=document.location.hash.substring(1);if(e){const t=(0,g.parseQueryString)(e),i=[];if("true"===t.get("disableworker")&&i.push(async function(){c.GlobalWorkerOptions.workerSrc||(c.GlobalWorkerOptions.workerSrc=m.AppOptions.get("workerSrc"));await(0,c.loadScript)(c.PDFWorker.workerSrc)}()),t.has("disablerange")&&m.AppOptions.set("disableRange","true"===t.get("disablerange")),t.has("disablestream")&&m.AppOptions.set("disableStream","true"===t.get("disablestream")),t.has("disableautofetch")&&m.AppOptions.set("disableAutoFetch","true"===t.get("disableautofetch")),t.has("disablefontface")&&m.AppOptions.set("disableFontFace","true"===t.get("disablefontface")),t.has("disablehistory")&&m.AppOptions.set("disableHistory","true"===t.get("disablehistory")),t.has("verbosity")&&m.AppOptions.set("verbosity",0|t.get("verbosity")),t.has("textlayer"))switch(t.get("textlayer")){case"off":m.AppOptions.set("textLayerMode",g.TextLayerMode.DISABLE);break;case"visible":case"shadow":case"hover":const n=this.appConfig.viewerContainer;n.classList.add(`textLayer-${t.get("textlayer")}`)}if(t.has("pdfbug")&&(m.AppOptions.set("pdfBug",!0),m.AppOptions.set("fontExtraProperties",!0),e=t.get("pdfbug").split(","),i.push(async function(e){var{debuggerScriptPath:t,mainContainer:i}=A.appConfig;await(0,c.loadScript)(t),PDFBug.init({OPS:c.OPS},i,e)}(e))),t.has("locale")&&m.AppOptions.set("locale",t.get("locale")),0!==i.length)try{await Promise.all(i)}catch(e){console.error(`_parseHashParameters: "${e.message}".`)}}}},async _initializeL10n(){this.l10n=this.externalServices.createL10n({locale:m.AppOptions.get("locale")});var e=await this.l10n.getDirection();document.getElementsByTagName("html")[0].dir=e},_forceCssTheme(){var i=m.AppOptions.get("viewerCssTheme");if(i!==l.AUTOMATIC&&Object.values(l).includes(i))try{const r=document.styleSheets[0];var n=r?.cssRules||[],e="prefers-color-scheme";const o=new RegExp(`^@media \\(${e}: dark\\) {\\n\\s*([\\w\\s-.,:;/\\\\{}()]+)\\n}$`);for(let e=0,t=n.length;e<t;e++){var s=n[e];if(s instanceof CSSMediaRule&&"(prefers-color-scheme: dark)"===s.media?.[0]){if(i===l.LIGHT)return void r.deleteRule(e);var a=o.exec(s.cssText);return void(a?.[1]&&(r.deleteRule(e),r.insertRule(a[1],e)))}}}catch(e){console.error(`_forceCssTheme: "${e?.message}".`)}},async _initializeViewerComponents(){const{appConfig:e,externalServices:t}=this;let i;i=e.eventBus||new(t.isInAutomation?g.AutomationEventBus:g.EventBus),this.eventBus=i,this.overlayManager=new p.OverlayManager;const n=new u.PDFRenderingQueue;n.onIdle=this._cleanup.bind(this),this.pdfRenderingQueue=n;const s=new S.PDFLinkService({eventBus:i,externalLinkTarget:m.AppOptions.get("externalLinkTarget"),externalLinkRel:m.AppOptions.get("externalLinkRel"),ignoreDestinationZoom:m.AppOptions.get("ignoreDestinationZoom")});this.pdfLinkService=s;var a=t.createDownloadManager();this.downloadManager=a;var r=new b.PDFFindController({linkService:s,eventBus:i});this.findController=r;const o=new C.PDFScriptingManager({eventBus:i,sandboxBundleSrc:m.AppOptions.get("sandboxBundleSrc"),scriptingFactory:t,docPropertiesLookup:this._scriptingDocProperties.bind(this)});this.pdfScriptingManager=o;var l=e.mainContainer,h=e.viewerContainer;this.pdfViewer=new M.PDFViewer({container:l,viewer:h,eventBus:i,renderingQueue:n,linkService:s,downloadManager:a,findController:r,scriptingManager:m.AppOptions.get("enableScripting")&&o,renderer:m.AppOptions.get("renderer"),l10n:this.l10n,textLayerMode:m.AppOptions.get("textLayerMode"),annotationMode:m.AppOptions.get("annotationMode"),imageResourcesPath:m.AppOptions.get("imageResourcesPath"),enablePrintAutoRotate:m.AppOptions.get("enablePrintAutoRotate"),useOnlyCssZoom:m.AppOptions.get("useOnlyCssZoom"),maxCanvasPixels:m.AppOptions.get("maxCanvasPixels")}),n.setViewer(this.pdfViewer),s.setViewer(this.pdfViewer),o.setViewer(this.pdfViewer),this.pdfThumbnailViewer=new D.PDFThumbnailViewer({container:e.sidebar.thumbnailView,eventBus:i,renderingQueue:n,linkService:s,l10n:this.l10n}),n.setThumbnailViewer(this.pdfThumbnailViewer),this.isViewerEmbedded||m.AppOptions.get("disableHistory")||(this.pdfHistory=new y.PDFHistory({linkService:s,eventBus:i}),s.setHistory(this.pdfHistory)),this.supportsIntegratedFind||(this.findBar=new w.PDFFindBar(e.findBar,i,this.l10n)),this.pdfDocumentProperties=new _.PDFDocumentProperties(e.documentProperties,this.overlayManager,i,this.l10n),this.pdfCursorTools=new d.PDFCursorTools({container:l,eventBus:i,cursorToolOnLoad:m.AppOptions.get("cursorToolOnLoad")}),this.toolbar=new N.Toolbar(e.toolbar,i,this.l10n),this.secondaryToolbar=new B.SecondaryToolbar(e.secondaryToolbar,l,i),this.supportsFullscreen&&(this.pdfPresentationMode=new L.PDFPresentationMode({container:l,pdfViewer:this.pdfViewer,eventBus:i})),this.passwordPrompt=new f.PasswordPrompt(e.passwordOverlay,this.overlayManager,this.l10n,this.isViewerEmbedded),this.pdfOutlineViewer=new E.PDFOutlineViewer({container:e.sidebar.outlineView,eventBus:i,linkService:s}),this.pdfAttachmentViewer=new v.PDFAttachmentViewer({container:e.sidebar.attachmentsView,eventBus:i,downloadManager:a}),this.pdfLayerViewer=new P.PDFLayerViewer({container:e.sidebar.layersView,eventBus:i,l10n:this.l10n}),this.pdfSidebar=new T.PDFSidebar({elements:e.sidebar,pdfViewer:this.pdfViewer,pdfThumbnailViewer:this.pdfThumbnailViewer,eventBus:i,l10n:this.l10n}),this.pdfSidebar.onToggled=this.forceRendering.bind(this),this.pdfSidebarResizer=new I.PDFSidebarResizer(e.sidebarResizer,i,this.l10n)},run(e){this.initialize(e).then(O)},get initialized(){return this._initializedCapability.settled},get initializedPromise(){return this._initializedCapability.promise},zoomIn(e){this.pdfViewer.isInPresentationMode||this.pdfViewer.increaseScale(e)},zoomOut(e){this.pdfViewer.isInPresentationMode||this.pdfViewer.decreaseScale(e)},zoomReset(){this.pdfViewer.isInPresentationMode||(this.pdfViewer.currentScaleValue=g.DEFAULT_SCALE_VALUE)},get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0},get page(){return this.pdfViewer.currentPageNumber},set page(e){this.pdfViewer.currentPageNumber=e},get supportsPrinting(){return Me.instance.supportsPrinting},get supportsFullscreen(){return(0,c.shadow)(this,"supportsFullscreen",document.fullscreenEnabled||document.mozFullScreenEnabled||document.webkitFullscreenEnabled)},get supportsIntegratedFind(){return this.externalServices.supportsIntegratedFind},get supportsDocumentFonts(){return this.externalServices.supportsDocumentFonts},get loadingBar(){var e=new g.ProgressBar("#loadingBar");return(0,c.shadow)(this,"loadingBar",e)},get supportedMouseWheelZoomModifierKeys(){return this.externalServices.supportedMouseWheelZoomModifierKeys},initPassiveLoading(){throw new Error("Not implemented: initPassiveLoading")},setTitleUsingUrl(t="",e=null){this.url=t,this.baseUrl=t.split("#")[0],e&&(this._downloadUrl=e===t?this.baseUrl:e.split("#")[0]);let i=(0,c.getPdfFilenameFromUrl)(t,"");if(!i)try{i=decodeURIComponent((0,c.getFilenameFromUrl)(t))||t}catch(e){i=t}this.setTitle(i)},setTitle(e){this.isViewerEmbedded||(document.title=e)},get _docFilename(){return this._contentDispositionFilename||(0,c.getPdfFilenameFromUrl)(this.url)},_hideViewBookmark(){const{toolbar:e,secondaryToolbar:t}=this.appConfig;e.viewBookmark.hidden=!0,t.viewBookmarkButton.hidden=!0},_cancelIdleCallbacks(){if(this._idleCallbacks.size){for(const e of this._idleCallbacks)window.cancelIdleCallback(e);this._idleCallbacks.clear()}},async close(){this._unblockDocumentLoadEvent(),this._hideViewBookmark();const e=this.appConfig.errorWrapper["container"];if(e.hidden=!0,this.pdfLoadingTask){if(0<this.pdfDocument?.annotationStorage.size&&this._annotationStorageModified)try{await this.save({sourceEventType:"save"})}catch(e){}const t=[];t.push(this.pdfLoadingTask.destroy()),this.pdfLoadingTask=null,this.pdfDocument&&(this.pdfDocument=null,this.pdfThumbnailViewer.setDocument(null),this.pdfViewer.setDocument(null),this.pdfLinkService.setDocument(null),this.pdfDocumentProperties.setDocument(null)),function(){const e=A["appConfig"];e&&e.viewerContainer.classList.remove(s)}(),this.pdfLinkService.externalLinkEnabled=!0,this._fellback=!1,this.store=null,this.isInitialViewSet=!1,this.downloadComplete=!1,this.url="",this.baseUrl="",this._downloadUrl="",this.documentInfo=null,this.metadata=null,this._contentDispositionFilename=null,this._contentLength=null,this._saveInProgress=!1,this._cancelIdleCallbacks(),t.push(this.pdfScriptingManager.destroyPromise),this.pdfSidebar.reset(),this.pdfOutlineViewer.reset(),this.pdfAttachmentViewer.reset(),this.pdfLayerViewer.reset(),this.pdfHistory?.reset(),this.findBar?.reset(),this.toolbar.reset(),this.secondaryToolbar.reset(),"undefined"!=typeof PDFBug&&PDFBug.cleanup(),await Promise.all(t)}},async open(e,t){this.pdfLoadingTask&&await this.close();var i=m.AppOptions.getAll(m.OptionKind.WORKER);for(const o in i)c.GlobalWorkerOptions[o]=i[o];const n=Object.create(null);"string"==typeof e?(this.setTitleUsingUrl(e,e),n.url=e):e&&"byteLength"in e?n.data=e:e.url&&e.originalUrl&&(this.setTitleUsingUrl(e.originalUrl,e.url),n.url=e.url);var s=m.AppOptions.getAll(m.OptionKind.API);for(const l in s){var a=s[l];l,n[l]=a}if(t)for(const h in t)n[h]=t[h];const r=(0,c.getDocument)(n);return this.pdfLoadingTask=r,r.onPassword=(e,t)=>{this.pdfLinkService.externalLinkEnabled=!1,this.passwordPrompt.setUpdateCallback(e,t),this.passwordPrompt.open()},r.onProgress=({loaded:e,total:t})=>{this.progress(e/t)},r.onUnsupportedFeature=this.fallback.bind(this),r.promise.then(e=>{this.load(e)},t=>{if(r===this.pdfLoadingTask){let e="loading_error";return t instanceof c.InvalidPDFException?e="invalid_file_error":t instanceof c.MissingPDFException?e="missing_file_error":t instanceof c.UnexpectedResponseException&&(e="unexpected_response_error"),this.l10n.get(e).then(e=>{throw this._documentError(e,{message:t?.message}),t})}})},_ensureDownloadComplete(){if(!this.pdfDocument||!this.downloadComplete)throw new Error("PDF document not downloaded.")},async download({sourceEventType:e="download"}={}){var t=this._downloadUrl,i=this._docFilename;try{this._ensureDownloadComplete();var n=await this.pdfDocument.getData(),s=new Blob([n],{type:"application/pdf"});await this.downloadManager.download(s,t,i,e)}catch(e){await this.downloadManager.downloadUrl(t,i)}},async save({sourceEventType:t="download"}={}){if(!this._saveInProgress){this._saveInProgress=!0,await this.pdfScriptingManager.dispatchWillSave();var e=this._downloadUrl,i=this._docFilename;try{this._ensureDownloadComplete();var n=await this.pdfDocument.saveDocument(),s=new Blob([n],{type:"application/pdf"});await this.downloadManager.download(s,e,i,t)}catch(e){console.error(`Error when saving the document: ${e.message}`),await this.download({sourceEventType:t})}finally{await this.pdfScriptingManager.dispatchDidSave(),this._saveInProgress=!1}}},downloadOrSave(e){0<this.pdfDocument?.annotationStorage.size?this.save(e):this.download(e)},fallback(e){this.externalServices.reportTelemetry({type:"unsupportedFeature",featureId:e}),this._fellback||(this._fellback=!0,this.externalServices.fallback({featureId:e,url:this.baseUrl}).then(e=>{e&&this.download({sourceEventType:"download"})}))},_documentError(e,t=null){this._unblockDocumentLoadEvent(),this._otherError(e,t)},_otherError(e,t=null){const i=[this.l10n.get("error_version_info",{version:c.version||"?",build:c.build||"?"})];t&&(i.push(this.l10n.get("error_message",{message:t.message})),t.stack?i.push(this.l10n.get("error_stack",{stack:t.stack})):(t.filename&&i.push(this.l10n.get("error_file",{file:t.filename})),t.lineNumber&&i.push(this.l10n.get("error_line",{line:t.lineNumber}))));t=this.appConfig.errorWrapper;const n=t.container;n.hidden=!1;const s=t.errorMessage;s.textContent=e;const a=t.closeButton;a.onclick=function(){n.hidden=!0};const r=t.errorMoreInfo,o=t.moreInfoButton,l=t.lessInfoButton;o.onclick=function(){r.hidden=!1,o.hidden=!0,l.hidden=!1,r.style.height=r.scrollHeight+"px"},l.onclick=function(){r.hidden=!0,o.hidden=!1,l.hidden=!0},o.oncontextmenu=g.noContextMenuHandler,l.oncontextmenu=g.noContextMenuHandler,a.oncontextmenu=g.noContextMenuHandler,o.hidden=!1,l.hidden=!0,Promise.all(i).then(e=>{r.value=e.join("\n")})},progress(e){this.downloadComplete||((e=Math.round(100*e))>this.loadingBar.percent||isNaN(e))&&(this.loadingBar.percent=e,(this.pdfDocument?this.pdfDocument.loadingParams.disableAutoFetch:m.AppOptions.get("disableAutoFetch"))&&e&&(this.disableAutoFetchLoadingBarTimeout&&(clearTimeout(this.disableAutoFetchLoadingBarTimeout),this.disableAutoFetchLoadingBarTimeout=null),this.loadingBar.show(),this.disableAutoFetchLoadingBarTimeout=setTimeout(()=>{this.loadingBar.hide(),this.disableAutoFetchLoadingBarTimeout=null},5e3)))},load(c){(this.pdfDocument=c).getDownloadInfo().then(({length:e})=>{this._contentLength=e,this.downloadComplete=!0,this.loadingBar.hide(),s.then(()=>{this.eventBus.dispatch("documentloaded",{source:this})})});const t=c.getPageLayout().catch(function(){}),i=c.getPageMode().catch(function(){}),n=c.getOpenAction().catch(function(){});this.toolbar.setPagesCount(c.numPages,!1),this.secondaryToolbar.setPagesCount(c.numPages),this.pdfLinkService.setDocument(c,null),this.pdfDocumentProperties.setDocument(c,this.url);const u=this.pdfViewer;u.setDocument(c);const{firstPagePromise:s,onePageRendered:e,pagesPromise:p}=u,a=this.pdfThumbnailViewer;a.setDocument(c);const r=(this.store=new o.ViewHistory(c.fingerprints[0])).getMultiple({page:null,zoom:g.DEFAULT_SCALE_VALUE,scrollLeft:"0",scrollTop:"0",rotation:null,sidebarView:g.SidebarView.UNKNOWN,scrollMode:g.ScrollMode.UNKNOWN,spreadMode:g.SpreadMode.UNKNOWN}).catch(()=>Object.create(null));s.then(e=>{this.loadingBar.setWidth(this.appConfig.viewerContainer),this._initializeAnnotationStorageCallbacks(c),Promise.all([g.animationStarted,r,t,i,n]).then(async([,e,t,i,n])=>{var s=m.AppOptions.get("viewOnLoad");this._initializePdfHistory({fingerprint:c.fingerprints[0],viewOnLoad:s,initialDest:n?.dest});var a=this.initialBookmark,n=m.AppOptions.get("defaultZoomValue");let r=n?`zoom=${n}`:null,o=null,l=m.AppOptions.get("sidebarViewOnLoad"),h=m.AppOptions.get("scrollModeOnLoad"),d=m.AppOptions.get("spreadModeOnLoad");e.page&&s!==x.INITIAL&&(r=`page=${e.page}&zoom=${n||e.zoom},`+`${e.scrollLeft},${e.scrollTop}`,o=parseInt(e.rotation,10),l===g.SidebarView.UNKNOWN&&(l=0|e.sidebarView),h===g.ScrollMode.UNKNOWN&&(h=0|e.scrollMode),d===g.SpreadMode.UNKNOWN&&(d=0|e.spreadMode)),i&&l===g.SidebarView.UNKNOWN&&(l=(0,g.apiPageModeToSidebarView)(i)),t&&d===g.SpreadMode.UNKNOWN&&(d=(0,g.apiPageLayoutToSpreadMode)(t)),this.setInitialView(r,{rotation:o,sidebarView:l,scrollMode:h,spreadMode:d}),this.eventBus.dispatch("documentinit",{source:this}),this.isViewerEmbedded||u.focus(),this._initializePermissions(c),await Promise.race([p,new Promise(e=>{setTimeout(e,1e4)})]),(a||r)&&(u.hasEqualPageSizes||(this.initialBookmark=a,u.currentScaleValue=u.currentScaleValue,this.setInitialView(r)))}).catch(()=>{this.setInitialView()}).then(function(){u.update()})}),p.then(()=>{this._unblockDocumentLoadEvent(),this._initializeAutoPrint(c,n)}),e.then(()=>{if(c.getOutline().then(e=>{c===this.pdfDocument&&this.pdfOutlineViewer.render({outline:e,pdfDocument:c})}),c.getAttachments().then(e=>{c===this.pdfDocument&&this.pdfAttachmentViewer.render({attachments:e})}),u.optionalContentConfigPromise.then(e=>{c===this.pdfDocument&&this.pdfLayerViewer.render({optionalContentConfig:e,pdfDocument:c})}),"requestIdleCallback"in window){const e=window.requestIdleCallback(()=>{this._collectTelemetry(c),this._idleCallbacks.delete(e)},{timeout:1e3});this._idleCallbacks.add(e)}}),this._initializePageLabels(c),this._initializeMetadata(c)},async _scriptingDocProperties(e){return(this.documentInfo||(await new Promise(e=>{this.eventBus._on("metadataloaded",e,{once:!0})}),e===this.pdfDocument))&&(this._contentLength||(await new Promise(e=>{this.eventBus._on("documentloaded",e,{once:!0})}),e===this.pdfDocument))?{...this.documentInfo,baseURL:this.baseUrl,filesize:this._contentLength,filename:this._docFilename,metadata:this.metadata?.getRaw(),authors:this.metadata?.get("dc:creator"),numPages:this.pagesCount,URL:this.url}:null},async _collectTelemetry(e){var t=await this.pdfDocument.getMarkInfo();e===this.pdfDocument&&this.externalServices.reportTelemetry({type:"tagged",tagged:t?.Marked||!1})},async _initializeAutoPrint(e,t){const[i,n]=await Promise.all([t,this.pdfViewer.enableScripting?null:e.getJavaScript()]);if(e===this.pdfDocument){let e="Print"===i?.action?!0:!1;if(n&&(n.some(e=>!!e&&(console.warn("Warning: JavaScript support is not enabled"),this.fallback(c.UNSUPPORTED_FEATURES.javaScript),!0)),!e))for(const s of n)if(s&&g.AutoPrintRegExp.test(s)){e=!0;break}e&&this.triggerPrinting()}},async _initializeMetadata(s){const{info:a,metadata:r,contentDispositionFilename:o,contentLength:l}=await s.getMetadata();if(s===this.pdfDocument){this.documentInfo=a,this.metadata=r,this._contentDispositionFilename??=o,this._contentLength??=l,console.log(`PDF ${s.fingerprints[0]} [${a.PDFFormatVersion} `+`${(a.Producer||"-").trim()} / ${(a.Creator||"-").trim()}] `+`(PDF.js: ${c.version||"-"})`);let e=a?.Title;var h=r?.get("dc:title");h&&("Untitled"===h||/[\uFFF0-\uFFFF]/g.test(h)||(e=h)),e?this.setTitle(`${e} - ${o||document.title}`):o&&this.setTitle(o),!a.IsXFAPresent||a.IsAcroFormPresent||s.isPureXfa?!a.IsAcroFormPresent&&!a.IsXFAPresent||this.pdfViewer.renderForms||(console.warn("Warning: Interactive form support is not enabled"),this.fallback(c.UNSUPPORTED_FEATURES.forms)):(s.loadingParams.enableXfa?console.warn("Warning: XFA Foreground documents are not supported"):console.warn("Warning: XFA support is not enabled"),this.fallback(c.UNSUPPORTED_FEATURES.forms)),a.IsSignaturesPresent&&(console.warn("Warning: Digital signatures validation is not supported"),this.fallback(c.UNSUPPORTED_FEATURES.signatures));let t="other";V.includes(a.PDFFormatVersion)&&(t=`v${a.PDFFormatVersion.replace(".","_")}`);let i="other";if(a.Producer){const d=a.Producer.toLowerCase();k.some(function(e){return!!d.includes(e)&&(i=e.replace(/[ .-]/g,"_"),!0)})}let n=null;a.IsXFAPresent?n="xfa":a.IsAcroFormPresent&&(n="acroform"),this.externalServices.reportTelemetry({type:"documentInfo",version:t,generator:i,formType:n}),this.eventBus.dispatch("metadataloaded",{source:this})}},async _initializePageLabels(e){var t=await e.getPageLabels();if(e===this.pdfDocument&&t&&!m.AppOptions.get("disablePageLabels")){var i=t.length;if(i===this.pagesCount){let e=0;for(;e<i&&t[e]===(e+1).toString();)e++;if(e!==i){const{pdfViewer:n,pdfThumbnailViewer:s,toolbar:a}=this;n.setPageLabels(t),s.setPageLabels(t),a.setPagesCount(i,!0),a.setPageNumber(n.currentPageNumber,n.currentPageLabel)}}else console.error("The number of Page Labels does not match the number of pages in the document.")}},_initializePdfHistory({fingerprint:e,viewOnLoad:t,initialDest:i=null}){this.pdfHistory&&(this.pdfHistory.initialize({fingerprint:e,resetHistory:t===x.INITIAL,updateUrl:m.AppOptions.get("historyUpdateUrl")}),this.pdfHistory.initialBookmark&&(this.initialBookmark=this.pdfHistory.initialBookmark,this.initialRotation=this.pdfHistory.initialRotation),i&&!this.initialBookmark&&t===x.UNKNOWN&&(this.initialBookmark=JSON.stringify(i),this.pdfHistory.push({explicitDest:i,pageNumber:null})))},async _initializePermissions(e){const t=await e.getPermissions();e===this.pdfDocument&&t&&m.AppOptions.get("enablePermissions")&&(t.includes(c.PermissionFlag.COPY)||this.appConfig.viewerContainer.classList.add(s))},_initializeAnnotationStorageCallbacks(e){if(e===this.pdfDocument){const t=e["annotationStorage"];t.onSetModified=()=>{window.addEventListener("beforeunload",De),this._annotationStorageModified=!0},t.onResetModified=()=>{window.removeEventListener("beforeunload",De),delete this._annotationStorageModified}}},setInitialView(e,{rotation:t,sidebarView:i,scrollMode:n,spreadMode:s}={}){var a,r,o=e=>{(0,g.isValidRotation)(e)&&(this.pdfViewer.pagesRotation=e)};this.isInitialViewSet=!0,this.pdfSidebar.setInitialView(i),a=n,r=s,(0,g.isValidScrollMode)(a)&&(this.pdfViewer.scrollMode=a),(0,g.isValidSpreadMode)(r)&&(this.pdfViewer.spreadMode=r),this.initialBookmark?(o(this.initialRotation),delete this.initialRotation,this.pdfLinkService.setHash(this.initialBookmark),this.initialBookmark=null):e&&(o(t),this.pdfLinkService.setHash(e)),this.toolbar.setPageNumber(this.pdfViewer.currentPageNumber,this.pdfViewer.currentPageLabel),this.secondaryToolbar.setPageNumber(this.pdfViewer.currentPageNumber),this.pdfViewer.currentScaleValue||(this.pdfViewer.currentScaleValue=g.DEFAULT_SCALE_VALUE)},_cleanup(){this.pdfDocument&&(this.pdfViewer.cleanup(),this.pdfThumbnailViewer.cleanup(),this.pdfDocument.cleanup(this.pdfViewer.renderer===g.RendererType.SVG))},forceRendering(){this.pdfRenderingQueue.printing=!!this.printService,this.pdfRenderingQueue.isThumbnailViewEnabled=this.pdfSidebar.isThumbnailViewVisible,this.pdfRenderingQueue.renderHighestPriority()},beforePrint(){if(this.pdfScriptingManager.dispatchWillPrint(),!this.printService)if(this.supportsPrinting)if(this.pdfViewer.pageViewsReady){var e=this.pdfViewer.getPagesOverview(),t=this.appConfig.printContainer,i=m.AppOptions.get("printResolution"),n=this.pdfViewer.optionalContentConfigPromise;const s=Me.instance.createPrintService(this.pdfDocument,e,t,i,n,this.l10n);this.printService=s,this.forceRendering(),s.layout(),this.externalServices.reportTelemetry({type:"print"})}else this.l10n.get("printing_not_ready").then(e=>{window.alert(e)});else this.l10n.get("printing_not_supported").then(e=>{this._otherError(e)})},afterPrint(){this.pdfScriptingManager.dispatchDidPrint(),this.printService&&(this.printService.destroy(),this.printService=null,this.pdfDocument?.annotationStorage.resetModified()),this.forceRendering()},rotatePages(e){this.pdfViewer.pagesRotation+=e},requestPresentationMode(){this.pdfPresentationMode?.request()},triggerPrinting(){this.supportsPrinting&&window.print()},bindEvents(){const{eventBus:e,_boundEvents:t}=this;t.beforePrint=this.beforePrint.bind(this),t.afterPrint=this.afterPrint.bind(this),e._on("resize",q),e._on("hashchange",X),e._on("beforeprint",t.beforePrint),e._on("afterprint",t.afterPrint),e._on("pagerendered",R),e._on("updateviewarea",W),e._on("pagechanging",ye),e._on("scalechanging",we),e._on("rotationchanging",be),e._on("sidebarviewchanged",z),e._on("pagemode",F),e._on("namedaction",U),e._on("presentationmodechanged",H),e._on("presentationmode",Q),e._on("print",Z),e._on("download",Y),e._on("save",J),e._on("firstpage",ee),e._on("lastpage",te),e._on("nextpage",ie),e._on("previouspage",ne),e._on("zoomin",se),e._on("zoomout",ae),e._on("zoomreset",re),e._on("pagenumberchanged",oe),e._on("scalechanged",le),e._on("rotatecw",he),e._on("rotateccw",de),e._on("optionalcontentconfig",ce),e._on("switchscrollmode",ue),e._on("scrollmodechanged",j),e._on("switchspreadmode",pe),e._on("spreadmodechanged",$),e._on("documentproperties",ge),e._on("find",me),e._on("findfromurlhash",fe),e._on("updatefindmatchescount",ve),e._on("updatefindcontrolstate",_e),m.AppOptions.get("pdfBug")&&(t.reportPageStatsPDFBug=h,e._on("pagerendered",t.reportPageStatsPDFBug),e._on("pagechanging",t.reportPageStatsPDFBug)),e._on("fileinputchange",K),e._on("openfile",G)},bindWindowEvents(){const{eventBus:t,_boundEvents:e}=this;e.windowResize=()=>{t.dispatch("resize",{source:window})},e.windowHashChange=()=>{t.dispatch("hashchange",{source:window,hash:document.location.hash.substring(1)})},e.windowBeforePrint=()=>{t.dispatch("beforeprint",{source:window})},e.windowAfterPrint=()=>{t.dispatch("afterprint",{source:window})},e.windowUpdateFromSandbox=e=>{t.dispatch("updatefromsandbox",{source:window,detail:e.detail})},window.addEventListener("visibilitychange",Pe),window.addEventListener("wheel",Le,{passive:!1}),window.addEventListener("touchstart",Ce,{passive:!1}),window.addEventListener("click",Te),window.addEventListener("keydown",Ie),window.addEventListener("resize",e.windowResize),window.addEventListener("hashchange",e.windowHashChange),window.addEventListener("beforeprint",e.windowBeforePrint),window.addEventListener("afterprint",e.windowAfterPrint),window.addEventListener("updatefromsandbox",e.windowUpdateFromSandbox)},unbindEvents(){const{eventBus:e,_boundEvents:t}=this;e._off("resize",q),e._off("hashchange",X),e._off("beforeprint",t.beforePrint),e._off("afterprint",t.afterPrint),e._off("pagerendered",R),e._off("updateviewarea",W),e._off("pagechanging",ye),e._off("scalechanging",we),e._off("rotationchanging",be),e._off("sidebarviewchanged",z),e._off("pagemode",F),e._off("namedaction",U),e._off("presentationmodechanged",H),e._off("presentationmode",Q),e._off("print",Z),e._off("download",Y),e._off("save",J),e._off("firstpage",ee),e._off("lastpage",te),e._off("nextpage",ie),e._off("previouspage",ne),e._off("zoomin",se),e._off("zoomout",ae),e._off("zoomreset",re),e._off("pagenumberchanged",oe),e._off("scalechanged",le),e._off("rotatecw",he),e._off("rotateccw",de),e._off("optionalcontentconfig",ce),e._off("switchscrollmode",ue),e._off("scrollmodechanged",j),e._off("switchspreadmode",pe),e._off("spreadmodechanged",$),e._off("documentproperties",ge),e._off("find",me),e._off("findfromurlhash",fe),e._off("updatefindmatchescount",ve),e._off("updatefindcontrolstate",_e),t.reportPageStatsPDFBug&&(e._off("pagerendered",t.reportPageStatsPDFBug),e._off("pagechanging",t.reportPageStatsPDFBug),t.reportPageStatsPDFBug=null),e._off("fileinputchange",K),e._off("openfile",G),t.beforePrint=null,t.afterPrint=null},unbindWindowEvents(){const e=this["_boundEvents"];window.removeEventListener("visibilitychange",Pe),window.removeEventListener("wheel",Le,{passive:!1}),window.removeEventListener("touchstart",Ce,{passive:!1}),window.removeEventListener("click",Te),window.removeEventListener("keydown",Ie),window.removeEventListener("resize",e.windowResize),window.removeEventListener("hashchange",e.windowHashChange),window.removeEventListener("beforeprint",e.windowBeforePrint),window.removeEventListener("afterprint",e.windowAfterPrint),window.removeEventListener("updatefromsandbox",e.windowUpdateFromSandbox),e.windowResize=null,e.windowHashChange=null,e.windowBeforePrint=null,e.windowAfterPrint=null,e.windowUpdateFromSandbox=null},accumulateWheelTicks(e){(0<this._wheelUnusedTicks&&e<0||this._wheelUnusedTicks<0&&0<e)&&(this._wheelUnusedTicks=0),this._wheelUnusedTicks+=e;e=Math.sign(this._wheelUnusedTicks)*Math.floor(Math.abs(this._wheelUnusedTicks));return this._wheelUnusedTicks-=e,e},_unblockDocumentLoadEvent(){document.blockUnblockOnload&&document.blockUnblockOnload(!1),this._unblockDocumentLoadEvent=()=>{}},get scriptingReady(){return this.pdfScriptingManager.ready}};t.PDFViewerApplication=A;let r;{const Be=["null","http://mozilla.github.io","https://mozilla.github.io"];r=function(e){if(void 0!==e)try{var t=new URL(window.location.href).origin||"null";if(Be.includes(t))return;var{}=new URL(e,window.location.href)}catch(t){throw A.l10n.get("loading_error").then(e=>{A._documentError(e,{message:t?.message})}),t}}}function h({pageNumber:e}){var t;"undefined"==typeof Stats||!Stats.enabled||(t=A.pdfViewer.getPageView(e-1)?.pdfPage?.stats)&&Stats.add(e,t)}function O(){const e=A.appConfig;var t,i=document.location.search.substring(1);const n=(0,g.parseQueryString)(i);i=n.get("file")??m.AppOptions.get("defaultUrl"),r(i);const s=document.createElement("input");s.id=e.openFileInputName,s.className="fileInput",s.setAttribute("type","file"),s.oncontextmenu=g.noContextMenuHandler,document.body.appendChild(s),window.File&&window.FileReader&&window.FileList&&window.Blob?s.value=null:(e.toolbar.openFile.hidden=!0,e.secondaryToolbar.openFileButton.hidden=!0),s.addEventListener("change",function(e){var t=e.target.files;t&&0!==t.length&&A.eventBus.dispatch("fileinputchange",{source:this,fileInput:e.target})}),e.mainContainer.addEventListener("dragover",function(e){e.preventDefault(),e.dataTransfer.dropEffect="move"}),e.mainContainer.addEventListener("drop",function(e){e.preventDefault();var t=e.dataTransfer.files;t&&0!==t.length&&A.eventBus.dispatch("fileinputchange",{source:this,fileInput:e.dataTransfer})}),A.supportsDocumentFonts||(m.AppOptions.set("disableFontFace",!0),A.l10n.get("web_fonts_disabled").then(e=>{console.warn(e)})),A.supportsPrinting||(e.toolbar.print.classList.add("hidden"),e.secondaryToolbar.printButton.classList.add("hidden")),A.supportsFullscreen||(e.toolbar.presentationModeButton.classList.add("hidden"),e.secondaryToolbar.presentationModeButton.classList.add("hidden")),A.supportsIntegratedFind&&e.toolbar.viewFind.classList.add("hidden"),e.mainContainer.addEventListener("transitionend",function(e){e.target===this&&A.eventBus.dispatch("resize",{source:this})},!0);try{(t=i)?A.open(t):A._hideViewBookmark()}catch(t){A.l10n.get("loading_error").then(e=>{A._documentError(e,t)})}}function R({pageNumber:e,timestamp:t,error:i}){if(e===A.page&&A.toolbar.updateLoadingIndicatorState(!1),A.pdfSidebar.isThumbnailViewVisible){var n=A.pdfViewer.getPageView(e-1);const s=A.pdfThumbnailViewer.getThumbnail(e-1);n&&s&&s.setImage(n)}i&&A.l10n.get("rendering_error").then(e=>{A._otherError(e,i)}),A.externalServices.reportTelemetry({type:"pageInfo",timestamp:t}),A.pdfDocument.getStats().then(function(e){A.externalServices.reportTelemetry({type:"documentStats",stats:e})})}function F({mode:e}){let t;switch(e){case"thumbs":t=g.SidebarView.THUMBS;break;case"bookmarks":case"outline":t=g.SidebarView.OUTLINE;break;case"attachments":t=g.SidebarView.ATTACHMENTS;break;case"layers":t=g.SidebarView.LAYERS;break;case"none":t=g.SidebarView.NONE;break;default:return void console.error('Invalid "pagemode" hash parameter: '+e)}A.pdfSidebar.switchView(t,!0)}function U(e){switch(e.action){case"GoToPage":A.appConfig.toolbar.pageNumber.select();break;case"Find":A.supportsIntegratedFind||A.findBar.toggle();break;case"Print":A.triggerPrinting();break;case"SaveAs":J()}}function H(e){A.pdfViewer.presentationModeState=e.state}function z(e){A.pdfRenderingQueue.isThumbnailViewEnabled=A.pdfSidebar.isThumbnailViewVisible;const t=A.store;t&&A.isInitialViewSet&&t.set("sidebarView",e.view).catch(function(){})}function W(e){const t=e.location,i=A.store;i&&A.isInitialViewSet&&i.setMultiple({page:t.pageNumber,zoom:t.scale,scrollLeft:t.left,scrollTop:t.top,rotation:t.rotation}).catch(function(){});e=A.pdfLinkService.getAnchorUrl(t.pdfOpenParams);A.appConfig.toolbar.viewBookmark.href=e,A.appConfig.secondaryToolbar.viewBookmarkButton.href=e;e=A.pdfViewer.getPageView(A.page-1)?.renderingState!==u.RenderingStates.FINISHED;A.toolbar.updateLoadingIndicatorState(e)}function j(e){const t=A.store;t&&A.isInitialViewSet&&t.set("scrollMode",e.mode).catch(function(){})}function $(e){const t=A.store;t&&A.isInitialViewSet&&t.set("spreadMode",e.mode).catch(function(){})}function q(){const{pdfDocument:e,pdfViewer:t}=A;var i;e&&("auto"!==(i=t.currentScaleValue)&&"page-fit"!==i&&"page-width"!==i||(t.currentScaleValue=i),t.update())}function X(e){e=e.hash;e&&(A.isInitialViewSet?A.pdfHistory?.popStateInProgress||A.pdfLinkService.setHash(e):A.initialBookmark=e)}let K,G;function Q(){A.requestPresentationMode()}function Z(){A.triggerPrinting()}function Y(){A.downloadOrSave({sourceEventType:"download"})}function J(){A.downloadOrSave({sourceEventType:"save"})}function ee(){A.pdfDocument&&(A.page=1)}function te(){A.pdfDocument&&(A.page=A.pagesCount)}function ie(){A.pdfViewer.nextPage()}function ne(){A.pdfViewer.previousPage()}function se(){A.zoomIn()}function ae(){A.zoomOut()}function re(){A.zoomReset()}function oe(e){const t=A.pdfViewer;""!==e.value&&A.pdfLinkService.goToPage(e.value),e.value!==t.currentPageNumber.toString()&&e.value!==t.currentPageLabel&&A.toolbar.setPageNumber(t.currentPageNumber,t.currentPageLabel)}function le(e){A.pdfViewer.currentScaleValue=e.value}function he(){A.rotatePages(90)}function de(){A.rotatePages(-90)}function ce(e){A.pdfViewer.optionalContentConfigPromise=e.promise}function ue(e){A.pdfViewer.scrollMode=e.mode}function pe(e){A.pdfViewer.spreadMode=e.mode}function ge(){A.pdfDocumentProperties.open()}function me(e){A.findController.executeCommand("find"+e.type,{query:e.query,phraseSearch:e.phraseSearch,caseSensitive:e.caseSensitive,entireWord:e.entireWord,highlightAll:e.highlightAll,findPrevious:e.findPrevious})}function fe(e){A.findController.executeCommand("find",{query:e.query,phraseSearch:e.phraseSearch,caseSensitive:!1,entireWord:!1,highlightAll:!0,findPrevious:!1})}function ve({matchesCount:e}){A.supportsIntegratedFind?A.externalServices.updateFindMatchesCount(e):A.findBar.updateResultsCount(e)}function _e({state:e,previous:t,matchesCount:i,rawQuery:n}){A.supportsIntegratedFind?A.externalServices.updateFindControlState({result:e,findPrevious:t,matchesCount:i,rawQuery:n}):A.findBar.updateUIState(e,t,i)}function we(e){A.toolbar.setPageScale(e.presetValue,e.scale),A.pdfViewer.update()}function be(e){A.pdfThumbnailViewer.pagesRotation=e.pagesRotation,A.forceRendering(),A.pdfViewer.currentPageNumber=e.pageNumber}function ye({pageNumber:e,pageLabel:t}){A.toolbar.setPageNumber(e,t),A.secondaryToolbar.setPageNumber(e),A.pdfSidebar.isThumbnailViewVisible&&A.pdfThumbnailViewer.scrollThumbnailIntoView(e)}function Pe(e){"visible"===document.visibilityState&&Ee()}K=function(t){if(!A.pdfViewer?.isInPresentationMode){t=t.fileInput.files[0];if(m.compatibilityParams.disableCreateObjectURL){A.setTitleUsingUrl(t.name);const e=new FileReader;e.onload=function(e){e=e.target.result;A.open(new Uint8Array(e))},e.readAsArrayBuffer(t)}else{let e=URL.createObjectURL(t);t.name&&(e={url:e,originalUrl:t.name}),A.open(e)}}},G=function(e){var t=A.appConfig.openFileInputName;document.getElementById(t).click()};let Se=null;function Ee(){Se&&clearTimeout(Se),Se=setTimeout(function(){Se=null},n)}function Le(t){const{pdfViewer:i,supportedMouseWheelZoomModifierKeys:e}=A;if(!i.isInPresentationMode)if(t.ctrlKey&&e.ctrlKey||t.metaKey&&e.metaKey){if(t.preventDefault(),!Se&&"hidden"!==document.visibilityState){var n=i.currentScale,s=(0,g.normalizeWheelEventDirection)(t);let e=0;e=t.deltaMode===WheelEvent.DOM_DELTA_LINE||t.deltaMode===WheelEvent.DOM_DELTA_PAGE?1<=Math.abs(s)?Math.sign(s):A.accumulateWheelTicks(s):A.accumulateWheelTicks(s/30),e<0?A.zoomOut(-e):0<e&&A.zoomIn(e);var a=i.currentScale;n!==a&&(s=a/n-1,a=i.container.getBoundingClientRect(),n=t.clientX-a.left,a=t.clientY-a.top,i.container.scrollLeft+=n*s,i.container.scrollTop+=a*s)}}else Ee()}function Ce(e){1<e.touches.length&&e.preventDefault()}function Te(e){if(A.secondaryToolbar.isOpen){const t=A.appConfig;(A.pdfViewer.containsElement(e.target)||t.toolbar.container.contains(e.target)&&e.target!==t.secondaryToolbar.toggleButton)&&A.secondaryToolbar.close()}}function Ie(s){if(!A.overlayManager.active){let i=!1,n=!1;var e=(s.ctrlKey?1:0)|(s.altKey?2:0)|(s.shiftKey?4:0)|(s.metaKey?8:0);const o=A.pdfViewer;var t,a=o?.isInPresentationMode;if(1==e||8==e||5==e||12==e)switch(s.keyCode){case 70:A.supportsIntegratedFind||s.shiftKey||(A.findBar.open(),i=!0);break;case 71:A.supportsIntegratedFind||((t=A.findController.state)&&A.findController.executeCommand("findagain",{query:t.query,phraseSearch:t.phraseSearch,caseSensitive:t.caseSensitive,entireWord:t.entireWord,highlightAll:t.highlightAll,findPrevious:5==e||12==e}),i=!0);break;case 61:case 107:case 187:case 171:a||A.zoomIn(),i=!0;break;case 173:case 109:case 189:a||A.zoomOut(),i=!0;break;case 48:case 96:a||(setTimeout(function(){A.zoomReset()}),i=!1);break;case 38:(a||1<A.page)&&(A.page=1,i=!0,n=!0);break;case 40:(a||A.page<A.pagesCount)&&(A.page=A.pagesCount,i=!0,n=!0)}const l=A["eventBus"];if(1==e||8==e)switch(s.keyCode){case 83:l.dispatch("download",{source:window}),i=!0;break;case 79:l.dispatch("openfile",{source:window}),i=!0}if(3==e||10==e)switch(s.keyCode){case 80:A.requestPresentationMode(),i=!0;break;case 71:A.appConfig.toolbar.pageNumber.select(),i=!0}if(i)return n&&!a&&o.focus(),void s.preventDefault();const h=(0,g.getActiveOrFocusedElement)();var r=h?.tagName.toUpperCase();if("INPUT"!==r&&"TEXTAREA"!==r&&"SELECT"!==r&&!h?.isContentEditable||27===s.keyCode){if(0==e){let e=0,t=!1;switch(s.keyCode){case 38:case 33:o.isVerticalScrollbarEnabled&&(t=!0),e=-1;break;case 8:a||(t=!0),e=-1;break;case 37:o.isHorizontalScrollbarEnabled&&(t=!0);case 75:case 80:e=-1;break;case 27:A.secondaryToolbar.isOpen&&(A.secondaryToolbar.close(),i=!0),!A.supportsIntegratedFind&&A.findBar.opened&&(A.findBar.close(),i=!0);break;case 40:case 34:o.isVerticalScrollbarEnabled&&(t=!0),e=1;break;case 13:case 32:a||(t=!0),e=1;break;case 39:o.isHorizontalScrollbarEnabled&&(t=!0);case 74:case 78:e=1;break;case 36:(a||1<A.page)&&(A.page=1,i=!0,n=!0);break;case 35:(a||A.page<A.pagesCount)&&(A.page=A.pagesCount,i=!0,n=!0);break;case 83:A.pdfCursorTools.switchTool(d.CursorTool.SELECT);break;case 72:A.pdfCursorTools.switchTool(d.CursorTool.HAND);break;case 82:A.rotatePages(90);break;case 115:A.pdfSidebar.toggle()}0===e||t&&"page-fit"!==o.currentScaleValue||(0<e?o.nextPage():o.previousPage(),i=!0)}if(4==e)switch(s.keyCode){case 13:case 32:if(!a&&"page-fit"!==o.currentScaleValue)break;1<A.page&&A.page--,i=!0;break;case 82:A.rotatePages(-90)}i||a||(33<=s.keyCode&&s.keyCode<=40||32===s.keyCode&&"BUTTON"!==r)&&(n=!0),n&&!o.containsElement(h)&&o.focus(),i&&s.preventDefault()}}}function De(e){return e.preventDefault(),e.returnValue="",!1}const Me={instance:{supportsPrinting:!1,createPrintService(){throw new Error("Not implemented: createPrintService")}}};t.PDFPrintServiceFactory=Me},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.apiPageLayoutToSpreadMode=function(e){switch(e){case"SinglePage":case"OneColumn":return s.NONE;case"TwoColumnLeft":case"TwoPageLeft":return s.ODD;case"TwoColumnRight":case"TwoPageRight":return s.EVEN}return s.NONE},t.apiPageModeToSidebarView=function(e){switch(e){case"UseNone":return i.NONE;case"UseThumbs":return i.THUMBS;case"UseOutlines":return i.OUTLINE;case"UseAttachments":return i.ATTACHMENTS;case"UseOC":return i.LAYERS}return i.NONE},t.approximateFraction=function(e){if(Math.floor(e)===e)return[e,1];var t=1/e;{if(8<t)return[1,8];if(Math.floor(t)===t)return[1,t]}var i=1<e?t:e;let n=0,s=1,a=1,r=1;for(;;){var o=n+a,l=s+r;if(8<l)break;i<=o/l?(a=o,r=l):(n=o,s=l)}let h;h=i-n/s<a/r-i?i===e?[n,s]:[s,n]:i===e?[a,r]:[r,a];return h},t.backtrackBeforeAllVisibleElements=P,t.binarySearchFirstItem=y,t.getActiveOrFocusedElement=function(){let e=document,t=e.activeElement||e.querySelector(":focus");for(;t?.shadowRoot;)e=t.shadowRoot,t=e.activeElement||e.querySelector(":focus");return t},t.getOutputScale=function(e){var t=window.devicePixelRatio||1,e=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.backingStorePixelRatio||1,e=t/e;return{sx:e,sy:e,scaled:1!=e}},t.getPageSizeInches=function({view:e,userUnit:t,rotate:i}){var[n,s,a,e]=e,i=i%180!=0,n=(a-n)/72*t,t=(e-s)/72*t;return{width:i?t:n,height:i?n:t}},t.getVisibleElements=function({scrollEl:e,views:t,sortByVisibility:i=!1,horizontal:n=!1,rtl:s=!1}){const a=e.scrollTop,r=a+e.clientHeight,o=e.scrollLeft,l=o+e.clientWidth;const h=[],d=t.length;let c=y(t,n?function(e){var t=e.div,t=(e=t.offsetLeft+t.clientLeft)+t.clientWidth;return s?e<l:t>o}:function(e){return(e=e.div).offsetTop+e.clientTop+e.clientHeight>a});0<c&&c<d&&!n&&(c=P(c,t,a));let u=n?l:-1;for(let e=c;e<d;e++){var p=t[e],g=p.div,m=g.offsetLeft+g.clientLeft,f=g.offsetTop+g.clientTop,v=g.clientWidth,_=g.clientHeight,w=m+v,g=f+_;if(-1===u)r<=g&&(u=g);else if((n?m:f)>u)break;g<=a||r<=f||w<=o||m>=l||(g=Math.max(0,a-f)+Math.max(0,g-r),w=Math.max(0,o-m)+Math.max(0,w-l),v=(_-g)/_*(w=(v-w)/v)*100|0,h.push({id:p.id,x:m,y:f,view:p,percent:v,widthPercent:100*w|0}))}var b=h[0],e=h[h.length-1];i&&h.sort(function(e,t){var i=e.percent-t.percent;return.001<Math.abs(i)?-i:e.id-t.id});return{first:b,last:e,views:h}},t.isPortraitOrientation=function(e){return e.width<=e.height},t.isValidRotation=function(e){return Number.isInteger(e)&&e%90==0},t.isValidScrollMode=function(e){return Number.isInteger(e)&&Object.values(n).includes(e)&&e!==n.UNKNOWN},t.isValidSpreadMode=function(e){return Number.isInteger(e)&&Object.values(s).includes(e)&&e!==s.UNKNOWN},t.moveToEndOfArray=function(t,i){const n=[],s=t.length;let a=0;for(let e=0;e<s;++e)i(t[e])?n.push(t[e]):(t[a]=t[e],++a);for(let e=0;a<s;++e,++a)t[a]=n[e]},t.noContextMenuHandler=function(e){e.preventDefault()},t.normalizeWheelEventDelta=function(e){let t=a(e);0===e.deltaMode?t/=900:1===e.deltaMode&&(t/=30);return t},t.normalizeWheelEventDirection=a,t.parseQueryString=function(e){const t=new Map;for(const i of e.split("&")){const n=i.split("="),s=n[0].toLowerCase(),a=1<n.length?n[1]:"";t.set(decodeURIComponent(s),decodeURIComponent(a))}return t},t.roundToDivide=function(e,t){var i=e%t;return 0==i?e:Math.round(e-i+t)},t.scrollIntoView=function(i,n,s=!1){let a=i.offsetParent;if(a){let e=i.offsetTop+i.clientTop,t=i.offsetLeft+i.clientLeft;for(;a.clientHeight===a.scrollHeight&&a.clientWidth===a.scrollWidth||s&&(a.classList.contains("markedContent")||"hidden"===getComputedStyle(a).overflow);)if(e+=a.offsetTop,t+=a.offsetLeft,a=a.offsetParent,!a)return;n&&(void 0!==n.top&&(e+=n.top),void 0!==n.left&&(t+=n.left,a.scrollLeft=t)),a.scrollTop=e}else console.error("offsetParent is not set -- cannot scroll")},t.waitOnEventOrTimeout=function({target:r,name:o,delay:l=0}){return new Promise(function(t,e){if("object"!=typeof r||!o||"string"!=typeof o||!(Number.isInteger(l)&&0<=l))throw new Error("waitOnEventOrTimeout - invalid parameters.");function i(e){r instanceof d?r._off(o,n):r.removeEventListener(o,n),a&&clearTimeout(a),t(e)}const n=i.bind(null,h.EVENT);r instanceof d?r._on(o,n):r.addEventListener(o,n);var s=i.bind(null,h.TIMEOUT);const a=setTimeout(s,l)})},t.watchScroll=function(i,n){function e(e){a=a||window.requestAnimationFrame(function(){a=null;var e=i.scrollLeft,t=s.lastX;e!==t&&(s.right=t<e),s.lastX=e;t=i.scrollTop,e=s.lastY;t!==e&&(s.down=e<t),s.lastY=t,n(s)})}const s={right:!0,down:!0,lastX:i.scrollLeft,lastY:i.scrollTop,_eventHandler:e};let a=null;return i.addEventListener("scroll",e,!0),s},t.WaitOnType=t.VERTICAL_PADDING=t.UNKNOWN_SCALE=t.TextLayerMode=t.SpreadMode=t.SidebarView=t.ScrollMode=t.SCROLLBAR_PADDING=t.RendererType=t.ProgressBar=t.PresentationModeState=t.MIN_SCALE=t.MAX_SCALE=t.MAX_AUTO_SCALE=t.EventBus=t.DEFAULT_SCALE_VALUE=t.DEFAULT_SCALE_DELTA=t.DEFAULT_SCALE=t.AutoPrintRegExp=t.AutomationEventBus=t.animationStarted=void 0;t.DEFAULT_SCALE_VALUE="auto";t.DEFAULT_SCALE=1;t.DEFAULT_SCALE_DELTA=1.1;t.MIN_SCALE=.1;t.MAX_SCALE=10;t.UNKNOWN_SCALE=0;t.MAX_AUTO_SCALE=1.25;t.SCROLLBAR_PADDING=40;t.VERTICAL_PADDING=5;t.PresentationModeState={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3};const i={UNKNOWN:-1,NONE:0,THUMBS:1,OUTLINE:2,ATTACHMENTS:3,LAYERS:4};t.SidebarView=i;t.RendererType={CANVAS:"canvas",SVG:"svg"};t.TextLayerMode={DISABLE:0,ENABLE:1,ENABLE_ENHANCE:2};const n={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2};t.ScrollMode=n;const s={UNKNOWN:-1,NONE:0,ODD:1,EVEN:2};t.SpreadMode=s;function y(e,t){let i=0,n=e.length-1;if(n<0||!t(e[n]))return e.length;if(t(e[i]))return i;for(;i<n;){var s=i+n>>1;t(e[s])?n=s:i=1+s}return i}function P(t,i,e){if(t<2)return t;let n=i[t].div,s=n.offsetTop+n.clientTop;s>=e&&(n=i[t-1].div,s=n.offsetTop+n.clientTop);for(let e=t-2;0<=e&&(n=i[e].div,!(n.offsetTop+n.clientTop+n.clientHeight<=s));--e)t=e;return t}function a(e){let t=Math.hypot(e.deltaX,e.deltaY);e=Math.atan2(e.deltaY,e.deltaX);return-.25*Math.PI<e&&e<.75*Math.PI&&(t=-t),t}t.AutoPrintRegExp=/\bprint\s*\(/;const h={EVENT:"event",TIMEOUT:"timeout"};t.WaitOnType=h;var r=new Promise(function(e){window.requestAnimationFrame(e)});t.animationStarted=r;class d{constructor(){this._listeners=Object.create(null)}on(e,t,i=null){this._on(e,t,{external:!0,once:i?.once})}off(e,t,i=null){this._off(e,t,{external:!0,once:i?.once})}dispatch(t,i){const n=this._listeners[t];if(n&&0!==n.length){let e;for(var{listener:s,external:a,once:r}of n.slice(0))r&&this._off(t,s),a?(e||=[]).push(s):s(i);if(e){for(const o of e)o(i);e=null}}}_on(e,t,i=null){const n=this._listeners[e]||=[];n.push({listener:t,external:!0===i?.external,once:!0===i?.once})}_off(e,i,t=0){const n=this._listeners[e];if(n)for(let e=0,t=n.length;e<t;e++)if(n[e].listener===i)return void n.splice(e,1)}}t.EventBus=d;class o extends d{dispatch(e,t){throw new Error("Not implemented: AutomationEventBus.dispatch")}}t.AutomationEventBus=o;t.ProgressBar=class{constructor(e,{height:t,width:i,units:n}={}){this.visible=!0,this.div=document.querySelector(e+" .progress"),this.bar=this.div.parentNode,this.height=t||100,this.width=i||100,this.units=n||"%",this.div.style.height=this.height+this.units,this.percent=0}_updateBar(){if(this._indeterminate)return this.div.classList.add("indeterminate"),void(this.div.style.width=this.width+this.units);this.div.classList.remove("indeterminate");var e=this.width*this._percent/100;this.div.style.width=e+this.units}get percent(){return this._percent}set percent(e){var t,i;this._indeterminate=isNaN(e),this._percent=(t=0,i=100,Math.min(Math.max(e,t),i)),this._updateBar()}setWidth(e){if(e){e=e.parentNode.offsetWidth-e.offsetWidth;if(0<e){const t=document.documentElement;t.style.setProperty("--loadingBar-end-offset",`${e}px`)}}}hide(){this.visible&&(this.visible=!1,this.bar.classList.add("hidden"))}show(){this.visible||(this.visible=!0,this.bar.classList.remove("hidden"))}}},e=>{let t;t="undefined"!=typeof window&&window["pdfjs-dist/build/pdf"]?window["pdfjs-dist/build/pdf"]:require("../build/pdf.js"),e.exports=t},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFCursorTools=t.CursorTool=void 0;var n=i(6),s=i(3);const a={SELECT:0,HAND:1,ZOOM:2};t.CursorTool=a;t.PDFCursorTools=class{constructor({container:e,eventBus:t,cursorToolOnLoad:i=a.SELECT}){this.container=e,this.eventBus=t,this.active=a.SELECT,this.activeBeforePresentationMode=null,this.handTool=new n.GrabToPan({element:this.container}),this._addEventListeners(),Promise.resolve().then(()=>{this.switchTool(i)})}get activeTool(){return this.active}switchTool(e){if(null===this.activeBeforePresentationMode&&e!==this.active){var t=()=>{switch(this.active){case a.SELECT:break;case a.HAND:this.handTool.deactivate();break;case a.ZOOM:}};switch(e){case a.SELECT:t();break;case a.HAND:t(),this.handTool.activate();break;default:a.ZOOM;return void console.error(`switchTool: "${e}" is an unsupported value.`)}this.active=e,this._dispatchEvent()}}_dispatchEvent(){this.eventBus.dispatch("cursortoolchanged",{source:this,tool:this.active})}_addEventListeners(){this.eventBus._on("switchcursortool",e=>{this.switchTool(e.tool)}),this.eventBus._on("presentationmodechanged",e=>{switch(e.state){case s.PresentationModeState.FULLSCREEN:var t=this.active;this.switchTool(a.SELECT),this.activeBeforePresentationMode=t;break;case s.PresentationModeState.NORMAL:t=this.activeBeforePresentationMode;this.activeBeforePresentationMode=null,this.switchTool(t)}})}}},(e,t)=>{function i(e){this.element=e.element,this.document=e.element.ownerDocument,"function"==typeof e.ignoreTarget&&(this.ignoreTarget=e.ignoreTarget),this.onActiveChanged=e.onActiveChanged,this.activate=this.activate.bind(this),this.deactivate=this.deactivate.bind(this),this.toggle=this.toggle.bind(this),this._onmousedown=this._onmousedown.bind(this),this._onmousemove=this._onmousemove.bind(this),this._endPan=this._endPan.bind(this);const t=this.overlay=document.createElement("div");t.className="grab-to-pan-grabbing"}Object.defineProperty(t,"__esModule",{value:!0}),(t.GrabToPan=i).prototype={CSS_CLASS_GRAB:"grab-to-pan-grab",activate:function(){this.active||(this.active=!0,this.element.addEventListener("mousedown",this._onmousedown,!0),this.element.classList.add(this.CSS_CLASS_GRAB),this.onActiveChanged&&this.onActiveChanged(!0))},deactivate:function(){this.active&&(this.active=!1,this.element.removeEventListener("mousedown",this._onmousedown,!0),this._endPan(),this.element.classList.remove(this.CSS_CLASS_GRAB),this.onActiveChanged&&this.onActiveChanged(!1))},toggle:function(){this.active?this.deactivate():this.activate()},ignoreTarget:function(e){return e.matches("a[href], a[href] *, input, textarea, button, button *, select, option")},_onmousedown:function(e){if(0===e.button&&!this.ignoreTarget(e.target)){if(e.originalTarget)try{e.originalTarget.tagName}catch(e){return}this.scrollLeftStart=this.element.scrollLeft,this.scrollTopStart=this.element.scrollTop,this.clientXStart=e.clientX,this.clientYStart=e.clientY,this.document.addEventListener("mousemove",this._onmousemove,!0),this.document.addEventListener("mouseup",this._endPan,!0),this.element.addEventListener("scroll",this._endPan,!0),e.preventDefault(),e.stopPropagation();const t=document.activeElement;t&&!t.contains(e.target)&&t.blur()}},_onmousemove:function(e){var t;this.element.removeEventListener("scroll",this._endPan,!0),function(e){if("buttons"in e)return!(1&e.buttons);var t=window.chrome,i=t&&(t.webstore||t.app),t=/Apple/.test(navigator.vendor)&&/Version\/([6-9]\d*|[1-5]\d+)/.test(navigator.userAgent);if(i||t)return 0===e.which;return!1}(e)?this._endPan():(t=e.clientX-this.clientXStart,e=e.clientY-this.clientYStart,e=this.scrollTopStart-e,t=this.scrollLeftStart-t,this.element.scrollTo?this.element.scrollTo({top:e,left:t,behavior:"instant"}):(this.element.scrollTop=e,this.element.scrollLeft=t),this.overlay.parentNode||document.body.appendChild(this.overlay))},_endPan:function(){this.element.removeEventListener("scroll",this._endPan,!0),this.document.removeEventListener("mousemove",this._onmousemove,!0),this.document.removeEventListener("mouseup",this._endPan,!0),this.overlay.remove()}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RenderingStates=t.PDFRenderingQueue=void 0;var n=i(4);const s={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3};t.RenderingStates=s;t.PDFRenderingQueue=class{constructor(){this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1}setViewer(e){this.pdfViewer=e}setThumbnailViewer(e){this.pdfThumbnailViewer=e}isHighestPriority(e){return this.highestPriorityPage===e.renderingId}hasViewer(){return!!this.pdfViewer}renderHighestPriority(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.pdfThumbnailViewer&&this.isThumbnailViewEnabled&&this.pdfThumbnailViewer.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),3e4))}getHighestPriority(e,t,i,n=!1){var s=e.views,a=s.length;if(0===a)return null;for(let e=0;e<a;++e){var r=s[e].view;if(!this.isViewFinished(r))return r}e=i?e.last.id:e.first.id-2;let o=t[e];return o&&!this.isViewFinished(o)||n&&(e+=i?1:-1,o=t[e],o&&!this.isViewFinished(o))?o:null}isViewFinished(e){return e.renderingState===s.FINISHED}renderView(e){switch(e.renderingState){case s.FINISHED:return!1;case s.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case s.RUNNING:this.highestPriorityPage=e.renderingId;break;case s.INITIAL:this.highestPriorityPage=e.renderingId,e.draw().finally(()=>{this.renderHighestPriority()}).catch(e=>{e instanceof n.RenderingCancelledException||console.error(`renderView: "${e}"`)})}return!0}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OverlayManager=void 0;t.OverlayManager=class{constructor(){this._overlays={},this._active=null,this._keyDownBound=this._keyDown.bind(this)}get active(){return this._active}async register(e,t,i=null,n=!1){let s;if(!(e&&t&&(s=t.parentNode)))throw new Error("Not enough parameters.");if(this._overlays[e])throw new Error("The overlay is already registered.");this._overlays[e]={element:t,container:s,callerCloseMethod:i,canForceClose:n}}async unregister(e){if(!this._overlays[e])throw new Error("The overlay does not exist.");if(this._active===e)throw new Error("The overlay cannot be removed while it is active.");delete this._overlays[e]}async open(e){if(!this._overlays[e])throw new Error("The overlay does not exist.");if(this._active){if(!this._overlays[e].canForceClose)throw this._active===e?new Error("The overlay is already active."):new Error("Another overlay is currently active.");this._closeThroughCaller()}this._active=e,this._overlays[this._active].element.classList.remove("hidden"),this._overlays[this._active].container.classList.remove("hidden"),window.addEventListener("keydown",this._keyDownBound)}async close(e){if(!this._overlays[e])throw new Error("The overlay does not exist.");if(!this._active)throw new Error("The overlay is currently not active.");if(this._active!==e)throw new Error("Another overlay is currently active.");this._overlays[this._active].container.classList.add("hidden"),this._overlays[this._active].element.classList.add("hidden"),this._active=null,window.removeEventListener("keydown",this._keyDownBound)}_keyDown(e){this._active&&27===e.keyCode&&(this._closeThroughCaller(),e.preventDefault())}_closeThroughCaller(){this._overlays[this._active].callerCloseMethod&&this._overlays[this._active].callerCloseMethod(),this._active&&this.close(this._active)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PasswordPrompt=void 0;var n=i(4);t.PasswordPrompt=class{constructor(e,t,i,n=!1){this.overlayName=e.overlayName,this.container=e.container,this.label=e.label,this.input=e.input,this.submitButton=e.submitButton,this.cancelButton=e.cancelButton,this.overlayManager=t,this.l10n=i,this._isViewerEmbedded=n,this.updateCallback=null,this.reason=null,this.submitButton.addEventListener("click",this.verify.bind(this)),this.cancelButton.addEventListener("click",this.close.bind(this)),this.input.addEventListener("keydown",e=>{13===e.keyCode&&this.verify()}),this.overlayManager.register(this.overlayName,this.container,this.close.bind(this),!0)}async open(){await this.overlayManager.open(this.overlayName);var e=this.reason===n.PasswordResponses.INCORRECT_PASSWORD;this._isViewerEmbedded&&!e||this.input.focus(),this.label.textContent=await this.l10n.get(`password_${e?"invalid":"label"}`)}close(){this.overlayManager.close(this.overlayName).then(()=>{this.input.value=""})}verify(){var e=this.input.value;0<e?.length&&(this.close(),this.updateCallback(e))}setUpdateCallback(e,t){this.updateCallback=e,this.reason=t}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFAttachmentViewer=void 0;var h=i(4);class n extends i(11).BaseTreeViewer{constructor(e){super(e),this.downloadManager=e.downloadManager,this.eventBus._on("fileattachmentannotation",this._appendAttachment.bind(this))}reset(e=!1){super.reset(),this._attachments=null,e||(this._renderedCapability=(0,h.createPromiseCapability)()),this._pendingDispatchEvent&&clearTimeout(this._pendingDispatchEvent),this._pendingDispatchEvent=null}_dispatchEvent(e){this._renderedCapability.resolve(),this._pendingDispatchEvent&&(clearTimeout(this._pendingDispatchEvent),this._pendingDispatchEvent=null),0!==e?this.eventBus.dispatch("attachmentsloaded",{source:this,attachmentsCount:e}):this._pendingDispatchEvent=setTimeout(()=>{this.eventBus.dispatch("attachmentsloaded",{source:this,attachmentsCount:0}),this._pendingDispatchEvent=null})}_bindLink(e,{content:t,filename:i}){e.onclick=()=>(this.downloadManager.openOrDownloadData(e,t,i),!1)}render({attachments:t,keepRenderedCapability:i=!1}){if(this._attachments&&this.reset(i),this._attachments=t||null,t){i=Object.keys(t).sort(function(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())});const a=document.createDocumentFragment();let e=0;for(const r of i){var n=t[r],s=n.content,n=(0,h.getFilenameFromUrl)(n.filename);const o=document.createElement("div");o.className="treeItem";const l=document.createElement("a");this._bindLink(l,{content:s,filename:n}),l.textContent=this._normalizeTextContent(n),o.appendChild(l),a.appendChild(o),e++}this._finishRendering(a,e)}else this._dispatchEvent(0)}_appendAttachment({id:i,filename:n,content:s}){const e=this._renderedCapability.promise;e.then(()=>{if(e===this._renderedCapability.promise){let e=this._attachments;if(e){for(const t in e)if(i===t)return}else e=Object.create(null);e[i]={filename:n,content:s},this.render({attachments:e,keepRenderedCapability:!0})}})}}t.PDFAttachmentViewer=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTreeViewer=void 0;var n=i(4);const s="selected";t.BaseTreeViewer=class a{constructor(e){if(this.constructor===a)throw new Error("Cannot initialize BaseTreeViewer.");this.container=e.container,this.eventBus=e.eventBus,this.reset()}reset(){this._pdfDocument=null,this._lastToggleIsShow=!0,this._currentTreeItem=null,this.container.textContent="",this.container.classList.remove("treeWithDeepNesting")}_dispatchEvent(e){throw new Error("Not implemented: _dispatchEvent")}_bindLink(e,t){throw new Error("Not implemented: _bindLink")}_normalizeTextContent(e){return(0,n.removeNullCharacters)(e)||"–"}_addToggleButton(t,e=!1){const i=document.createElement("div");i.className="treeItemToggler",e&&i.classList.add("treeItemsHidden"),i.onclick=e=>{e.stopPropagation(),i.classList.toggle("treeItemsHidden"),e.shiftKey&&(e=!i.classList.contains("treeItemsHidden"),this._toggleTreeItem(t,e))},t.insertBefore(i,t.firstChild)}_toggleTreeItem(e,t=!1){this._lastToggleIsShow=t;for(const i of e.querySelectorAll(".treeItemToggler"))i.classList.toggle("treeItemsHidden",!t)}_toggleAllTreeItems(){this._toggleTreeItem(this.container,!this._lastToggleIsShow)}_finishRendering(e,t,i=!1){i&&(this.container.classList.add("treeWithDeepNesting"),this._lastToggleIsShow=!e.querySelector(".treeItemsHidden")),this.container.appendChild(e),this._dispatchEvent(t)}render(e){throw new Error("Not implemented: render")}_updateCurrentTreeItem(e=null){this._currentTreeItem&&(this._currentTreeItem.classList.remove(s),this._currentTreeItem=null),e&&(e.classList.add(s),this._currentTreeItem=e)}_scrollToCurrentTreeItem(t){if(t){let e=t.parentNode;for(;e&&e!==this.container;){if(e.classList.contains("treeItem")){const i=e.firstElementChild;i?.classList.remove("treeItemsHidden")}e=e.parentNode}this._updateCurrentTreeItem(t),this.container.scrollTo(t.offsetLeft,t.offsetTop+-100)}}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDocumentProperties=void 0;var u=i(4),p=i(3);const o=["en-us","en-lr","my"],g={"8.5x11":"Letter","8.5x14":"Legal"},m={"297x420":"A3","210x297":"A4"};function f(e,t,i){return i[`${t?e.width:e.height}x${t?e.height:e.width}`]}t.PDFDocumentProperties=class{constructor({overlayName:e,fields:t,container:i,closeButton:n},s,a,r){this.overlayName=e,this.fields=t,this.container=i,this.overlayManager=s,this.l10n=r,this._reset(),n.addEventListener("click",this.close.bind(this)),this.overlayManager.register(this.overlayName,this.container,this.close.bind(this)),a._on("pagechanging",e=>{this._currentPageNumber=e.pageNumber}),a._on("rotationchanging",e=>{this._pagesRotation=e.pagesRotation}),this._isNonMetricLocale=!0,r.getLanguage().then(e=>{this._isNonMetricLocale=o.includes(e)})}async open(){var e=e=>{Object.defineProperty(this,"fieldData",{value:Object.freeze(e),writable:!1,enumerable:!0,configurable:!0})};await Promise.all([this.overlayManager.open(this.overlayName),this._dataAvailableCapability.promise]);var t=this._currentPageNumber;const i=this._pagesRotation;if(this.fieldData&&t===this.fieldData._currentPageNumber&&i===this.fieldData._pagesRotation)this._updateUI();else{var{info:n,contentDispositionFilename:s,contentLength:a}=await this.pdfDocument.getMetadata(),[r,o,l,h,d,s]=await Promise.all([s||(0,u.getPdfFilenameFromUrl)(this.url),this._parseFileSize(a),this._parseDate(n.CreationDate),this._parseDate(n.ModDate),this.pdfDocument.getPage(t).then(e=>this._parsePageSize((0,p.getPageSizeInches)(e),i)),this._parseLinearization(n.IsLinearized)]);e({fileName:r,fileSize:o,title:n.Title,author:n.Author,subject:n.Subject,keywords:n.Keywords,creationDate:l,modificationDate:h,creator:n.Creator,producer:n.Producer,version:n.PDFFormatVersion,pageCount:this.pdfDocument.numPages,pageSize:d,linearized:s,_currentPageNumber:t,_pagesRotation:i}),this._updateUI();var t=(await this.pdfDocument.getDownloadInfo())["length"];if(a!==t){const c=Object.assign(Object.create(null),this.fieldData);c.fileSize=await this._parseFileSize(t),e(c),this._updateUI()}}}close(){this.overlayManager.close(this.overlayName)}setDocument(e,t=null){this.pdfDocument&&(this._reset(),this._updateUI(!0)),e&&(this.pdfDocument=e,this.url=t,this._dataAvailableCapability.resolve())}_reset(){this.pdfDocument=null,this.url=null,delete this.fieldData,this._dataAvailableCapability=(0,u.createPromiseCapability)(),this._currentPageNumber=1,this._pagesRotation=0}_updateUI(e=!1){if(!e&&this.fieldData){if(this.overlayManager.active===this.overlayName)for(const i in this.fields){var t=this.fieldData[i];this.fields[i].textContent=t||0===t?t:"-"}}else for(const n in this.fields)this.fields[n].textContent="-"}async _parseFileSize(e=0){const t=e/1024,i=t/1024;if(t)return this.l10n.get(`document_properties_${1<=i?"mb":"kb"}`,{size_mb:1<=i&&(+i.toPrecision(3)).toLocaleString(),size_kb:i<1&&(+t.toPrecision(3)).toLocaleString(),size_b:e.toLocaleString()})}async _parsePageSize(n,s){if(n){s%180!=0&&(n={width:n.height,height:n.width});s=(0,p.isPortraitOrientation)(n);let e={width:Math.round(100*n.width)/100,height:Math.round(100*n.height)/100},t={width:Math.round(25.4*n.width*10)/10,height:Math.round(25.4*n.height*10)/10},i=f(e,s,g)||f(t,s,m);if(!(i||Number.isInteger(t.width)&&Number.isInteger(t.height))){const d=25.4*n.width,c=25.4*n.height;n={width:Math.round(t.width),height:Math.round(t.height)};Math.abs(d-n.width)<.1&&Math.abs(c-n.height)<.1&&(i=f(n,s,m),i&&(e={width:Math.round(n.width/25.4*100)/100,height:Math.round(n.height/25.4*100)/100},t=n))}const[{width:a,height:r},o,l,h]=await Promise.all([this._isNonMetricLocale?e:t,this.l10n.get(`document_properties_page_size_unit_${this._isNonMetricLocale?"inches":"millimeters"}`),i&&this.l10n.get(`document_properties_page_size_name_${i.toLowerCase()}`),this.l10n.get(`document_properties_page_size_orientation_${s?"portrait":"landscape"}`)]);return this.l10n.get(`document_properties_page_size_dimension_${l?"name_":""}string`,{width:a.toLocaleString(),height:r.toLocaleString(),unit:o,name:l,orientation:h})}}async _parseDate(e){const t=u.PDFDateString.toDateObject(e);if(t)return this.l10n.get("document_properties_date_string",{date:t.toLocaleDateString(),time:t.toLocaleTimeString()})}_parseLinearization(e){return this.l10n.get(`document_properties_linearized_${e?"yes":"no"}`)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindBar=void 0;var a=i(14);t.PDFFindBar=class{constructor(e,t,i){this.opened=!1,this.bar=e.bar,this.toggleButton=e.toggleButton,this.findField=e.findField,this.highlightAll=e.highlightAllCheckbox,this.caseSensitive=e.caseSensitiveCheckbox,this.entireWord=e.entireWordCheckbox,this.findMsg=e.findMsg,this.findResultsCount=e.findResultsCount,this.findPreviousButton=e.findPreviousButton,this.findNextButton=e.findNextButton,this.eventBus=t,this.l10n=i,this.toggleButton.addEventListener("click",()=>{this.toggle()}),this.findField.addEventListener("input",()=>{this.dispatchEvent("")}),this.bar.addEventListener("keydown",e=>{switch(e.keyCode){case 13:e.target===this.findField&&this.dispatchEvent("again",e.shiftKey);break;case 27:this.close()}}),this.findPreviousButton.addEventListener("click",()=>{this.dispatchEvent("again",!0)}),this.findNextButton.addEventListener("click",()=>{this.dispatchEvent("again",!1)}),this.highlightAll.addEventListener("click",()=>{this.dispatchEvent("highlightallchange")}),this.caseSensitive.addEventListener("click",()=>{this.dispatchEvent("casesensitivitychange")}),this.entireWord.addEventListener("click",()=>{this.dispatchEvent("entirewordchange")}),this.eventBus._on("resize",this._adjustWidth.bind(this))}reset(){this.updateUIState()}dispatchEvent(e,t){this.eventBus.dispatch("find",{source:this,type:e,query:this.findField.value,phraseSearch:!0,caseSensitive:this.caseSensitive.checked,entireWord:this.entireWord.checked,highlightAll:this.highlightAll.checked,findPrevious:t})}updateUIState(e,t,i){let n=Promise.resolve(""),s="";switch(e){case a.FindState.FOUND:break;case a.FindState.PENDING:s="pending";break;case a.FindState.NOT_FOUND:n=this.l10n.get("find_not_found"),s="notFound";break;case a.FindState.WRAPPED:n=this.l10n.get(`find_reached_${t?"top":"bottom"}`)}this.findField.setAttribute("data-status",s),n.then(e=>{this.findMsg.textContent=e,this._adjustWidth()}),this.updateResultsCount(i)}updateResultsCount({current:e=0,total:t=0}={}){let i=Promise.resolve("");0<t&&(i=1e3<t?this.l10n.get("find_match_count_limit",{limit:1e3}):this.l10n.get("find_match_count",{current:e,total:t})),i.then(e=>{this.findResultsCount.textContent=e,this.findResultsCount.classList.toggle("hidden",!t),this._adjustWidth()})}open(){this.opened||(this.opened=!0,this.toggleButton.classList.add("toggled"),this.toggleButton.setAttribute("aria-expanded","true"),this.bar.classList.remove("hidden")),this.findField.select(),this.findField.focus(),this._adjustWidth()}close(){this.opened&&(this.opened=!1,this.toggleButton.classList.remove("toggled"),this.toggleButton.setAttribute("aria-expanded","false"),this.bar.classList.add("hidden"),this.eventBus.dispatch("findbarclose",{source:this}))}toggle(){this.opened?this.close():this.open()}_adjustWidth(){var e;this.opened&&(this.bar.classList.remove("wrapContainers"),e=this.bar.clientHeight,this.bar.firstElementChild.clientHeight<e&&this.bar.classList.add("wrapContainers"))}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindController=t.FindState=void 0;var n=i(4),a=i(15),s=i(3);const r={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3};t.FindState=r;const o={"‐":"-","‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"};let l=null;function h(e){var t;l||(t=Object.keys(o).join(""),l=new RegExp(`[${t}]`,"g"));let n=null;return[e.replace(l,function(e,t){var i=o[e],e=i.length-e.length;return 0!=e&&(n||=[]).push([t,e]),i}),n]}function c(e,t=null){if(!t)return e;let i=0;for(var[n,s]of t){n=n+i;if(e<=n)break;if(n+s>e){i+=e-n;break}i+=s}return e-i}t.PDFFindController=class{constructor({linkService:e,eventBus:t}){this._linkService=e,this._eventBus=t,this._reset(),t._on("findbarclose",this._onFindBarClose.bind(this))}get highlightMatches(){return this._highlightMatches}get pageMatches(){return this._pageMatches}get pageMatchesLength(){return this._pageMatchesLength}get selected(){return this._selected}get state(){return this._state}setDocument(e){this._pdfDocument&&this._reset(),e&&(this._pdfDocument=e,this._firstPageCapability.resolve())}executeCommand(i,e){if(e){const n=this._pdfDocument;null!==this._state&&!this._shouldDirtyMatch(i,e)||(this._dirtyMatch=!0),this._state=e,"findhighlightallchange"!==i&&this._updateUIState(r.PENDING),this._firstPageCapability.promise.then(()=>{var e,t;!this._pdfDocument||n&&this._pdfDocument!==n||(this._extractText(),e=!this._highlightMatches,t=!!this._findTimeout,this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),"find"===i?this._findTimeout=setTimeout(()=>{this._nextMatch(),this._findTimeout=null},250):this._dirtyMatch?this._nextMatch():"findagain"===i?(this._nextMatch(),e&&this._state.highlightAll&&this._updateAllPages()):"findhighlightallchange"===i?(t?this._nextMatch():this._highlightMatches=!0,this._updateAllPages()):this._nextMatch())})}}scrollMatchIntoView({element:e=null,selectedLeft:t=0,pageIndex:i=-1,matchIndex:n=-1}){this._scrollMatches&&e&&-1!==n&&n===this._selected.matchIdx&&-1!==i&&i===this._selected.pageIdx&&(this._scrollMatches=!1,s.scrollIntoView)(e,{top:-50,left:t+-400},!0)}_reset(){this._highlightMatches=!1,this._scrollMatches=!1,this._pdfDocument=null,this._pageMatches=[],this._pageMatchesLength=[],this._state=null,this._selected={pageIdx:-1,matchIdx:-1},this._offset={pageIdx:null,matchIdx:null,wrapped:!1},this._extractTextPromises=[],this._pageContents=[],this._pageDiffs=[],this._matchesCountTotal=0,this._pagesToSearch=null,this._pendingFindMatches=new Set,this._resumePageIdx=null,this._dirtyMatch=!1,clearTimeout(this._findTimeout),this._findTimeout=null,this._firstPageCapability=(0,n.createPromiseCapability)()}get _query(){return this._state.query!==this._rawQuery&&(this._rawQuery=this._state.query,[this._normalizedQuery]=h(this._state.query)),this._normalizedQuery}_shouldDirtyMatch(e,t){if(t.query!==this._state.query)return!0;switch(e){case"findagain":var i=this._selected.pageIdx+1;const n=this._linkService;return 1<=i&&i<=n.pagesCount&&i!==n.page&&!n.isPageVisible(i)?!0:!1;case"findhighlightallchange":return!1}return!0}_prepareMatches(s,i,n){s.sort(function(e,t){return e.match===t.match?e.matchLength-t.matchLength:e.match-t.match});for(let e=0,t=s.length;e<t;e++)!function(t){const i=s[t];var e=s[t+1];if(t<s.length-1&&i.match===e.match)return i.skipped=!0;for(let e=t-1;0<=e;e--){var n=s[e];if(!n.skipped){if(n.match+n.matchLength<i.match)break;if(n.match+n.matchLength>=i.match+i.matchLength)return i.skipped=!0}}}(e)&&(i.push(s[e].match),n.push(s[e].matchLength))}_isEntireWord(e,t,i){if(0<t){var n=e.charCodeAt(t),s=e.charCodeAt(t-1);if((0,a.getCharacterType)(n)===(0,a.getCharacterType)(s))return!1}t=t+i-1;if(t<e.length-1){i=e.charCodeAt(t),t=e.charCodeAt(1+t);if((0,a.getCharacterType)(i)===(0,a.getCharacterType)(t))return!1}return!0}_calculatePhraseMatch(e,t,i,n,s){const a=[],r=[];var o,l,h=e.length;let d=-h;for(;;){if(d=i.indexOf(e,d+h),-1===d)break;s&&!this._isEntireWord(i,d,h)||(o=c(d,n),l=c(d+h-1,n)-o+1,a.push(o),r.push(l))}this._pageMatches[t]=a,this._pageMatchesLength[t]=r}_calculateWordMatch(e,t,i,n,s){const a=[];var r=e.match(/\S+/g);for(let t=0,e=r.length;t<e;t++){var o,l,h=r[t],d=h.length;let e=-d;for(;;){if(e=i.indexOf(h,e+d),-1===e)break;s&&!this._isEntireWord(i,e,d)||(o=c(e,n),l=c(e+d-1,n)-o+1,a.push({match:o,matchLength:l,skipped:!1}))}}this._pageMatchesLength[t]=[],this._pageMatches[t]=[],this._prepareMatches(a,this._pageMatches[t],this._pageMatchesLength[t])}_calculateMatch(e){let t=this._pageContents[e];var i=this._pageDiffs[e];let n=this._query;var{caseSensitive:s,entireWord:a,phraseSearch:r}=this._state;0!==n.length&&(s||(t=t.toLowerCase(),n=n.toLowerCase()),r?this._calculatePhraseMatch(n,e,t,i,a):this._calculateWordMatch(n,e,t,i,a),this._state.highlightAll&&this._updatePage(e),this._resumePageIdx===e&&(this._resumePageIdx=null,this._nextPageMatch()),0<(e=this._pageMatches[e].length)&&(this._matchesCountTotal+=e,this._updateUIResultsCount()))}_extractText(){if(!(0<this._extractTextPromises.length)){let i=Promise.resolve();for(let t=0,e=this._linkService.pagesCount;t<e;t++){const s=(0,n.createPromiseCapability)();this._extractTextPromises[t]=s.promise,i=i.then(()=>this._pdfDocument.getPage(t+1).then(e=>e.getTextContent({normalizeWhitespace:!0})).then(e=>{var i=e.items;const n=[];for(let e=0,t=i.length;e<t;e++)n.push(i[e].str);[this._pageContents[t],this._pageDiffs[t]]=h(n.join("")),s.resolve(t)},e=>{console.error(`Unable to get text content for page ${t+1}`,e),this._pageContents[t]="",this._pageDiffs[t]=null,s.resolve(t)}))}}}_updatePage(e){this._scrollMatches&&this._selected.pageIdx===e&&(this._linkService.page=e+1),this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:e})}_updateAllPages(){this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:-1})}_nextMatch(){var e=this._state.findPrevious,t=this._linkService.page-1,i=this._linkService.pagesCount;if(this._highlightMatches=!0,this._dirtyMatch){this._dirtyMatch=!1,this._selected.pageIdx=this._selected.matchIdx=-1,this._offset.pageIdx=t,this._offset.matchIdx=null,this._offset.wrapped=!1,this._resumePageIdx=null,this._pageMatches.length=0,this._pageMatchesLength.length=0,this._matchesCountTotal=0,this._updateAllPages();for(let e=0;e<i;e++)this._pendingFindMatches.has(e)||(this._pendingFindMatches.add(e),this._extractTextPromises[e].then(e=>{this._pendingFindMatches.delete(e),this._calculateMatch(e)}))}if(""!==this._query){if(!this._resumePageIdx){const n=this._offset;if(this._pagesToSearch=i,null!==n.matchIdx){t=this._pageMatches[n.pageIdx].length;if(!e&&n.matchIdx+1<t||e&&0<n.matchIdx)return n.matchIdx=e?n.matchIdx-1:n.matchIdx+1,void this._updateMatch(!0);this._advanceOffsetPage(e)}this._nextPageMatch()}}else this._updateUIState(r.FOUND)}_matchesReady(e){const t=this._offset;var i=e.length,e=this._state.findPrevious;return i?(t.matchIdx=e?i-1:0,this._updateMatch(!0),!0):(this._advanceOffsetPage(e),!!(t.wrapped&&(t.matchIdx=null,this._pagesToSearch<0))&&(this._updateMatch(!1),!0))}_nextPageMatch(){null!==this._resumePageIdx&&console.error("There can only be one pending page.");let e=null;do{var t=this._offset.pageIdx;if(e=this._pageMatches[t],!e){this._resumePageIdx=t;break}}while(!this._matchesReady(e))}_advanceOffsetPage(e){const t=this._offset;var i=this._linkService.pagesCount;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this._pagesToSearch--,(t.pageIdx>=i||t.pageIdx<0)&&(t.pageIdx=e?i-1:0,t.wrapped=!0)}_updateMatch(e=!1){let t=r.NOT_FOUND;var i=this._offset.wrapped;this._offset.wrapped=!1,e&&(e=this._selected.pageIdx,this._selected.pageIdx=this._offset.pageIdx,this._selected.matchIdx=this._offset.matchIdx,t=i?r.WRAPPED:r.FOUND,-1!==e&&e!==this._selected.pageIdx&&this._updatePage(e)),this._updateUIState(t,this._state.findPrevious),-1!==this._selected.pageIdx&&(this._scrollMatches=!0,this._updatePage(this._selected.pageIdx))}_onFindBarClose(e){const t=this._pdfDocument;this._firstPageCapability.promise.then(()=>{!this._pdfDocument||t&&this._pdfDocument!==t||(this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),this._resumePageIdx&&(this._resumePageIdx=null,this._dirtyMatch=!0),this._updateUIState(r.FOUND),this._highlightMatches=!1,this._updateAllPages())})}_requestMatchesCount(){var{pageIdx:t,matchIdx:e}=this._selected;let i=0,n=this._matchesCountTotal;if(-1!==e){for(let e=0;e<t;e++)i+=this._pageMatches[e]?.length||0;i+=e+1}return(i<1||i>n)&&(i=n=0),{current:i,total:n}}_updateUIResultsCount(){this._eventBus.dispatch("updatefindmatchescount",{source:this,matchesCount:this._requestMatchesCount()})}_updateUIState(e,t){this._eventBus.dispatch("updatefindcontrolstate",{source:this,state:e,previous:t,matchesCount:this._requestMatchesCount(),rawQuery:this._state?.query??null})}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getCharacterType=function(e){if(e<11904)return 0==(65408&e)?function(e){return 32===e||9===e||13===e||10===e}(e)?i.SPACE:function(e){return 97<=e&&e<=122||65<=e&&e<=90}(e)||function(e){return 48<=e&&e<=57}(e)||95===e?i.ALPHA_LETTER:i.PUNCT:3584==(65408&e)?i.THAI_LETTER:160===e?i.SPACE:i.ALPHA_LETTER;{if(function(e){return 13312<=e&&e<=40959||63744<=e&&e<=64255}(e))return i.HAN_LETTER;if(function(e){return 12448<=e&&e<=12543}(e))return i.KATAKANA_LETTER;if(function(e){return 12352<=e&&e<=12447}(e))return i.HIRAGANA_LETTER;if(function(e){return 65376<=e&&e<=65439}(e))return i.HALFWIDTH_KATAKANA_LETTER}return i.ALPHA_LETTER},t.CharacterType=void 0;const i={SPACE:0,ALPHA_LETTER:1,PUNCT:2,HAN_LETTER:3,KATAKANA_LETTER:4,HIRAGANA_LETTER:5,HALFWIDTH_KATAKANA_LETTER:6,THAI_LETTER:7};t.CharacterType=i},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isDestArraysEqual=o,t.isDestHashesEqual=s,t.PDFHistory=void 0;var a=i(3);function r(){return document.location.hash}function s(e,t){return"string"==typeof e&&"string"==typeof t&&(e===t||(0,a.parseQueryString)(e).get("nameddest")===t)}function o(i,n){if(!Array.isArray(i)||!Array.isArray(n))return!1;if(i.length!==n.length)return!1;for(let e=0,t=i.length;e<t;e++)if(!function e(t,i){if(typeof t==typeof i&&!Array.isArray(t)&&!Array.isArray(i)){if(null===t||"object"!=typeof t||null===i)return t===i||Number.isNaN(t)&&Number.isNaN(i);if(Object.keys(t).length===Object.keys(i).length){for(const n in t)if(!e(t[n],i[n]))return;return 1}}}(i[e],n[e]))return!1;return!0}t.PDFHistory=class{constructor({linkService:e,eventBus:t}){this.linkService=e,this.eventBus=t,this._initialized=!1,this._fingerprint="",this.reset(),this._boundEvents=null,this._isViewerInPresentationMode=!1,this.eventBus._on("presentationmodechanged",e=>{this._isViewerInPresentationMode=e.state!==a.PresentationModeState.NORMAL}),this.eventBus._on("pagesinit",()=>{this._isPagesLoaded=!1,this.eventBus._on("pagesloaded",e=>{this._isPagesLoaded=!!e.pagesCount},{once:!0})})}initialize({fingerprint:e,resetHistory:t=!1,updateUrl:i=!1}){if(e&&"string"==typeof e){this._initialized&&this.reset();var n=""!==this._fingerprint&&this._fingerprint!==e;this._fingerprint=e,this._updateUrl=!0===i,this._initialized=!0,this._bindEvents();var s=window.history.state;if(this._popStateInProgress=!1,this._blockHashChange=0,this._currentHash=r(),this._numPositionUpdates=0,this._uid=this._maxUid=0,this._destination=null,this._position=null,!this._isValidState(s,!0)||t){var{hash:e,page:i,rotation:a}=this._parseCurrentHash(!0);return!e||n||t?void this._pushOrReplaceState(null,!0):void this._pushOrReplaceState({hash:e,page:i,rotation:a},!0)}a=s.destination;this._updateInternalState(a,s.uid,!0),void 0!==a.rotation&&(this._initialRotation=a.rotation),a.dest?(this._initialBookmark=JSON.stringify(a.dest),this._destination.page=null):a.hash?this._initialBookmark=a.hash:a.page&&(this._initialBookmark=`page=${a.page}`)}else console.error('PDFHistory.initialize: The "fingerprint" must be a non-empty string.')}reset(){this._initialized&&(this._pageHide(),this._initialized=!1,this._unbindEvents()),this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._initialBookmark=null,this._initialRotation=null}push({namedDest:t=null,explicitDest:i,pageNumber:n}){if(this._initialized)if(t&&"string"!=typeof t)console.error("PDFHistory.push: "+`"${t}" is not a valid namedDest parameter.`);else if(Array.isArray(i))if(this._isValidPage(n)||null===n&&!this._destination){t=t||JSON.stringify(i);if(t){let e=!1;if(this._destination&&(s(this._destination.hash,t)||o(this._destination.dest,i))){if(this._destination.page)return;e=!0}this._popStateInProgress&&!e||(this._pushOrReplaceState({dest:i,hash:t,page:n,rotation:this.linkService.rotation},e),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then(()=>{this._popStateInProgress=!1})))}}else console.error("PDFHistory.push: "+`"${n}" is not a valid pageNumber parameter.`);else console.error("PDFHistory.push: "+`"${i}" is not a valid explicitDest parameter.`)}pushPage(e){this._initialized&&(this._isValidPage(e)?this._destination?.page!==e&&(this._popStateInProgress||(this._pushOrReplaceState({dest:null,hash:`page=${e}`,page:e,rotation:this.linkService.rotation}),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then(()=>{this._popStateInProgress=!1})))):console.error(`PDFHistory.pushPage: "${e}" is not a valid page number.`))}pushCurrentPosition(){this._initialized&&!this._popStateInProgress&&this._tryPushCurrentPosition()}back(){var e;this._initialized&&!this._popStateInProgress&&(e=window.history.state,this._isValidState(e)&&0<e.uid&&window.history.back())}forward(){var e;this._initialized&&!this._popStateInProgress&&(e=window.history.state,this._isValidState(e)&&e.uid<this._maxUid&&window.history.forward())}get popStateInProgress(){return this._initialized&&(this._popStateInProgress||0<this._blockHashChange)}get initialBookmark(){return this._initialized?this._initialBookmark:null}get initialRotation(){return this._initialized?this._initialRotation:null}_pushOrReplaceState(e,t=!1){var i=t||!this._destination,t={fingerprint:this._fingerprint,uid:i?this._uid:this._uid+1,destination:e};this._updateInternalState(e,t.uid);let n;if(this._updateUrl&&e?.hash){const s=document.location.href.split("#")[0];s.startsWith("file://")||(n=`${s}#${e.hash}`)}i?window.history.replaceState(t,"",n):window.history.pushState(t,"",n)}_tryPushCurrentPosition(e=!1){if(this._position){let t=this._position;if(e&&(t=Object.assign(Object.create(null),this._position),t.temporary=!0),this._destination){if(this._destination.temporary)this._pushOrReplaceState(t,!0);else if(this._destination.hash!==t.hash&&(this._destination.page||!(this._numPositionUpdates<=50))){let e=!1;if(this._destination.page>=t.first&&this._destination.page<=t.page){if(void 0!==this._destination.dest||!this._destination.first)return;e=!0}this._pushOrReplaceState(t,e)}}else this._pushOrReplaceState(t)}}_isValidPage(e){return Number.isInteger(e)&&0<e&&e<=this.linkService.pagesCount}_isValidState(e,t=!1){if(!e)return!1;if(e.fingerprint!==this._fingerprint){if(!t)return!1;if("string"!=typeof e.fingerprint||e.fingerprint.length!==this._fingerprint.length)return!1;var[t]=performance.getEntriesByType("navigation");if("reload"!==t?.type)return!1}return!(!Number.isInteger(e.uid)||e.uid<0)&&(null!==e.destination&&"object"==typeof e.destination)}_updateInternalState(e,t,i=!1){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),i&&e?.temporary&&delete e.temporary,this._destination=e,this._uid=t,this._maxUid=Math.max(this._maxUid,t),this._numPositionUpdates=0}_parseCurrentHash(e=!1){var t=unescape(r()).substring(1);const i=(0,a.parseQueryString)(t);var n=i.get("nameddest")||"";let s=0|i.get("page");return(!this._isValidPage(s)||e&&0<n.length)&&(s=null),{hash:t,page:s,rotation:this.linkService.rotation}}_updateViewarea({location:e}){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._position={hash:this._isViewerInPresentationMode?`page=${e.pageNumber}`:e.pdfOpenParams.substring(1),page:this.linkService.page,first:e.pageNumber,rotation:e.rotation},this._popStateInProgress||(this._isPagesLoaded&&this._destination&&!this._destination.page&&this._numPositionUpdates++,this._updateViewareaTimeout=setTimeout(()=>{this._popStateInProgress||this._tryPushCurrentPosition(!0),this._updateViewareaTimeout=null},1e3))}_popState({state:e}){var t,i=r(),n=this._currentHash!==i;this._currentHash=i,e?this._isValidState(e)&&(this._popStateInProgress=!0,n&&(this._blockHashChange++,(0,a.waitOnEventOrTimeout)({target:window,name:"hashchange",delay:1e3}).then(()=>{this._blockHashChange--})),t=e.destination,this._updateInternalState(t,e.uid,!0),(0,a.isValidRotation)(t.rotation)&&(this.linkService.rotation=t.rotation),t.dest?this.linkService.goToDestination(t.dest):t.hash?this.linkService.setHash(t.hash):t.page&&(this.linkService.page=t.page),Promise.resolve().then(()=>{this._popStateInProgress=!1})):(this._uid++,{hash:n,page:e,rotation:t}=this._parseCurrentHash(),this._pushOrReplaceState({hash:n,page:e,rotation:t},!0))}_pageHide(){this._destination&&!this._destination.temporary||this._tryPushCurrentPosition()}_bindEvents(){this._boundEvents||(this._boundEvents={updateViewarea:this._updateViewarea.bind(this),popState:this._popState.bind(this),pageHide:this._pageHide.bind(this)},this.eventBus._on("updateviewarea",this._boundEvents.updateViewarea),window.addEventListener("popstate",this._boundEvents.popState),window.addEventListener("pagehide",this._boundEvents.pageHide))}_unbindEvents(){this._boundEvents&&(this.eventBus._off("updateviewarea",this._boundEvents.updateViewarea),window.removeEventListener("popstate",this._boundEvents.popState),window.removeEventListener("pagehide",this._boundEvents.pageHide),this._boundEvents=null)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFLayerViewer=void 0;class n extends i(11).BaseTreeViewer{constructor(e){super(e),this.l10n=e.l10n,this.eventBus._on("resetlayers",this._resetLayers.bind(this)),this.eventBus._on("togglelayerstree",this._toggleAllTreeItems.bind(this))}reset(){super.reset(),this._optionalContentConfig=null}_dispatchEvent(e){this.eventBus.dispatch("layersloaded",{source:this,layersCount:e})}_bindLink(t,{groupId:e,input:i}){const n=()=>{this._optionalContentConfig.setVisibility(e,i.checked),this.eventBus.dispatch("optionalcontentconfig",{source:this,promise:Promise.resolve(this._optionalContentConfig)})};t.onclick=e=>e.target===i?(n(),!0):e.target!==t||(i.checked=!i.checked,n(),!1)}async _setNestedName(e,{name:t=null}){"string"!=typeof t?(e.textContent=await this.l10n.get("additional_layers"),e.style.fontStyle="italic"):e.textContent=this._normalizeTextContent(t)}_addToggleButton(e,{name:t=null}){super._addToggleButton(e,null===t)}_toggleAllTreeItems(){this._optionalContentConfig&&super._toggleAllTreeItems()}render({optionalContentConfig:i,pdfDocument:n}){this._optionalContentConfig&&this.reset(),this._optionalContentConfig=i||null,this._pdfDocument=n||null;n=i?.getOrder();if(n){const a=document.createDocumentFragment(),r=[{parent:a,groups:n}];let e=0,t=!1;for(;0<r.length;){const o=r.shift();for(const l of o.groups){const h=document.createElement("div");h.className="treeItem";const d=document.createElement("a");if(h.appendChild(d),"object"==typeof l){t=!0,this._addToggleButton(h,l),this._setNestedName(d,l);const c=document.createElement("div");c.className="treeItems",h.appendChild(c),r.push({parent:c,groups:l.order})}else{var s=i.getGroup(l);const u=document.createElement("input");this._bindLink(d,{groupId:l,input:u}),u.type="checkbox",u.id=l,u.checked=s.visible;const p=document.createElement("label");p.setAttribute("for",l),p.textContent=this._normalizeTextContent(s.name),d.appendChild(u),d.appendChild(p),e++}o.parent.appendChild(h)}}this._finishRendering(a,e,t)}else this._dispatchEvent(0)}async _resetLayers(){var e;this._optionalContentConfig&&(e=await this._pdfDocument.getOptionalContentConfig(),this.eventBus.dispatch("optionalcontentconfig",{source:this,promise:Promise.resolve(e)}),this.render({optionalContentConfig:e,pdfDocument:this._pdfDocument}))}}t.PDFLayerViewer=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=void 0;var n=i(4),o=i(3);t.PDFLinkService=class{constructor({eventBus:e,externalLinkTarget:t=null,externalLinkRel:i=null,ignoreDestinationZoom:n=!1}={}){this.eventBus=e,this.externalLinkTarget=t,this.externalLinkRel=i,this.externalLinkEnabled=!0,this._ignoreDestinationZoom=n,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null,this._pagesRefCache=null}setDocument(e,t=null){this.baseUrl=t,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}setViewer(e){this.pdfViewer=e}setHistory(e){this.pdfHistory=e}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return this.pdfViewer.currentPageNumber}set page(e){this.pdfViewer.currentPageNumber=e}get rotation(){return this.pdfViewer.pagesRotation}set rotation(e){this.pdfViewer.pagesRotation=e}_goToDestinationHelper(t,i=null,n){const s=n[0];let e;if("object"==typeof s&&null!==s){if(e=this._cachedPageNumber(s),null===e)return void this.pdfDocument.getPageIndex(s).then(e=>{this.cachePageRef(e+1,s),this._goToDestinationHelper(t,i,n)}).catch(()=>{console.error(`PDFLinkService._goToDestinationHelper: "${s}" is not `+`a valid page reference, for dest="${t}".`)})}else{if(!Number.isInteger(s))return void console.error(`PDFLinkService._goToDestinationHelper: "${s}" is not `+`a valid destination reference, for dest="${t}".`);e=s+1}!e||e<1||e>this.pagesCount?console.error(`PDFLinkService._goToDestinationHelper: "${e}" is not `+`a valid page number, for dest="${t}".`):(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.push({namedDest:i,explicitDest:n,pageNumber:e})),this.pdfViewer.scrollPageIntoView({pageNumber:e,destArray:n,ignoreDestinationZoom:this._ignoreDestinationZoom}))}async goToDestination(i){if(this.pdfDocument){let e,t;t="string"==typeof i?(e=i,await this.pdfDocument.getDestination(i)):(e=null,await i),Array.isArray(t)?this._goToDestinationHelper(i,e,t):console.error(`PDFLinkService.goToDestination: "${t}" is not `+`a valid destination array, for dest="${i}".`)}}goToPage(e){var t;this.pdfDocument&&(t="string"==typeof e&&this.pdfViewer.pageLabelToPageNumber(e)||0|e,Number.isInteger(t)&&0<t&&t<=this.pagesCount?(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.pushPage(t)),this.pdfViewer.scrollPageIntoView({pageNumber:t})):console.error(`PDFLinkService.goToPage: "${e}" is not a valid page.`))}addLinkAttributes(e,t,i=!1){(0,n.addLinkAttributes)(e,{url:t,target:i?n.LinkTarget.BLANK:this.externalLinkTarget,rel:this.externalLinkRel,enabled:this.externalLinkEnabled})}getDestinationHash(e){if("string"==typeof e){if(0<e.length)return this.getAnchorUrl("#"+escape(e))}else if(Array.isArray(e)){e=JSON.stringify(e);if(0<e.length)return this.getAnchorUrl("#"+escape(e))}return this.getAnchorUrl("")}getAnchorUrl(e){return(this.baseUrl||"")+e}setHash(i){if(this.pdfDocument){let e,t;if(i.includes("=")){const a=(0,o.parseQueryString)(i);if(a.has("search")&&this.eventBus.dispatch("findfromurlhash",{source:this,query:a.get("search").replace(/"/g,""),phraseSearch:"true"===a.get("phrase")}),a.has("page")&&(e=0|a.get("page")||1),a.has("zoom")){var n=a.get("zoom").split(",");const r=n[0];var s=parseFloat(r);r.includes("Fit")?"Fit"===r||"FitB"===r?t=[null,{name:r}]:"FitH"===r||"FitBH"===r||"FitV"===r||"FitBV"===r?t=[null,{name:r},1<n.length?0|n[1]:null]:"FitR"===r?5!==n.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):t=[null,{name:r},0|n[1],0|n[2],0|n[3],0|n[4]]:console.error(`PDFLinkService.setHash: "${r}" is not `+"a valid zoom value."):t=[null,{name:"XYZ"},1<n.length?0|n[1]:null,2<n.length?0|n[2]:null,s?s/100:r]}t?this.pdfViewer.scrollPageIntoView({pageNumber:e||this.page,destArray:t,allowNegativeOffset:!0}):e&&(this.page=e),a.has("pagemode")&&this.eventBus.dispatch("pagemode",{source:this,mode:a.get("pagemode")}),a.has("nameddest")&&this.goToDestination(a.get("nameddest"))}else{t=unescape(i);try{t=JSON.parse(t),Array.isArray(t)||(t=t.toString())}catch(e){}"string"==typeof t||function(t){if(!Array.isArray(t))return!1;var i=t.length;if(i<2)return!1;var e=t[0];if(!("object"==typeof e&&Number.isInteger(e.num)&&Number.isInteger(e.gen)||Number.isInteger(e)&&0<=e))return!1;e=t[1];if("object"!=typeof e||"string"!=typeof e.name)return!1;let n=!0;switch(e.name){case"XYZ":if(5!==i)return!1;break;case"Fit":case"FitB":return 2===i;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==i)return!1;break;case"FitR":if(6!==i)return!1;n=!1;break;default:return!1}for(let e=2;e<i;e++){var s=t[e];if(!("number"==typeof s||n&&null===s))return!1}return!0}(t)?this.goToDestination(t):console.error(`PDFLinkService.setHash: "${unescape(i)}" is not `+"a valid destination.")}}}executeNamedAction(e){switch(e){case"GoBack":this.pdfHistory?.back();break;case"GoForward":this.pdfHistory?.forward();break;case"NextPage":this.pdfViewer.nextPage();break;case"PrevPage":this.pdfViewer.previousPage();break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}this.eventBus.dispatch("namedaction",{source:this,action:e})}cachePageRef(e,t){t&&(t=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`,this._pagesRefCache[t]=e)}_cachedPageNumber(e){e=0===e.gen?`${e.num}R`:`${e.num}R${e.gen}`;return this._pagesRefCache?.[e]||null}isPageVisible(e){return this.pdfViewer.isPageVisible(e)}isPageCached(e){return this.pdfViewer.isPageCached(e)}};t.SimpleLinkService=class{constructor(){this.externalLinkEnabled=!0}get pagesCount(){return 0}get page(){return 0}set page(e){}get rotation(){return 0}set rotation(e){}async goToDestination(e){}goToPage(e){}addLinkAttributes(e,t,i=0){(0,n.addLinkAttributes)(e,{url:t,enabled:this.externalLinkEnabled})}getDestinationHash(e){return"#"}getAnchorUrl(e){return"#"}setHash(e){}executeNamedAction(e){}cachePageRef(e,t){}isPageVisible(e){return!0}isPageCached(e){return!0}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFOutlineViewer=void 0;var n=i(11),d=i(4),s=i(3);class a extends n.BaseTreeViewer{constructor(e){super(e),this.linkService=e.linkService,this.eventBus._on("toggleoutlinetree",this._toggleAllTreeItems.bind(this)),this.eventBus._on("currentoutlineitem",this._currentOutlineItem.bind(this)),this.eventBus._on("pagechanging",e=>{this._currentPageNumber=e.pageNumber}),this.eventBus._on("pagesloaded",e=>{this._isPagesLoaded=!!e.pagesCount,this._currentOutlineItemCapability&&!this._currentOutlineItemCapability.settled&&this._currentOutlineItemCapability.resolve(this._isPagesLoaded)}),this.eventBus._on("sidebarviewchanged",e=>{this._sidebarView=e.view})}reset(){super.reset(),this._outline=null,this._pageNumberToDestHashCapability=null,this._currentPageNumber=1,this._isPagesLoaded=!1,this._currentOutlineItemCapability&&!this._currentOutlineItemCapability.settled&&this._currentOutlineItemCapability.resolve(!1),this._currentOutlineItemCapability=null}_dispatchEvent(e){this._currentOutlineItemCapability=(0,d.createPromiseCapability)(),0===e||this._pdfDocument?.loadingParams.disableAutoFetch?this._currentOutlineItemCapability.resolve(!1):this._isPagesLoaded&&this._currentOutlineItemCapability.resolve(!0),this.eventBus.dispatch("outlineloaded",{source:this,outlineCount:e,currentOutlineItemPromise:this._currentOutlineItemCapability.promise})}_bindLink(e,{url:t,newWindow:i,dest:n}){const s=this["linkService"];t?s.addLinkAttributes(e,t,i):(e.href=s.getDestinationHash(n),e.onclick=e=>(this._updateCurrentTreeItem(e.target.parentNode),n&&s.goToDestination(n),!1))}_setStyles(e,{bold:t,italic:i}){t&&(e.style.fontWeight="bold"),i&&(e.style.fontStyle="italic")}_addToggleButton(e,{count:t,items:i}){let n=!1;if(t<0){let e=i.length;if(0<e){const r=[...i];for(;0<r.length;){var{count:s,items:a}=r.shift();0<s&&0<a.length&&(e+=a.length,r.push(...a))}}Math.abs(t)===e&&(n=!0)}super._addToggleButton(e,n)}_toggleAllTreeItems(){this._outline&&super._toggleAllTreeItems()}render({outline:i,pdfDocument:n}){if(this._outline&&this.reset(),this._outline=i||null,this._pdfDocument=n||null,i){n=document.createDocumentFragment();const s=[{parent:n,items:i}];let e=0,t=!1;for(;0<s.length;){const a=s.shift();for(const r of a.items){const o=document.createElement("div");o.className="treeItem";const l=document.createElement("a");if(this._bindLink(l,r),this._setStyles(l,r),l.textContent=this._normalizeTextContent(r.title),o.appendChild(l),0<r.items.length){t=!0,this._addToggleButton(o,r);const h=document.createElement("div");h.className="treeItems",o.appendChild(h),s.push({parent:h,items:r.items})}a.parent.appendChild(o),e++}}this._finishRendering(n,e,t)}else this._dispatchEvent(0)}async _currentOutlineItem(){if(!this._isPagesLoaded)throw new Error("_currentOutlineItem: All pages have not been loaded.");if(this._outline&&this._pdfDocument){const i=await this._getPageNumberToDestHash(this._pdfDocument);if(i&&(this._updateCurrentTreeItem(null),this._sidebarView===s.SidebarView.OUTLINE))for(let e=this._currentPageNumber;0<e;e--){var t=i.get(e);if(t){t=this.container.querySelector(`a[href="${t}"]`);if(t){this._scrollToCurrentTreeItem(t.parentNode);break}}}}}async _getPageNumberToDestHash(i){if(this._pageNumberToDestHashCapability)return this._pageNumberToDestHashCapability.promise;this._pageNumberToDestHashCapability=(0,d.createPromiseCapability)();const n=new Map,s=new Map,a=[{nesting:0,items:this._outline}];for(;0<a.length;){var r,o,e=a.shift(),l=e.nesting;for({dest:r,items:o}of e.items){let e,t;if("string"==typeof r){if(e=await i.getDestination(r),i!==this._pdfDocument)return null}else e=r;if(Array.isArray(e)){var[h]=e;if("object"==typeof h&&null!==h){if(t=this.linkService._cachedPageNumber(h),!t)try{if(t=await i.getPageIndex(h)+1,i!==this._pdfDocument)return null;this.linkService.cachePageRef(t,h)}catch(e){}}else Number.isInteger(h)&&(t=h+1);Number.isInteger(t)&&(!n.has(t)||l>s.get(t))&&(h=this.linkService.getDestinationHash(r),n.set(t,h),s.set(t,l))}0<o.length&&a.push({nesting:l+1,items:o})}}return this._pageNumberToDestHashCapability.resolve(0<n.size?n:null),this._pageNumberToDestHashCapability.promise}}t.PDFOutlineViewer=a},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPresentationMode=void 0;var n=i(3);const s="pdfPresentationMode",a="pdfPresentationModeControls",r=Math.PI/6;t.PDFPresentationMode=class{constructor({container:e,pdfViewer:t,eventBus:i}){this.container=e,this.pdfViewer=t,this.eventBus=i,this.active=!1,this.args=null,this.contextMenuOpen=!1,this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0,this.touchSwipeState=null}request(){if(this.switchInProgress||this.active||!this.pdfViewer.pagesCount)return!1;if(this._addFullscreenChangeListeners(),this._setSwitchInProgress(),this._notifyStateChange(),this.container.requestFullscreen)this.container.requestFullscreen();else if(this.container.mozRequestFullScreen)this.container.mozRequestFullScreen();else{if(!this.container.webkitRequestFullscreen)return!1;this.container.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}return this.args={page:this.pdfViewer.currentPageNumber,previousScale:this.pdfViewer.currentScaleValue},!0}_mouseWheel(e){var t,i;this.active&&(e.preventDefault(),i=(0,n.normalizeWheelEventDelta)(e),t=Date.now(),(e=this.mouseScrollTimeStamp)<t&&t-e<50||((0<this.mouseScrollDelta&&i<0||this.mouseScrollDelta<0&&0<i)&&this._resetMouseScrollState(),this.mouseScrollDelta+=i,.1<=Math.abs(this.mouseScrollDelta)&&(i=this.mouseScrollDelta,this._resetMouseScrollState(),(0<i?this.pdfViewer.previousPage():this.pdfViewer.nextPage())&&(this.mouseScrollTimeStamp=t))))}get isFullscreen(){return!!(document.fullscreenElement||document.mozFullScreen||document.webkitIsFullScreen)}_notifyStateChange(){let e=n.PresentationModeState.NORMAL;this.switchInProgress?e=n.PresentationModeState.CHANGING:this.active&&(e=n.PresentationModeState.FULLSCREEN),this.eventBus.dispatch("presentationmodechanged",{source:this,state:e})}_setSwitchInProgress(){this.switchInProgress&&clearTimeout(this.switchInProgress),this.switchInProgress=setTimeout(()=>{this._removeFullscreenChangeListeners(),delete this.switchInProgress,this._notifyStateChange()},1500)}_resetSwitchInProgress(){this.switchInProgress&&(clearTimeout(this.switchInProgress),delete this.switchInProgress)}_enter(){this.active=!0,this._resetSwitchInProgress(),this._notifyStateChange(),this.container.classList.add(s),setTimeout(()=>{this.pdfViewer.currentPageNumber=this.args.page,this.pdfViewer.currentScaleValue="page-fit"},0),this._addWindowListeners(),this._showControls(),this.contextMenuOpen=!1,window.getSelection().removeAllRanges()}_exit(){const e=this.pdfViewer.currentPageNumber;this.container.classList.remove(s),setTimeout(()=>{this.active=!1,this._removeFullscreenChangeListeners(),this._notifyStateChange(),this.pdfViewer.currentScaleValue=this.args.previousScale,this.pdfViewer.currentPageNumber=e,this.args=null},0),this._removeWindowListeners(),this._hideControls(),this._resetMouseScrollState(),this.contextMenuOpen=!1}_mouseDown(e){if(this.contextMenuOpen)return this.contextMenuOpen=!1,void e.preventDefault();0===e.button&&(e.target.href&&e.target.classList.contains("internalLink")||(e.preventDefault(),e.shiftKey?this.pdfViewer.previousPage():this.pdfViewer.nextPage()))}_contextMenu(){this.contextMenuOpen=!0}_showControls(){this.controlsTimeout?clearTimeout(this.controlsTimeout):this.container.classList.add(a),this.controlsTimeout=setTimeout(()=>{this.container.classList.remove(a),delete this.controlsTimeout},3e3)}_hideControls(){this.controlsTimeout&&(clearTimeout(this.controlsTimeout),this.container.classList.remove(a),delete this.controlsTimeout)}_resetMouseScrollState(){this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0}_touchSwipe(t){if(this.active)if(1<t.touches.length)this.touchSwipeState=null;else switch(t.type){case"touchstart":this.touchSwipeState={startX:t.touches[0].pageX,startY:t.touches[0].pageY,endX:t.touches[0].pageX,endY:t.touches[0].pageY};break;case"touchmove":if(null===this.touchSwipeState)return;this.touchSwipeState.endX=t.touches[0].pageX,this.touchSwipeState.endY=t.touches[0].pageY,t.preventDefault();break;case"touchend":if(null===this.touchSwipeState)return;let e=0;var i=this.touchSwipeState.endX-this.touchSwipeState.startX,n=this.touchSwipeState.endY-this.touchSwipeState.startY,s=Math.abs(Math.atan2(n,i));50<Math.abs(i)&&(s<=r||s>=Math.PI-r)?e=i:50<Math.abs(n)&&Math.abs(s-Math.PI/2)<=r&&(e=n),0<e?this.pdfViewer.previousPage():e<0&&this.pdfViewer.nextPage()}}_addWindowListeners(){this.showControlsBind=this._showControls.bind(this),this.mouseDownBind=this._mouseDown.bind(this),this.mouseWheelBind=this._mouseWheel.bind(this),this.resetMouseScrollStateBind=this._resetMouseScrollState.bind(this),this.contextMenuBind=this._contextMenu.bind(this),this.touchSwipeBind=this._touchSwipe.bind(this),window.addEventListener("mousemove",this.showControlsBind),window.addEventListener("mousedown",this.mouseDownBind),window.addEventListener("wheel",this.mouseWheelBind,{passive:!1}),window.addEventListener("keydown",this.resetMouseScrollStateBind),window.addEventListener("contextmenu",this.contextMenuBind),window.addEventListener("touchstart",this.touchSwipeBind),window.addEventListener("touchmove",this.touchSwipeBind),window.addEventListener("touchend",this.touchSwipeBind)}_removeWindowListeners(){window.removeEventListener("mousemove",this.showControlsBind),window.removeEventListener("mousedown",this.mouseDownBind),window.removeEventListener("wheel",this.mouseWheelBind,{passive:!1}),window.removeEventListener("keydown",this.resetMouseScrollStateBind),window.removeEventListener("contextmenu",this.contextMenuBind),window.removeEventListener("touchstart",this.touchSwipeBind),window.removeEventListener("touchmove",this.touchSwipeBind),window.removeEventListener("touchend",this.touchSwipeBind),delete this.showControlsBind,delete this.mouseDownBind,delete this.mouseWheelBind,delete this.resetMouseScrollStateBind,delete this.contextMenuBind,delete this.touchSwipeBind}_fullscreenChange(){this.isFullscreen?this._enter():this._exit()}_addFullscreenChangeListeners(){this.fullscreenChangeBind=this._fullscreenChange.bind(this),window.addEventListener("fullscreenchange",this.fullscreenChangeBind),window.addEventListener("mozfullscreenchange",this.fullscreenChangeBind),window.addEventListener("webkitfullscreenchange",this.fullscreenChangeBind)}_removeFullscreenChangeListeners(){window.removeEventListener("fullscreenchange",this.fullscreenChangeBind),window.removeEventListener("mozfullscreenchange",this.fullscreenChangeBind),window.removeEventListener("webkitfullscreenchange",this.fullscreenChangeBind),delete this.fullscreenChangeBind}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFScriptingManager=void 0;var a=i(4),l=i(3),r=i(7);t.PDFScriptingManager=class{constructor({eventBus:e,sandboxBundleSrc:t=null,scriptingFactory:i=null,docPropertiesLookup:n=null}){this._pdfDocument=null,this._pdfViewer=null,this._closeCapability=null,this._destroyCapability=null,this._scripting=null,this._mouseState=Object.create(null),this._ready=!1,this._eventBus=e,this._sandboxBundleSrc=t,this._scriptingFactory=i,this._docPropertiesLookup=n}setViewer(e){this._pdfViewer=e}async setDocument(e){if(this._pdfDocument&&await this._destroyScripting(),this._pdfDocument=e){var[t,i,n]=await Promise.all([e.getFieldObjects(),e.getCalculationOrderIds(),e.getJSActions()]);if(t||n){if(e===this._pdfDocument){try{this._scripting=this._createScripting()}catch(e){return console.error(`PDFScriptingManager.setDocument: "${e?.message}".`),void await this._destroyScripting()}this._internalEvents.set("updatefromsandbox",e=>{e?.source===window&&this._updateFromSandbox(e.detail)}),this._internalEvents.set("dispatcheventinsandbox",e=>{this._scripting?.dispatchEventInSandbox(e.detail)}),this._internalEvents.set("pagechanging",({pageNumber:e,previous:t})=>{e!==t&&(this._dispatchPageClose(t),this._dispatchPageOpen(e))}),this._internalEvents.set("pagerendered",({pageNumber:e})=>{this._pageOpenPending.has(e)&&e===this._pdfViewer.currentPageNumber&&this._dispatchPageOpen(e)}),this._internalEvents.set("pagesdestroy",async e=>{await this._dispatchPageClose(this._pdfViewer.currentPageNumber),await this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillClose"}),this._closeCapability?.resolve()}),this._domEvents.set("mousedown",e=>{this._mouseState.isDown=!0}),this._domEvents.set("mouseup",e=>{this._mouseState.isDown=!1});for(var[s,a]of this._internalEvents)this._eventBus._on(s,a);for(var[r,o]of this._domEvents)window.addEventListener(r,o);try{var l=await this._getDocProperties();if(e!==this._pdfDocument)return;await this._scripting.createSandbox({objects:t,calculationOrder:i,appInfo:{platform:navigator.platform,language:navigator.language},docInfo:{...l,actions:n}}),this._eventBus.dispatch("sandboxcreated",{source:this})}catch(e){return console.error(`PDFScriptingManager.setDocument: "${e?.message}".`),void await this._destroyScripting()}await this._scripting?.dispatchEventInSandbox({id:"doc",name:"Open"}),await this._dispatchPageOpen(this._pdfViewer.currentPageNumber,!0),Promise.resolve().then(()=>{e===this._pdfDocument&&(this._ready=!0)})}}else await this._destroyScripting()}}async dispatchWillSave(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillSave"})}async dispatchDidSave(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"DidSave"})}async dispatchWillPrint(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillPrint"})}async dispatchDidPrint(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"DidPrint"})}get mouseState(){return this._mouseState}get destroyPromise(){return this._destroyCapability?.promise||null}get ready(){return this._ready}get _internalEvents(){return(0,a.shadow)(this,"_internalEvents",new Map)}get _domEvents(){return(0,a.shadow)(this,"_domEvents",new Map)}get _pageOpenPending(){return(0,a.shadow)(this,"_pageOpenPending",new Set)}get _visitedPages(){return(0,a.shadow)(this,"_visitedPages",new Map)}async _updateFromSandbox(e){var t=this._pdfViewer.isInPresentationMode||this._pdfViewer.isChangingPresentationMode,{id:i,siblings:n,command:s,value:a}=e;if(i){if(!t||!e.focus){delete e.id,delete e.siblings;for(const r of n?[i,...n]:[i]){const o=document.getElementById(r);o?o.dispatchEvent(new CustomEvent("updatefromsandbox",{detail:e})):this._pdfDocument?.annotationStorage.setValue(r,e)}}}else switch(s){case"clear":console.clear();break;case"error":console.error(a);break;case"layout":this._pdfViewer.spreadMode=(0,l.apiPageLayoutToSpreadMode)(a);break;case"page-num":this._pdfViewer.currentPageNumber=a+1;break;case"print":await this._pdfViewer.pagesPromise,this._eventBus.dispatch("print",{source:this});break;case"println":console.log(a);break;case"zoom":if(t)return;this._pdfViewer.currentScaleValue=a;break;case"SaveAs":this._eventBus.dispatch("save",{source:this});break;case"FirstPage":this._pdfViewer.currentPageNumber=1;break;case"LastPage":this._pdfViewer.currentPageNumber=this._pdfViewer.pagesCount;break;case"NextPage":this._pdfViewer.nextPage();break;case"PrevPage":this._pdfViewer.previousPage();break;case"ZoomViewIn":if(t)return;this._pdfViewer.increaseScale();break;case"ZoomViewOut":if(t)return;this._pdfViewer.decreaseScale()}}async _dispatchPageOpen(t,e=!1){const i=this._pdfDocument,n=this._visitedPages;if(e&&(this._closeCapability=(0,a.createPromiseCapability)()),this._closeCapability){const s=this._pdfViewer.getPageView(t-1);s?.renderingState===r.RenderingStates.FINISHED?(this._pageOpenPending.delete(t),e=(async()=>{var e=await(n.has(t)?null:s.pdfPage?.getJSActions());i===this._pdfDocument&&await this._scripting?.dispatchEventInSandbox({id:"page",name:"PageOpen",pageNumber:t,actions:e})})(),n.set(t,e)):this._pageOpenPending.add(t)}}async _dispatchPageClose(e){const t=this._pdfDocument,i=this._visitedPages;var n;this._closeCapability&&(this._pageOpenPending.has(e)||(n=i.get(e))&&(i.set(e,null),await n,t===this._pdfDocument&&await this._scripting?.dispatchEventInSandbox({id:"page",name:"PageClose",pageNumber:e})))}async _getDocProperties(){if(this._docPropertiesLookup)return this._docPropertiesLookup(this._pdfDocument);throw new Error("_getDocProperties: Unable to lookup properties.")}_createScripting(){if(this._destroyCapability=(0,a.createPromiseCapability)(),this._scripting)throw new Error("_createScripting: Scripting already exists.");if(this._scriptingFactory)return this._scriptingFactory.createScripting({sandboxBundleSrc:this._sandboxBundleSrc});throw new Error("_createScripting: Cannot create scripting.")}async _destroyScripting(){if(!this._scripting)return this._pdfDocument=null,void this._destroyCapability?.resolve();this._closeCapability&&(await Promise.race([this._closeCapability.promise,new Promise(e=>{setTimeout(e,1e3)})]).catch(e=>{}),this._closeCapability=null),this._pdfDocument=null;try{await this._scripting.destroySandbox()}catch(e){}for(var[e,t]of this._internalEvents)this._eventBus._off(e,t);this._internalEvents.clear();for(var[i,n]of this._domEvents)window.removeEventListener(i,n);this._domEvents.clear(),this._pageOpenPending.clear(),this._visitedPages.clear(),this._scripting=null,delete this._mouseState.isDown,this._ready=!1,this._destroyCapability?.resolve()}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSidebar=void 0;var a=i(3),r=i(7);const n="pdfSidebarNotification";t.PDFSidebar=class{constructor({elements:e,pdfViewer:t,pdfThumbnailViewer:i,eventBus:n,l10n:s}){this.isOpen=!1,this.active=a.SidebarView.THUMBS,this.isInitialViewSet=!1,this.onToggled=null,this.pdfViewer=t,this.pdfThumbnailViewer=i,this.outerContainer=e.outerContainer,this.viewerContainer=e.viewerContainer,this.toggleButton=e.toggleButton,this.thumbnailButton=e.thumbnailButton,this.outlineButton=e.outlineButton,this.attachmentsButton=e.attachmentsButton,this.layersButton=e.layersButton,this.thumbnailView=e.thumbnailView,this.outlineView=e.outlineView,this.attachmentsView=e.attachmentsView,this.layersView=e.layersView,this._outlineOptionsContainer=e.outlineOptionsContainer,this._currentOutlineItemButton=e.currentOutlineItemButton,this.eventBus=n,this.l10n=s,this._addEventListeners()}reset(){this.isInitialViewSet=!1,this._hideUINotification(!0),this.switchView(a.SidebarView.THUMBS),this.outlineButton.disabled=!1,this.attachmentsButton.disabled=!1,this.layersButton.disabled=!1,this._currentOutlineItemButton.disabled=!0}get visibleView(){return this.isOpen?this.active:a.SidebarView.NONE}get isThumbnailViewVisible(){return this.isOpen&&this.active===a.SidebarView.THUMBS}get isOutlineViewVisible(){return this.isOpen&&this.active===a.SidebarView.OUTLINE}get isAttachmentsViewVisible(){return this.isOpen&&this.active===a.SidebarView.ATTACHMENTS}get isLayersViewVisible(){return this.isOpen&&this.active===a.SidebarView.LAYERS}setInitialView(e=a.SidebarView.NONE){this.isInitialViewSet||(this.isInitialViewSet=!0,e!==a.SidebarView.NONE&&e!==a.SidebarView.UNKNOWN&&this._switchView(e,!0)||this._dispatchEvent())}switchView(e,t=!1){this._switchView(e,t)}_switchView(e,t=!1){var i=e!==this.active;let n=!1;switch(e){case a.SidebarView.NONE:return this.isOpen?(this.close(),!0):!1;case a.SidebarView.THUMBS:this.isOpen&&i&&(n=!0);break;case a.SidebarView.OUTLINE:if(this.outlineButton.disabled)return!1;break;case a.SidebarView.ATTACHMENTS:if(this.attachmentsButton.disabled)return!1;break;case a.SidebarView.LAYERS:if(this.layersButton.disabled)return!1;break;default:return console.error(`PDFSidebar._switchView: "${e}" is not a valid view.`),!1}return this.active=e,this.thumbnailButton.classList.toggle("toggled",e===a.SidebarView.THUMBS),this.outlineButton.classList.toggle("toggled",e===a.SidebarView.OUTLINE),this.attachmentsButton.classList.toggle("toggled",e===a.SidebarView.ATTACHMENTS),this.layersButton.classList.toggle("toggled",e===a.SidebarView.LAYERS),this.thumbnailView.classList.toggle("hidden",e!==a.SidebarView.THUMBS),this.outlineView.classList.toggle("hidden",e!==a.SidebarView.OUTLINE),this.attachmentsView.classList.toggle("hidden",e!==a.SidebarView.ATTACHMENTS),this.layersView.classList.toggle("hidden",e!==a.SidebarView.LAYERS),this._outlineOptionsContainer.classList.toggle("hidden",e!==a.SidebarView.OUTLINE),t&&!this.isOpen?(this.open(),!0):(n&&(this._updateThumbnailViewer(),this._forceRendering()),i&&this._dispatchEvent(),i)}open(){this.isOpen||(this.isOpen=!0,this.toggleButton.classList.add("toggled"),this.toggleButton.setAttribute("aria-expanded","true"),this.outerContainer.classList.add("sidebarMoving","sidebarOpen"),this.active===a.SidebarView.THUMBS&&this._updateThumbnailViewer(),this._forceRendering(),this._dispatchEvent(),this._hideUINotification())}close(){this.isOpen&&(this.isOpen=!1,this.toggleButton.classList.remove("toggled"),this.toggleButton.setAttribute("aria-expanded","false"),this.outerContainer.classList.add("sidebarMoving"),this.outerContainer.classList.remove("sidebarOpen"),this._forceRendering(),this._dispatchEvent())}toggle(){this.isOpen?this.close():this.open()}_dispatchEvent(){this.eventBus.dispatch("sidebarviewchanged",{source:this,view:this.visibleView})}_forceRendering(){this.onToggled?this.onToggled():(this.pdfViewer.forceRendering(),this.pdfThumbnailViewer.forceRendering())}_updateThumbnailViewer(){const{pdfViewer:t,pdfThumbnailViewer:i}=this;var n=t.pagesCount;for(let e=0;e<n;e++){var s=t.getPageView(e);if(s?.renderingState===r.RenderingStates.FINISHED){const a=i.getThumbnail(e);a.setImage(s)}}i.scrollThumbnailIntoView(t.currentPageNumber)}_showUINotification(){this.l10n.get("toggle_sidebar_notification2.title").then(e=>{this.toggleButton.title=e}),this.isOpen||this.toggleButton.classList.add(n)}_hideUINotification(e=!1){(this.isOpen||e)&&this.toggleButton.classList.remove(n),e&&this.l10n.get("toggle_sidebar.title").then(e=>{this.toggleButton.title=e})}_addEventListeners(){this.viewerContainer.addEventListener("transitionend",e=>{e.target===this.viewerContainer&&this.outerContainer.classList.remove("sidebarMoving")}),this.toggleButton.addEventListener("click",()=>{this.toggle()}),this.thumbnailButton.addEventListener("click",()=>{this.switchView(a.SidebarView.THUMBS)}),this.outlineButton.addEventListener("click",()=>{this.switchView(a.SidebarView.OUTLINE)}),this.outlineButton.addEventListener("dblclick",()=>{this.eventBus.dispatch("toggleoutlinetree",{source:this})}),this.attachmentsButton.addEventListener("click",()=>{this.switchView(a.SidebarView.ATTACHMENTS)}),this.layersButton.addEventListener("click",()=>{this.switchView(a.SidebarView.LAYERS)}),this.layersButton.addEventListener("dblclick",()=>{this.eventBus.dispatch("resetlayers",{source:this})}),this._currentOutlineItemButton.addEventListener("click",()=>{this.eventBus.dispatch("currentoutlineitem",{source:this})});const t=(e,t,i)=>{t.disabled=!e,e?this._showUINotification():this.active===i&&this.switchView(a.SidebarView.THUMBS)};this.eventBus._on("outlineloaded",e=>{t(e.outlineCount,this.outlineButton,a.SidebarView.OUTLINE),e.currentOutlineItemPromise.then(e=>{this.isInitialViewSet&&(this._currentOutlineItemButton.disabled=!e)})}),this.eventBus._on("attachmentsloaded",e=>{t(e.attachmentsCount,this.attachmentsButton,a.SidebarView.ATTACHMENTS)}),this.eventBus._on("layersloaded",e=>{t(e.layersCount,this.layersButton,a.SidebarView.LAYERS)}),this.eventBus._on("presentationmodechanged",e=>{e.state===a.PresentationModeState.NORMAL&&this.isThumbnailViewVisible&&this._updateThumbnailViewer()})}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSidebarResizer=void 0;const i="sidebarResizing";t.PDFSidebarResizer=class{constructor(e,t,i){this.isRTL=!1,this.sidebarOpen=!1,this.doc=document.documentElement,this._width=null,this._outerContainerWidth=null,this._boundEvents=Object.create(null),this.outerContainer=e.outerContainer,this.resizer=e.resizer,this.eventBus=t,i.getDirection().then(e=>{this.isRTL="rtl"===e}),this._addEventListeners()}get outerContainerWidth(){return this._outerContainerWidth||=this.outerContainer.clientWidth}_updateWidth(e=0){var t=Math.floor(this.outerContainerWidth/2);return(e=(e=t<e?t:e)<200?200:e)!==this._width&&(this._width=e,this.doc.style.setProperty("--sidebar-width",`${e}px`),!0)}_mouseMove(e){let t=e.clientX;this.isRTL&&(t=this.outerContainerWidth-t),this._updateWidth(t)}_mouseUp(e){this.outerContainer.classList.remove(i),this.eventBus.dispatch("resize",{source:this});var t=this._boundEvents;window.removeEventListener("mousemove",t.mouseMove),window.removeEventListener("mouseup",t.mouseUp)}_addEventListeners(){const t=this._boundEvents;t.mouseMove=this._mouseMove.bind(this),t.mouseUp=this._mouseUp.bind(this),this.resizer.addEventListener("mousedown",e=>{0===e.button&&(this.outerContainer.classList.add(i),window.addEventListener("mousemove",t.mouseMove),window.addEventListener("mouseup",t.mouseUp))}),this.eventBus._on("sidebarviewchanged",e=>{this.sidebarOpen=!!e?.view}),this.eventBus._on("resize",e=>{if(e?.source===window&&(this._outerContainerWidth=null,this._width))if(this.sidebarOpen){this.outerContainer.classList.add(i);const t=this._updateWidth(this._width);Promise.resolve().then(()=>{this.outerContainer.classList.remove(i),t&&this.eventBus.dispatch("resize",{source:this})})}else this._updateWidth(this._width)})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFThumbnailViewer=void 0;var r=i(3),h=i(25),n=i(7);const d="selected";t.PDFThumbnailViewer=class{constructor({container:e,eventBus:t,linkService:i,renderingQueue:n,l10n:s}){this.container=e,this.linkService=i,this.renderingQueue=n,this.l10n=s,this.scroll=(0,r.watchScroll)(this.container,this._scrollUpdated.bind(this)),this._resetView(),t._on("optionalcontentconfigchanged",()=>{this._setImageDisabled=!0})}_scrollUpdated(){this.renderingQueue.renderHighestPriority()}getThumbnail(e){return this._thumbnails[e]}_getVisibleThumbs(){return(0,r.getVisibleElements)({scrollEl:this.container,views:this._thumbnails})}scrollThumbnailIntoView(i){if(this.pdfDocument){const s=this._thumbnails[i-1];if(s){if(i!==this._currentPageNumber){const t=this._thumbnails[this._currentPageNumber-1];t.div.classList.remove(d),s.div.classList.add(d)}const a=this._getVisibleThumbs();var e=a.views.length;if(0<e){var n=a.first.id,e=1<e?a.last.id:n;let t=!1;i<=n||e<=i?t=!0:a.views.some(function(e){return e.id===i&&(t=e.percent<100,!0)}),t&&(0,r.scrollIntoView)(s.div,{top:-19})}this._currentPageNumber=i}else console.error('scrollThumbnailIntoView: Invalid "pageNumber" parameter.')}}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!(0,r.isValidRotation)(e))throw new Error("Invalid thumbnails rotation angle.");if(this.pdfDocument&&this._pagesRotation!==e){var t={rotation:this._pagesRotation=e};for(const i of this._thumbnails)i.update(t)}}cleanup(){for(let e=0,t=this._thumbnails.length;e<t;e++)this._thumbnails[e]&&this._thumbnails[e].renderingState!==n.RenderingStates.FINISHED&&this._thumbnails[e].reset();h.TempImageFactory.destroyCanvas()}_resetView(){this._thumbnails=[],this._currentPageNumber=1,this._pageLabels=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._pagesRequests=new WeakMap,this._setImageDisabled=!1,this.container.textContent=""}setDocument(o){if(this.pdfDocument&&(this._cancelRendering(),this._resetView()),this.pdfDocument=o){const e=o.getPage(1),l=o.getOptionalContentConfig();e.then(e=>{this._optionalContentConfigPromise=l;var t=o.numPages;const i=e.getViewport({scale:1});var n=()=>this._setImageDisabled;for(let e=1;e<=t;++e){var s=new h.PDFThumbnailView({container:this.container,id:e,defaultViewport:i.clone(),optionalContentConfigPromise:l,linkService:this.linkService,renderingQueue:this.renderingQueue,checkSetImageDisabled:n,l10n:this.l10n});this._thumbnails.push(s)}const a=this._thumbnails[0];a&&a.setPdfPage(e);const r=this._thumbnails[this._currentPageNumber-1];r.div.classList.add(d)}).catch(e=>{console.error("Unable to initialize thumbnail viewer",e)})}}_cancelRendering(){for(let e=0,t=this._thumbnails.length;e<t;e++)this._thumbnails[e]&&this._thumbnails[e].cancelRendering()}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("PDFThumbnailViewer_setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._thumbnails.length;e<t;e++)this._thumbnails[e].setPageLabel(this._pageLabels?.[e]??null)}}_ensurePdfPageLoaded(t){if(t.pdfPage)return Promise.resolve(t.pdfPage);if(this._pagesRequests.has(t))return this._pagesRequests.get(t);var e=this.pdfDocument.getPage(t.id).then(e=>(t.pdfPage||t.setPdfPage(e),this._pagesRequests.delete(t),e)).catch(e=>{console.error("Unable to get page for thumb view",e),this._pagesRequests.delete(t)});return this._pagesRequests.set(t,e),e}forceRendering(){var e=this._getVisibleThumbs();const t=this.renderingQueue.getHighestPriority(e,this._thumbnails,this.scroll.down);return!!t&&(this._ensurePdfPageLoaded(t).then(()=>{this.renderingQueue.renderView(t)}),!0)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TempImageFactory=t.PDFThumbnailView=void 0;var s=i(3),l=i(4),c=i(7);const o=function(){let s=null;return{getCanvas(e,t){let i=s;i||(i=document.createElement("canvas"),s=i),i.width=e,i.height=t,i.mozOpaque=!0;const n=i.getContext("2d",{alpha:!1});return n.save(),n.fillStyle="rgb(255, 255, 255)",n.fillRect(0,0,e,t),n.restore(),[i,i.getContext("2d")]},destroyCanvas(){const e=s;e&&(e.width=0,e.height=0),s=null}}}();t.TempImageFactory=o;t.PDFThumbnailView=class{constructor({container:e,id:t,defaultViewport:i,optionalContentConfigPromise:n,linkService:s,renderingQueue:a,checkSetImageDisabled:r,l10n:o}){this.id=t,this.renderingId="thumbnail"+t,this.pageLabel=null,this.pdfPage=null,this.rotation=0,this.viewport=i,this.pdfPageRotate=i.rotation,this._optionalContentConfigPromise=n||null,this.linkService=s,this.renderingQueue=a,this.renderTask=null,this.renderingState=c.RenderingStates.INITIAL,this.resume=null,this._checkSetImageDisabled=r||function(){return!1};a=this.viewport.width,r=a/this.viewport.height;this.canvasWidth=98,this.canvasHeight=this.canvasWidth/r|0,this.scale=this.canvasWidth/a,this.l10n=o;const l=document.createElement("a");l.href=s.getAnchorUrl("#page="+t),this._thumbPageTitle.then(e=>{l.title=e}),l.onclick=function(){return s.goToPage(t),!1},this.anchor=l;const h=document.createElement("div");h.className="thumbnail",h.setAttribute("data-page-number",this.id),this.div=h;const d=document.createElement("div");d.className="thumbnailSelectionRing";d.style.width=this.canvasWidth+2+"px",d.style.height=this.canvasHeight+2+"px",this.ring=d,h.appendChild(d),l.appendChild(h),e.appendChild(l)}setPdfPage(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:1,rotation:t}),this.reset()}reset(){this.cancelRendering(),this.renderingState=c.RenderingStates.INITIAL;var e=this.viewport.width,t=this.viewport.height;this.canvasHeight=this.canvasWidth/(e/t)|0,this.scale=this.canvasWidth/e,this.div.removeAttribute("data-loaded");const i=this.ring;i.textContent="";i.style.width=this.canvasWidth+2+"px",i.style.height=this.canvasHeight+2+"px",this.canvas&&(this.canvas.width=0,this.canvas.height=0,delete this.canvas),this.image&&(this.image.removeAttribute("src"),delete this.image)}update({rotation:e=null}){"number"==typeof e&&(this.rotation=e);e=(this.rotation+this.pdfPageRotate)%360;this.viewport=this.viewport.clone({scale:1,rotation:e}),this.reset()}cancelRendering(){this.renderTask&&(this.renderTask.cancel(),this.renderTask=null),this.resume=null}_getPageDrawContext(e=1){const t=document.createElement("canvas");t.mozOpaque=!0;var i=t.getContext("2d",{alpha:!1}),n=(0,s.getOutputScale)(i);t.width=e*this.canvasWidth*n.sx|0,t.height=e*this.canvasHeight*n.sy|0;n=n.scaled?[n.sx,0,0,n.sy,0,0]:null;return{ctx:i,canvas:t,transform:n}}_convertCanvasToImage(e){if(this.renderingState!==c.RenderingStates.FINISHED)throw new Error("_convertCanvasToImage: Rendering has not finished.");const t=this._reduceImage(e),i=document.createElement("img");i.className="thumbnailImage",this._thumbPageCanvas.then(e=>{i.setAttribute("aria-label",e)}),i.style.width=this.canvasWidth+"px",i.style.height=this.canvasHeight+"px",i.src=t.toDataURL(),this.image=i,this.div.setAttribute("data-loaded",!0),this.ring.appendChild(i),t.width=0,t.height=0}draw(){if(this.renderingState!==c.RenderingStates.INITIAL)return console.error("Must be in new state before drawing"),Promise.resolve(void 0);const e=this["pdfPage"];if(!e)return this.renderingState=c.RenderingStates.FINISHED,Promise.reject(new Error("pdfPage is not loaded"));this.renderingState=c.RenderingStates.RUNNING;const t=async(e=null)=>{if(r===this.renderTask&&(this.renderTask=null),!(e instanceof l.RenderingCancelledException)&&(this.renderingState=c.RenderingStates.FINISHED,this._convertCanvasToImage(n),e))throw e},{ctx:i,canvas:n,transform:s}=this._getPageDrawContext(2);var a={canvasContext:i,transform:s,viewport:this.viewport.clone({scale:2*this.scale}),optionalContentConfigPromise:this._optionalContentConfigPromise};const r=this.renderTask=e.render(a);r.onContinue=e=>{if(!this.renderingQueue.isHighestPriority(this))return this.renderingState=c.RenderingStates.PAUSED,void(this.resume=()=>{this.renderingState=c.RenderingStates.RUNNING,e()});e()};const o=r.promise.then(function(){return t(null)},function(e){return t(e)});return o.finally(()=>{n.width=0,n.height=0,this.linkService.isPageCached(this.id)||this.pdfPage?.cleanup()}),o}setImage(e){var t;this._checkSetImageDisabled()||this.renderingState===c.RenderingStates.INITIAL&&({canvas:t,pdfPage:e}=e,t&&(this.pdfPage||this.setPdfPage(e),this.renderingState=c.RenderingStates.FINISHED,this._convertCanvasToImage(t)))}_reduceImage(e){const{ctx:t,canvas:i}=this._getPageDrawContext();if(e.width<=2*i.width)return t.drawImage(e,0,0,e.width,e.height,0,0,i.width,i.height),i;let n=i.width<<3,s=i.height<<3;const[a,r]=o.getCanvas(n,s);for(;n>e.width||s>e.height;)n>>=1,s>>=1;for(r.drawImage(e,0,0,e.width,e.height,0,0,n,s);n>2*i.width;)r.drawImage(a,0,0,n,s,0,0,n>>1,s>>1),n>>=1,s>>=1;return t.drawImage(a,0,0,n,s,0,0,i.width,i.height),i}get _thumbPageTitle(){return this.l10n.get("thumb_page_title",{page:this.pageLabel??this.id})}get _thumbPageCanvas(){return this.l10n.get("thumb_page_canvas",{page:this.pageLabel??this.id})}setPageLabel(e){this.pageLabel="string"==typeof e?e:null,this._thumbPageTitle.then(e=>{this.anchor.title=e}),this.renderingState===c.RenderingStates.FINISHED&&this._thumbPageCanvas.then(e=>{this.image?.setAttribute("aria-label",e)})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewer=void 0;var s=i(3),n=i(27),a=i(4);class r extends n.BaseViewer{get _viewerElement(){return(0,a.shadow)(this,"_viewerElement",this.viewer)}_scrollIntoView({pageDiv:e,pageSpot:t=null,pageNumber:i=null}){var n,s,a,r;t||this.isInPresentationMode||(s=(n=e.offsetLeft+e.clientLeft)+e.clientWidth,{scrollLeft:a,clientWidth:r}=this.container,(this._isScrollModeHorizontal||n<a||a+r<s)&&(t={left:0,top:0})),super._scrollIntoView({pageDiv:e,pageSpot:t,pageNumber:i})}_getVisiblePages(){return this.isInPresentationMode?this._getCurrentVisiblePage():super._getVisiblePages()}_updateHelper(i){if(!this.isInPresentationMode){let e=this._currentPageNumber,t=!1;for(const n of i){if(n.percent<100)break;if(n.id===e&&this._scrollMode===s.ScrollMode.VERTICAL&&this._spreadMode===s.SpreadMode.NONE){t=!0;break}}t||(e=i[0].id),this._setCurrentPageNumber(e)}}}t.PDFViewer=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseViewer=void 0;var v=i(4),b=i(3),n=i(7),d=i(28),c=i(29),u=i(30),s=i(18),a=i(31),r=i(32),o=i(33),l=i(34);function h(s){const a=[];this.push=function(e){var t=a.indexOf(e);0<=t&&a.splice(t,1),a.push(e),a.length>s&&a.shift().destroy()},this.resize=function(e,i){if(s=e,i){const n=new Set;for(let e=0,t=i.length;e<t;++e)n.add(i[e].id);(0,b.moveToEndOfArray)(a,function(e){return n.has(e.id)})}for(;a.length>s;)a.shift().destroy()},this.has=function(e){return a.includes(e)}}t.BaseViewer=class p{constructor(e){if(this.constructor===p)throw new Error("Cannot initialize BaseViewer.");var t="2.11.338";if(v.version!==t)throw new Error(`The API version "${v.version}" does not match the Viewer version "${t}".`);if(this.container=e.container,this.viewer=e.viewer||e.container.firstElementChild,"DIV"!==this.container?.tagName.toUpperCase()||"DIV"!==this.viewer?.tagName.toUpperCase())throw new Error("Invalid `container` and/or `viewer` option.");if(this.container.offsetParent&&"absolute"!==getComputedStyle(this.container).position)throw new Error("The `container` must be absolutely positioned.");this.eventBus=e.eventBus,this.linkService=e.linkService||new s.SimpleLinkService,this.downloadManager=e.downloadManager||null,this.findController=e.findController||null,this._scriptingManager=e.scriptingManager||null,this.removePageBorders=e.removePageBorders||!1,this.textLayerMode=e.textLayerMode??b.TextLayerMode.ENABLE,this._annotationMode=e.annotationMode??v.AnnotationMode.ENABLE_FORMS,this.imageResourcesPath=e.imageResourcesPath||"",this.enablePrintAutoRotate=e.enablePrintAutoRotate||!1,this.renderer=e.renderer||b.RendererType.CANVAS,this.useOnlyCssZoom=e.useOnlyCssZoom||!1,this.maxCanvasPixels=e.maxCanvasPixels,this.l10n=e.l10n||c.NullL10n,this.defaultRenderingQueue=!e.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new n.PDFRenderingQueue,this.renderingQueue.setViewer(this)):this.renderingQueue=e.renderingQueue,this._doc=document.documentElement,this.scroll=(0,b.watchScroll)(this.container,this._scrollUpdate.bind(this)),this.presentationModeState=b.PresentationModeState.UNKNOWN,this._onBeforeDraw=this._onAfterDraw=null,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders"),Promise.resolve().then(()=>{this.eventBus.dispatch("baseviewerinit",{source:this})})}get pagesCount(){return this._pages.length}getPageView(e){return this._pages[e]}get pageViewsReady(){return!!this._pagesCapability.settled&&this._pages.every(function(e){return e?.pdfPage})}get renderForms(){return this._annotationMode===v.AnnotationMode.ENABLE_FORMS}get enableScripting(){return!!this._scriptingManager}get currentPageNumber(){return this._currentPageNumber}set currentPageNumber(e){if(!Number.isInteger(e))throw new Error("Invalid page number.");this.pdfDocument&&(this._setCurrentPageNumber(e,!0)||console.error(`currentPageNumber: "${e}" is not a valid page.`))}_setCurrentPageNumber(e,t=!1){if(this._currentPageNumber===e)return t&&this._resetCurrentPageView(),!0;if(!(0<e&&e<=this.pagesCount))return!1;var i=this._currentPageNumber;return this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",{source:this,pageNumber:e,pageLabel:this._pageLabels?.[e-1]??null,previous:i}),t&&this._resetCurrentPageView(),!0}get currentPageLabel(){return this._pageLabels?.[this._currentPageNumber-1]??null}set currentPageLabel(t){if(this.pdfDocument){let e=0|t;var i;!this._pageLabels||0<=(i=this._pageLabels.indexOf(t))&&(e=i+1),this._setCurrentPageNumber(e,!0)||console.error(`currentPageLabel: "${t}" is not a valid page.`)}}get currentScale(){return this._currentScale!==b.UNKNOWN_SCALE?this._currentScale:b.DEFAULT_SCALE}set currentScale(e){if(isNaN(e))throw new Error("Invalid numeric scale.");this.pdfDocument&&this._setScale(e,!1)}get currentScaleValue(){return this._currentScaleValue}set currentScaleValue(e){this.pdfDocument&&this._setScale(e,!1)}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!(0,b.isValidRotation)(e))throw new Error("Invalid pages rotation angle.");if(this.pdfDocument&&((e%=360)<0&&(e+=360),this._pagesRotation!==e)){this._pagesRotation=e;var t=this._currentPageNumber,i={rotation:e};for(const n of this._pages)n.update(i);this._currentScaleValue&&this._setScale(this._currentScaleValue,!0),this.eventBus.dispatch("rotationchanging",{source:this,pagesRotation:e,pageNumber:t}),this.defaultRenderingQueue&&this.update()}}get firstPagePromise(){return this.pdfDocument?this._firstPageCapability.promise:null}get onePageRendered(){return this.pdfDocument?this._onePageRenderedCapability.promise:null}get pagesPromise(){return this.pdfDocument?this._pagesCapability.promise:null}get _viewerElement(){throw new Error("Not implemented: _viewerElement")}_onePageRenderedOrForceFetch(){return this.container.offsetParent&&0!==this._getVisiblePages().views.length?this._onePageRenderedCapability.promise:Promise.resolve()}setDocument(l){if(this.pdfDocument&&(this.eventBus.dispatch("pagesdestroy",{source:this}),this._cancelRendering(),this._resetView(),this.findController&&this.findController.setDocument(null),this._scriptingManager&&this._scriptingManager.setDocument(null)),this.pdfDocument=l){const h=l.isPureXfa,d=l.numPages,e=l.getPage(1),c=l.getOptionalContentConfig();this._pagesCapability.promise.then(()=>{this.eventBus.dispatch("pagesloaded",{source:this,pagesCount:d})}),this._onBeforeDraw=e=>{(e=this._pages[e.pageNumber-1])&&this._buffer.push(e)},this.eventBus._on("pagerender",this._onBeforeDraw),this._onAfterDraw=e=>{e.cssTransform||this._onePageRenderedCapability.settled||(this._onePageRenderedCapability.resolve(),this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null)},this.eventBus._on("pagerendered",this._onAfterDraw),e.then(e=>{this._firstPageCapability.resolve(e),this._optionalContentConfigPromise=c;var t=this.currentScale;const i=e.getViewport({scale:t*v.PixelsPerInch.PDF_TO_CSS_UNITS});var n=this.textLayerMode===b.TextLayerMode.DISABLE||h?null:this,s=this._annotationMode!==v.AnnotationMode.DISABLE?this:null,a=h?this:null;for(let e=1;e<=d;++e){var r=new u.PDFPageView({container:this._viewerElement,eventBus:this.eventBus,id:e,scale:t,defaultViewport:i.clone(),optionalContentConfigPromise:c,renderingQueue:this.renderingQueue,textLayerFactory:n,textLayerMode:this.textLayerMode,annotationLayerFactory:s,annotationMode:this._annotationMode,xfaLayerFactory:a,textHighlighterFactory:this,structTreeLayerFactory:this,imageResourcesPath:this.imageResourcesPath,renderer:this.renderer,useOnlyCssZoom:this.useOnlyCssZoom,maxCanvasPixels:this.maxCanvasPixels,l10n:this.l10n});this._pages.push(r)}const o=this._pages[0];o&&(o.setPdfPage(e),this.linkService.cachePageRef(1,e.ref)),this._spreadMode!==b.SpreadMode.NONE&&this._updateSpreadMode(),this._onePageRenderedOrForceFetch().then(()=>{if(this.findController&&this.findController.setDocument(l),this._scriptingManager&&this._scriptingManager.setDocument(l),l.loadingParams.disableAutoFetch||7500<d)this._pagesCapability.resolve();else{let n=d-1;if(n<=0)this._pagesCapability.resolve();else for(let i=2;i<=d;++i)l.getPage(i).then(e=>{const t=this._pages[i-1];t.pdfPage||t.setPdfPage(e),this.linkService.cachePageRef(i,e.ref),0==--n&&this._pagesCapability.resolve()},e=>{console.error(`Unable to get page ${i} to initialize viewer`,e),0==--n&&this._pagesCapability.resolve()})}}),this.eventBus.dispatch("pagesinit",{source:this}),this.defaultRenderingQueue&&this.update()}).catch(e=>{console.error("Unable to initialize viewer",e)})}}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._pages.length;e<t;e++)this._pages[e].setPageLabel(this._pageLabels?.[e]??null)}}_resetView(){this._pages=[],this._currentPageNumber=1,this._currentScale=b.UNKNOWN_SCALE,this._currentScaleValue=null,this._pageLabels=null,this._buffer=new h(10),this._location=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._pagesRequests=new WeakMap,this._firstPageCapability=(0,v.createPromiseCapability)(),this._onePageRenderedCapability=(0,v.createPromiseCapability)(),this._pagesCapability=(0,v.createPromiseCapability)(),this._scrollMode=b.ScrollMode.VERTICAL,this._spreadMode=b.SpreadMode.NONE,this._onBeforeDraw&&(this.eventBus._off("pagerender",this._onBeforeDraw),this._onBeforeDraw=null),this._onAfterDraw&&(this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null),this.viewer.textContent="",this._updateScrollMode()}_scrollUpdate(){0!==this.pagesCount&&this.update()}_scrollIntoView({pageDiv:e,pageSpot:t=null}){(0,b.scrollIntoView)(e,t)}_setScaleUpdatePages(e,t,i=!1,n=!1){if(this._currentScaleValue=t.toString(),s=this._currentScale,(a=e)===s||Math.abs(a-s)<1e-15)n&&this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:t});else{var s,a;this._doc.style.setProperty("--zoom-factor",e);var r={scale:e};for(const o of this._pages)o.update(r);if(this._currentScale=e,!i){let e=this._currentPageNumber,t;!this._location||this.isInPresentationMode||this.isChangingPresentationMode||(e=this._location.pageNumber,t=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:e,destArray:t,allowNegativeOffset:!0})}this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:n?t:void 0}),this.defaultRenderingQueue&&this.update()}}get _pageWidthScaleFactor(){return this._spreadMode===b.SpreadMode.NONE||this._scrollMode===b.ScrollMode.HORIZONTAL||this.isInPresentationMode?1:2}_setScale(i,n=!1){let s=parseFloat(i);if(0<s)this._setScaleUpdatePages(s,i,n,!1);else{var a=this._pages[this._currentPageNumber-1];if(a){var r=this.isInPresentationMode||this.removePageBorders;let e=r?0:b.SCROLLBAR_PADDING,t=r?0:b.VERTICAL_PADDING;!r&&this._isScrollModeHorizontal&&([e,t]=[t,e]);var o=(this.container.clientWidth-e)/a.width*a.scale/this._pageWidthScaleFactor,l=(this.container.clientHeight-t)/a.height*a.scale;switch(i){case"page-actual":s=1;break;case"page-width":s=o;break;case"page-height":s=l;break;case"page-fit":s=Math.min(o,l);break;case"auto":var h=(0,b.isPortraitOrientation)(a)?o:Math.min(l,o);s=Math.min(b.MAX_AUTO_SCALE,h);break;default:return void console.error(`_setScale: "${i}" is an unknown zoom value.`)}this._setScaleUpdatePages(s,i,n,!0)}}}_resetCurrentPageView(){this.isInPresentationMode&&this._setScale(this._currentScaleValue,!0);var e=this._pages[this._currentPageNumber-1];this._scrollIntoView({pageDiv:e.div})}pageLabelToPageNumber(e){return!this._pageLabels||(e=this._pageLabels.indexOf(e))<0?null:e+1}scrollPageIntoView({pageNumber:o,destArray:l=null,allowNegativeOffset:h=!1,ignoreDestinationZoom:d=!1}){if(this.pdfDocument){const f=Number.isInteger(o)&&this._pages[o-1];if(f)if(!this.isInPresentationMode&&l){let i=0,n=0,s=0,a=0,e,t;var c=f.rotation%180!=0,u=(c?f.height:f.width)/f.scale/v.PixelsPerInch.PDF_TO_CSS_UNITS,p=(c?f.width:f.height)/f.scale/v.PixelsPerInch.PDF_TO_CSS_UNITS;let r=0;switch(l[1].name){case"XYZ":i=l[2],n=l[3],r=l[4],i=null!==i?i:0,n=null!==n?n:p;break;case"Fit":case"FitB":r="page-fit";break;case"FitH":case"FitBH":n=l[2],r="page-width",null===n&&this._location?(i=this._location.left,n=this._location.top):"number"!=typeof n&&(n=p);break;case"FitV":case"FitBV":i=l[2],s=u,a=p,r="page-height";break;case"FitR":i=l[2],n=l[3],s=l[4]-i,a=l[5]-n;var g=this.removePageBorders?0:b.SCROLLBAR_PADDING,m=this.removePageBorders?0:b.VERTICAL_PADDING;e=(this.container.clientWidth-g)/s/v.PixelsPerInch.PDF_TO_CSS_UNITS,t=(this.container.clientHeight-m)/a/v.PixelsPerInch.PDF_TO_CSS_UNITS,r=Math.min(Math.abs(e),Math.abs(t));break;default:return void console.error(`scrollPageIntoView: "${l[1].name}" is not a valid destination type.`)}if(d||(r&&r!==this._currentScale?this.currentScaleValue=r:this._currentScale===b.UNKNOWN_SCALE&&(this.currentScaleValue=b.DEFAULT_SCALE_VALUE)),"page-fit"!==r||l[4]){d=[f.viewport.convertToViewportPoint(i,n),f.viewport.convertToViewportPoint(i+s,n+a)];let e=Math.min(d[0][0],d[1][0]),t=Math.min(d[0][1],d[1][1]);h||(e=Math.max(e,0),t=Math.max(t,0)),this._scrollIntoView({pageDiv:f.div,pageSpot:{left:e,top:t},pageNumber:o})}else this._scrollIntoView({pageDiv:f.div,pageNumber:o})}else this._setCurrentPageNumber(o,!0);else console.error(`scrollPageIntoView: "${o}" is not a valid pageNumber parameter.`)}}_updateLocation(e){var t=this._currentScale,i=this._currentScaleValue,n=parseFloat(i)===t?Math.round(1e4*t)/100:i,s=e.id,t="#page="+s;t+="&zoom="+n;const a=this._pages[s-1];i=this.container,i=a.getPagePoint(i.scrollLeft-e.x,i.scrollTop-e.y),e=Math.round(i[0]),i=Math.round(i[1]),this._location={pageNumber:s,scale:n,top:i,left:e,rotation:this._pagesRotation,pdfOpenParams:t+=","+e+","+i}}_updateHelper(e){throw new Error("Not implemented: _updateHelper")}update(){var e=this._getVisiblePages(),t=e.views,i=t.length;0!==i&&(i=Math.max(10,2*i+1),this._buffer.resize(i,t),this.renderingQueue.renderHighestPriority(e),this._updateHelper(t),this._updateLocation(e.first),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location}))}containsElement(e){return this.container.contains(e)}focus(){this.container.focus()}get _isScrollModeHorizontal(){return!this.isInPresentationMode&&this._scrollMode===b.ScrollMode.HORIZONTAL}get _isContainerRtl(){return"rtl"===getComputedStyle(this.container).direction}get isInPresentationMode(){return this.presentationModeState===b.PresentationModeState.FULLSCREEN}get isChangingPresentationMode(){return this.presentationModeState===b.PresentationModeState.CHANGING}get isHorizontalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}get isVerticalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollHeight>this.container.clientHeight}_getCurrentVisiblePage(){if(!this.pagesCount)return{views:[]};var e=this._pages[this._currentPageNumber-1],t=e.div;return{first:e={id:e.id,x:t.offsetLeft+t.clientLeft,y:t.offsetTop+t.clientTop,view:e},last:e,views:[e]}}_getVisiblePages(){return(0,b.getVisibleElements)({scrollEl:this.container,views:this._pages,sortByVisibility:!0,horizontal:this._isScrollModeHorizontal,rtl:this._isScrollModeHorizontal&&this._isContainerRtl})}isPageVisible(t){return!!this.pdfDocument&&(Number.isInteger(t)&&0<t&&t<=this.pagesCount?this._getVisiblePages().views.some(function(e){return e.id===t}):(console.error(`isPageVisible: "${t}" is not a valid page.`),!1))}isPageCached(e){return!(!this.pdfDocument||!this._buffer)&&(Number.isInteger(e)&&0<e&&e<=this.pagesCount?!!(e=this._pages[e-1])&&this._buffer.has(e):(console.error(`isPageCached: "${e}" is not a valid page.`),!1))}cleanup(){for(let e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].renderingState!==n.RenderingStates.FINISHED&&this._pages[e].reset()}_cancelRendering(){for(let e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].cancelRendering()}_ensurePdfPageLoaded(t){if(t.pdfPage)return Promise.resolve(t.pdfPage);if(this._pagesRequests.has(t))return this._pagesRequests.get(t);var e=this.pdfDocument.getPage(t.id).then(e=>(t.pdfPage||t.setPdfPage(e),this._pagesRequests.delete(t),e)).catch(e=>{console.error("Unable to get page for page view",e),this._pagesRequests.delete(t)});return this._pagesRequests.set(t,e),e}forceRendering(e){var t=e||this._getVisiblePages(),i=this._isScrollModeHorizontal?this.scroll.right:this.scroll.down,e=this._scrollMode===b.ScrollMode.VERTICAL&&this._spreadMode!==b.SpreadMode.NONE&&!this.isInPresentationMode;const n=this.renderingQueue.getHighestPriority(t,this._pages,i,e);return!!n&&(this._ensurePdfPageLoaded(n).then(()=>{this.renderingQueue.renderView(n)}),!0)}createTextLayerBuilder(e,t,i,n=!1,s,a){return new o.TextLayerBuilder({textLayerDiv:e,eventBus:s,pageIndex:t,viewport:i,enhanceTextSelection:!this.isInPresentationMode&&n,highlighter:a})}createTextHighlighter(e,t){return new r.TextHighlighter({eventBus:t,pageIndex:e,findController:this.isInPresentationMode?null:this.findController})}createAnnotationLayerBuilder(e,t,i=null,n="",s=!0,a=c.NullL10n,r=null,o=null,l=null,h=null){return new d.AnnotationLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i||this.pdfDocument?.annotationStorage,imageResourcesPath:n,renderForms:s,linkService:this.linkService,downloadManager:this.downloadManager,l10n:a,enableScripting:r??this.enableScripting,hasJSActionsPromise:o||this.pdfDocument?.hasJSActions(),fieldObjectsPromise:h||this.pdfDocument?.getFieldObjects(),mouseState:l||this._scriptingManager?.mouseState})}createXfaLayerBuilder(e,t,i=null){return new l.XfaLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i||this.pdfDocument?.annotationStorage,linkService:this.linkService})}createStructTreeLayerBuilder(e){return new a.StructTreeLayerBuilder({pdfPage:e})}get hasEqualPageSizes(){var i=this._pages[0];for(let e=1,t=this._pages.length;e<t;++e){var n=this._pages[e];if(n.width!==i.width||n.height!==i.height)return!1}return!0}getPagesOverview(){return this._pages.map(e=>(e=e.pdfPage.getViewport({scale:1}),!this.enablePrintAutoRotate||(0,b.isPortraitOrientation)(e)?{width:e.width,height:e.height,rotation:e.rotation}:{width:e.height,height:e.width,rotation:(e.rotation-90)%360}))}get optionalContentConfigPromise(){return this.pdfDocument?this._optionalContentConfigPromise||this.pdfDocument.getOptionalContentConfig():Promise.resolve(null)}set optionalContentConfigPromise(e){if(!(e instanceof Promise))throw new Error(`Invalid optionalContentConfigPromise: ${e}`);if(this.pdfDocument&&this._optionalContentConfigPromise){var t={optionalContentConfigPromise:this._optionalContentConfigPromise=e};for(const i of this._pages)i.update(t);this.update(),this.eventBus.dispatch("optionalcontentconfigchanged",{source:this,promise:e})}}get scrollMode(){return this._scrollMode}set scrollMode(e){if(this._scrollMode!==e){if(!(0,b.isValidScrollMode)(e))throw new Error(`Invalid scroll mode: ${e}`);this._scrollMode=e,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e}),this._updateScrollMode(this._currentPageNumber)}}_updateScrollMode(e=null){const t=this._scrollMode,i=this.viewer;i.classList.toggle("scrollHorizontal",t===b.ScrollMode.HORIZONTAL),i.classList.toggle("scrollWrapped",t===b.ScrollMode.WRAPPED),this.pdfDocument&&e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this._setScale(this._currentScaleValue,!0),this._setCurrentPageNumber(e,!0),this.update())}get spreadMode(){return this._spreadMode}set spreadMode(e){if(this._spreadMode!==e){if(!(0,b.isValidSpreadMode)(e))throw new Error(`Invalid spread mode: ${e}`);this._spreadMode=e,this.eventBus.dispatch("spreadmodechanged",{source:this,mode:e}),this._updateSpreadMode(this._currentPageNumber)}}_updateSpreadMode(e=null){if(this.pdfDocument){const s=this.viewer,a=this._pages;if(s.textContent="",this._spreadMode===b.SpreadMode.NONE)for(let e=0,t=a.length;e<t;++e)s.appendChild(a[e].div);else{var n=this._spreadMode-1;let i=null;for(let e=0,t=a.length;e<t;++e)null===i?(i=document.createElement("div"),i.className="spread",s.appendChild(i)):e%2==n&&(i=i.cloneNode(!1),s.appendChild(i)),i.appendChild(a[e].div)}e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this._setScale(this._currentScaleValue,!0),this._setCurrentPageNumber(e,!0),this.update())}}_getPageAdvance(i,e=!1){if(this.isInPresentationMode)return 1;switch(this._scrollMode){case b.ScrollMode.WRAPPED:{const v=this._getVisiblePages().views,_=new Map;for(var{id:t,y:n,percent:s,widthPercent:a}of v)if(!(0===s||a<100)){let e=_.get(n);e||_.set(n,e||=[]),e.push(t)}for(const w of _.values()){var r=w.indexOf(i);if(-1!==r){var o=w.length;if(1===o)break;if(e)for(let e=r-1;0<=e;e--){var l=w[e],h=w[e+1]-1;if(l<h)return i-h}else for(let e=r+1,t=o;e<t;e++){var d=w[e],c=w[e-1]+1;if(c<d)return c-i}if(e){if((r=w[0])<i)return i-r+1}else if(o=w[o-1],i<o)return o-i+1;break}}break}case b.ScrollMode.HORIZONTAL:break;case b.ScrollMode.VERTICAL:if(this._spreadMode===b.SpreadMode.NONE)break;var u=this._spreadMode-1;if(e&&i%2!=u)break;if(!e&&i%2==u)break;var p,g,m,u=this._getVisiblePages().views,f=e?i-1:i+1;for({id:p,percent:g,widthPercent:m}of u)if(p===f){if(0<g&&100===m)return 2;break}}return 1}nextPage(){var e=this._currentPageNumber,t=this.pagesCount;if(t<=e)return!1;var i=this._getPageAdvance(e,!1)||1;return this.currentPageNumber=Math.min(e+i,t),!0}previousPage(){var e=this._currentPageNumber;if(e<=1)return!1;var t=this._getPageAdvance(e,!0)||1;return this.currentPageNumber=Math.max(e-t,1),!0}increaseScale(e=1){let t=this._currentScale;for(;t=(t*b.DEFAULT_SCALE_DELTA).toFixed(2),t=Math.ceil(10*t)/10,t=Math.min(b.MAX_SCALE,t),0<--e&&t<b.MAX_SCALE;);this.currentScaleValue=t}decreaseScale(e=1){let t=this._currentScale;for(;t=(t/b.DEFAULT_SCALE_DELTA).toFixed(2),t=Math.floor(10*t)/10,t=Math.max(b.MIN_SCALE,t),0<--e&&t>b.MIN_SCALE;);this.currentScaleValue=t}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultAnnotationLayerFactory=t.AnnotationLayerBuilder=void 0;var a=i(4),u=i(29),d=i(18);class c{constructor({pageDiv:e,pdfPage:t,linkService:i,downloadManager:n,annotationStorage:s=null,imageResourcesPath:a="",renderForms:r=!0,l10n:o=u.NullL10n,enableScripting:l=!1,hasJSActionsPromise:h=null,fieldObjectsPromise:d=null,mouseState:c=null}){this.pageDiv=e,this.pdfPage=t,this.linkService=i,this.downloadManager=n,this.imageResourcesPath=a,this.renderForms=r,this.l10n=o,this.annotationStorage=s,this.enableScripting=l,this._hasJSActionsPromise=h,this._fieldObjectsPromise=d,this._mouseState=c,this.div=null,this._cancelled=!1}async render(e,t="display"){var[i,n=!1,t=null]=await Promise.all([this.pdfPage.getAnnotations({intent:t}),this._hasJSActionsPromise,this._fieldObjectsPromise]);if(!this._cancelled&&0!==i.length){const s={viewport:e.clone({dontFlip:!0}),div:this.div,annotations:i,page:this.pdfPage,imageResourcesPath:this.imageResourcesPath,renderForms:this.renderForms,linkService:this.linkService,downloadManager:this.downloadManager,annotationStorage:this.annotationStorage,enableScripting:this.enableScripting,hasJSActions:n,fieldObjects:t,mouseState:this._mouseState};this.div?a.AnnotationLayer.update(s):(this.div=document.createElement("div"),this.div.className="annotationLayer",this.pageDiv.appendChild(this.div),s.div=this.div,a.AnnotationLayer.render(s),this.l10n.translate(this.div))}}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}t.AnnotationLayerBuilder=c;t.DefaultAnnotationLayerFactory=class{createAnnotationLayerBuilder(e,t,i=null,n="",s=!0,a=u.NullL10n,r=!1,o=null,l=null,h=null){return new c({pageDiv:e,pdfPage:t,imageResourcesPath:n,renderForms:s,linkService:new d.SimpleLinkService,l10n:a,annotationStorage:i,enableScripting:r,hasJSActionsPromise:o,fieldObjectsPromise:h,mouseState:l})}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.fixupLangCode=function(e){return n[e?.toLowerCase()]||e},t.getL10nFallback=s,t.NullL10n=void 0;const i={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",print_progress_percent:"{{progress}}%","toggle_sidebar.title":"Toggle Sidebar","toggle_sidebar_notification2.title":"Toggle Sidebar (document contains outline/attachments/layers)",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",error_version_info:"PDF.js v{{version}} (build: {{build}})",error_message:"Message: {{message}}",error_stack:"Stack: {{stack}}",error_file:"File: {{file}}",error_line:"Line: {{line}}",rendering_error:"An error occurred while rendering the page.",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading:"Loading…",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts."};function s(e,t){switch(e){case"find_match_count":e=`find_match_count[${1===t.total?"one":"other"}]`;break;case"find_match_count_limit":e=`find_match_count_limit[${1===t.limit?"one":"other"}]`}return i[e]||""}const n={en:"en-US",es:"es-ES",fy:"fy-NL",ga:"ga-IE",gu:"gu-IN",hi:"hi-IN",hy:"hy-AM",nb:"nb-NO",ne:"ne-NP",nn:"nn-NO",pa:"pa-IN",pt:"pt-PT",sv:"sv-SE",zh:"zh-CN"};t.NullL10n={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(e,t=null,i=s(e,t)){return i=i,(n=t)?i.replace(/\{\{\s*(\w+)\s*\}\}/g,(e,t)=>t in n?n[t]:"{{"+t+"}}"):i;var n},async translate(e){}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPageView=void 0;var u=i(4),p=i(3),r=i(1),s=i(29),h=i(7);const a=r.compatibilityParams.maxCanvasPixels||16777216;t.PDFPageView=class{constructor(e){const t=e.container;var i=e.defaultViewport;this.id=e.id,this.renderingId="page"+this.id,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=e.scale||p.DEFAULT_SCALE,this.viewport=i,this.pdfPageRotate=i.rotation,this._optionalContentConfigPromise=e.optionalContentConfigPromise||null,this.hasRestrictedScaling=!1,this.textLayerMode=e.textLayerMode??p.TextLayerMode.ENABLE,this._annotationMode=e.annotationMode??u.AnnotationMode.ENABLE_FORMS,this.imageResourcesPath=e.imageResourcesPath||"",this.useOnlyCssZoom=e.useOnlyCssZoom||!1,this.maxCanvasPixels=e.maxCanvasPixels||a,this.eventBus=e.eventBus,this.renderingQueue=e.renderingQueue,this.textLayerFactory=e.textLayerFactory,this.annotationLayerFactory=e.annotationLayerFactory,this.xfaLayerFactory=e.xfaLayerFactory,this.textHighlighter=e.textHighlighterFactory?.createTextHighlighter(this.id-1,this.eventBus),this.structTreeLayerFactory=e.structTreeLayerFactory,this.renderer=e.renderer||p.RendererType.CANVAS,this.l10n=e.l10n||s.NullL10n,this.paintTask=null,this.paintedViewportMap=new WeakMap,this.renderingState=h.RenderingStates.INITIAL,this.resume=null,this._renderError=null,this._isStandalone=!this.renderingQueue?.hasViewer(),this.annotationLayer=null,this.textLayer=null,this.zoomLayer=null,this.xfaLayer=null,this.structTreeLayer=null;const n=document.createElement("div");n.className="page",n.style.width=Math.floor(this.viewport.width)+"px",n.style.height=Math.floor(this.viewport.height)+"px",n.setAttribute("data-page-number",this.id),n.setAttribute("role","region"),this.l10n.get("page_landmark",{page:this.id}).then(e=>{n.setAttribute("aria-label",e)}),this.div=n,t.appendChild(n)}setPdfPage(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:this.scale*u.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:t}),this.reset()}destroy(){this.reset(),this.pdfPage&&this.pdfPage.cleanup()}async _renderAnnotationLayer(){let t=null;try{await this.annotationLayer.render(this.viewport,"display")}catch(e){t=e}finally{this.eventBus.dispatch("annotationlayerrendered",{source:this,pageNumber:this.id,error:t})}}async _renderXfaLayer(){let t=null;try{var e=await this.xfaLayer.render(this.viewport,"display");this.textHighlighter&&this._buildXfaTextContentItems(e.textDivs)}catch(e){t=e}finally{this.eventBus.dispatch("xfalayerrendered",{source:this,pageNumber:this.id,error:t})}}async _buildXfaTextContentItems(e){const t=[];for(const i of(await this.pdfPage.getTextContent()).items)t.push(i.str);this.textHighlighter.setTextMapping(e,t),this.textHighlighter.enable()}_resetZoomLayer(e=!1){if(this.zoomLayer){const t=this.zoomLayer.firstChild;this.paintedViewportMap.delete(t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}}reset({keepZoomLayer:e=!1,keepAnnotationLayer:t=!1,keepXfaLayer:i=!1}={}){this.cancelRendering({keepAnnotationLayer:t,keepXfaLayer:i}),this.renderingState=h.RenderingStates.INITIAL;const n=this.div;n.style.width=Math.floor(this.viewport.width)+"px",n.style.height=Math.floor(this.viewport.height)+"px";var s=n.childNodes,a=e&&this.zoomLayer||null,r=t&&this.annotationLayer?.div||null,o=i&&this.xfaLayer?.div||null;for(let e=s.length-1;0<=e;e--){var l=s[e];switch(l){case a:case r:case o:continue}n.removeChild(l)}n.removeAttribute("data-loaded"),r&&this.annotationLayer.hide(),o&&this.xfaLayer.hide(),a||(this.canvas&&(this.paintedViewportMap.delete(this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer()),this.svg&&(this.paintedViewportMap.delete(this.svg),delete this.svg),this.loadingIconDiv=document.createElement("div"),this.loadingIconDiv.className="loadingIcon",this.loadingIconDiv.setAttribute("role","img"),this.l10n.get("loading").then(e=>{this.loadingIconDiv?.setAttribute("aria-label",e)}),n.appendChild(this.loadingIconDiv)}update({scale:e=0,rotation:t=null,optionalContentConfigPromise:i=null}){if("object"!=typeof arguments[0])return console.error("PDFPageView.update called with separate parameters, please use an object instead."),void this.update({scale:arguments[0],rotation:arguments[1],optionalContentConfigPromise:arguments[2]});if(this.scale=e||this.scale,"number"==typeof t&&(this.rotation=t),i instanceof Promise&&(this._optionalContentConfigPromise=i),this._isStandalone){const s=document.documentElement;s.style.setProperty("--zoom-factor",this.scale)}i=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*u.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:i}),this.svg)return this.cssTransform({target:this.svg,redrawAnnotationLayer:!0,redrawXfaLayer:!0}),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this._renderError});let n=!1;if(this.canvas&&0<this.maxCanvasPixels&&(i=this.outputScale,(Math.floor(this.viewport.width)*i.sx|0)*(Math.floor(this.viewport.height)*i.sy|0)>this.maxCanvasPixels&&(n=!0)),this.canvas){if(this.useOnlyCssZoom||this.hasRestrictedScaling&&n)return this.cssTransform({target:this.canvas,redrawAnnotationLayer:!0,redrawXfaLayer:!0}),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this._renderError});this.zoomLayer||this.canvas.hidden||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform({target:this.zoomLayer.firstChild}),this.reset({keepZoomLayer:!0,keepAnnotationLayer:!0,keepXfaLayer:!0})}cancelRendering({keepAnnotationLayer:e=!1,keepXfaLayer:t=!1}={}){this.paintTask&&(this.paintTask.cancel(),this.paintTask=null),this.resume=null,this.textLayer&&(this.textLayer.cancel(),this.textLayer=null),!this.annotationLayer||e&&this.annotationLayer.div||(this.annotationLayer.cancel(),this.annotationLayer=null),!this.xfaLayer||t&&this.xfaLayer.div||(this.xfaLayer.cancel(),this.xfaLayer=null,this.textHighlighter?.disable()),this._onTextLayerRendered&&(this.eventBus._off("textlayerrendered",this._onTextLayerRendered),this._onTextLayerRendered=null)}cssTransform({target:n,redrawAnnotationLayer:e=!1,redrawXfaLayer:t=!1}){var s=this.viewport.width,i=this.viewport.height;const a=this.div;n.style.width=n.parentNode.style.width=a.style.width=Math.floor(s)+"px",n.style.height=n.parentNode.style.height=a.style.height=Math.floor(i)+"px";var r=this.viewport.rotation-this.paintedViewportMap.get(n).rotation,o=Math.abs(r);let l=1,h=1;if(90!==o&&270!==o||(l=i/s,h=s/i),n.style.transform=`rotate(${r}deg) scale(${l}, ${h})`,this.textLayer){n=this.textLayer.viewport,r=this.viewport.rotation-n.rotation,r=Math.abs(r);let e=s/n.width;90!==r&&270!==r||(e=s/n.height);const d=this.textLayer.textLayerDiv;let t,i;switch(r){case 0:t=i=0;break;case 90:t=0,i="-"+d.style.height;break;case 180:t="-"+d.style.width,i="-"+d.style.height;break;case 270:t="-"+d.style.width,i=0;break;default:console.error("Bad rotation value.")}d.style.transform=`rotate(${r}deg) `+`scale(${e}) `+`translate(${t}, ${i})`,d.style.transformOrigin="0% 0%"}e&&this.annotationLayer&&this._renderAnnotationLayer(),t&&this.xfaLayer&&this._renderXfaLayer()}get width(){return this.viewport.width}get height(){return this.viewport.height}getPagePoint(e,t){return this.viewport.convertToPdfPoint(e,t)}draw(){this.renderingState!==h.RenderingStates.INITIAL&&(console.error("Must be in new state before drawing"),this.reset());const{div:t,pdfPage:i}=this;if(!i)return this.renderingState=h.RenderingStates.FINISHED,this.loadingIconDiv&&(t.removeChild(this.loadingIconDiv),delete this.loadingIconDiv),Promise.reject(new Error("pdfPage is not loaded"));this.renderingState=h.RenderingStates.RUNNING;const e=document.createElement("div");e.style.width=t.style.width,e.style.height=t.style.height,e.classList.add("canvasWrapper"),this.annotationLayer?.div?t.insertBefore(e,this.annotationLayer.div):t.appendChild(e);let n=null;if(this.textLayerMode!==p.TextLayerMode.DISABLE&&this.textLayerFactory){const l=document.createElement("div");l.className="textLayer",l.style.width=e.style.width,l.style.height=e.style.height,this.annotationLayer?.div?t.insertBefore(l,this.annotationLayer.div):t.appendChild(l),n=this.textLayerFactory.createTextLayerBuilder(l,this.id-1,this.viewport,this.textLayerMode===p.TextLayerMode.ENABLE_ENHANCE,this.eventBus,this.textHighlighter)}this.textLayer=n,this.xfaLayer?.div&&t.appendChild(this.xfaLayer.div);let s=null;this.renderingQueue&&(s=e=>{if(!this.renderingQueue.isHighestPriority(this))return this.renderingState=h.RenderingStates.PAUSED,void(this.resume=()=>{this.renderingState=h.RenderingStates.RUNNING,e()});e()});const a=async(e=null)=>{if(r===this.paintTask&&(this.paintTask=null),e instanceof u.RenderingCancelledException)this._renderError=null;else if(this._renderError=e,this.renderingState=h.RenderingStates.FINISHED,this.loadingIconDiv&&(t.removeChild(this.loadingIconDiv),delete this.loadingIconDiv),this._resetZoomLayer(!0),this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!1,timestamp:performance.now(),error:this._renderError}),e)throw e},r=this.renderer===p.RendererType.SVG?this.paintOnSvg(e):this.paintOnCanvas(e);r.onRenderContinue=s,this.paintTask=r;var o=r.promise.then(()=>a(null).then(()=>{var e;n&&(e=i.streamTextContent({normalizeWhitespace:!0,includeMarkedContent:!0}),n.setTextContentStream(e),n.render())}),function(e){return a(e)});return this._annotationMode!==u.AnnotationMode.DISABLE&&this.annotationLayerFactory&&(this.annotationLayer||(this.annotationLayer=this.annotationLayerFactory.createAnnotationLayerBuilder(t,i,null,this.imageResourcesPath,this._annotationMode===u.AnnotationMode.ENABLE_FORMS,this.l10n,null,null,null,null)),this._renderAnnotationLayer()),this.xfaLayerFactory&&(this.xfaLayer||(this.xfaLayer=this.xfaLayerFactory.createXfaLayerBuilder(t,i,null)),this._renderXfaLayer()),this.structTreeLayerFactory&&this.textLayer&&this.canvas&&(this._onTextLayerRendered=e=>{e.pageNumber===this.id&&(this.eventBus._off("textlayerrendered",this._onTextLayerRendered),this._onTextLayerRendered=null,this.canvas&&this.pdfPage.getStructTree().then(e=>{if(e&&this.canvas){const t=this.structTreeLayer.render(e);t.classList.add("structTree"),this.canvas.appendChild(t)}}))},this.eventBus._on("textlayerrendered",this._onTextLayerRendered),this.structTreeLayer=this.structTreeLayerFactory.createStructTreeLayerBuilder(i)),t.setAttribute("data-loaded",!0),this.eventBus.dispatch("pagerender",{source:this,pageNumber:this.id}),o}paintOnCanvas(e){const t=(0,u.createPromiseCapability)(),i={promise:t.promise,onRenderContinue(e){e()},cancel(){c.cancel()}},n=this.viewport,s=document.createElement("canvas");let a=s.hidden=!0;function r(){a&&(s.hidden=!1,a=!1)}e.appendChild(s),this.canvas=s,s.mozOpaque=!0;var o=s.getContext("2d",{alpha:!1});const l=(0,p.getOutputScale)(o);this.outputScale=l,this.useOnlyCssZoom&&(e=n.clone({scale:u.PixelsPerInch.PDF_TO_CSS_UNITS}),l.sx*=e.width/n.width,l.sy*=e.height/n.height,l.scaled=!0),0<this.maxCanvasPixels&&(h=n.width*n.height,d=Math.sqrt(this.maxCanvasPixels/h),l.sx>d||l.sy>d?(l.sx=d,l.sy=d,l.scaled=!0,this.hasRestrictedScaling=!0):this.hasRestrictedScaling=!1);var h=(0,p.approximateFraction)(l.sx),d=(0,p.approximateFraction)(l.sy);s.width=(0,p.roundToDivide)(n.width*l.sx,h[0]),s.height=(0,p.roundToDivide)(n.height*l.sy,d[0]),s.style.width=(0,p.roundToDivide)(n.width,h[1])+"px",s.style.height=(0,p.roundToDivide)(n.height,d[1])+"px",this.paintedViewportMap.set(s,n);o={canvasContext:o,transform:l.scaled?[l.sx,0,0,l.sy,0,0]:null,viewport:this.viewport,annotationMode:this._annotationMode,optionalContentConfigPromise:this._optionalContentConfigPromise};const c=this.pdfPage.render(o);return c.onContinue=function(e){r(),i.onRenderContinue?i.onRenderContinue(e):e()},c.promise.then(function(){r(),t.resolve(void 0)},function(e){r(),t.reject(e)}),i}paintOnSvg(i){let e=!1;const n=()=>{if(e)throw new u.RenderingCancelledException(`Rendering cancelled, page ${this.id}`,"svg")},s=this.pdfPage,a=this.viewport.clone({scale:u.PixelsPerInch.PDF_TO_CSS_UNITS});return{promise:s.getOperatorList({annotationMode:this._annotationMode}).then(e=>{n();const t=new u.SVGGraphics(s.commonObjs,s.objs,r.compatibilityParams.disableCreateObjectURL);return t.getSVG(e,a).then(e=>{n(),this.svg=e,this.paintedViewportMap.set(e,a),e.style.width=i.style.width,e.style.height=i.style.height,this.renderingState=h.RenderingStates.FINISHED,i.appendChild(e)})}),onRenderContinue(e){e()},cancel(){e=!0}}}setPageLabel(e){this.pageLabel="string"==typeof e?e:null,null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StructTreeLayerBuilder=t.DefaultStructTreeLayerFactory=void 0;const a={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},r=/^H(\d+)$/;class i{constructor({pdfPage:e}){this.pdfPage=e}render(e){return this._walk(e)}_setAttributes(e,t){void 0!==e.alt&&t.setAttribute("aria-label",e.alt),void 0!==e.id&&t.setAttribute("aria-owns",e.id)}_walk(e){if(!e)return null;const t=document.createElement("span");if("role"in e){const n=e["role"];var i=n.match(r);i?(t.setAttribute("role","heading"),t.setAttribute("aria-level",i[1])):a[n]&&t.setAttribute("role",a[n])}if(this._setAttributes(e,t),e.children)if(1===e.children.length&&"id"in e.children[0])this._setAttributes(e.children[0],t);else for(const s of e.children)t.appendChild(this._walk(s));return t}}t.StructTreeLayerBuilder=i;t.DefaultStructTreeLayerFactory=class{createStructTreeLayerBuilder(e){return new i({pdfPage:e})}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextHighlighter=void 0;t.TextHighlighter=class{constructor({findController:e,eventBus:t,pageIndex:i}){this.findController=e,this.matches=[],this.eventBus=t,this.pageIdx=i,this._onUpdateTextLayerMatches=null,this.textDivs=null,this.textContentItemsStr=null,this.enabled=!1}setTextMapping(e,t){this.textDivs=e,this.textContentItemsStr=t}enable(){if(!this.textDivs||!this.textContentItemsStr)throw new Error("Text divs and strings have not been set.");if(this.enabled)throw new Error("TextHighlighter is already enabled.");this.enabled=!0,this._onUpdateTextLayerMatches||(this._onUpdateTextLayerMatches=e=>{e.pageIndex!==this.pageIdx&&-1!==e.pageIndex||this._updateMatches()},this.eventBus._on("updatetextlayermatches",this._onUpdateTextLayerMatches)),this._updateMatches()}disable(){this.enabled&&(this.enabled=!1,this._onUpdateTextLayerMatches&&(this.eventBus._off("updatetextlayermatches",this._onUpdateTextLayerMatches),this._onUpdateTextLayerMatches=null))}_convertMatches(i,n){if(!i)return[];var s=this["textContentItemsStr"];let a=0,r=0;var o=s.length-1;const l=[];for(let t=0,e=i.length;t<e;t++){let e=i[t];for(;a!==o&&e>=r+s[a].length;)r+=s[a].length,a++;a===s.length&&console.error("Could not find a matching mapping");const h={begin:{divIdx:a,offset:e-r}};for(e+=n[t];a!==o&&e>r+s[a].length;)r+=s[a].length,a++;h.end={divIdx:a,offset:e-r},l.push(h)}return l}_renderMatches(s){if(0!==s.length){const{findController:g,pageIdx:m}=this,{textContentItemsStr:f,textDivs:v}=this;var a=m===g.selected.pageIdx,r=g.selected.matchIdx,t=g.state.highlightAll;let i=null;var o={divIdx:-1,offset:void 0};let e=r,n=e+1;if(t)e=0,n=s.length;else if(!a)return;for(let t=e;t<n;t++){var l=s[t],h=l.begin,d=l.end,l=a&&t===r,c=l?" selected":"";let e=0;if(i&&h.divIdx===i.divIdx?p(i.divIdx,i.offset,h.offset):(null!==i&&p(i.divIdx,i.offset,o.offset),u(h)),h.divIdx===d.divIdx)e=p(h.divIdx,h.offset,d.offset,"highlight"+c);else{e=p(h.divIdx,h.offset,o.offset,"highlight begin"+c);for(let e=h.divIdx+1,t=d.divIdx;e<t;e++)v[e].className="highlight middle"+c;u(d,"highlight end"+c)}i=d,l&&g.scrollMatchIntoView({element:v[h.divIdx],selectedLeft:e,pageIndex:m,matchIndex:r})}function u(e,t){var i=e.divIdx;return v[i].textContent="",p(i,0,e.offset,t)}function p(e,t,i,n){let s=v[e];if(s.nodeType===Node.TEXT_NODE){const a=document.createElement("span");s.parentNode.insertBefore(a,s),a.appendChild(s),v[e]=a,s=a}i=f[e].substring(t,i),i=document.createTextNode(i);if(n){const r=document.createElement("span");return r.className=`${n} appended`,r.appendChild(i),s.appendChild(r),n.includes("selected")?r.offsetLeft:0}return s.appendChild(i),0}i&&p(i.divIdx,i.offset,o.offset)}}_updateMatches(){if(this.enabled){var e,{findController:t,matches:n,pageIdx:s}=this,{textContentItemsStr:a,textDivs:r}=this;let i=-1;for(let e=0,t=n.length;e<t;e++){var o=n[e];for(let e=Math.max(i,o.begin.divIdx),t=o.end.divIdx;e<=t;e++){const l=r[e];l.textContent=a[e],l.className=""}i=o.end.divIdx+1}t?.highlightMatches&&(e=t.pageMatches[s]||null,s=t.pageMatchesLength[s]||null,this.matches=this._convertMatches(e,s),this._renderMatches(this.matches))}}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextLayerBuilder=t.DefaultTextLayerFactory=void 0;var n=i(4);class r{constructor({textLayerDiv:e,eventBus:t,pageIndex:i,viewport:n,highlighter:s=null,enhanceTextSelection:a=!1}){this.textLayerDiv=e,this.eventBus=t,this.textContent=null,this.textContentItemsStr=[],this.textContentStream=null,this.renderingDone=!1,this.pageNumber=i+1,this.viewport=n,this.textDivs=[],this.textLayerRenderTask=null,this.highlighter=s,this.enhanceTextSelection=a,this._bindMouse()}_finishRendering(){if(this.renderingDone=!0,!this.enhanceTextSelection){const e=document.createElement("div");e.className="endOfContent",this.textLayerDiv.appendChild(e)}this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.pageNumber,numTextDivs:this.textDivs.length})}render(e=0){if((this.textContent||this.textContentStream)&&!this.renderingDone){this.cancel(),this.textDivs.length=0,this.highlighter?.setTextMapping(this.textDivs,this.textContentItemsStr);const t=document.createDocumentFragment();this.textLayerRenderTask=(0,n.renderTextLayer)({textContent:this.textContent,textContentStream:this.textContentStream,container:t,viewport:this.viewport,textDivs:this.textDivs,textContentItemsStr:this.textContentItemsStr,timeout:e,enhanceTextSelection:this.enhanceTextSelection}),this.textLayerRenderTask.promise.then(()=>{this.textLayerDiv.appendChild(t),this._finishRendering(),this.highlighter?.enable()},function(e){})}}cancel(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null),this.highlighter?.disable()}setTextContentStream(e){this.cancel(),this.textContentStream=e}setTextContent(e){this.cancel(),this.textContent=e}_bindMouse(){const n=this.textLayerDiv;let s=null;n.addEventListener("mousedown",e=>{if(this.enhanceTextSelection&&this.textLayerRenderTask)return this.textLayerRenderTask.expandTextDivs(!0),void(s&&(clearTimeout(s),s=null));const t=n.querySelector(".endOfContent");var i;t&&(e.target!==n&&"none"!==window.getComputedStyle(t).getPropertyValue("-moz-user-select")&&(i=n.getBoundingClientRect(),i=Math.max(0,(e.pageY-i.top)/i.height),t.style.top=(100*i).toFixed(2)+"%"),t.classList.add("active"))}),n.addEventListener("mouseup",()=>{if(this.enhanceTextSelection&&this.textLayerRenderTask)s=setTimeout(()=>{this.textLayerRenderTask&&this.textLayerRenderTask.expandTextDivs(!1),s=null},300);else{const e=n.querySelector(".endOfContent");e&&(e.style.top="",e.classList.remove("active"))}})}}t.TextLayerBuilder=r;t.DefaultTextLayerFactory=class{createTextLayerBuilder(e,t,i,n=!1,s,a){return new r({textLayerDiv:e,pageIndex:t,viewport:i,enhanceTextSelection:n,eventBus:s,highlighter:a})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.XfaLayerBuilder=t.DefaultXfaLayerFactory=void 0;var s=i(18),a=i(4);class r{constructor({pageDiv:e,pdfPage:t,annotationStorage:i,linkService:n,xfaHtml:s}){this.pageDiv=e,this.pdfPage=t,this.annotationStorage=i,this.linkService=n,this.xfaHtml=s,this.div=null,this._cancelled=!1}render(i,n="display"){if("print"!==n)return this.pdfPage.getXfa().then(e=>{if(this._cancelled||!e)return{textDivs:[]};const t={viewport:i.clone({dontFlip:!0}),div:this.div,xfa:e,page:this.pdfPage,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:n};return this.div?a.XfaLayer.update(t):(this.div=document.createElement("div"),this.pageDiv.appendChild(this.div),t.div=this.div,a.XfaLayer.render(t))}).catch(e=>{console.error(e)});{const t={viewport:i.clone({dontFlip:!0}),div:this.div,xfa:this.xfaHtml,page:null,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:n};var e=document.createElement("div");this.pageDiv.appendChild(e),t.div=e;e=a.XfaLayer.render(t);return Promise.resolve(e)}}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}t.XfaLayerBuilder=r;t.DefaultXfaLayerFactory=class{createXfaLayerBuilder(e,t,i=null,n=null){return new r({pageDiv:e,pdfPage:t,annotationStorage:i,linkService:new s.SimpleLinkService,xfaHtml:n})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SecondaryToolbar=void 0;var n=i(3),s=i(5),a=i(36);t.SecondaryToolbar=class{constructor(e,t,i){this.toolbar=e.toolbar,this.toggleButton=e.toggleButton,this.toolbarButtonContainer=e.toolbarButtonContainer,this.buttons=[{element:e.presentationModeButton,eventName:"presentationmode",close:!0},{element:e.openFileButton,eventName:"openfile",close:!0},{element:e.printButton,eventName:"print",close:!0},{element:e.downloadButton,eventName:"download",close:!0},{element:e.viewBookmarkButton,eventName:null,close:!0},{element:e.firstPageButton,eventName:"firstpage",close:!0},{element:e.lastPageButton,eventName:"lastpage",close:!0},{element:e.pageRotateCwButton,eventName:"rotatecw",close:!1},{element:e.pageRotateCcwButton,eventName:"rotateccw",close:!1},{element:e.cursorSelectToolButton,eventName:"switchcursortool",eventDetails:{tool:s.CursorTool.SELECT},close:!0},{element:e.cursorHandToolButton,eventName:"switchcursortool",eventDetails:{tool:s.CursorTool.HAND},close:!0},{element:e.scrollVerticalButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.VERTICAL},close:!0},{element:e.scrollHorizontalButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.HORIZONTAL},close:!0},{element:e.scrollWrappedButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.WRAPPED},close:!0},{element:e.spreadNoneButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.NONE},close:!0},{element:e.spreadOddButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.ODD},close:!0},{element:e.spreadEvenButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.EVEN},close:!0},{element:e.documentPropertiesButton,eventName:"documentproperties",close:!0}],this.items={firstPage:e.firstPageButton,lastPage:e.lastPageButton,pageRotateCw:e.pageRotateCwButton,pageRotateCcw:e.pageRotateCcwButton},this.mainContainer=t,this.eventBus=i,this.opened=!1,this.containerHeight=null,this.previousContainerHeight=null,this.reset(),this._bindClickListeners(),this._bindCursorToolsListener(e),this._bindScrollModeListener(e),this._bindSpreadModeListener(e),this.eventBus._on("resize",this._setMaxHeight.bind(this)),this.eventBus._on("baseviewerinit",e=>{e.source instanceof a.PDFSinglePageViewer?this.toolbarButtonContainer.classList.add("hiddenScrollModeButtons","hiddenSpreadModeButtons"):this.toolbarButtonContainer.classList.remove("hiddenScrollModeButtons","hiddenSpreadModeButtons")})}get isOpen(){return this.opened}setPageNumber(e){this.pageNumber=e,this._updateUIState()}setPagesCount(e){this.pagesCount=e,this._updateUIState()}reset(){this.pageNumber=0,this.pagesCount=0,this._updateUIState(),this.eventBus.dispatch("secondarytoolbarreset",{source:this})}_updateUIState(){this.items.firstPage.disabled=this.pageNumber<=1,this.items.lastPage.disabled=this.pageNumber>=this.pagesCount,this.items.pageRotateCw.disabled=0===this.pagesCount,this.items.pageRotateCcw.disabled=0===this.pagesCount}_bindClickListeners(){this.toggleButton.addEventListener("click",this.toggle.bind(this));for(const{element:e,eventName:n,close:s,eventDetails:a}of this.buttons)e.addEventListener("click",e=>{if(null!==n){const t={source:this};for(const i in a)t[i]=a[i];this.eventBus.dispatch(n,t)}s&&this.close()})}_bindCursorToolsListener(t){this.eventBus._on("cursortoolchanged",function({tool:e}){t.cursorSelectToolButton.classList.toggle("toggled",e===s.CursorTool.SELECT),t.cursorHandToolButton.classList.toggle("toggled",e===s.CursorTool.HAND)})}_bindScrollModeListener(t){function i({mode:e}){t.scrollVerticalButton.classList.toggle("toggled",e===n.ScrollMode.VERTICAL),t.scrollHorizontalButton.classList.toggle("toggled",e===n.ScrollMode.HORIZONTAL),t.scrollWrappedButton.classList.toggle("toggled",e===n.ScrollMode.WRAPPED);e=e===n.ScrollMode.HORIZONTAL;t.spreadNoneButton.disabled=e,t.spreadOddButton.disabled=e,t.spreadEvenButton.disabled=e}this.eventBus._on("scrollmodechanged",i),this.eventBus._on("secondarytoolbarreset",e=>{e.source===this&&i({mode:n.ScrollMode.VERTICAL})})}_bindSpreadModeListener(t){function i({mode:e}){t.spreadNoneButton.classList.toggle("toggled",e===n.SpreadMode.NONE),t.spreadOddButton.classList.toggle("toggled",e===n.SpreadMode.ODD),t.spreadEvenButton.classList.toggle("toggled",e===n.SpreadMode.EVEN)}this.eventBus._on("spreadmodechanged",i),this.eventBus._on("secondarytoolbarreset",e=>{e.source===this&&i({mode:n.SpreadMode.NONE})})}open(){this.opened||(this.opened=!0,this._setMaxHeight(),this.toggleButton.classList.add("toggled"),this.toggleButton.setAttribute("aria-expanded","true"),this.toolbar.classList.remove("hidden"))}close(){this.opened&&(this.opened=!1,this.toolbar.classList.add("hidden"),this.toggleButton.classList.remove("toggled"),this.toggleButton.setAttribute("aria-expanded","false"))}toggle(){this.opened?this.close():this.open()}_setMaxHeight(){this.opened&&(this.containerHeight=this.mainContainer.clientHeight,this.containerHeight!==this.previousContainerHeight&&(this.toolbarButtonContainer.style.maxHeight=`${this.containerHeight-n.SCROLLBAR_PADDING}px`,this.previousContainerHeight=this.containerHeight))}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSinglePageViewer=void 0;var n=i(27),s=i(4);class a extends n.BaseViewer{constructor(e){super(e),this.eventBus._on("pagesinit",e=>{this._ensurePageViewVisible()})}get _viewerElement(){return(0,s.shadow)(this,"_viewerElement",this._shadowViewer)}get _pageWidthScaleFactor(){return 1}_resetView(){super._resetView(),this._previousPageNumber=1,this._shadowViewer=document.createDocumentFragment(),this._updateScrollDown=null}_ensurePageViewVisible(){var e=this._pages[this._currentPageNumber-1],t=this._pages[this._previousPageNumber-1],i=this.viewer.childNodes;switch(i.length){case 0:this.viewer.appendChild(e.div);break;case 1:if(i[0]!==t.div)throw new Error("_ensurePageViewVisible: Unexpected previously visible page.");if(e===t)break;this._shadowViewer.appendChild(t.div),this.viewer.appendChild(e.div),this.container.scrollTop=0;break;default:throw new Error("_ensurePageViewVisible: Only one page should be visible at a time.")}this._previousPageNumber=this._currentPageNumber}_scrollUpdate(){this._updateScrollDown&&this._updateScrollDown(),super._scrollUpdate()}_scrollIntoView({pageDiv:e,pageSpot:t=null,pageNumber:i=null}){i&&this._setCurrentPageNumber(i);const n=this._currentPageNumber>=this._previousPageNumber;this._ensurePageViewVisible(),this.update(),super._scrollIntoView({pageDiv:e,pageSpot:t,pageNumber:i}),this._updateScrollDown=()=>{this.scroll.down=n,this._updateScrollDown=null}}_getVisiblePages(){return this._getCurrentVisiblePage()}_updateHelper(e){}get _isScrollModeHorizontal(){return(0,s.shadow)(this,"_isScrollModeHorizontal",!1)}_updateScrollMode(){}_updateSpreadMode(){}_getPageAdvance(){return 1}}t.PDFSinglePageViewer=a},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Toolbar=void 0;var u=i(3);t.Toolbar=class{constructor(e,t,i){this.toolbar=e.container,this.eventBus=t,this.l10n=i,this.buttons=[{element:e.previous,eventName:"previouspage"},{element:e.next,eventName:"nextpage"},{element:e.zoomIn,eventName:"zoomin"},{element:e.zoomOut,eventName:"zoomout"},{element:e.openFile,eventName:"openfile"},{element:e.print,eventName:"print"},{element:e.presentationModeButton,eventName:"presentationmode"},{element:e.download,eventName:"download"},{element:e.viewBookmark,eventName:null}],this.items={numPages:e.numPages,pageNumber:e.pageNumber,scaleSelect:e.scaleSelect,customScaleOption:e.customScaleOption,previous:e.previous,next:e.next,zoomIn:e.zoomIn,zoomOut:e.zoomOut},this._wasLocalized=!1,this.reset(),this._bindListeners()}setPageNumber(e,t){this.pageNumber=e,this.pageLabel=t,this._updateUIState(!1)}setPagesCount(e,t){this.pagesCount=e,this.hasPageLabels=t,this._updateUIState(!0)}setPageScale(e,t){this.pageScaleValue=(e||t).toString(),this.pageScale=t,this._updateUIState(!1)}reset(){this.pageNumber=0,this.pageLabel=null,this.hasPageLabels=!1,this.pagesCount=0,this.pageScaleValue=u.DEFAULT_SCALE_VALUE,this.pageScale=u.DEFAULT_SCALE,this._updateUIState(!0),this.updateLoadingIndicatorState()}_bindListeners(){const{pageNumber:e,scaleSelect:t}=this.items,i=this;for(const{element:n,eventName:s}of this.buttons)n.addEventListener("click",e=>{null!==s&&this.eventBus.dispatch(s,{source:this})});e.addEventListener("click",function(){this.select()}),e.addEventListener("change",function(){i.eventBus.dispatch("pagenumberchanged",{source:i,value:this.value})}),t.addEventListener("change",function(){"custom"!==this.value&&i.eventBus.dispatch("scalechanged",{source:i,value:this.value})}),t.addEventListener("click",function(e){const t=e.target;this.value===i.pageScaleValue&&"OPTION"===t.tagName.toUpperCase()&&this.blur()}),t.oncontextmenu=u.noContextMenuHandler,this.eventBus._on("localized",()=>{this._wasLocalized=!0,this._adjustScaleWidth(),this._updateUIState(!0)})}_updateUIState(e=!1){if(this._wasLocalized){const{pageNumber:t,pagesCount:i,pageScaleValue:n,pageScale:s,items:a}=this;e&&(this.hasPageLabels?a.pageNumber.type="text":(a.pageNumber.type="number",this.l10n.get("of_pages",{pagesCount:i}).then(e=>{a.numPages.textContent=e})),a.pageNumber.max=i),this.hasPageLabels?(a.pageNumber.value=this.pageLabel,this.l10n.get("page_of_pages",{pageNumber:t,pagesCount:i}).then(e=>{a.numPages.textContent=e})):a.pageNumber.value=t,a.previous.disabled=t<=1,a.next.disabled=i<=t,a.zoomOut.disabled=s<=u.MIN_SCALE,a.zoomIn.disabled=s>=u.MAX_SCALE,this.l10n.get("page_scale_percent",{scale:Math.round(1e4*s)/100}).then(e=>{let t=!1;for(const i of a.scaleSelect.options)i.value===n?(i.selected=!0,t=!0):i.selected=!1;t||(a.customScaleOption.textContent=e,a.customScaleOption.selected=!0)})}}updateLoadingIndicatorState(e=!1){const t=this.items.pageNumber;t.classList.toggle("visiblePageIsLoading",e)}async _adjustScaleWidth(){const{items:e,l10n:t}=this;var i=Promise.all([t.get("page_scale_auto"),t.get("page_scale_actual"),t.get("page_scale_fit"),t.get("page_scale_width")]);const n=getComputedStyle(e.scaleSelect),s=parseInt(n.getPropertyValue("--scale-select-container-width"),10),a=parseInt(n.getPropertyValue("--scale-select-overflow"),10);let r=document.createElement("canvas");r.mozOpaque=!0;let o=r.getContext("2d",{alpha:!1});await u.animationStarted,o.font=`${n.fontSize} ${n.fontFamily}`;let l=0;for(const d of await i){var h=o.measureText(d)["width"];h>l&&(l=h)}if(l+=2*a,l>s){const c=document.documentElement;c.style.setProperty("--scale-select-container-width",`${l}px`)}r.width=0,r.height=0,r=o=null}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewHistory=void 0;t.ViewHistory=class{constructor(e,t=20){this.fingerprint=e,this.cacheSize=t,this._initializedPromise=this._readFromStorage().then(e=>{const i=JSON.parse(e||"{}");let n=-1;if(Array.isArray(i.files)){for(;i.files.length>=this.cacheSize;)i.files.shift();for(let e=0,t=i.files.length;e<t;e++)if(i.files[e].fingerprint===this.fingerprint){n=e;break}}else i.files=[];-1===n&&(n=i.files.push({fingerprint:this.fingerprint})-1),this.file=i.files[n],this.database=i})}async _writeToStorage(){var e=JSON.stringify(this.database);localStorage.setItem("pdfjs.history",e)}async _readFromStorage(){return localStorage.getItem("pdfjs.history")}async set(e,t){return await this._initializedPromise,this.file[e]=t,this._writeToStorage()}async setMultiple(e){await this._initializedPromise;for(const t in e)this.file[t]=e[t];return this._writeToStorage()}async get(e,t){await this._initializedPromise;e=this.file[e];return void 0!==e?e:t}async getMultiple(e){await this._initializedPromise;const t=Object.create(null);for(const n in e){var i=this.file[n];t[n]=void 0!==i?i:e[n]}return t}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericCom=void 0;var n=i(2),s=i(40),a=i(41),r=i(42),o=i(44);t.GenericCom={};class l extends s.BasePreferences{async _writeToStorage(e){localStorage.setItem("pdfjs.preferences",JSON.stringify(e))}async _readFromStorage(e){return JSON.parse(localStorage.getItem("pdfjs.preferences"))}}class h extends n.DefaultExternalServices{static createDownloadManager(e){return new a.DownloadManager}static createPreferences(){return new l}static createL10n({locale:e="en-US"}){return new r.GenericL10n(e)}static createScripting({sandboxBundleSrc:e}){return new o.GenericScripting(e)}}n.PDFViewerApplication.externalServices=h},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BasePreferences=void 0;i(1);t.BasePreferences=class n{constructor(){if(this.constructor===n)throw new Error("Cannot initialize BasePreferences.");Object.defineProperty(this,"defaults",{value:Object.freeze({annotationMode:2,cursorToolOnLoad:0,defaultZoomValue:"",disablePageLabels:!1,enablePermissions:!1,enablePrintAutoRotate:!0,enableScripting:!0,externalLinkTarget:0,historyUpdateUrl:!1,ignoreDestinationZoom:!1,pdfBugEnabled:!1,renderer:"canvas",sidebarViewOnLoad:-1,scrollModeOnLoad:-1,spreadModeOnLoad:-1,textLayerMode:1,useOnlyCssZoom:!1,viewerCssTheme:0,viewOnLoad:0,disableAutoFetch:!1,disableFontFace:!1,disableRange:!1,disableStream:!1,enableXfa:!0}),writable:!1,enumerable:!0,configurable:!1}),this.prefs=Object.create(null),this._initializedPromise=this._readFromStorage(this.defaults).then(e=>{for(const i in this.defaults){var t=e?.[i];typeof t==typeof this.defaults[i]&&(this.prefs[i]=t)}})}async _writeToStorage(e){throw new Error("Not implemented: _writeToStorage")}async _readFromStorage(e){throw new Error("Not implemented: _readFromStorage")}async reset(){return await this._initializedPromise,this.prefs=Object.create(null),this._writeToStorage(this.defaults)}async set(e,t){await this._initializedPromise;var i=this.defaults[e];if(void 0===i)throw new Error(`Set preference: "${e}" is undefined.`);if(void 0===t)throw new Error("Set preference: no value is specified.");var n=typeof t;if(n!=(i=typeof i)){if("number"!=n||"string"!=i)throw new Error(`Set preference: "${t}" is a ${n}, expected a ${i}.`);t=t.toString()}else if("number"==n&&!Number.isInteger(t))throw new Error(`Set preference: "${t}" must be an integer.`);return this.prefs[e]=t,this._writeToStorage(this.prefs)}async get(e){await this._initializedPromise;var t=this.defaults[e],i=this.prefs[e];if(void 0===t)throw new Error(`Get preference: "${e}" is undefined.`);return void 0!==i?i:t}async getAll(){await this._initializedPromise;const e=Object.create(null);for(const i in this.defaults){var t=this.prefs[i];e[i]=void 0!==t?t:this.defaults[i]}return e}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadManager=void 0;var r=i(4),o=i(1);function s(e,t){const i=document.createElement("a");if(!i.click)throw new Error('DownloadManager: "a.click()" is not supported.');i.href=e,i.target="_parent","download"in i&&(i.download=t),(document.body||document.documentElement).appendChild(i),i.click(),i.remove()}t.DownloadManager=class{constructor(){this._openBlobUrls=new WeakMap}downloadUrl(e,t){(0,r.createValidAbsoluteUrl)(e,"http://example.com")?s(e+"#pdfjs.action=download",t):console.error(`downloadUrl - not a valid URL: ${e}`)}downloadData(e,t,i){s((0,r.createObjectURL)(e,i,o.compatibilityParams.disableCreateObjectURL),t)}openOrDownloadData(i,e,n){var s=(0,r.isPdfFile)(n),a=s?"application/pdf":"";if(s&&!o.compatibilityParams.disableCreateObjectURL){let t=this._openBlobUrls.get(i);t||(t=URL.createObjectURL(new Blob([e],{type:a})),this._openBlobUrls.set(i,t)),s="?file="+encodeURIComponent(t+"#"+n);try{return window.open(s),!0}catch(e){console.error(`openOrDownloadData: ${e}`),URL.revokeObjectURL(t),this._openBlobUrls.delete(i)}}return this.downloadData(e,n,a),!1}download(e,t,i,n=0){o.compatibilityParams.disableCreateObjectURL?this.downloadUrl(t,i):s(URL.createObjectURL(e),i)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericL10n=void 0,i(43);var s=i(29);const n=document.webL10n;t.GenericL10n=class{constructor(i){this._lang=i,this._ready=new Promise((e,t)=>{n.setLanguage((0,s.fixupLangCode)(i),()=>{e(n)})})}async getLanguage(){const e=await this._ready;return e.getLanguage()}async getDirection(){const e=await this._ready;return e.getDirection()}async get(e,t=null,i=(0,s.getL10nFallback)(e,t)){const n=await this._ready;return n.get(e,t,i)}async translate(e){const t=await this._ready;return t.translate(e)}}},()=>{function _(e,t,i){t=t||function(e){},i=i||function(){};var n=new XMLHttpRequest;n.open("GET",e,s),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=utf-8"),n.onreadystatechange=function(){4==n.readyState&&(200==n.status||0===n.status?t(n.responseText):i())},n.onerror=i,n.ontimeout=i;try{n.send(null)}catch(e){i()}}function u(e,f,s,t){var v=e.replace(/[^\/]*$/,"")||"./";function i(e,t){var h={},d=/^\s*|\s*$/,c=/^\s*#|^\s*$/,u=/^\s*\[(.*)\]\s*$/,p=/^\s*@import\s+url\((.*)\)\s*$/i,g=/^([^=\s]*)\s*=\s*(.+)$/;function m(e,i,n){var s=e.replace(d,"").split(/[\r\n]+/),a="*",r=f.split("-",1)[0],o=!1,l="";!function e(){for(;;){if(!s.length)return void n();var t=s.shift();if(!c.test(t)){if(i){if(l=u.exec(t)){a=l[1].toLowerCase(),o="*"!==a&&a!==f&&a!==r;continue}if(o)continue;if(l=p.exec(t))return void function(e,t){_(e,function(e){m(e,!1,t)},function(){console.warn(e+" not found."),t()})}(v+l[1],e)}(t=t.match(g))&&3==t.length&&(h[t[1]]=(t=t[2]).lastIndexOf("\\")<0?t:t.replace(/\\\\/g,"\\").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\b/g,"\b").replace(/\\f/g,"\f").replace(/\\{/g,"{").replace(/\\}/g,"}").replace(/\\"/g,'"').replace(/\\'/g,"'"))}}}()}m(e,!0,function(){t(h)})}_(e,function(e){m+=e,i(e,function(e){for(var t in e){var i,n=t.lastIndexOf("."),n=0<n?(i=t.substring(0,n),t.substring(n+1)):(i=t,d);g[i]||(g[i]={}),g[i][n]=e[t]}s&&s()})},t)}function i(e,t){e=e&&e.toLowerCase(),t=t||function(){},g={},f=m="",f=e;var i=p.querySelectorAll('link[type="application/l10n"]'),n=i.length;if(0!==n)for(var s,a=0,r=function(){n<=++a&&(t(),v="complete")},o=0;o<n;o++)new c(i[o]).load(e,r);else{var l=(s=p.querySelector('script[type="application/l10n"]'))?JSON.parse(s.innerHTML):null;if(l&&l.locales&&l.default_locale){if(console.log("using the embedded JSON directory, early way out"),!(g=l.locales[e])){var h,d=l.default_locale.toLowerCase();for(h in l.locales){if((h=h.toLowerCase())===e){g=l.locales[e];break}h===d&&(g=l.locales[d])}}t()}else console.log("no resource to load, early way out");v="complete"}function c(e){var i=e.href;this.load=function(e,t){u(i,e,t,function(){console.warn(i+" not found."),console.warn('"'+e+'" resource not found'),f="",t()})}}}function h(e,t,i){var n=g[e];if(!n){if(console.warn("#"+e+" is undefined."),!i)return null;n=i}var s,a,r={};for(s in n)a=function(e,i,n){return e.replace(/\{\{\s*(.+?)\s*\}\}/g,function(e,t){return i&&t in i?i[t]:t in g?g[t]:(console.log("argument {{"+t+"}} for #"+n+" is undefined."),e)})}(a=function(e,t,i,n){var s=/\{\[\s*([a-zA-Z]+)\(([a-zA-Z]+)\)\s*\]\}/.exec(e);if(!s||!s.length)return e;var a,r=s[1],s=s[2];t&&s in t?a=t[s]:s in g&&(a=g[s]);r in c&&(r=c[r],e=r(e,a,i,n));return e}(a=n[s],t,e,s),t,e),r[s]=a;return r}function a(e){var t=function(e){if(!e)return{};var t=e.getAttribute("data-l10n-id"),i=e.getAttribute("data-l10n-args"),e={};if(i)try{e=JSON.parse(i)}catch(e){console.warn("could not parse arguments for #"+t)}return{id:t,args:e}}(e);if(t.id){var i=h(t.id,t.args);if(i){if(i[d]){if(0===function(e){if(e.children)return e.children.length;if(void 0!==e.childElementCount)return e.childElementCount;for(var t=0,i=0;i<e.childNodes.length;i++)t+=1===e.nodeType?1:0;return t}(e))e[d]=i[d];else{for(var n,s=e.childNodes,a=!1,r=0,o=s.length;r<o;r++)3===s[r].nodeType&&/\S/.test(s[r].nodeValue)&&(a?s[r].nodeValue="":(s[r].nodeValue=i[d],a=!0));a||(n=p.createTextNode(i[d]),e.insertBefore(n,e.firstChild))}delete i[d]}for(var l in i)e[l]=i[l]}else console.warn("#"+t.id+" is undefined.")}}var e,p,g,m,d,f,c,v,s;document.webL10n=(e=window,p=document,g={},d="textContent",v="loading",s=!(f=m=""),(c={}).plural=function(e,t,i,n){var s,a=parseFloat(t);if(isNaN(a))return e;if(n!=d)return e;function r(e,t){return-1!==t.indexOf(e)}function o(e,t,i){return t<=e&&e<=i}c._pluralRules||(c._pluralRules=(s={0:function(e){return"other"},1:function(e){return o(e%100,3,10)?"few":0===e?"zero":o(e%100,11,99)?"many":2==e?"two":1==e?"one":"other"},2:function(e){return 0!==e&&e%10==0?"many":2==e?"two":1==e?"one":"other"},3:function(e){return 1==e?"one":"other"},4:function(e){return o(e,0,1)?"one":"other"},5:function(e){return o(e,0,2)&&2!=e?"one":"other"},6:function(e){return 0===e?"zero":e%10==1&&e%100!=11?"one":"other"},7:function(e){return 2==e?"two":1==e?"one":"other"},8:function(e){return o(e,3,6)?"few":o(e,7,10)?"many":2==e?"two":1==e?"one":"other"},9:function(e){return 0===e||1!=e&&o(e%100,1,19)?"few":1==e?"one":"other"},10:function(e){return o(e%10,2,9)&&!o(e%100,11,19)?"few":e%10!=1||o(e%100,11,19)?"other":"one"},11:function(e){return o(e%10,2,4)&&!o(e%100,12,14)?"few":e%10==0||o(e%10,5,9)||o(e%100,11,14)?"many":e%10==1&&e%100!=11?"one":"other"},12:function(e){return o(e,2,4)?"few":1==e?"one":"other"},13:function(e){return o(e%10,2,4)&&!o(e%100,12,14)?"few":1!=e&&o(e%10,0,1)||o(e%10,5,9)||o(e%100,12,14)?"many":1==e?"one":"other"},14:function(e){return o(e%100,3,4)?"few":e%100==2?"two":e%100==1?"one":"other"},15:function(e){return 0===e||o(e%100,2,10)?"few":o(e%100,11,19)?"many":1==e?"one":"other"},16:function(e){return e%10==1&&11!=e?"one":"other"},17:function(e){return 3==e?"few":0===e?"zero":6==e?"many":2==e?"two":1==e?"one":"other"},18:function(e){return 0===e?"zero":o(e,0,2)&&0!==e&&2!=e?"one":"other"},19:function(e){return o(e,2,10)?"few":o(e,0,1)?"one":"other"},20:function(e){return!o(e%10,3,4)&&e%10!=9||o(e%100,10,19)||o(e%100,70,79)||o(e%100,90,99)?e%1e6==0&&0!==e?"many":e%10!=2||r(e%100,[12,72,92])?e%10!=1||r(e%100,[11,71,91])?"other":"one":"two":"few"},21:function(e){return 0===e?"zero":1==e?"one":"other"},22:function(e){return o(e,0,1)||o(e,11,99)?"one":"other"},23:function(e){return o(e%10,1,2)||e%20==0?"one":"other"},24:function(e){return o(e,3,10)||o(e,13,19)?"few":r(e,[2,12])?"two":r(e,[1,11])?"one":"other"}},(t={af:3,ak:4,am:4,ar:1,asa:3,az:0,be:11,bem:3,bez:3,bg:3,bh:4,bm:0,bn:3,bo:0,br:20,brx:3,bs:11,ca:3,cgg:3,chr:3,cs:12,cy:17,da:3,de:3,dv:3,dz:0,ee:3,el:3,en:3,eo:3,es:3,et:3,eu:3,fa:0,ff:5,fi:3,fil:4,fo:3,fr:5,fur:3,fy:3,ga:8,gd:24,gl:3,gsw:3,gu:3,guw:4,gv:23,ha:3,haw:3,he:2,hi:4,hr:11,hu:0,id:0,ig:0,ii:0,is:3,it:3,iu:7,ja:0,jmc:3,jv:0,ka:0,kab:5,kaj:3,kcg:3,kde:0,kea:0,kk:3,kl:3,km:0,kn:0,ko:0,ksb:3,ksh:21,ku:3,kw:7,lag:18,lb:3,lg:3,ln:4,lo:0,lt:10,lv:6,mas:3,mg:4,mk:16,ml:3,mn:3,mo:9,mr:3,ms:0,mt:15,my:0,nah:3,naq:7,nb:3,nd:3,ne:3,nl:3,nn:3,no:3,nr:3,nso:4,ny:3,nyn:3,om:3,or:3,pa:3,pap:3,pl:13,ps:3,pt:3,rm:3,ro:9,rof:3,ru:11,rwk:3,sah:0,saq:3,se:7,seh:3,ses:0,sg:0,sh:11,shi:19,sk:12,sl:14,sma:7,smi:7,smj:7,smn:7,sms:7,sn:3,so:3,sq:3,sr:11,ss:3,ssy:3,st:3,sv:3,sw:3,syr:3,ta:3,te:3,teo:3,th:0,ti:4,tig:3,tk:3,tl:4,tn:3,to:0,tr:0,ts:3,tzm:22,uk:11,ur:3,ve:3,vi:0,vun:3,wa:4,wae:3,wo:0,xh:3,xog:3,yo:0,zh:0,zu:3}[(l=f).replace(/-.*$/,"")])in s?s[t]:(console.warn("plural form unknown for ["+l+"]"),function(){return"other"})));var l="["+c._pluralRules(a)+"]";return 0===a&&i+"[zero]"in g?e=g[i+"[zero]"][n]:1==a&&i+"[one]"in g?e=g[i+"[one]"][n]:2==a&&i+"[two]"in g?e=g[i+"[two]"][n]:i+l in g?e=g[i+l][n]:i+"[other]"in g&&(e=g[i+"[other]"][n]),e},{get:function(e,t,i){var n=e.lastIndexOf("."),s=d;0<n&&(s=e.substring(n+1),e=e.substring(0,n)),i&&((a={})[s]=i);var a=h(e,t,a);return a&&s in a?a[s]:"{{"+e+"}}"},getData:function(){return g},getText:function(){return m},getLanguage:function(){return f},setLanguage:function(e,t){i(e,function(){t&&t()})},getDirection:function(){var e=f.split("-",1)[0];return 0<=["ar","he","fa","ps","ur"].indexOf(e)?"rtl":"ltr"},translate:function(e){e=e||p.documentElement;for(var t,i=(t=e)?t.querySelectorAll("*[data-l10n-id]"):[],n=i.length,s=0;s<n;s++)a(i[s]);a(e)},getReadyState:function(){return v},ready:function(t){t&&("complete"==v||"interactive"==v?e.setTimeout(function(){t()}):p.addEventListener&&p.addEventListener("localized",function e(){p.removeEventListener("localized",e),t()}))}})},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.docPropertiesLookup=async function(e){var t="".split("#")[0];let{info:i,metadata:n,contentDispositionFilename:s,contentLength:a}=await e.getMetadata();{var r;a||(r=(await e.getDownloadInfo())["length"],a=r)}return{...i,baseURL:t,filesize:a,filename:s||(0,o.getPdfFilenameFromUrl)(""),metadata:n?.getRaw(),authors:n?.get("dc:creator"),numPages:e.numPages,URL:""}},t.GenericScripting=void 0;var o=i(4);t.GenericScripting=class{constructor(e){this._ready=(0,o.loadScript)(e,!0).then(()=>window.pdfjsSandbox.QuickJSSandbox())}async createSandbox(e){const t=await this._ready;t.create(e)}async dispatchEventInSandbox(e){const t=await this._ready;t.dispatchEvent(e)}async destroySandbox(){const e=await this._ready;e.nukeSandbox()}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPrintService=o;var l=i(4),n=i(2),s=i(1),a=i(46);let h=null,r=null;function o(e,t,i,n,s=null,a){this.pdfDocument=e,this.pagesOverview=t,this.printContainer=i,this._printResolution=n||150,this._optionalContentConfigPromise=s||e.getOptionalContentConfig(),this.l10n=a,this.currentPage=-1,this.scratchCanvas=document.createElement("canvas")}o.prototype={layout(){this.throwIfInactive();const e=document.querySelector("body");e.setAttribute("data-pdfjsprinting",!0),this.pagesOverview.every(function(e){return e.width===this.pagesOverview[0].width&&e.height===this.pagesOverview[0].height},this)||console.warn("Not all pages have the same size. The printed result may be incorrect!"),this.pageStyleSheet=document.createElement("style");var t=this.pagesOverview[0];this.pageStyleSheet.textContent="@page { size: "+t.width+"pt "+t.height+"pt;}",e.appendChild(this.pageStyleSheet)},destroy(){if(h===this){this.printContainer.textContent="";const e=document.querySelector("body");e.removeAttribute("data-pdfjsprinting"),this.pageStyleSheet&&(this.pageStyleSheet.remove(),this.pageStyleSheet=null),this.scratchCanvas.width=this.scratchCanvas.height=0,this.scratchCanvas=null,h=null,m().then(function(){"printServiceOverlay"===r.active&&r.close("printServiceOverlay")})}},renderPages(){if(this.pdfDocument.isPureXfa)return(0,a.getXfaHtmlForPrinting)(this.printContainer,this.pdfDocument),Promise.resolve();const n=this.pagesOverview.length,s=(e,t)=>{if(this.throwIfInactive(),++this.currentPage>=n)return p(n,n,this.l10n),void e();var i=this.currentPage;p(i,n,this.l10n),function(e,t,i,n,s){const a=h.scratchCanvas,r=n/l.PixelsPerInch.PDF;a.width=Math.floor(i.width*r),a.height=Math.floor(i.height*r);const o=a.getContext("2d");return o.save(),o.fillStyle="rgb(255, 255, 255)",o.fillRect(0,0,a.width,a.height),o.restore(),e.getPage(t).then(function(e){var t={canvasContext:o,transform:[r,0,0,r,0,0],viewport:e.getViewport({scale:1,rotation:i.rotation}),intent:"print",annotationMode:l.AnnotationMode.ENABLE_STORAGE,optionalContentConfigPromise:s};return e.render(t).promise})}(this.pdfDocument,i+1,this.pagesOverview[i],this._printResolution,this._optionalContentConfigPromise).then(this.useRenderedPage.bind(this)).then(function(){s(e,t)},t)};return new Promise(s)},useRenderedPage(){this.throwIfInactive();const i=document.createElement("img"),e=this.scratchCanvas;"toBlob"in e&&!s.compatibilityParams.disableCreateObjectURL?e.toBlob(function(e){i.src=URL.createObjectURL(e)}):i.src=e.toDataURL();const t=document.createElement("div");return t.className="printedPage",t.appendChild(i),this.printContainer.appendChild(t),new Promise(function(e,t){i.onload=e,i.onerror=t})},performPrint(){return this.throwIfInactive(),new Promise(e=>{setTimeout(()=>{this.active?(d.call(window),setTimeout(e,20)):e()},0)})},get active(){return this===h},throwIfInactive(){if(!this.active)throw new Error("This print request was cancelled or completed.")}};const d=window.print;function c(e){const t=document.createEvent("CustomEvent");t.initCustomEvent(e,!1,!1,"custom"),window.dispatchEvent(t)}function u(){h&&(h.destroy(),c("afterprint"))}function p(e,t,i){const n=document.getElementById("printServiceOverlay");t=Math.round(100*e/t);const s=n.querySelector("progress"),a=n.querySelector(".relative-progress");s.value=t,i.get("print_progress_percent",{progress:t}).then(e=>{a.textContent=e})}window.print=function(){if(h)console.warn("Ignored window.print() because of a pending print job.");else{m().then(function(){h&&r.open("printServiceOverlay")});try{c("beforeprint")}finally{if(!h)return console.error("Expected print service to be initialized."),void m().then(function(){"printServiceOverlay"===r.active&&r.close("printServiceOverlay")});const e=h;h.renderPages().then(function(){return e.performPrint()}).catch(function(){}).then(function(){e.active&&u()})}}},window.addEventListener("keydown",function(e){80!==e.keyCode||!e.ctrlKey&&!e.metaKey||e.altKey||e.shiftKey&&!window.chrome&&!window.opera||(window.print(),e.preventDefault(),e.stopImmediatePropagation?e.stopImmediatePropagation():e.stopPropagation())},!0),"onbeforeprint"in window&&(i=function(e){"custom"!==e.detail&&e.stopImmediatePropagation&&e.stopImmediatePropagation()},window.addEventListener("beforeprint",i),window.addEventListener("afterprint",i));let g;function m(){if(!g){if(r=n.PDFViewerApplication.overlayManager,!r)throw new Error("The overlay manager has not yet been initialized.");g=r.register("printServiceOverlay",document.getElementById("printServiceOverlay"),u,!0),document.getElementById("printCancel").onclick=u}return g}n.PDFPrintServiceFactory.instance={supportsPrinting:!0,createPrintService(e,t,i,n,s,a){if(h)throw new Error("The print service is created and active.");return h=new o(e,t,i,n,s,a),h}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getXfaHtmlForPrinting=function(e,t){const i=t.allXfaHtml,n=new d.DefaultXfaLayerFactory,s=Math.round(100*h.PixelsPerInch.PDF_TO_CSS_UNITS)/100;for(const r of i.children){const o=document.createElement("div");o.className="xfaPrintedPage",e.appendChild(o);const l=n.createXfaLayerBuilder(o,null,t.annotationStorage,r);var a=(0,h.getXfaPageViewport)(r,{scale:s});l.render(a,"print")}};var h=i(4),d=i(34)}],n={};function s(e){var t=n[e];if(void 0!==t)return t.exports;t=n[e]={exports:{}};return i[e](t,t.exports,s),t.exports}var a={};(()=>{var e=a;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PDFViewerApplicationOptions",{enumerable:!0,get:function(){return t.AppOptions}}),Object.defineProperty(e,"PDFViewerApplication",{enumerable:!0,get:function(){return i.PDFViewerApplication}});var t=s(1),i=s(2);function n(){var e,e=(e={container:document.getElementById("errorWrapper"),errorMessage:document.getElementById("errorMessage"),closeButton:document.getElementById("errorClose"),errorMoreInfo:document.getElementById("errorMoreInfo"),moreInfoButton:document.getElementById("errorShowMore"),lessInfoButton:document.getElementById("errorShowLess")},{appContainer:document.body,mainContainer:document.getElementById("viewerContainer"),viewerContainer:document.getElementById("viewer"),eventBus:null,toolbar:{container:document.getElementById("toolbarViewer"),numPages:document.getElementById("numPages"),pageNumber:document.getElementById("pageNumber"),scaleSelect:document.getElementById("scaleSelect"),customScaleOption:document.getElementById("customScaleOption"),previous:document.getElementById("previous"),next:document.getElementById("next"),zoomIn:document.getElementById("zoomIn"),zoomOut:document.getElementById("zoomOut"),viewFind:document.getElementById("viewFind"),openFile:document.getElementById("openFile"),print:document.getElementById("print"),presentationModeButton:document.getElementById("presentationMode"),download:document.getElementById("download"),viewBookmark:document.getElementById("viewBookmark")},secondaryToolbar:{toolbar:document.getElementById("secondaryToolbar"),toggleButton:document.getElementById("secondaryToolbarToggle"),toolbarButtonContainer:document.getElementById("secondaryToolbarButtonContainer"),presentationModeButton:document.getElementById("secondaryPresentationMode"),openFileButton:document.getElementById("secondaryOpenFile"),printButton:document.getElementById("secondaryPrint"),downloadButton:document.getElementById("secondaryDownload"),viewBookmarkButton:document.getElementById("secondaryViewBookmark"),firstPageButton:document.getElementById("firstPage"),lastPageButton:document.getElementById("lastPage"),pageRotateCwButton:document.getElementById("pageRotateCw"),pageRotateCcwButton:document.getElementById("pageRotateCcw"),cursorSelectToolButton:document.getElementById("cursorSelectTool"),cursorHandToolButton:document.getElementById("cursorHandTool"),scrollVerticalButton:document.getElementById("scrollVertical"),scrollHorizontalButton:document.getElementById("scrollHorizontal"),scrollWrappedButton:document.getElementById("scrollWrapped"),spreadNoneButton:document.getElementById("spreadNone"),spreadOddButton:document.getElementById("spreadOdd"),spreadEvenButton:document.getElementById("spreadEven"),documentPropertiesButton:document.getElementById("documentProperties")},sidebar:{outerContainer:document.getElementById("outerContainer"),viewerContainer:document.getElementById("viewerContainer"),toggleButton:document.getElementById("sidebarToggle"),thumbnailButton:document.getElementById("viewThumbnail"),outlineButton:document.getElementById("viewOutline"),attachmentsButton:document.getElementById("viewAttachments"),layersButton:document.getElementById("viewLayers"),thumbnailView:document.getElementById("thumbnailView"),outlineView:document.getElementById("outlineView"),attachmentsView:document.getElementById("attachmentsView"),layersView:document.getElementById("layersView"),outlineOptionsContainer:document.getElementById("outlineOptionsContainer"),currentOutlineItemButton:document.getElementById("currentOutlineItem")},sidebarResizer:{outerContainer:document.getElementById("outerContainer"),resizer:document.getElementById("sidebarResizer")},findBar:{bar:document.getElementById("findbar"),toggleButton:document.getElementById("viewFind"),findField:document.getElementById("findInput"),highlightAllCheckbox:document.getElementById("findHighlightAll"),caseSensitiveCheckbox:document.getElementById("findMatchCase"),entireWordCheckbox:document.getElementById("findEntireWord"),findMsg:document.getElementById("findMsg"),findResultsCount:document.getElementById("findResultsCount"),findPreviousButton:document.getElementById("findPrevious"),findNextButton:document.getElementById("findNext")},passwordOverlay:{overlayName:"passwordOverlay",container:document.getElementById("passwordOverlay"),label:document.getElementById("passwordText"),input:document.getElementById("password"),submitButton:document.getElementById("passwordSubmit"),cancelButton:document.getElementById("passwordCancel")},documentProperties:{overlayName:"documentPropertiesOverlay",container:document.getElementById("documentPropertiesOverlay"),closeButton:document.getElementById("documentPropertiesClose"),fields:{fileName:document.getElementById("fileNameField"),fileSize:document.getElementById("fileSizeField"),title:document.getElementById("titleField"),author:document.getElementById("authorField"),subject:document.getElementById("subjectField"),keywords:document.getElementById("keywordsField"),creationDate:document.getElementById("creationDateField"),modificationDate:document.getElementById("modificationDateField"),creator:document.getElementById("creatorField"),producer:document.getElementById("producerField"),version:document.getElementById("versionField"),pageCount:document.getElementById("pageCountField"),pageSize:document.getElementById("pageSizeField"),linearized:document.getElementById("linearizedField")}},errorWrapper:e,printContainer:document.getElementById("printContainer"),openFileInputName:"fileInput",debuggerScriptPath:"./debugger.js"});const t=document.createEvent("CustomEvent");t.initCustomEvent("webviewerloaded",!0,!0,{source:window});try{parent.document.dispatchEvent(t)}catch(e){console.error(`webviewerloaded: ${e}`),document.dispatchEvent(t)}i.PDFViewerApplication.run(e)}window.PDFViewerApplication=i.PDFViewerApplication,window.PDFViewerApplicationOptions=t.AppOptions,s(39),s(45),document.blockUnblockOnload&&document.blockUnblockOnload(!0),"interactive"===document.readyState||"complete"===document.readyState?n():document.addEventListener("DOMContentLoaded",n,!0)})()})();