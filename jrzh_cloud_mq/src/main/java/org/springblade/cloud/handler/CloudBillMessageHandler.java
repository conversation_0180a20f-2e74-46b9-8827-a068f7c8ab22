package org.springblade.cloud.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.cloud.constant.CloudConstant;
import org.springblade.cloud.entity.CloudAssets;
import org.springblade.cloud.entity.CloudPay;
import org.springblade.cloud.service.ICloudAssetsService;
import org.springblade.cloud.service.ICloudPayService;
import org.springblade.cloud.service.ICloudTrackCodeService;
import org.springblade.common.enums.QuotaUseDetailsEnum;
import org.springblade.common.enums.cloud.CloudResolutionStatusEnum;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.customer.service.IQuotaUseDetailsService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 云信开单操作
 * <AUTHOR>
 */
@Component("cloud_bill_sign")
@RequiredArgsConstructor
@TenantIgnore
public class CloudBillMessageHandler implements MessageHandler {

	/**
	 * 云信资产 服务器类
	 */
	private final ICloudAssetsService cloudAssetsService;

	/**
	 * 云信兑付表 服务类
	 */
	private final ICloudPayService cloudPayService;

	/**
	 * 云信资产编号关联
	 */
	private final ICloudTrackCodeService cloudTrackCodeService;

	private final IQuotaUseDetailsService quotaUseDetailsService;



	@Override
	public void handler(DelayMessage delayMessage) {
		String msg = delayMessage.getMsg();
		Integer status = delayMessage.getStatus();
		switch (status) {
			case 1:
				cloudBillSignHandler(msg);
				break;
			case 2:
				cloudBillTransferSignHandler(delayMessage);
				break;
			default:
		}
	}

	/**
	 * 云信开单-待签收，根据id查询云信资产信息，状态是否为待签收，是的话修改状态为超时作废
	 * @param msg
	 */
	@Transactional(rollbackFor = Exception.class)
	public void cloudBillSignHandler(String msg) {
		Long cloudAssetsId = Long.valueOf(msg);
		CloudAssets cloudAssets = cloudAssetsService.getById(cloudAssetsId);
		//云信资产状态修改为超时作废
		if (cloudAssets.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_ZERO){
			cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_THREE);
			//额度扣除
			cloudAssetsService.quotaAmount(cloudAssets.getEnterpriseId(),cloudAssets.getAmount(),2);
			//核心企业兑付信息状态改变
			CloudPay cloudPay = cloudPayService.getOne(Wrappers.<CloudPay>lambdaQuery().eq(CloudPay::getCloudCode, cloudAssets.getCloudCode()));
			cloudPay.setStatus(CloudConstant.CLOUD_STATUS_THREE);
			cloudPayService.saveOrUpdate(cloudPay);
			cloudAssetsService.updateById(cloudAssets);
			//修改额度历史状态
			Long quotaUseDetailsId = cloudPay.getQuotaUseDetailsId();
			Integer code = QuotaUseDetailsEnum.CLOUD_FAIL.getCode();
			quotaUseDetailsService.updateStatus(quotaUseDetailsId,code);
		}
	}

	/**
	 * 云信开单-转让-待签收
	 * @param delayMessage
	 */
	@Transactional(rollbackFor = Exception.class)
	public void cloudBillTransferSignHandler(DelayMessage delayMessage) {
		List<Long> ids = delayMessage.getIds();
		List<CloudAssets> cloudAssetsList = cloudAssetsService.listByIds(ids);
		for (CloudAssets cloudAssets : cloudAssetsList) {
			if (cloudAssets.getStatus() == CloudConstant.CLOUD_ASSETS_STATUS_ZERO){
				cloudAssets.setStatus(CloudConstant.CLOUD_ASSETS_STATUS_THREE);
				cloudAssets.setResolutionStatus(CloudResolutionStatusEnum.CLOUD_RESOLUTION_STATUS_ASSIGNMENT_FAILURE.getCode());
				cloudAssetsService.commonSaveTrackRecord(cloudAssets.getCloudCode(),2,cloudAssets.getAmount());
			}
		}
		cloudAssetsService.updateBatchById(cloudAssetsList);
	}

}
