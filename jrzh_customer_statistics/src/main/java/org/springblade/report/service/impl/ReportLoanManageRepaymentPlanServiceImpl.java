package org.springblade.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.QualityProductsEnum;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaNewVo;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.entity.RepaymentFee;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.mapper.LoanManageRepaymentPlanMapper;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.IRepaymentFeeService;
import org.springblade.loan.vo.LoanManageIouNewVo;
import org.springblade.plugin.jimureport.config.JimuUtils;
import org.springblade.product.common.entity.BaseProduct;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.report.dto.CreditReportDto;
import org.springblade.report.enums.HomeFinancingOrQuotaTypeEnum;
import org.springblade.report.handler.IProductLoanReportStrategy;
import org.springblade.report.service.IReportLoanManageRepaymentPlanService;
import org.springblade.report.vo.CreditPassVO;
import org.springblade.report.vo.CreditReport;
import org.springblade.resource.cache.DictBizCache;
import org.springblade.system.utils.UserUtils;
import org.springblade.vo.HomeNewFinancingAndQuotaVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ReportLoanManageRepaymentPlanServiceImpl implements IReportLoanManageRepaymentPlanService {
    private final ILoanManageIouService loanManageIouService;
    private final LoanManageIouMapper loanManageIouMapper;

    private final LoanManageRepaymentPlanMapper loanManageRepaymentPlanMapper;
    private final LoanManageRepaymentMapper loanManageRepaymentMapper;
    private final ProductDirector productDirector;
    private final IRepaymentFeeService repaymentFeeService;

    private final IEnterpriseQuotaService enterpriseQuotaService;

    @Override
    public List<CreditReport> creditReport(List<String> timeList, List<Integer> type, String tentId) {

        List<CreditReport> creditReports = new ArrayList<>();
        for (String time : timeList) {
            CreditReport creditReport = initCreditReport(time, type, tentId);
            //如果等于-1表示需要统计系统内所有的产品业务 将其循环进行统计
            for (Integer goodsType : type) {
                IProductLoanReportStrategy productLoanReportStrategy = IProductLoanReportStrategy.getInstance(goodsType);
                if (ObjectUtil.isNotEmpty(productLoanReportStrategy)) {
                    CreditReport typeOfProductReport = productLoanReportStrategy.creditReport(time, tentId);
                    //累计计算
                    accumulateCal(creditReport, typeOfProductReport);
                }
            }
            //计算新增客户
            Set<Long> customerNewCountByTime = loanManageIouService.getCustomerNewCountByTime(time, type, tentId);
            creditReport.setNewCustomers(customerNewCountByTime.size());
            creditReports.add(creditReport);
        }
        return creditReports;
    }

    @Override
    public List<CreditPassVO> actualRepaymentReport(CreditReportDto dto) {
        new ArrayList<>();
        List<Integer> goodsTypeList = dto.getType() == -1 ? productDirector.getAllType() : Arrays.asList(dto.getType());

        List<LoanManageRepayment> loanRepayments = loanManageRepaymentMapper.actualRepaymentByDtoList(dto.getQueryTime(), goodsTypeList, dto.getTenantId());
        if (CollUtil.isEmpty(loanRepayments)) {
            return Collections.emptyList();
        }
        //费用
        Map<Long, List<RepaymentFee>> feeMap = repaymentFeeService.listByRepaymentIds(loanRepayments.stream().map(LoanManageRepayment::getId).collect(Collectors.toList())).stream()
                .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus()))
                .collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));
        List<Long> userIds = loanRepayments.stream().distinct().map(LoanManageRepayment::getUserId).collect(Collectors.toList());
        Map<Long, String> userName = UserUtils.mapUserName(userIds);
        return loanRepayments.stream().map(loan -> {
            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setRepaymentNo(loan.getRepaymentNo());
            vo.setFinanceNo(loan.getIouNo());
            vo.setUserName(userName.getOrDefault(loan.getUserId(), ""));
            vo.setType(QualityProductsEnum.getProductName(loan.getGoodsType()));
            vo.setPeriod(loan.getPeriod().toString());

            vo.setPayableAmount(loan.getPrincipal());
            vo.setActualPayable(loan.getActualAmount());
            BigDecimal reduce = feeMap.getOrDefault(loan.getId(), new ArrayList<>()).stream().map(RepaymentFee::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal otherExpenses = loan.getServiceCharge().add(loan.getPenaltyInterest());
            vo.setOtherExpenses(reduce);
            vo.setInterest(loan.getInterest());

            vo.setRepaymentTime(loan.getRepaymentTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            vo.setRepaymentType(RepaymentConstant.RepaymentTypeEnum.getNameByCode(loan.getRepaymentType()));
            vo.setStatus(RepaymentConstant.RepaymentStatusEnum.getNameByCode(loan.getStatus()));
            return vo;
        }).collect(Collectors.toList());

    }

    @Override
    public List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto) {
        List<Integer> goodsTypeList = dto.getType() == -1 ? productDirector.getAllType() : Arrays.asList(dto.getType());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans =
                loanManageRepaymentPlanMapper.payableByDtoList(dto.getQueryTime(), goodsTypeList, dto.getTenantId());
        if (CollUtil.isEmpty(loanManageRepaymentPlans)) {
            return Collections.emptyList();
        }
        List<Long> userIds = loanManageRepaymentPlans.stream().distinct().map(LoanManageRepaymentPlan::getUserId).collect(Collectors.toList());
        List<Long> goodsIds = loanManageRepaymentPlans.stream().distinct().map(LoanManageRepaymentPlan::getGoodsId).collect(Collectors.toList());
        Map<Long, String> goodsNameMap = productDirector.selectList(goodsIds).stream().collect(Collectors.toMap(BaseEntity::getId, BaseProduct::getGoodsName));
        Map<Long, String> userName = UserUtils.mapUserName(userIds);
        return loanManageRepaymentPlans.stream().map(plan -> {
            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setType(QualityProductsEnum.getProductName(plan.getGoodsType()));
            vo.setFinanceNo(plan.getFinanceNo());
            vo.setUserId(plan.getUserId());
            vo.setUserName(userName.getOrDefault(plan.getUserId(), ""));
            vo.setGoodsName(goodsNameMap.getOrDefault(plan.getGoodsId(), ""));
            vo.setPeriod(plan.getPeriod().toString());
            vo.setRepaymentTime(LocalDateTimeUtil.formatNormal(plan.getRepaymentTime()));
            vo.setStatus(RepaymentConstant.getRepaymentPlanRepaymentStatusStr(plan.getRepaymentStatus()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CreditReport> customerCreditReport(Integer year, String month, List<Integer> type, Long userId, String tentId) {
        List<String> timeList = new ArrayList<>(32);
        timeList = JimuUtils.getDateList(month, year);
        ArrayList<CreditReport> creditReports = new ArrayList<>();
        for (String time : timeList) {
            CreditReport creditReport = initCreditReport(time, type, tentId);
            for (Integer goodsType : type) {
                IProductLoanReportStrategy productLoanReportStrategy = IProductLoanReportStrategy.getInstance(goodsType);
                if (ObjectUtil.isNotEmpty(productLoanReportStrategy)) {
                    CreditReport typeOfProductReport = productLoanReportStrategy.customerCreditReport(time, userId, tentId);
                    //累计计算
                    accumulateCal(creditReport, typeOfProductReport);
                }
            }
            creditReports.add(creditReport);
        }
        return creditReports;
    }

    private List<CreditPassVO> creditCreditReport(CreditReportDto dto, List<LoanManageIou> loanManageIous) {
        if (CollUtil.isEmpty(loanManageIous)) {
            return Collections.emptyList();
        }
        List<Long> userIds = loanManageIous.stream().distinct().map(LoanManageIou::getUserId).collect(Collectors.toList());
        List<Long> goodsIds = loanManageIous.stream().distinct().map(LoanManageIou::getGoodsId).collect(Collectors.toList());
        Map<Long, String> goodsNameMap = productDirector.selectList(goodsIds).stream().collect(Collectors.toMap(BaseEntity::getId, BaseProduct::getGoodsName));
        Map<Long, String> userName = UserUtils.mapUserName(userIds);
        return loanManageIous.stream().map(iou -> {
            CreditPassVO vo = new CreditPassVO();
            vo.setQueryTime(dto.getQueryTime());
            vo.setType(QualityProductsEnum.getProductName(iou.getGoodsType()));
            vo.setFinanceNo(iou.getFinanceNo());
            vo.setUserId(iou.getUserId());
            vo.setUserName(userName.getOrDefault(iou.getUserId(), ""));
            vo.setGoodsName(goodsNameMap.getOrDefault(iou.getGoodsId(), ""));
            vo.setIouAmount(iou.getIouAmount());
            vo.setLoanTime(LocalDateTimeUtil.formatNormal(iou.getLoanTime()));
            vo.setStatus(DictBizCache.getValue("loan_manage_iou_status", iou.getStatus()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CreditPassVO> creditNewCreditReport(CreditReportDto dto) {
        List<Integer> goodsTypeList = dto.getType() != -1 ? Arrays.asList(dto.getType()) : productDirector.getAllType();
        //时间内新增订单
        List<LoanManageIou> loanManageIous = loanManageIouService.listNewByTime(dto.getQueryTime(), goodsTypeList, dto.getTenantId());
        return creditCreditReport(dto, loanManageIous);
    }

    @Override
    public List<CreditPassVO> creditNewCustomerReport(CreditReportDto dto) {
        List<Integer> goodsTypeList = dto.getType() != -1 ? Arrays.asList(dto.getType()) : productDirector.getAllType();
        //查询系统内新客户所关联订单
        List<LoanManageIou> loanManageIous = loanManageIouService.listCustomerNewByTime(dto.getQueryTime(), goodsTypeList, dto.getTenantId());
        List<CreditPassVO> voList = creditCreditReport(dto, loanManageIous);
        voList.stream().forEach(e -> {
            e.setNum(voList.size());
        });
        return voList;
    }

    @Override
    public List<CreditReport> repaymentAnalysisReport(List<Integer> type, Integer year, String month, String tentId) {
        List<String> timeList = new ArrayList<>(32);
        timeList = JimuUtils.getDateList(month, year);

        ArrayList<CreditReport> creditReports = new ArrayList<>();
        for (String time : timeList) {
            CreditReport creditReport = initCreditReport(time, type, tentId);
            for (Integer goodsType : type) {
                IProductLoanReportStrategy productLoanReportStrategy = IProductLoanReportStrategy.getInstance(goodsType);
                if (ObjectUtil.isNotEmpty(productLoanReportStrategy)) {
                    CreditReport typeOfProductReport = productLoanReportStrategy.repaymentAnalysisReport(time, year, tentId);
                    //累计计算
                    accumulateAnalysisCal(creditReport, typeOfProductReport);
                }
            }
            //正常回款率
            BigDecimal normRate = creditReport.getRepaymentNum() == 0 ? BigDecimal.ZERO
                    : BigDecimal.valueOf(creditReport.getNormalNum()).divide(BigDecimal.valueOf(creditReport.getRepaymentNum()), 2, CommonConstant.YES).multiply(BigDecimal.valueOf(100));
            //逾期率
            BigDecimal overDueRate = creditReport.getRepaymentNum() == 0 ? BigDecimal.ZERO
                    : BigDecimal.valueOf(creditReport.getOverDueNum()).divide(BigDecimal.valueOf(creditReport.getRepaymentNum()), 2, CommonConstant.YES).multiply(BigDecimal.valueOf(100));
            creditReport.setRepaymentRate(normRate);
            creditReport.setOverdueRate(overDueRate);
            creditReports.add(creditReport);
        }
        return creditReports;
    }

    @Override
    public Map<String, Object> getTotalHome() {
        Map<String,Object> resultMap = loanManageIouService.getTotalLoanManageIou();
        resultMap.put("totalQuota",enterpriseQuotaService.getTotalQuota().divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP));
        return resultMap;
    }

    @Override
    public List<HomeNewFinancingAndQuotaVo> selectHomeNewFinancing() {
        List<HomeNewFinancingAndQuotaVo> homeNewFinancingAndQuotaVos = new ArrayList<>();
        List<LoanManageIouNewVo> loanManageIouNews = loanManageIouService.selectLoanManageIouNewList();

        if(CollUtil.isNotEmpty(loanManageIouNews)){
            loanManageIouNews.forEach(e->{
                HomeNewFinancingAndQuotaVo homeNewFinancingAndQuotaVo = new HomeNewFinancingAndQuotaVo();
                homeNewFinancingAndQuotaVo.setDataTime(e.getLoanTime());
                homeNewFinancingAndQuotaVo.setCompanyName(e.getUserName());
                homeNewFinancingAndQuotaVo.setType(HomeFinancingOrQuotaTypeEnum.FINANCING.getType());
                BigDecimal bigDecimal = e.getLoanAmount().divide(BigDecimal.valueOf(10000),5, RoundingMode.HALF_UP);
                homeNewFinancingAndQuotaVo.setDataName("放款" + bigDecimal + "万");
                homeNewFinancingAndQuotaVos.add(homeNewFinancingAndQuotaVo);
            });
        }

        return homeNewFinancingAndQuotaVos;
    }


    @Override
    public List<HomeNewFinancingAndQuotaVo> selectHomeNewQuota() {
        List<HomeNewFinancingAndQuotaVo> homeNewFinancingAndQuotaVos = new ArrayList<>();

        List<EnterpriseQuotaNewVo> enterpriseQuotaNews = enterpriseQuotaService.selectEnterpriseQuotaNewList();

        if(CollUtil.isNotEmpty(enterpriseQuotaNews)){
            enterpriseQuotaNews.forEach(e->{
                HomeNewFinancingAndQuotaVo homeNewFinancingAndQuotaVo = new HomeNewFinancingAndQuotaVo();
                homeNewFinancingAndQuotaVo.setDataTime(e.getEffectiveTime());
                homeNewFinancingAndQuotaVo.setCompanyName(e.getUserName());
                homeNewFinancingAndQuotaVo.setType(HomeFinancingOrQuotaTypeEnum.QUOTA.getType());
                BigDecimal bigDecimal = e.getCreditAmount().divide(BigDecimal.valueOf(10000),5, RoundingMode.HALF_UP);
                homeNewFinancingAndQuotaVo.setDataName("授信" + bigDecimal + "万");
                homeNewFinancingAndQuotaVos.add(homeNewFinancingAndQuotaVo);
            });
        }

        return homeNewFinancingAndQuotaVos;
    }

    private void accumulateAnalysisCal(CreditReport currentCreditReport, CreditReport typeOfProductReport) {
        currentCreditReport.setPayableAmount(currentCreditReport.getPayableAmount().add(typeOfProductReport.getPayableAmount()));
        currentCreditReport.setPayableInterest(currentCreditReport.getPayableInterest().add(typeOfProductReport.getPayableInterest()));
        currentCreditReport.setPayInterest(currentCreditReport.getPayInterest().add(typeOfProductReport.getPayInterest()));
        currentCreditReport.setPayAmount(currentCreditReport.getPayAmount().add(typeOfProductReport.getPayAmount()));
        currentCreditReport.setOverDueMoney(currentCreditReport.getOverDueMoney().add(typeOfProductReport.getOverDueMoney()));
        currentCreditReport.setOverDueNum(currentCreditReport.getOverDueNum() + typeOfProductReport.getOverDueNum());
        currentCreditReport.setRepaymentNum(currentCreditReport.getRepaymentNum() + typeOfProductReport.getRepaymentNum());
        currentCreditReport.setNormalNum(currentCreditReport.getNormalNum() + typeOfProductReport.getNormalNum());
    }


    /**
     * 拼接时间 -01 格式
     * 2022 -> 2022-01-01
     * 2022-08 -> 2022-08-01
     * 2022-08-06 -> 2022-08-06
     *
     * @param time
     * @return
     */
    private String getDateStr(String time) {
        String[] split = time.split("-");
        int length = split.length;
        System.out.println("length = " + length);
        Integer num = 3 - (split.length);
        String str = "";
        if (num > 0) {
            for (Integer integer = 0; integer < num; integer++) {
                str = str + "-01";
            }
        }
        String timeDate = time + str;
        return timeDate;
    }

    /**
     * 累计统计
     *
     * @param currentCreditReport 当前信贷报表参数
     * @param typeOfProductReport 业务产品信贷参数
     */
    private void accumulateCal(CreditReport currentCreditReport, CreditReport typeOfProductReport) {
        currentCreditReport.setNumberOfLoans(currentCreditReport.getNumberOfLoans() + typeOfProductReport.getNumberOfLoans());//新增放款笔数
        currentCreditReport.setNumberOfUnPay(currentCreditReport.getNumberOfUnPay() + typeOfProductReport.getNumberOfUnPay());//未还款笔数
        currentCreditReport.setNumberOfPay(currentCreditReport.getNumberOfPay() + typeOfProductReport.getNumberOfPay());//已还款笔数
        currentCreditReport.setLoanAmount(currentCreditReport.getLoanAmount().add(typeOfProductReport.getLoanAmount()));//放款金额汇总
        currentCreditReport.setPayAmount(currentCreditReport.getPayAmount().add(typeOfProductReport.getPayAmount()));//实还金额汇总
        currentCreditReport.setPayInterest(currentCreditReport.getPayInterest().add(typeOfProductReport.getPayInterest()));//已还款利息汇总
        currentCreditReport.setNeedPayCount(currentCreditReport.getNeedPayCount() + typeOfProductReport.getNeedPayCount());//已还款利息汇总
        currentCreditReport.setOverDueMoney(currentCreditReport.getOverDueMoney().add(typeOfProductReport.getOverDueMoney()));//逾期金额汇总
    }

    private CreditReport initCreditReport(String time, List<Integer> type, String tenantId) {
        Integer typeStr = type.size() > 1 ? -1 : type.get(0);
        return CreditReport.builder()
                .newUserIds(new HashSet<>())
                .queryTime(time)
                .newCustomers(0)
                .numberOfLoans(0)
                .numberOfPay(0)
                .numberOfUnPay(0)
                .needPayCount(0)
                .loanAmount(BigDecimal.ZERO)
                .payableAmount(BigDecimal.ZERO)
                .payableInterest(BigDecimal.ZERO)
                .payAmount(BigDecimal.ZERO)
                .payInterest(BigDecimal.ZERO)
                .overDueNum(0)
                .overDueMoney(BigDecimal.ZERO)
                .type(typeStr.toString())
                .tenantId(tenantId)
                .repaymentNum(0)
                .normalNum(0)
                .build();
    }
//
//    @Override
//    public List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto) {
//        ArrayList<CreditPassVO> vos = new ArrayList<>();
//
//        //查询应收 代采应还的订单
//        List<LoanManageRepaymentPlan> payable = new ArrayList<>();
//        List<CreditPassVO> repaymentVo = new ArrayList<>();
//        if (dto.getType() != 3) {
//            payable = loanManageRepaymentPlanMapper.payableByDto(dto);
//            List<Long> userIds = payable.stream().map(LoanManageRepaymentPlan::getUserId).collect(Collectors.toList());
//            Map<Long, String> userNameMap = UserUtils.mapUserName(userIds);
//
//            repaymentVo = payable.stream().map(loan -> {
//                CreditPassVO vo = new CreditPassVO();
//                vo.setQueryTime(dto.getQueryTime());
//                vo.setType(loan.getGoodsType() == 1 ? "应收账款" : "代采融资");
//                vo.setFinanceNo(loan.getIouNo());
//                vo.setUserName(userNameMap.get(loan.getUserId()));
//
//                vo.setPeriod(loan.getPeriod().toString());
//                vo.setRepaymentTime(loan.getRepaymentTime().toString());
//                vo.setStatus(loan.getRepaymentStatus() == 1 ? "使用中" : "已结清");
//                return vo;
//            }).collect(Collectors.toList());
//        }
//
//        //云信应还订单
//        List<CloudFinancing> payableCloud = new ArrayList<>();
//        List<CreditPassVO> cloudVO = new ArrayList<>();
//        if (dto.getType() == 3 || dto.getType() == -1) {
//
//            payableCloud = cloudFinancingMapper.payableCloudByDtoList(dto);
//            cloudVO = payableCloud.stream().map(cloud -> {
//                CloudAssets assets = cloudAssetsMapper.selectById(cloud.getAssetsId());
//                CreditPassVO vo = new CreditPassVO();
//                vo.setQueryTime(dto.getQueryTime());
//                vo.setType("云信");
//                vo.setFinanceNo(cloud.getFinancingNo());
//                vo.setUserName(cloud.getCompanyName());
//                vo.setPeriod("1");
//                vo.setRepaymentTime(assets.getEndDate().toString());
//
//                String valueByKey = CloudFinanceStatusEnum.getValueByKey(cloud.getStatus());
//                vo.setStatus(valueByKey);
//                return vo;
//            }).collect(Collectors.toList());
//        }
//        repaymentVo.addAll(cloudVO);
//        return repaymentVo;
//    }
//
//    /**
//     * 客户贷款报表
//     *
//     * @param year   年
//     * @param month  月
//     * @param type   业务类型
//     * @param userId 用户id
//     * @param tentId 租户id
//     * @return
//     */
//    @Override
//    public List<CreditReport> customerCreditReport(Integer year, String month, Integer type, Long userId, String tentId) {
//        List<String> timeList = new ArrayList<>(32);
//        if (year == -1) {
//            year = cn.hutool.core.date.DateUtil.year(new Date());
//        }
//        timeList = getDateList(month, year);
//
//
//        ArrayList<CreditReport> creditReports = new ArrayList<>();
//        for (String time : timeList) {
//
//            List<LoanManageIou> newLoanList = new ArrayList<>();
//            List<LoanManageRepayment> payList = new ArrayList<>();
//            List<LoanManageOverdue> overDueList = new ArrayList<>();
//            List<LoanManageRepaymentPlan> payable = new ArrayList<>();
//
//            if (type == 1 || type == 2 || type == -1) {
//                //新增贷款
//                newLoanList = loanManageIouMapper.newLoansByName(time, type, userId, tentId);
//                //查询待还
//                payable = loanManageRepaymentPlanMapper.payableByUserId(time, type, userId, tentId);
//
//                //已还完笔数
//                payList = loanManageRepaymentMapper.payLoanByName(time, type, userId, tentId);
//                //逾期订单
//                overDueList = loanManageOverdueMapper.overDue(time, type, userId, tentId);
//            }
//
//            List<CloudFinancing> newCloudList = new ArrayList<>();
//            List<CloudAssets> unPayCloudList = new ArrayList<>();
//            List<CloudAssets> overDueCloud = new ArrayList<>();
//            List<CloudPaymentList> payCloudList = new ArrayList<>();
//            List<CloudFinancing> cloudUserList = new ArrayList<>();
//            if (type == 3 || type == -1) {
//                //查询融资企业云信新增信贷
//                CreditReportDto dto = new CreditReportDto();
//                dto.setQueryTime(time);
//                dto.setUserId(userId);
//                dto.setTenantId(tentId);
//                newCloudList = cloudFinancingMapper.newLoansList(dto);
//
//
//                unPayCloudList = cloudAssetsMapper.unCloudpayByName(time, userId, tentId);
//
//                payCloudList = cloudPaymentListMapper.payByName(time, userId, tentId);
//                //云信逾期订单
//                overDueCloud = cloudAssetsMapper.overdue(time, userId, tentId);
//
//            }
//
//            //当月待还笔数
//            int umPayNum = payable.size() + unPayCloudList.size();
//            //当月已还笔数
//            int payNum = payCloudList.size() + payList.size();
//            //放款金额(元)
//            BigDecimal iouAmount = newLoanList.stream().map(LoanManageIou::getIouAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal cloudAmount = newCloudList.stream().map(CloudFinancing::getLoanMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal loanTotal = cloudAmount.add(iouAmount);
//
//            //新增贷款数量
//            int newLoanNum = (newLoanList.size()) + (newCloudList.size());
//
//            //还款金额(元)
//            BigDecimal payLoan = payList.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payCloud = payCloudList.stream().map(CloudPaymentList::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            //还款汇总
//            BigDecimal payTotal = payLoan.add(payCloud);
//
//            //逾期金额
//            BigDecimal overDue = overDueList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal overCloud = overDueCloud.stream().map(CloudAssets::getCloudBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal overDueMoney = overDue.add(overCloud);
//
//            //应收和代采实还利息
//            BigDecimal payInterest = payList.stream().map(LoanManageRepayment::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//
//            CreditReport creditReport = CreditReport.builder()
//                    .queryTime(time)//时间
//                    .numberOfLoans(newLoanNum)//新增放款笔数
//                    .numberOfUnPay(umPayNum)//未还款笔数
//                    .numberOfPay(payNum)//已还款笔数
//                    .loanAmount(loanTotal)//放款金额汇总
//                    .payAmount(payTotal)//已还款金额汇总
//                    .overDueMoney(overDueMoney)//逾期金额
//                    .payInterest(payInterest)//实还利息
//                    .build();
//            creditReports.add(creditReport);
//        }
//
//        return creditReports;
//    }
//
//    @Override
//    public List<CreditReport> repaymentAnalysisReport(Integer type, Integer year, String month, String tentId) {
//        List<String> timeList = new ArrayList<>(32);
//        if (year == -1) {
//            year = cn.hutool.core.date.DateUtil.year(new Date());
//        }
//        timeList = getDateList(month, year);
//
//        ArrayList<CreditReport> creditReports = new ArrayList<>();
//        for (String time : timeList) {
//
//            List<LoanManageRepaymentPlan> payable = new ArrayList<>();
//            List<LoanManageRepayment> payList = new ArrayList<>();
//            List<LoanManageOverdue> overDueList = new ArrayList<>();
//
//            if (type == 1 || type == 2 || type == -1) {
//                //应还订单
//                payable = loanManageRepaymentPlanMapper.payableList(time, type, tentId);
//                //实还订单
//                payList = loanManageRepaymentMapper.payLoanByName(time, type, 0L, tentId);
//                //逾期订单
//                overDueList = loanManageOverdueMapper.overDue(time, type, 0L, tentId);
//            }
//
//            List<CloudAssets> unPayCloudList = new ArrayList<>();
//            List<CloudAssets> overDueCloud = new ArrayList<>();
//            List<CloudPaymentList> payCloudList = new ArrayList<>();
//            if (type == 3 || type == -1) {
//                unPayCloudList = cloudAssetsMapper.unCloudpayByName(time, 0L, tentId);
//                payCloudList = cloudPaymentListMapper.payByName(time, 0L, tentId);
//                //云信逾期订单
//                overDueCloud = cloudAssetsMapper.overdue(time, 0L, tentId);
//            }
//            //应还金额
//            BigDecimal payableIou = payable.stream().map(LoanManageRepaymentPlan::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payableCloud = unPayCloudList.stream().map(CloudAssets::getCloudBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payableAmount = payableIou.add(payableCloud);
//
//            //应还利息
//            BigDecimal payableIouInterest = payable.stream().map(LoanManageRepaymentPlan::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            //实还金额
//            BigDecimal payIou = payList.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payCloud = payCloudList.stream().map(CloudPaymentList::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal payAmount = payIou.add(payCloud);
//
//            //实还利息
//            BigDecimal payIouInterest = payList.stream().map(LoanManageRepayment::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//
//            //逾期金额
//            BigDecimal overDueIou = overDueList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal overDue = overDueCloud.stream().map(CloudAssets::getCloudBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal overDueMoney = overDueIou.add(overDue);
//
//
//            BigDecimal repaymentRate = new BigDecimal(0);
//            BigDecimal overdueRate = new BigDecimal(0);
//            if (payableAmount.compareTo(new BigDecimal(0)) != 0) {
//                repaymentRate = payAmount.divide(payableAmount, 4, BigDecimal.ROUND_HALF_UP);
//                overdueRate = overDueMoney.divide(payableAmount, 4, BigDecimal.ROUND_HALF_UP);
//            }
//
//            CreditReport creditReport = CreditReport.builder()
//                    .queryTime(time)
//                    .payableAmount(payableAmount)//应还本金
//                    .payableInterest(payableIouInterest)//应还利息
//                    .payAmount(payAmount)//实还本金
//                    .payInterest(payIouInterest)//实还利息
//                    .overDueMoney(overDueMoney)//逾期金额
//                    .repaymentRate(repaymentRate)//正常还款率
//                    .overdueRate(overdueRate)//逾期率
//                    .build();
//
//            creditReports.add(creditReport);
//
//        }
//        return creditReports;
//    }
//
//    @Override
//    public List<CreditReport> cloudAssetsReport(Integer type, Integer year, String month, String tentId) {
//        type = 3;
//        if (year == -1) {
//            year = cn.hutool.core.date.DateUtil.year(new Date());
//        }
//        List<String> timeList = new ArrayList<>(32);
//        timeList = getDateList(month, year);
//
//        ArrayList<CreditReport> creditReports = new ArrayList<>();
//        for (String time : timeList) {
//
//            //查询融资企业云信新增信贷
//            CreditReportDto dto = new CreditReportDto();
//            dto.setQueryTime(time);
//            dto.setTenantId(tentId);
//            List<CloudFinancing> newCloudList = cloudFinancingMapper.newLoansList(dto);
//
//            //云信新增回款订单
//            List<CloudPaymentList> payCloudList = cloudPaymentListMapper.payList(time, tentId);
//            //云信逾期待回订单
//            List<CloudAssets> overDueCloud = cloudAssetsMapper.overdue(time, 0L, tentId);
//
//            Integer numberOfLoans = newCloudList.size();
//            Integer numberOfPay = payCloudList.size();
//            Integer overDueNum = overDueCloud.size();
//
//            BigDecimal payAmount = payCloudList.stream().map(CloudPaymentList::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal overDueMoney = overDueCloud.stream().map(CloudAssets::getCloudBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            //云信开单金额
//            BigDecimal loanAmount = newCloudList.stream().map(CloudFinancing::getLoanMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            CreditReport creditReport = CreditReport.builder()
//                    .queryTime(time)
//                    .numberOfLoans(numberOfLoans)//新增放款笔数
//                    .loanAmount(loanAmount)//开单金额
//                    .numberOfPay(numberOfPay)//已还完笔数
//                    .payAmount(payAmount)//实还金额
//                    .overDueNum(overDueNum)//逾期件数
//                    .overDueMoney(overDueMoney)//逾期金额
//                    .build();
//            creditReports.add(creditReport);
//        }
//
//        return creditReports;
//    }
//
//    /**
//     * 获取查询时间集合
//     *
//     * @param month
//     * @param year
//     * @return
//     */
//    public static List<String> getDateList(String month, Integer year) {
//        List<String> timeList;
//        if ("week".equals(month)) {
//            //查询最近7天
//            timeList = org.springblade.common.utils.DateUtils.getWeekStr();
//        } else if (StringUtil.isNotBlank(month)) {
//            //查询某年某月所有天数
//            Integer mon = Integer.valueOf(month);
//            timeList = org.springblade.common.utils.DateUtils.getDayByMth(year, mon);
//
//        } else {
//            //按某年所有月份
//            timeList = org.springblade.common.utils.DateUtils.getMonStr(year);
//        }
//        return timeList;
//    }

}
