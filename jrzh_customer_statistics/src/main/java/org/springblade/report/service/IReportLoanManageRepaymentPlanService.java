package org.springblade.report.service;

import org.springblade.report.dto.CreditReportDto;
import org.springblade.report.vo.CreditPassVO;
import org.springblade.report.vo.CreditReport;
import org.springblade.vo.HomeNewFinancingAndQuotaVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IReportLoanManageRepaymentPlanService {
    /**
     * 信贷报表
     *
     * @param timeList
     * @param type
     * @param tentId
     * @return
     */
    List<CreditReport> creditReport(List<String> timeList, List<Integer> type, String tentId);

    /**
     * 信贷实际还款穿透报表
     *
     * @param dto
     * @return
     */
    List<CreditPassVO> actualRepaymentReport(CreditReportDto dto);

    /**
     * 信贷约定还款穿透报表
     *
     * @param dto
     * @return
     */
    List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto);

    /**
     * 客户贷款报表
     *
     * @param year
     * @param month
     * @param type
     * @param userId
     * @return
     */
    List<CreditReport> customerCreditReport(Integer year, String month, List<Integer> type, Long userId, String tentId);

    /**
     * 新增融资客户穿透报表
     *
     * @param dto
     * @return
     */
    List<CreditPassVO> creditNewCreditReport(CreditReportDto dto);

    /**
     * 新客户时间内下单的订单
     *
     * @param dto
     * @return
     */
    List<CreditPassVO> creditNewCustomerReport(CreditReportDto dto);

    /**
     * 回款分析表
     *
     * @param type
     * @param year
     * @param month
     * @return
     */
    List<CreditReport> repaymentAnalysisReport(List<Integer> type, Integer year, String month, String tentId);

    /**
     * 统计平台融资用户数、授信总额度、放款金额
     * <AUTHOR>
     * @date 2025/3/15 16:24
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String, Object> getTotalHome();

    /**
     * 获取最新10条放款记录
     * <AUTHOR>
     * @date 2025/3/15 18:01
     * @return java.util.List<org.springblade.report.vo.HomeNewFinancingAndQuotaVo>
     */
    List<HomeNewFinancingAndQuotaVo> selectHomeNewFinancing();
    /**
     * 获取最新10条授信记录
     * <AUTHOR>
     * @date 2025/3/15 18:01
     * @return java.util.List<org.springblade.report.vo.HomeNewFinancingAndQuotaVo>
     */
    List<HomeNewFinancingAndQuotaVo> selectHomeNewQuota();


//
//    /**
//     * 信贷约定还款穿透报表
//     *
//     * @param dto
//     * @return
//     */
//    List<CreditPassVO> agreedRepaymentReport(CreditReportDto dto);
//
//    /**
//     * 客户贷款报表
//     *
//     * @param year
//     * @param month
//     * @param type
//     * @param userId
//     * @return
//     */
//    List<CreditReport> customerCreditReport(Integer year, String month, Integer type, Long userId, String tentId);
//
//    /**
//     * 回款分析表
//     *
//     * @param type
//     * @param year
//     * @param month
//     * @return
//     */
//    List<CreditReport> repaymentAnalysisReport(Integer type, Integer year, String month, String tentId);
//
//    /**
//     * 云信报表
//     * @param type
//     * @param year
//     * @param month
//     * @return
//     */
//    List<CreditReport> cloudAssetsReport(Integer type, Integer year, String month,String tentId);
}
