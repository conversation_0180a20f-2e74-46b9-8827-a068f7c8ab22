package org.springblade.report.controller;

import cn.hutool.core.date.DateUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.service.IEnterpriseAdoptService;
import org.springblade.customer.vo.FrontFinancingListVO;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.report.service.IReportLoanManageRepaymentPlanService;
import org.springblade.report.vo.CreditReport;
import org.springblade.vo.HomeNewFinancingAndQuotaVo;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 信贷报表
 *
 * <AUTHOR>
 * @data 2022/9/26
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(CommonConstant.BLADE_REPORT + CommonConstant.WEB_BACK + "/report/credit")
@Slf4j
public class CreditReportController {


    private final IEnterpriseAdoptService enterpriseAdoptService;
    private final IReportLoanManageRepaymentPlanService reportLoanManageRepaymentPlanService;
    private final RedisTemplate redisTemplate;
    private final ProductDirector productDirector;


    /**
     * 首页信贷报表
     *
     * @param dsName
     * @param datasetName
     * @param parameters
     * @return
     */
    @PostMapping("/homeTable")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "首页信贷报表", notes = "传入parameters")
    //@DataAuth(code="creditReport",type = DataScopeEnum.OWN_DEPT_CHILD)
    public R<List<CreditReport>> creditReportMethod(String dsName, String datasetName, @RequestBody Map<String, Object> parameters) {

        //时间查询范围和产品业务类型
        List<Integer> type = Func.toInt(parameters.get("type")) == -1 ? productDirector.getAllType()
                : Arrays.asList(Func.toInt(parameters.get("type")));

        //1.最近7天  例子： month = "week"
        //2.指定某一个月  例子： year= "2022" month= "09"   后台查询2022年09月数据
        //3.指定某一年  例子： year="2021"   month = null   后台查询2021年数据
        String month = Func.toStr(parameters.get("month"));
        Integer year = Func.toInt(parameters.get("year"));
        Integer date = Func.toInt(parameters.get("date"));
        if (year == -1) {
            year = DateUtil.year(new Date());
        }

        List<String> timeList = new ArrayList<>(32);
        timeList = getDateListCredit(month, year, date);

        String tentId = AuthUtil.getTenantId();
        List<CreditReport> creditReports = reportLoanManageRepaymentPlanService.creditReport(timeList, type, tentId);

        return R.data(creditReports);
    }


    /**
     * 获取报表的redisKey
     *
     * @param name    类名
     * @param type    查询类型
     * @param month   月份
     * @param year    年份
     * @param queryId 要查询的id
     * @return
     */
    public static StringBuilder getRedisKey(String name, Integer type, Integer year, String month, Long queryId, String tentId, Integer date) {
        return new StringBuilder()
                .append(name).append("_")
                .append(type).append("_")
                .append(year).append("_")
                .append(month).append("_")
                .append(queryId).append("_")
                .append(date).append("_")
                .append(tentId);
    }

    /**
     * 首页统计融资用户数、授信总额度、放款金额
     * @return
     */
    @GetMapping("/home/<USER>")
    public R<Map<String,Object>> totalHome(){
        return R.data(reportLoanManageRepaymentPlanService.getTotalHome());
    }

    /**
     * 获取最新10条授信及放款记录
     * <AUTHOR>
     * @date 2025/3/15 18:16
     * @return org.springblade.core.tool.api.R<java.util.List<org.springblade.report.vo.HomeNewFinancingAndQuotaVo>>
     */
    @GetMapping("/home/<USER>/list")
    public R<List<HomeNewFinancingAndQuotaVo>> selectHomeNewFinancing(){
        List<HomeNewFinancingAndQuotaVo> homeNewFinancingAndQuotaVos = reportLoanManageRepaymentPlanService.selectHomeNewFinancing();
        return R.data(homeNewFinancingAndQuotaVos);
    }

    /**
     * 获取最新10条授信及放款记录
     * <AUTHOR>
     * @date 2025/3/15 18:16
     * @return org.springblade.core.tool.api.R<java.util.List<org.springblade.report.vo.HomeNewFinancingAndQuotaVo>>
     */
    @GetMapping("/home/<USER>/list")
    public R<List<HomeNewFinancingAndQuotaVo>> selectHomeNewQuota(){
        List<HomeNewFinancingAndQuotaVo> homeNewFinancingAndQuotaVos = reportLoanManageRepaymentPlanService.selectHomeNewQuota();
        return R.data(homeNewFinancingAndQuotaVos);
    }

//    /**
//     * 客户贷款报表
//     *
//     * @param dsName
//     * @param datasetName
//     * @param parameters
//     * @return
//     */
//    @PostMapping("/customerCredit")
//    @ApiOperationSupport(order = 2)
//    @ApiOperation(value = "客户贷款报表", notes = "传入parameters")
//    public R<List<CreditReport>> CustomerCreditReportMethod(String dsName, String datasetName, @RequestBody Map<String, Object> parameters) {
//
//        //查询条件1.时间 2.业务类型 3.客户名称
//        Integer type = Func.toInt(parameters.get(CreditReportEnum.TYPE.getName()));
//
//        Object nameObj = parameters.get(CreditReportEnum.USER_ID.getName());
//        long userId = Func.toLong(nameObj);
//
//        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
//        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
//
//        String tentId = MyAuthUtil.getTentId();
//
//        //redis拼接  租户id_报表_报表名_年_月_业务类型_用户id
//		/*StringBuilder stringBuilder = new StringBuilder();
//		String tenantId = AuthUtil.getTenantId();
//		String report = CreditReportEnum.REPORT.getName();
//		String customerCredit = CreditReportEnum.CUSTOMER_CREDIT.getName();
//		stringBuilder.append(tenantId).append("_");
//
//
//		//根据查询条件查redis
//		Object obj = redisTemplate.opsForValue().get("查询条件");
//		if(ObjectUtil.isNotEmpty(obj)){
//			String objStr = obj.toString();
//			List list = JSON.parseObject(objStr, List.class);
//			return list;
//		}*/
//
//        List<CreditReport> reports = cloudReportLoanManageRepaymentPlanService.customerCreditReport(year, month, type, userId, tentId);
//        return R.data(reports);
//    }
//
//
//    /**
//     * 回款分析表
//     *
//     * @param dsName
//     * @param datasetName
//     * @param parameters
//     * @return
//     */
//    @PostMapping("/repaymentAnalysis")
//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "回款分析表", notes = "传入parameters")
//    public R<List<CreditReport>> repaymentAnalysisMethod(String dsName, String datasetName, @RequestBody Map<String, Object> parameters) {
//        Integer type = Func.toInt(parameters.get(CreditReportEnum.TYPE.getName()));
//        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
//        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
//        String tentId = MyAuthUtil.getTentId();
//        List<CreditReport> reports = cloudReportLoanManageRepaymentPlanService.repaymentAnalysisReport(type, year, month, tentId);
//        return R.data(reports);
//    }
//
//    /**
//     * 云信报表
//     *
//     * @param dsName
//     * @param datasetName
//     * @param parameters
//     * @return
//     */
//    @PostMapping("/cloudAssets")
//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "云信报表", notes = "传入parameters")
//    public R<List<CreditReport>> cloudAssets(String dsName, String datasetName, @RequestBody Map<String, Object> parameters) {
//        Integer type = Func.toInt(parameters.get(CreditReportEnum.TYPE.getName()));
//        String month = Func.toStr(parameters.get(CreditReportEnum.MONTH.getName()));
//        Integer year = Func.toInt(parameters.get(CreditReportEnum.YEAR.getName()));
//        String tentId = MyAuthUtil.getTentId();
//        List<CreditReport> reports = cloudReportLoanManageRepaymentPlanService.cloudAssetsReport(type, year, month, tentId);
//        return R.data(reports);
//    }

    /**
     * 查询融资企业和融资个人户的姓名
     *
     * @return
     */
    @GetMapping("/getCustomerName")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "查询融资企业和融资个人户的姓名")
    public R<List<FrontFinancingListVO>> getCustomerName() {
        List<FrontFinancingListVO> financingName = enterpriseAdoptService.findFinancingName();
        return R.data(financingName);
    }


    /**
     * 获取查询时间集合
     *
     * @param month
     * @param year
     * @return
     */
    public static List<String> getDateList(String month, Integer year) {
        List<String> timeList;
        if ("week".equals(month)) {
            //查询最近7天
            timeList = DateUtils.getWeekStr();
        } else if (StringUtil.isNotBlank(month)) {
            //查询某年某月所有天数
            Integer mon = Integer.valueOf(month);
            timeList = DateUtils.getDayByMth(year, mon);

        } else {
            //按某年所有月份
            timeList = DateUtils.getMonStr(year);
        }
        return timeList;
    }

    /**
     * 信贷表获取查询时间集合
     *
     * @param month
     * @param year
     * @return
     */
    public static List<String> getDateListCredit(String month, Integer year, Integer date) {
        int yearByNow = DateUtil.year(new Date());
        int monthByNow = DateUtil.month(new Date()) + 1;
        List<String> timeList;
        if (date == 1 || "week".equals(month)) {
            //查询最近7天
            timeList = DateUtils.getWeekStr();
        } else if (date == 2) {
            //根据某年某月查询最近12个月份
            timeList = DateUtils.getMonthStr(yearByNow, monthByNow, 12);
        } else if (date == 3) {
            //根据某年获取过去的5个年份
            timeList = DateUtils.getYear(yearByNow);
        } else if (StringUtil.isNotBlank(month)) {
            //查询某年某月所有天数
            Integer mon = Integer.valueOf(month);
            timeList = DateUtils.getDayByMth(year, mon);
        } else {
            //按某年所有月份
            timeList = DateUtils.getMonStr(year);
        }
        return timeList;
    }

}
