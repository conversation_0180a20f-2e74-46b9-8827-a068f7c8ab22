/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.OverdueConstant;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.feign.RemoteCustomerGoodsService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.dto.LoanManageRepaymentDTO;
import org.springblade.loan.entity.LoanManageOverdue;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.entity.LoanManageRepaymentTerm;
import org.springblade.loan.expense.resp.RepaymentExpenseResp;
import org.springblade.loan.mapper.LoanManageOverdueMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.CapitalGoodsFrozenRuleVO;
import org.springblade.loan.vo.LoanManageOverdueJoinFinanceApplyVO;
import org.springblade.loan.vo.LoanManageOverdueVO;
import org.springblade.loan.wrapper.LoanManageOverdueWrapper;
import org.springblade.resource.builder.sms.SmsBuilder;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 还款列表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-25
 */
@Service
@RequiredArgsConstructor
public class LoanManageOverdueServiceImpl extends BaseServiceImpl<LoanManageOverdueMapper, LoanManageOverdue> implements ILoanManageOverdueService {

    //	private final ICustomerFrontUserTypeService customerFrontUserTypeService;
//	private final ICustomerService customerService;
    private final SmsBuilder smsBuilder;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final LoanManageRepaymentMapper loanManageRepaymentMapper;
    private final ILoanManageRepaymentService loanManageRepaymentService;

    private final IOverdueFollowService overdueFollowService;
    private final RemoteCustomerGoodsService customerGoodsService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;

    private final RemoteUserService remoteUserService;


    @Override
    public void insertBatch(List<LoanManageOverdue> loanManageOverdueList) {
        baseMapper.batchInsert(loanManageOverdueList);
    }

    @Override
    public boolean smsCollection(Long id) {
//		LoanManageOverdue loanManageOverdue = baseMapper.selectById(id);
//		if (Objects.isNull(loanManageOverdue)) {
//			return false;
//		}
//		CustomerFrontUserType customerFrontUserType = customerFrontUserTypeService.lambdaQuery()
//			.eq(CustomerFrontUserType::getUserId, loanManageOverdue.getUserId())
//			.eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode())
//			.one();
//		Long customerFrontUserId = customerFrontUserType.getCustomerFrontUserId();
//		Customer customer = customerService.getById(customerFrontUserId);
//		String phone = customer.getPhone();
//		return smsBuilder.template("verification").sendValidate(new SmsData(SmsUtil.getValidateParams()).setKey(PARAM_KEY), phone).isSuccess();
        return true;
    }


//	@Override
//	public IPage<LoanManageIouOverdueVO> selectOverdueList(LoanManageOverdueVO loanManageOverdue, Query query) {
//		List<LoanManageOverdue> page = baseMapper.selectList(buildQueryWrapper(loanManageOverdue));
//		List<LoanManageOverdueVO> pageVO = LoanManageOverdueWrapper.build().listVO(page);
//
//		List<LoanManageOverdueVO> records = pageVO.stream()
//			.filter(e -> ObjectUtil.isNotEmpty(e.getFinanceNo())).collect(Collectors.toList());
//		List<Long> repaymentPlanIdList = StreamUtil.map(records, LoanManageOverdueVO::getRepaymentPlanId);
//		List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.selectListByIds(repaymentPlanIdList);
//		Map<Long, RepaymentExpenseResp> repaymentExpenseRespMap = loanManageRepaymentPlanService.calculateOverdueExpense(loanManageRepaymentPlans);
//		Map<Long, User> mapInId = userService.getMapInId(StreamUtil.map(records, LoanManageOverdue::getUserId));
//		List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.selectOverdueHavePaid(repaymentPlanIdList);
//		Map<Long, LoanManageRepayment> loanManageRepaymentsMap = StreamUtil.toMap(loanManageRepayments, LoanManageRepayment::getRepaymentPlanId, obj -> obj);
//		for (LoanManageOverdueVO loanManageOverdueVO : records) {
//			RepaymentExpenseResp repaymentExpenseResp = repaymentExpenseRespMap.get(loanManageOverdueVO.getRepaymentPlanId());
//			if (repaymentExpenseResp != null) {
//				BigDecimal totalAmount = repaymentExpenseResp.getTotalAmount();
//				loanManageOverdueVO.setShouldTotalAmount(totalAmount);
//				//loanManageOverdueVO.setStatus(totalAmount.compareTo(BigDecimal.ZERO) == 0 ? OverdueConstant.OverdueStatus.PAY.getCode() : loanManageOverdueVO.getStatus());
//				loanManageOverdueVO.setShouldInterest(repaymentExpenseResp.getShouldInterest());
//				loanManageOverdueVO.setShouldOverdueInterest(repaymentExpenseResp.getShouldOverdueInterest());
//				loanManageOverdueVO.setShouldPrincipal(repaymentExpenseResp.getSurplusPrincipal());
//				if (loanManageOverdueVO.getShouldPrincipal().compareTo(BigDecimal.ZERO) == 0) {
//					loanManageOverdueVO.setShouldInterest(new BigDecimal("0.00"));
//					loanManageOverdueVO.setShouldTotalAmount(new BigDecimal("0.00"));
//					//loanManageOverdueVO.setStatus(OverdueConstant.OverdueStatus.PAY.getCode());
//				}
//				LoanManageRepayment loanManageRepayment = loanManageRepaymentsMap.get(loanManageOverdueVO.getRepaymentPlanId());
//				if (ObjectUtil.isNotEmpty(loanManageRepayment)) {
//					loanManageOverdueVO.setRepaidPrincipal(loanManageRepayment.getPrincipal());
//					loanManageOverdueVO.setRepaidInterest(loanManageRepayment.getInterest());
//					loanManageOverdueVO.setRepaidOverdueInterest(loanManageRepayment.getPenaltyInterest());
//					loanManageOverdueVO.setRepaidTotalSum(loanManageRepayment.getActualAmount());
//				}
//			}
//			loanManageOverdueVO.setFinancingUser(mapInId.get(loanManageOverdueVO.getUserId()) == null ? "" : mapInId.get(loanManageOverdueVO.getUserId()).getName());
//		}
//		List<LoanManageIouOverdueVO> iouOverdueVO = new ArrayList<>();
//		List<LoanManageIou> loanManageIouList = SpringUtil.getBean(LoanManageIouMapper.class).selectLoanManageIouList(StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getIouNo));
//		for (LoanManageIou loanManageIou : loanManageIouList) {
//			LoanManageIouOverdueVO loanManageIouOverdueVO = new LoanManageIouOverdueVO();
//			BeanUtil.copyProperties(loanManageIou, loanManageIouOverdueVO);
//			iouOverdueVO.add(loanManageIouOverdueVO);
//			List<LoanManageOverdueVO> loanManageOverdueVOList = new ArrayList<>();
//			for (LoanManageOverdueVO loanManageOverdueVO : records) {
//				if (loanManageIou.getIouNo().equals(loanManageOverdueVO.getIouNo())) {
//					loanManageOverdueVOList.add(loanManageOverdueVO);
//				}
//			}
//			loanManageIouOverdueVO.setLoanManageOverdueVO(loanManageOverdueVOList);
//		}
//		List<LoanManageIouOverdueVO> list = iouOverdueVO.stream()
//			.skip((long) (query.getCurrent() - 1) * (query.getSize()))
//			.limit(query.getSize()).collect(Collectors.toList());
//		Page<LoanManageIouOverdueVO> overdueVOPage = new Page<>();
//		overdueVOPage.setRecords(list);
//		overdueVOPage.setTotal(loanManageIouList.size());
//		overdueVOPage.setCurrent(query.getCurrent());
//		overdueVOPage.setSize(query.getSize());
//		return overdueVOPage;
//	}


    @Override
    public IPage<LoanManageOverdueVO> selectOverdueList(LoanManageOverdueVO loanManageOverdue, Query query) {
        LambdaQueryWrapper<LoanManageOverdue> queryWrapper = buildQueryWrapper(loanManageOverdue);
        if(StringUtil.isNotBlank(loanManageOverdue.getFinancingUser())){
            List<User> list = remoteUserService.getUserByName(loanManageOverdue.getFinancingUser()).getData();
            if(CollUtil.isNotEmpty(list)){
               List<Long> userIds =  CollStreamUtil.toList(list, User::getId);
               queryWrapper.in(LoanManageOverdue::getUserId, userIds);
            }else{
                queryWrapper.in(LoanManageOverdue::getUserId, 0L);
            }
        }

        IPage<LoanManageOverdue> page = baseMapper.selectPage(Condition.getPage(query), buildQueryWrapper(loanManageOverdue));
        if (page.getTotal() <= 0) {
            return Condition.getPage(query);
        }
        IPage<LoanManageOverdueVO> pageVO = LoanManageOverdueWrapper.build().pageVO(page);
        //过滤没有融资编号的数据
        List<LoanManageOverdueVO> records = pageVO.getRecords().stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getFinanceNo())).collect(Collectors.toList());
        List<Long> repaymentPlanIdList = StreamUtil.map(records, LoanManageOverdueVO::getRepaymentPlanId);
        //查询所有已逾期的还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIdList);
        Map<Long, LoanManageRepaymentPlan> loanManageRepaymentPlanMap = StreamUtil.toMap(loanManageRepaymentPlans, LoanManageRepaymentPlan::getId, loanManageRepaymentPlan -> loanManageRepaymentPlan);
        //过滤随借随还的还款计划使用产品配置的费用计算利息
        List<LoanManageRepaymentPlan> borrowRepaymentPlanList = loanManageRepaymentPlans.stream().filter(loanManageRepaymentPlan -> !GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())).collect(Collectors.toList());
        //分期还款查询还款计划的利息
        List<LoanManageRepaymentPlan> stagesRepaymentPlanList = loanManageRepaymentPlans.stream().filter(loanManageRepaymentPlan -> GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())).collect(Collectors.toList());
        Map<Long, LoanManageRepaymentPlan> stagesRepaymentPlanMap = StreamUtil.toMap(stagesRepaymentPlanList, LoanManageRepaymentPlan::getId, obj -> obj);
        //计算应还金额
        Map<Long, RepaymentExpenseResp> repaymentExpenseRespMap = loanManageRepaymentPlanService.calculateOverdueExpense(borrowRepaymentPlanList);
        Map<Long, User> mapInId = remoteUserService.getMapInId(StreamUtil.map(records, LoanManageOverdue::getUserId)).getData();
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.selectOverdueHavePaid(repaymentPlanIdList);
        //Map<Long, LoanManageRepayment> loanManageRepaymentsMap = StreamUtil.toMap(loanManageRepayments, LoanManageRepayment::getRepaymentPlanId, obj -> obj);
        Map<Long, LoanManageRepayment> loanManageRepaymentsMap = loanManageRepayments.stream().collect(Collectors.toMap(LoanManageRepayment::getRepaymentPlanId, obj -> obj, (oldVal, newVal) -> {
            oldVal.setActualPrincipal(oldVal.getActualPrincipal().add(newVal.getActualPrincipal()));
            oldVal.setActualInterest(oldVal.getActualInterest().add(newVal.getActualInterest()));
            oldVal.setActualAmount(oldVal.getActualAmount().add(newVal.getActualAmount()));
            return oldVal;
        }));
        for (LoanManageOverdueVO loanManageOverdueVO : records) {
            RepaymentExpenseResp repaymentExpenseResp = repaymentExpenseRespMap.get(loanManageOverdueVO.getRepaymentPlanId());
            LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanMap.get(loanManageOverdueVO.getRepaymentPlanId());
            LoanManageRepaymentPlan stagesRepaymentPlan = stagesRepaymentPlanMap.get(loanManageOverdueVO.getRepaymentPlanId());
            LoanManageRepayment loanManageRepayment = loanManageRepaymentsMap.get(loanManageOverdueVO.getRepaymentPlanId());
            //组装数据
            assemblePayableRecord(repaymentExpenseResp, loanManageRepaymentPlan, stagesRepaymentPlan, loanManageOverdueVO, loanManageRepayment);
            loanManageOverdueVO.setFinancingUser(mapInId.get(loanManageOverdueVO.getUserId()) == null ? "" : mapInId.get(loanManageOverdueVO.getUserId()).getName());
        }
        pageVO.setRecords(records);
        return pageVO;
    }

//	@Override
//	public IPage<LoanManageOverdueVO> selectOverdueList(LoanManageOverdueVO loanManageOverdue, Query query) {
//		IPage<LoanManageOverdue> page = baseMapper.selectPage(Condition.getPage(query), buildQueryWrapper(loanManageOverdue));
//		if (page.getTotal() <= 0) {
//			return Condition.getPage(query);
//		}
//		Page<LoanManageOverdueVO> pageVO = LoanManageOverdueWrapper.build().pageVO(page);
//		//selectAllOverdueRecord(loanManageOverdue);
//		List<LoanManageOverdueVO> records = pageVO.getRecords().stream()
//			.filter(e -> ObjectUtil.isNotEmpty(e.getFinanceNo())).collect(Collectors.toList());
//		List<Long> repaymentPlanIdList = StreamUtil.map(records, LoanManageOverdueVO::getRepaymentPlanId);
//		List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIdList);
//		Map<Long, RepaymentExpenseResp> repaymentExpenseRespMap = loanManageRepaymentPlanService.calculateOverdueExpense(loanManageRepaymentPlans);
//		Map<Long, User> mapInId = userService.getMapInId(StreamUtil.map(records, LoanManageOverdue::getUserId));
//		List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.selectOverdueHavePaid(repaymentPlanIdList);
//		Map<Long, LoanManageRepayment> loanManageRepaymentsMap = StreamUtil.toMap(loanManageRepayments, LoanManageRepayment::getRepaymentPlanId, obj -> obj);
//		for (LoanManageOverdueVO loanManageOverdueVO : records) {
//			RepaymentExpenseResp repaymentExpenseResp = repaymentExpenseRespMap.get(loanManageOverdueVO.getRepaymentPlanId());
//			if (repaymentExpenseResp != null) {
//				BigDecimal totalAmount = repaymentExpenseResp.getTotalAmount();
//				loanManageOverdueVO.setShouldTotalAmount(totalAmount);
//				//loanManageOverdueVO.setStatus(totalAmount.compareTo(BigDecimal.ZERO) == 0 ? OverdueConstant.OverdueStatus.PAY.getCode() : loanManageOverdueVO.getStatus());
//				loanManageOverdueVO.setShouldInterest(repaymentExpenseResp.getShouldInterest());
//				loanManageOverdueVO.setShouldOverdueInterest(repaymentExpenseResp.getShouldOverdueInterest());
//				loanManageOverdueVO.setShouldPrincipal(repaymentExpenseResp.getSurplusPrincipal());
//				if (loanManageOverdueVO.getShouldPrincipal().compareTo(BigDecimal.ZERO) == 0) {
//					loanManageOverdueVO.setShouldInterest(new BigDecimal("0.00"));
//					loanManageOverdueVO.setShouldTotalAmount(new BigDecimal("0.00"));
//					//loanManageOverdueVO.setStatus(OverdueConstant.OverdueStatus.PAY.getCode());
//				}
//				LoanManageRepayment loanManageRepayment = loanManageRepaymentsMap.get(loanManageOverdueVO.getRepaymentPlanId());
//				if (ObjectUtil.isNotEmpty(loanManageRepayment)) {
//					loanManageOverdueVO.setRepaidPrincipal(loanManageRepayment.getPrincipal());
//					loanManageOverdueVO.setRepaidInterest(loanManageRepayment.getInterest());
//					loanManageOverdueVO.setRepaidOverdueInterest(loanManageRepayment.getPenaltyInterest());
//					loanManageOverdueVO.setRepaidTotalSum(loanManageRepayment.getActualAmount());
//				}
//			}
//			loanManageOverdueVO.setFinancingUser(mapInId.get(loanManageOverdueVO.getUserId()) == null ? "" : mapInId.get(loanManageOverdueVO.getUserId()).getName());
//		}
//		pageVO.setRecords(records);
//		return pageVO;
//	}


    private void assemblePayableRecord(RepaymentExpenseResp repaymentExpenseResp, LoanManageRepaymentPlan loanManageRepaymentPlan, LoanManageRepaymentPlan stagesRepaymentPlan, LoanManageOverdueVO loanManageOverdueVO, LoanManageRepayment loanManageRepayment) {
        if (ObjectUtil.isNotEmpty(loanManageRepaymentPlan)){
            loanManageOverdueVO.setRepaidTotalSum(BigDecimal.ZERO);
            if (ObjectUtil.isNotEmpty(loanManageRepayment)) {
                loanManageOverdueVO.setRepaidPrincipal(loanManageRepayment.getActualPrincipal());
                loanManageOverdueVO.setRepaidInterest(loanManageRepayment.getActualInterest());
                loanManageOverdueVO.setRepaidOverdueInterest(loanManageRepayment.getActualPenaltyInterest());
                loanManageOverdueVO.setRepaidTotalSum(loanManageRepayment.getActualAmount());
            }
            //判断还款方式
            if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())){
                if (ObjectUtil.isNotEmpty(stagesRepaymentPlan)){
                    loanManageOverdueVO.setShouldTotalAmount(stagesRepaymentPlan.getPrincipal().add(stagesRepaymentPlan.getInterest().add(stagesRepaymentPlan.getPenaltyInterest())));
                    loanManageOverdueVO.setShouldInterest(stagesRepaymentPlan.getInterest());
                    loanManageOverdueVO.setShouldOverdueInterest(stagesRepaymentPlan.getPenaltyInterest());
                    loanManageOverdueVO.setShouldPrincipal(stagesRepaymentPlan.getPrincipal());
//				if (loanManageOverdueVO.getShouldPrincipal().compareTo(BigDecimal.ZERO) == 0) {
//					loanManageOverdueVO.setShouldInterest(new BigDecimal("0.00"));
//					loanManageOverdueVO.setShouldTotalAmount(new BigDecimal("0.00"));
//				}
                }
            }else {
                if (repaymentExpenseResp!=null){
                    // 当前应还逾期利息
                    BigDecimal overdueInterest = repaymentExpenseResp.getShouldOverdueInterest();
                    // 费用规则的利息
                    BigDecimal interestRuleInterest = repaymentExpenseResp.getShouldInterest();
                    // 应还本金
                    BigDecimal principal = loanManageRepaymentPlan.getPrincipal();
                    // 应还利息
                    BigDecimal interest = loanManageRepaymentPlan.getInterest();
                    // 当前应还总额 = 应还本金 + 应还利息 + 费用规则的利息 + 当前应还逾期利息 - 已还总额
                    BigDecimal totalAmount = principal.add(interest)
                            .add(overdueInterest)
                            .add(interestRuleInterest)
                            .subtract(loanManageOverdueVO.getRepaidTotalSum());
                    loanManageOverdueVO.setShouldTotalAmount(totalAmount);
                    // 当前应还利息 = 应还利息 + 费用规则的利息 + 当前应还逾期利息 - 已还利息
                    BigDecimal shouldInterest = interest
                            .add(interestRuleInterest)
                            .add(overdueInterest)
                            .subtract(loanManageOverdueVO.getRepaidInterest());
                    loanManageOverdueVO.setShouldInterest(shouldInterest);
                    loanManageOverdueVO.setShouldOverdueInterest(overdueInterest);
                    // 当前应还本金 = 应还本金 - 已还本金
                    loanManageOverdueVO.setShouldPrincipal(principal.subtract(loanManageOverdueVO.getRepaidPrincipal()));
                }
            }
        }
    }

    /**
     * 构建查询条件
     *
     * @param loanManageOverdue 查询条件
     * @return wrapper
     */
    private LambdaQueryWrapper<LoanManageOverdue> buildQueryWrapper(LoanManageOverdueVO loanManageOverdue) {
        LambdaQueryWrapper<LoanManageOverdue> wrapper = Wrappers.lambdaQuery();
        //借据号
        String iouNo = loanManageOverdue.getIouNo();
        //催款单号
        String collectionNo = loanManageOverdue.getCollectionNo();
        //负责人id
        Long directorUserId = loanManageOverdue.getDirectorUserId();
        //融资用户
        String financingUser = loanManageOverdue.getFinancingUser();
        //催收状态
        Integer status = loanManageOverdue.getStatus();
        //逾期阶段
        String overdueStage = loanManageOverdue.getOverdueStage();
        if (Objects.nonNull(iouNo)) {
            wrapper.eq(LoanManageOverdue::getIouNo, iouNo);
        }
        if (Objects.nonNull(status)) {
            wrapper.eq(LoanManageOverdue::getStatus, status);
        }
        if (Objects.nonNull(collectionNo)) {
            wrapper.eq(LoanManageOverdue::getCollectionNo, collectionNo);
        }
        if (Objects.nonNull(directorUserId)) {
            wrapper.eq(LoanManageOverdue::getDirectorUserId, directorUserId);
        }
        if (Objects.nonNull(overdueStage)) {
            wrapper.eq(LoanManageOverdue::getOverdueStage, overdueStage);
        }
        if(StringUtil.isNotBlank(financingUser)){
            List<User> list = remoteUserService.getUserByName(loanManageOverdue.getFinancingUser()).getData();
            if(CollUtil.isNotEmpty(list)){
                List<Long> userIds =  CollStreamUtil.toList(list, User::getId);
                wrapper.in(LoanManageOverdue::getUserId, userIds);
            }else{
                wrapper.in(LoanManageOverdue::getUserId, 0L);
            }
        }
        return wrapper;
    }

    @Override
    public LoanManageOverdueVO detail(Long id) {
//        if(true){
//            throw new UnsupportedOperationException("TODO");
//        }
        LoanManageOverdue loanManageOverdue = baseMapper.selectById(id);
        if (Objects.isNull(loanManageOverdue)) {
            return null;
        }
        LoanManageOverdueVO loanManageOverdueVO = LoanManageOverdueWrapper.build().entityVO(loanManageOverdue);
        Long repaymentPlanId = loanManageOverdue.getRepaymentPlanId();
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getRepaymentPlanById(repaymentPlanId);
        User financingUser = UserUtils.getUserById(loanManageOverdue.getUserId());
        User user =null;
        if(ObjectUtil.isNotEmpty(loanManageOverdue.getDirectorUserId())){
            user = UserUtils.getUserById(loanManageOverdue.getDirectorUserId());
        }
        //逾期已还的还款记录
        List<LoanManageRepayment> loanManageRepayment = loanManageRepaymentService.selectOverdueHavePaid(Collections.singletonList(repaymentPlanId));
        Map<Long, RepaymentExpenseResp> repaymentExpenseRespMap = loanManageRepaymentPlanService.calculateOverdueExpense(Collections.singletonList(loanManageRepaymentPlan));
        //组装已还的数据
        loanManageOverdueVO.setRepaidPrincipal(loanManageRepayment.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add));
        loanManageOverdueVO.setRepaidInterest(loanManageRepayment.stream().map(LoanManageRepayment::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add));
        loanManageOverdueVO.setRepaidOverdueInterest(loanManageRepayment.stream().map(LoanManageRepayment::getPenaltyInterest).reduce(BigDecimal.ZERO, BigDecimal::add));
        loanManageOverdueVO.setRepaidTotalSum(loanManageRepayment.stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        //如果是分期还款获取还款计划的应还金额
        if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())){
            loanManageOverdueVO.setShouldTotalAmount(loanManageRepaymentPlan.getPrincipal().add(loanManageRepaymentPlan.getInterest().add(loanManageRepaymentPlan.getPenaltyInterest())));
            loanManageOverdueVO.setShouldInterest(loanManageRepaymentPlan.getInterest());
            loanManageOverdueVO.setShouldOverdueInterest(loanManageRepaymentPlan.getPenaltyInterest());
            loanManageOverdueVO.setShouldPrincipal(loanManageRepaymentPlan.getPrincipal());
            //随借随还则用产品配置的计费规则获取应还金额
        }else {
            RepaymentExpenseResp repaymentExpenseResp = repaymentExpenseRespMap.get(repaymentPlanId);
            // 当前应还逾期利息
            BigDecimal overdueInterest = repaymentExpenseResp.getShouldOverdueInterest();
            // 费用规则的利息
            BigDecimal interestRuleInterest = repaymentExpenseResp.getShouldInterest();
            // 应还本金
            BigDecimal principal = loanManageRepaymentPlan.getPrincipal();
            // 应还利息
            BigDecimal interest = loanManageRepaymentPlan.getInterest();
            // 当前应还总额 = 应还本金 + 应还利息 + 费用规则的利息 + 当前应还逾期利息 - 已还总额
            BigDecimal totalAmount = principal.add(interest)
                    .add(overdueInterest)
                    .add(interestRuleInterest)
                    .subtract(loanManageOverdueVO.getRepaidTotalSum());
            loanManageOverdueVO.setShouldTotalAmount(totalAmount);
            // 当前应还利息 = 应还利息 + 费用规则的利息 + 当前应还逾期利息 - 已还利息
            BigDecimal shouldInterest = interest
                    .add(interestRuleInterest)
                    .add(overdueInterest)
                    .subtract(loanManageOverdueVO.getRepaidInterest());
            loanManageOverdueVO.setShouldInterest(shouldInterest);
            loanManageOverdueVO.setShouldOverdueInterest(overdueInterest);
            // 当前应还本金 = 应还本金 - 已还本金
            loanManageOverdueVO.setShouldPrincipal(principal.subtract(loanManageOverdueVO.getRepaidPrincipal()));
        }
        //查询逾期跟进的信息
        loanManageOverdueVO.setOverdueFollowVO(overdueFollowService.listByOverdueId(id));
        if (!ObjectUtil.isEmpty(user)) {
            loanManageOverdueVO.setPrincipalPerson(user.getName());
        }
        if (!ObjectUtil.isEmpty(financingUser)) {
            loanManageOverdueVO.setFinancingUser(financingUser.getName());
        }

        return loanManageOverdueVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doFrozenTask() {
        //筛选出符合冻结的未结清订单
        List<LoanManageOverdueJoinFinanceApplyVO> list = loanManageRepaymentMapper.selectListOverdueJoinFinanceApplyVO();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Long, List<LoanManageOverdueJoinFinanceApplyVO>> overDueGroupCapitalMap = list.stream().collect(Collectors.groupingBy(LoanManageOverdueJoinFinanceApplyVO::getCapitalId));
        // 查询资方设置的冻结规则 k:资方id v:冻结规则
        List<Long> capitalIds = list.stream().map(LoanManageOverdueJoinFinanceApplyVO::getCapitalId).distinct().collect(Collectors.toList());
        Map<Long, CapitalGoodsFrozenRuleVO> capitalGoodsFrozenRuleVoMap = mapRuleCapitalByCapitalIds(capitalIds);
        //建立资方待冻结客户产品列表 k:资方id v:冻结用户
        Map<Long, List<Long>> needFrozenCustomer = new HashMap<>();
        //根据资方设定的规则 若该用户符合则 需要将其下的所有相关产品进行冻结
        for (Long capitalId : overDueGroupCapitalMap.keySet()) {
            CapitalGoodsFrozenRuleVO capitalGoodsFrozenRuleVO = capitalGoodsFrozenRuleVoMap.get(capitalId);
            if (ObjectUtil.isEmpty(capitalGoodsFrozenRuleVO)) {
                continue;
            }
            List<Long> needFrozenUserList = new ArrayList<>();
            //对逾期列表进行用户分组
            List<LoanManageOverdueJoinFinanceApplyVO> overdueJoinFinanceApplyVOList = overDueGroupCapitalMap.get(capitalId);
            Map<String, List<LoanManageOverdueJoinFinanceApplyVO>> userOverDueMap = overdueJoinFinanceApplyVOList.stream()
                    .collect(Collectors.groupingBy(e -> e.getUserId().toString()));
            //查询出符合冻结的客户产品
            for (String userId : userOverDueMap.keySet()) {
                if (needFrozenOverdue(capitalGoodsFrozenRuleVoMap.get(capitalId), userOverDueMap.get(userId))) {
                    needFrozenUserList.add(Func.toLong(userId));
                }
            }
            if (CollectionUtil.isNotEmpty(needFrozenUserList)) {
                needFrozenCustomer.put(capitalId, needFrozenUserList);
            }
        }
        if (CollectionUtil.isEmpty(needFrozenCustomer)) {
            return;
        }
        List<Long> needFrozenCustomerGoodsIds = new ArrayList<>();
        List<CustomerGoods> capitalValidGoods = customerGoodsService
                .queryCustomerGoodsByCapitalIdsAndStatus(new ArrayList<>(needFrozenCustomer.keySet())
                        , Arrays.asList(CustomerGoodsEnum.FINANCING.getCode()
                                , CustomerGoodsEnum.QUOTA_CHANGE.getCode())).getData();


        Map<String, Map<String, List<CustomerGoods>>> capitalValidCustomerId = capitalValidGoods.stream().collect(Collectors.groupingBy(e -> e.getCapitalId().toString(), Collectors.groupingBy(e -> e.getEnterpriseId().toString())));
        for (Long capitalId : needFrozenCustomer.keySet()) {
            Map<String, List<CustomerGoods>> userGoodsMap = capitalValidCustomerId.get(capitalId.toString());
            List<Long> needFrozenUser = needFrozenCustomer.get(capitalId);
            for (Long userId : needFrozenUser) {
                if (userGoodsMap.containsKey(userId.toString())) {
                    List<CustomerGoods> customerGoods = userGoodsMap.get(userId.toString());
                    List<Long> needFrozenCustomerGoodsId = StreamUtil.map(customerGoods, CustomerGoods::getId);
                    needFrozenCustomerGoodsIds.addAll(needFrozenCustomerGoodsId);
                }
            }
        }
        //冻结客户的所有已开通产品 并将驳回相关流程申请
        SpringUtil.getBean(RemoteCustomerGoodsService.class).frozenValidCustomerGoodsByIds(new ArrayList<>(needFrozenCustomerGoodsIds));
    }

    /**
     * 是否可进行解冻流程
     *
     * @return
     */
    @Override
    public boolean canUnRunFrozen(CustomerGoods customerGoods) {
        // 获取 融资申请-服务类 的实例
        IFinanceApplyService financeApplyService = SpringUtil.getBean(IFinanceApplyService.class);
        // 查询该资方与该企业的所有融资订单
        List<FinanceApply> financeApplyList = financeApplyService.list(Wrappers.<FinanceApply>lambdaQuery()
                .eq(FinanceApply::getCapitalId, customerGoods.getCapitalId())
                .eq(FinanceApply::getUserId, customerGoods.getEnterpriseId()));
        if (CollectionUtil.isEmpty(financeApplyList)) {
            // 没有融资订单直接返回
            return true;
        }
        List<Long> financeId = StreamUtil.map(financeApplyList, FinanceApply::getId);
        // 逾期的还款计划 -> 还款计划表实体类
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listOverdueLoanManageRepaymentPlans(financeId);
        return loanManageRepaymentPlans.size() == 0;
    }

    /**
     * 判断该用户是否需要冻结
     *
     * @param capitalGoodsFrozenRuleVoMap
     * @param loanManageOverdueJoinFinanceApplyVOS
     * @return
     */
    private boolean needFrozenOverdue(CapitalGoodsFrozenRuleVO capitalGoodsFrozenRuleVoMap, List<LoanManageOverdueJoinFinanceApplyVO> loanManageOverdueJoinFinanceApplyVOS) {
        for (LoanManageOverdueJoinFinanceApplyVO financeApplyVO : loanManageOverdueJoinFinanceApplyVOS) {
            if (financeApplyVO.getRepaymentTime().isBefore(LocalDate.now())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询资方设置的冻结规则
     *
     * @param capitalIds
     * @return
     */
    private Map<Long, CapitalGoodsFrozenRuleVO> mapRuleCapitalByCapitalIds(List<Long> capitalIds) {
        HashMap<Long, CapitalGoodsFrozenRuleVO> map = new HashMap<>();
        for (Long capitalId : capitalIds) {
            CapitalGoodsFrozenRuleVO capitalGoodsFrozenRuleVO = new CapitalGoodsFrozenRuleVO();
            capitalGoodsFrozenRuleVO.setOverDueDayMax(1);
            map.put(capitalId, capitalGoodsFrozenRuleVO);
        }
        return map;
    }

    @Override
    public Boolean hasOverDueByUserId(Long userId) {
        return count(Wrappers.<LoanManageOverdue>lambdaQuery()
                .ne(LoanManageOverdue::getStatus, OverdueConstant.OverdueStatus.PAY.getCode())
                .eq(LoanManageOverdue::getUserId, userId)) > 0;
    }

    @Override
    public List<LoanManageOverdue> listOverdueUnPayByIouNo(String iouNo) {
        List<Integer> unPayStatus = Arrays.asList(OverdueConstant.OverdueStatus.OVERDUE_CONSULTING.getCode()
                , OverdueConstant.OverdueStatus.ARTIFICIAL.getCode()
                , OverdueConstant.OverdueStatus.SMS.getCode());
        return list(Wrappers.<LoanManageOverdue>lambdaQuery().eq(LoanManageOverdue::getIouNo, iouNo)
                .in(LoanManageOverdue::getStatus, unPayStatus));
    }


//	@Override
//	public ControlBoxVO controlBoxOverdueAmount() {
//		String tenantId = AuthUtil.getTenantId();
//		//当天金额
//		String nowDay = DateUtils.getNowDay();
//		BigDecimal amountDay = getOverdueByTime(nowDay);
//
//		//当周金额
//		List<String> nowWeek = DateUtils.getNowWeek(7);
//		String timeStart = nowWeek.get(0);
//		String timeEnd = nowWeek.get(1);
//		List<String> statusList = Func.toStrList("1,2");
//		List<LoanManageOverdue> loanManageOverdueList = baseMapper.selectList(Wrappers.<LoanManageOverdue>lambdaQuery()
//			.between(LoanManageOverdue::getRepaymentTime, timeStart, timeEnd)
//			.in(BaseEntity::getStatus, statusList));
//		BigDecimal amountWeek = loanManageOverdueList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//
//		//当月金额
//		String nowMonth = DateUtils.getNowMonth();
//		BigDecimal amountMonth = getOverdueByTime(nowMonth);
//
//		//当年金额
//		String yearStr = String.valueOf(DateUtil.thisYear());
//		BigDecimal amountYear = getOverdueByTime(yearStr);
//
//		ControlBoxVO controlBoxVO = ControlBoxVO.builder()
//			.overdueAmountDay(amountDay)
//			.overdueAmountWeek(amountWeek)
//			.overdueAmountMon(amountMonth)
//			.overdueAmountYear(amountYear)
//			.build();
//		return controlBoxVO;
//	}
//
//	@Override
//	public List<ControlBoxVO> controlBoxOverdueStage() {
//		ArrayList<ControlBoxVO> voList = new ArrayList<>();
//		String m1 = "M1";
//		getOverDueStageVo(voList, m1);
//		String m2 = "M2";
//		getOverDueStageVo(voList, m2);
//		String m3 = "M3";
//		getOverDueStageVo(voList, m3);
//		return voList;
//	}
//
//	@Override
//	public List<AmountChangeVO> controlBoxoverDueDiscussRecord(List<String> monthStr) {
//		ArrayList<AmountChangeVO> voList = new ArrayList<>();
//		for (String time : monthStr) {
//			AmountChangeVO vo = AmountChangeVO
//				.builder()
//				.queryTime(time)
//				.num(2)
//				.beforeAmount(new BigDecimal(1200))
//				.afterAmount(new BigDecimal(1000))
//				.build();
//			voList.add(vo);
//		}
//		return voList;
//	}

    @Override
    public Map<String, BigDecimal> getAbbrOverDueMapById(Set<Long> userIdList, Map<Long, String> enterpriseMap) {
        HashMap<String, BigDecimal> map = new HashMap<>();
        for (Long userId : userIdList) {
            String abbr = enterpriseMap.get(userId);
            if (StringUtil.isNotBlank(abbr)) {
                //逾期表
                List<String> statusList = Func.toStrList("1,2");
                List<LoanManageOverdue> iouList = baseMapper.selectList(Wrappers.<LoanManageOverdue>lambdaQuery()
                        .eq(LoanManageOverdue::getUserId, userId)
                        .in(BaseEntity::getStatus, statusList));
                BigDecimal overDueAmount = iouList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
                map.put(abbr, overDueAmount);
            }
        }
        return map;
    }

    @Override
    public void updateOverdueStatus(LoanManageRepaymentDTO loanManageRepaymentDTO) {
        List<LoanManageRepaymentTerm> loanManageRepaymentTerm = loanManageRepaymentTermService.list(Wrappers.<LoanManageRepaymentTerm>lambdaQuery()
                .eq(LoanManageRepaymentTerm::getRepaymentId, loanManageRepaymentDTO.getId()));
        if (ObjectUtil.isNotEmpty(loanManageRepaymentTerm)) {
            List<Long> repaymentPlanIdList = StreamUtil.map(loanManageRepaymentTerm, LoanManageRepaymentTerm::getRepaymentPlanId);
            //查询所有已逾期的还款计划
            List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIdList);
            Map<Long, LoanManageRepaymentPlan> loanManageRepaymentPlanMap = StreamUtil.toMap(loanManageRepaymentPlans, LoanManageRepaymentPlan::getId, loanManageRepaymentPlan -> loanManageRepaymentPlan);
            //过滤随借随还的还款计划使用产品配置的费用计算利息
            List<LoanManageRepaymentPlan> borrowRepaymentPlanList = loanManageRepaymentPlans.stream().filter(loanManageRepaymentPlan -> !GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())).collect(Collectors.toList());
            //分期还款查询还款计划的利息
            List<LoanManageRepaymentPlan> stagesRepaymentPlanList = loanManageRepaymentPlans.stream().filter(loanManageRepaymentPlan -> GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())).collect(Collectors.toList());
            Map<Long, LoanManageRepaymentPlan> stagesRepaymentPlanMap = StreamUtil.toMap(stagesRepaymentPlanList, LoanManageRepaymentPlan::getId, obj -> obj);
            //计算应还金额
            Map<Long, RepaymentExpenseResp> repaymentExpenseRespMap = loanManageRepaymentPlanService.calculateOverdueExpense(borrowRepaymentPlanList);

//			for (Long  repaymentPlanId : repaymentPlanIdList) {
//				RepaymentExpenseResp repaymentExpenseResp = repaymentExpenseRespMap.get(repaymentPlanId);
//				LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanMap.get(repaymentPlanId);
//				LoanManageRepaymentPlan stagesRepaymentPlan = stagesRepaymentPlanMap.get(repaymentPlanId);
//				//判断还款方式修改为已支付状态
//				if (ObjectUtil.isNotEmpty(loanManageRepaymentPlan)){
//					//判断还款方式
//					if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())){
//						if (ObjectUtil.isNotEmpty(stagesRepaymentPlan)){
//							//if (stagesRepaymentPlan)
//						}
//					}
//					}else {
//						if (repaymentExpenseResp!=null){
//							BigDecimal totalAmount = repaymentExpenseResp.getTotalAmount();
//
//						}
//					}
//			}


            update(Wrappers.<LoanManageOverdue>lambdaUpdate()
                    .set(LoanManageOverdue::getStatus, OverdueConstant.OverdueStatus.PAY.getCode())
                    .in(LoanManageOverdue::getRepaymentPlanId, StreamUtil.map(loanManageRepaymentTerm, LoanManageRepaymentTerm::getRepaymentPlanId)));
        }
    }

    @Override
    public void updateOverdueStatus(Long repaymentPlanId, Integer status) {
        update(Wrappers.<LoanManageOverdue>lambdaUpdate()
                .set(LoanManageOverdue::getStatus, OverdueConstant.OverdueStatus.PAY.getCode())
                .eq(LoanManageOverdue::getRepaymentPlanId, repaymentPlanId));
    }

//	/**
//	 * 根据逾期阶段查询
//	 *
//	 * @param voList
//	 * @param stage
//	 */
//	private void getOverDueStageVo(ArrayList<ControlBoxVO> voList, String stage) {
//		List<String> statusList = Func.toStrList("1,2");
//		List<LoanManageOverdue> overdueList = baseMapper.selectList(Wrappers.<LoanManageOverdue>lambdaQuery()
//			.in(BaseEntity::getStatus, statusList)
//			.like(LoanManageOverdue::getOverdueStage, stage));
//		BigDecimal amount = overdueList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//		ControlBoxVO vo = new ControlBoxVO();
//		vo.setOverdueStage(stage);
//		vo.setOverdueNum(overdueList.size());
//		vo.setOverdueAmount(amount);
//		voList.add(vo);
//	}

    /**
     * 根据时间查询逾期金额
     *
     * @param nowDay
     * @return
     */
    private BigDecimal getOverdueByTime(String nowDay) {
        String tenantId = AuthUtil.getTenantId();
        List<LoanManageOverdue> overdueDays = baseMapper.overDueList(nowDay, null, null, tenantId);
        BigDecimal reduce = overdueDays.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        return reduce;
    }


}
