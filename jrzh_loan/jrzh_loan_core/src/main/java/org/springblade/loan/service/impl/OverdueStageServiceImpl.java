/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.loan.entity.OverdueStage;
import org.springblade.loan.mapper.OverdueStageMapper;
import org.springblade.loan.service.IOverdueStageService;
import org.springblade.loan.vo.OverdueStageVO;
import org.springblade.loan.wrapper.OverdueStageWrapper;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 放款申请 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Service
@RequiredArgsConstructor
public class OverdueStageServiceImpl extends BaseServiceImpl<OverdueStageMapper, OverdueStage> implements IOverdueStageService {


	private final BladeRedis redis;
	private final RemoteUserService userService;


	@Override
	public String getNameByOverdueDay(Integer overdueDay) {
		for (OverdueStage overdueStage : selectList()) {
			if (overdueStage.getMinDay() >= overdueDay || overdueStage.getMaxDay() >= overdueDay) {
				Integer maxDay = overdueStage.getMaxDay();
				if (Objects.nonNull(maxDay) && overdueDay <= maxDay) {
					return overdueStage.getName();
				}
			}
 		}
		return selectMax();
	}

	@Override
	public IPage<OverdueStageVO> selectOverdueStagePage(OverdueStage overdueStage, Query query) {
		IPage<OverdueStage> page = baseMapper.selectPage(Condition.getPage(query), Condition.getQueryWrapper(overdueStage, OverdueStage.class));
		IPage<OverdueStageVO> pageVO = OverdueStageWrapper.build().pageVO(page);
		if (page.getTotal() <= 0){
			return pageVO;
		}
		List<OverdueStageVO> records = pageVO.getRecords();
		List<Long> updateUserIds = StreamUtil.map(records, OverdueStage::getUpdateUser);
		Map<Long, User> userMap = userService.getMapInId(updateUserIds).getData();
		records = records.stream().peek(overdueStageVO -> {
			// 设置操作人名称
			User user = userMap.get(overdueStageVO.getUpdateUser());
			overdueStageVO.setOperationName(Objects.isNull(user) ? "" : user.getName());
		}).collect(Collectors.toList());

		pageVO.setRecords(records);
		return pageVO;
	}

	@Override
	public String selectMax(){
		OverdueStage overdueStage = baseMapper.selectOne(new QueryWrapper<OverdueStage>().orderByDesc("max_day").last("limit 1"));
		return overdueStage.getName();
	}

	public List<OverdueStage> selectList() {
		String key = "overdue:stage:list";
		List<OverdueStage> overdueStages = redis.lRange(CacheUtil.formatCacheName(key, true), 0, -1);
		if (CollectionUtils.isEmpty(overdueStages)) {
			overdueStages = lambdaQuery().orderByAsc(OverdueStage::getMinDay).list();
		}
		return overdueStages;
	}
}
