package org.springblade.loan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description: 最近放款数据
 * @date 2025年03月15日 17:21
 */
@Data
public class LoanManageIouNewVo implements Serializable {

    /**
     * 融资用户id
     */
    @ApiModelProperty(value = "融资用户")
    private String userName;
    /**
     * 金额（万元）
     */
    @ApiModelProperty(value = "金额（万元）")
    private BigDecimal loanAmount;

    @ApiModelProperty(value = "日期")
    private String loanTime;

}
