/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.enums.IouEnum;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.service.ILoanManageIouService;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.vo.LoanManageIouNewVo;
import org.springblade.loan.vo.LoanManageIouTypeVO;
import org.springblade.loan.vo.LoanManageIouVO;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.wrapper.LoanManageIouWrapper;
import org.springblade.loan.wrapper.LoanManageRepaymentPlanWrapper;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 借据单 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@Service
@RequiredArgsConstructor
public class LoanManageIouServiceImpl extends BaseServiceImpl<LoanManageIouMapper, LoanManageIou> implements ILoanManageIouService {

    private final RemoteEnterpriseQuotaService enterpriseQuotaService;
    private final FinanceApplyMapper financeApplyMapper;
    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final ProductDirector productDirector;

    //private final CloudFinancingMapper cloudFinancingMapper;


    private final static String DATE_FORMAT = "M月d日";

    @Override
    public IPage<LoanManageIouVO> selectLoanManageIouPage(IPage<LoanManageIouVO> page, LoanManageIouVO loanManageIou) {
        return page.setRecords(baseMapper.selectLoanManageIouPage(page, loanManageIou));
    }

    @Override
    public boolean updateBatchFiveLevelCategoryById(List<Long> ids, Integer fiveLevelCategory) {
        BladeUser user = AuthUtil.getUser();
        List<LoanManageIou> list = new ArrayList<>();
        ids.forEach((id) -> {
            LoanManageIou entity = new LoanManageIou();
            if (user != null) {
                entity.setUpdateUser(user.getUserId());
            }
            entity.setUpdateTime(DateUtil.now());
            entity.setId(id);
            entity.setFiveLevelCategory(fiveLevelCategory);
            list.add(entity);
        });

        return updateBatchById(list);
    }

    @Override
    public List<LoanManageIouVO> listWithDayAndUser(Long userId, Integer recentDay) {
        LocalDate end = LocalDate.now();
        LocalDate begin = end.minusDays(recentDay);
        List<LoanManageIou> list = list(Wrappers.<LoanManageIou>lambdaQuery()
                .ge(LoanManageIou::getLoanTime, begin)
                .le(LoanManageIou::getLoanTime, end)
                .eq(LoanManageIou::getUserId, userId)
                .ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LoanManageIouVO> listVO = LoanManageIouWrapper.build().listVO(list);
        for (LoanManageIouVO loanManageIouVO : listVO) {
            loanManageIouVO.setTimes(LocalDateTimeUtil.format(loanManageIouVO.getLoanTime(), DATE_FORMAT));
        }
        return listVO;
    }

    @Override
    public List<LoanManageRepaymentVO> listWithDayAndUser(Long userId, Integer recentDay, Integer status) {
        LocalDate end = LocalDate.now();
        LocalDate begin = end.minusDays(recentDay);
        List<LoanManageRepayment> list = loanManageRepaymentService.list(Wrappers.<LoanManageRepayment>lambdaQuery()
                .ge(LoanManageRepayment::getRepaymentTime, begin)
                .le(LoanManageRepayment::getRepaymentTime, end)
                .eq(LoanManageRepayment::getUserId, userId)
                .eq(LoanManageRepayment::getStatus, status));
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LoanManageRepaymentVO> listVO = LoanManageRepaymentWrapper.build().listVO(list);
        for (LoanManageRepaymentVO loanManageRepaymentVO : listVO) {
            loanManageRepaymentVO.setTimes(LocalDateTimeUtil.format(loanManageRepaymentVO.getRepaymentTime(), DATE_FORMAT));
        }
        return listVO;
    }

//
//	@Override
//	public List<CreditPassVO> creditNewCreditReport(CreditReportDto dto) {
//
//		//查询应收和代采业务
//		List<CreditPassVO> vos = new ArrayList<>();
//		List<LoanManageIou> newLoanList = new ArrayList<>();
//		if (dto.getType() != 3) {
//			newLoanList = loanManageIouMapper.newLoansDtoList(dto);
//
//			//构建userId的Map
//			List<Long> userIds = newLoanList.stream().map(LoanManageIou::getUserId).collect(Collectors.toList());
//			Map<Long, User> mapInId = SpringUtil.getBean(IUserService.class).getMapInId(userIds);
//
//			vos = newLoanList.stream().map(loan -> {
//				FinanceApply finance = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, loan.getFinanceNo()));
//				User user = mapInId.get(loan.getUserId());
//				CreditPassVO vo = new CreditPassVO();
//				vo.setQueryTime(dto.getQueryTime());
//				vo.setType(finance.getGoodsType() == 1 ? "应收账款" : "代采融资");
//				vo.setFinanceNo(loan.getFinanceNo());
//				vo.setUserId(loan.getUserId());
//				vo.setUserName(ObjectUtil.isEmpty(user) ? "" : user.getName());
//				vo.setGoodsName(finance.getGoodsName());
//				vo.setIouAmount(loan.getIouAmount());
//				vo.setLoanTime(loan.getLoanTime().toString());
//
//				if (finance.getGoodsType() == 1) {
//					vo.setStatus(FinanceApplyStatusEnum.getName(finance.getStatus()));
//				} else {
//					vo.setStatus(PurchaseStateEnum.getName(finance.getStatus()));
//				}
//				return vo;
//			}).collect(Collectors.toList());
//		}
//
//		//云信业务
//		List<CreditPassVO> cloudVos = new ArrayList<>();
//		if (dto.getType() == 3 || dto.getType() == -1) {
//			List<CloudFinancing> newCloudList = cloudFinancingMapper.newLoansList(dto);
//
//			cloudVos = newCloudList.stream().map(cloud -> {
//				//CloudAssets cloudAssets = cloudAssetsMapper.selectById(cloud.getAssetsId());
//
//				CreditPassVO vo = new CreditPassVO();
//				vo.setQueryTime(dto.getQueryTime());
//				vo.setType("云信");
//				vo.setFinanceNo(cloud.getFinancingNo());
//				vo.setUserId(cloud.getApplyUser());
//				vo.setUserName(cloud.getCompanyName());
//				vo.setGoodsName(cloud.getCloudProductName());
//				//放款金额为空，暂用融资金额
//				vo.setIouAmount(cloud.getLoanMoney());
//				vo.setLoanTime(cloud.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
//				vo.setStatus(CloudFinanceStatusEnum.getValueByKey(cloud.getStatus()));
//				return vo;
//			}).collect(Collectors.toList());
//		}
//		vos.addAll(cloudVos);
//		return vos;
//	}
//
//	@Override
//	public List<CreditPassVO> creditNewCustomerReport(CreditReportDto dto) {
//		Integer type = dto.getType();
//		String time = dto.getQueryTime();
//		//查询新增的订单
//		List<CreditPassVO> creditPassVOS = creditNewCreditReport(dto);
//		Map<Long, List<CreditPassVO>> mapInId = creditPassVOS.stream().collect(Collectors.groupingBy(CreditPassVO::getUserId));
//		//取出userId集合去重
//		List<Long> userIds = creditPassVOS.stream().map(CreditPassVO::getUserId).distinct().collect(Collectors.toList());
//
//		//根据userid查询过去是否有成功融资记录
//		for (Long newId : userIds) {
//			String dateStr = getDateStr(time);
//			List<LoanManageIou> loanManageIous = new ArrayList<>();
//			//根据用户id查询信贷次数是否大于1
//			if (type == 1 || type == 2 || type == -1) {
//
//				loanManageIous = loanManageIouMapper.listIouByLtDateStr(dateStr, newId,dto.getTenantId());
//				/*loanManageIous = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
//					.eq(LoanManageIou::getUserId, newId)
//					.ne(BaseEntity::getStatus, IouEnum.INVALID.getCode())
//					.le(LoanManageIou::getLoanTime, time)
//					.ne(LoanManageIou::getLoanTime, time));*/
//
//				if (type == 1 || type == 2) {
//					List<Long> financeIds = loanManageIous.stream().map(LoanManageIou::getFinanceApplyId).collect(Collectors.toList());
//					List<FinanceApply> financeApplyList = new ArrayList<>();
//					if (CollUtil.isNotEmpty(financeIds)) {
//						financeApplyList = financeApplyMapper.selectList(Wrappers.<FinanceApply>lambdaQuery()
//							.in(FinanceApply::getId, financeIds)
//							.eq(FinanceApply::getGoodsType, type));
//						List<Long> financeIdList = financeApplyList.stream().map(FinanceApply::getId).collect(Collectors.toList());
//						if (CollUtil.isNotEmpty(financeIdList)) {
//							loanManageIous = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery().in(CollUtil.isNotEmpty(financeIdList), LoanManageIou::getFinanceApplyId, financeIdList));
//						} else {
//							loanManageIous = CollUtil.newArrayList();
//						}
//					}
//				}
//			}
//			List<CloudFinancing> cloudFinancings = new ArrayList<>();
//			if (type == 3 || type == -1) {
//				cloudFinancings = cloudFinancingMapper.listCloudByLtDateStr(dateStr,newId,dto.getTenantId());
//				/*List<Integer> list = Func.toIntList("4,9,10,11");
//				cloudFinancings = cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery()
//					.eq(CloudFinancing::getApplyUser, newId)
//					.le(CloudFinancing::getLoanTime, time)
//					.ne(CloudFinancing::getLoanTime, time)
//					.in(BaseEntity::getStatus, list));*/
//
//			}
//			int size = (loanManageIous.size() + cloudFinancings.size());
//			if (size != 0) {
//				//newCustomers = newCustomers - 1;
//				mapInId.remove(newId);
//			}
//		}
//		/*for (Long userId : userIds) {
//			//根据userid查询信贷的记录
//			List<LoanManageIou> loanManageIous = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
//				.eq(LoanManageIou::getUserId, userId)
//				.le(LoanManageIou::getLoanTime, dto.getQueryTime())
//				.ne(LoanManageIou::getLoanTime,dto.getQueryTime()));
//			List<Integer> list = Func.toIntList("4,9,10,11");
//			List<CloudFinancing> cloudIous =cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery()
//				.eq(CloudFinancing::getApplyUser, userId)
//				.le(CloudFinancing::getLoanTime, dto.getQueryTime())
//				.ne(CloudFinancing::getLoanTime, dto.getQueryTime())
//				.in(BaseEntity::getStatus, list));
//
//			if((loanManageIous.size()+cloudIous.size())>0){
//				//mapInId.remove(userId);
//				newUserIds.add(userId);
//			}
//		}*/
//		int size = mapInId.size();
//		//没有记录，取出userid对应的订单list封装vo
//		List<CreditPassVO> lists = mapInId.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
//		lists.stream().forEach(list -> {
//			list.setNum(size);
//		});
//		return lists;
//	}

    /**
     * 拼接时间 -01 格式
     * 2022 -> 2022-01-01
     * 2022-08 -> 2022-08-01
     * 2022-08-06 -> 2022-08-06
     *
     * @param time
     * @return
     */
    private String getDateStr(String time) {
        String[] split = time.split("-");
        int length = split.length;
        System.out.println("length = " + length);
        Integer num = 3 - (split.length);
        String str = "";
        if (num > 0) {
            for (Integer integer = 0; integer < num; integer++) {
                str = str + "-01";
            }
        }
        String timeDate = time + str;
        return timeDate;
    }

    @Override
    public LoanManageIouTypeVO selectType(String iouNo) {
        return baseMapper.selectType(iouNo);
    }

    @Override
    public Set<Long> financingCustomersNum() {
        //应收代采融资用户
        List<LoanManageIou> iouList = list(Wrappers.<LoanManageIou>lambdaQuery()
                .ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
        Set<Long> iouUserIds = iouList.stream().map(LoanManageIou::getUserId).collect(Collectors.toSet());
        List<String> statusList = Func.toStrList("4,9,10,11");
        //云信融资用户
        //TODO 需要迁移
//		List<CloudFinancing> cloudFinancings = cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery().in(BaseEntity::getStatus, statusList));
//		Set<Long> cloudUserIds = cloudFinancings.stream().map(CloudFinancing::getApplyUser).collect(Collectors.toSet());
//		iouUserIds.addAll(cloudUserIds);
        return iouUserIds;
    }

    @Override
    public Map<String,Object> getTotalLoanManageIou() {
        BigDecimal totalAmount = BigDecimal.ZERO;
        int userNum = 0;
        List<LoanManageIou> iouList = list(Wrappers.<LoanManageIou>lambdaQuery()
                .ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
        Map<String,Object> map = new HashMap<>();
        if(CollUtil.isEmpty(iouList)){
            map.put("totalAmount",totalAmount);
            map.put("userNum",userNum);
            return map;
        }
        totalAmount = iouList.stream().map(LoanManageIou::getIouAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        userNum = iouList.stream().map(LoanManageIou::getUserId).collect(Collectors.toSet()).size();

        map.put("totalAmount",totalAmount.divide(new BigDecimal(10000),2, RoundingMode.HALF_UP));


        map.put("userNum",userNum);
        return map;
    }

    @Override
    public List<LoanManageIouNewVo> selectLoanManageIouNewList() {
        return baseMapper.selectLoanManageIouNewList();
    }

//
//	@Override
//	public ControlBoxVO controlBoxFinancingAmount() {
//		ControlBoxVO controlBoxVO = new ControlBoxVO();
//		//今日融资金额
//		String nowDay = DateUtils.getNowDay();
//		BigDecimal IouDay = getIouAmountByTime(nowDay);
//		BigDecimal cloudDay = cloudFinancingService.getCloudAmountByTime(nowDay);
//		BigDecimal AmountDay = IouDay.add(cloudDay);
//		controlBoxVO.setFinancingAmountDay(AmountDay);
//
//		//本周融资金额
//		List<String> nowWeek = DateUtils.getNowWeek(7);
//		BigDecimal IouWeek = getIouAmountByTimes(nowWeek);
//		BigDecimal cloudWeek = cloudFinancingService.getCloudAmountByTimes(nowWeek);
//		BigDecimal AmountWeek = IouWeek.add(cloudWeek);
//		controlBoxVO.setFinancingAmountWeek(AmountWeek);
//
//		//本月融资金额
//		String nowMonth = DateUtils.getNowMonth();
//		BigDecimal IouMonth = getIouAmountByTime(nowMonth);
//		BigDecimal cloudMonth = cloudFinancingService.getCloudAmountByTime(nowMonth);
//		BigDecimal AmountMonth = IouMonth.add(cloudMonth);
//		controlBoxVO.setFinancingAmountMon(AmountMonth);
//
//		//今年融资金额
//		int year = cn.hutool.core.date.DateUtil.thisYear();
//		String yearStr = String.valueOf(year);
//		BigDecimal IouYear = getIouAmountByTime(yearStr);
//		BigDecimal cloudYear = cloudFinancingService.getCloudAmountByTime(yearStr);
//		BigDecimal AmountYear = IouYear.add(cloudYear);
//		controlBoxVO.setFinancingAmountYear(AmountYear);
//		return controlBoxVO;
//	}

    @Override
    public BigDecimal getIouAmountByTimes(List<String> nowWeek) {
        String timeStart = nowWeek.get(0);
        String timeEnd = nowWeek.get(1);
        List<LoanManageIou> loanManageIous = baseMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
                .between(LoanManageIou::getLoanTime, timeStart, timeEnd)
                .ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
        return loanManageIous.stream().map(LoanManageIou::getIouAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
//
//	@Override
//	public List<ControlBoxVO> controlBoxCompanyDetails(String nowMonth) {
//		String tenantId = AuthUtil.getTenantId();
//		//本月借据放款
//		List<LoanManageIou> IouList = baseMapper.newLoansList(nowMonth, null, tenantId);
//		CreditReportDto dtoYear = new CreditReportDto();
//		dtoYear.setTenantId(tenantId);
//		dtoYear.setQueryTime(nowMonth);
//		//本月云信放款
//		List<CloudFinancing> cloudList = cloudFinancingMapper.newLoansList(dtoYear);
//		//本月还款
//		List<LoanManageRepayment> repaymentList = loanManageRepaymentMapper.payLoanList(nowMonth, null, tenantId);
//		//本月逾期
//		List<LoanManageOverdue> overdueList = loanManageOverdueMapper.overDueList(nowMonth, null, null, tenantId);
//
//		//转为map
//		Map<Long, List<LoanManageIou>> iouMap = IouList.stream().collect(Collectors.groupingBy(LoanManageIou::getUserId));
//		Map<Long, List<CloudFinancing>> cloudMap = cloudList.stream().collect(Collectors.groupingBy(CloudFinancing::getApplyUser));
//		Map<Long, List<LoanManageRepayment>> repaymentMap = repaymentList.stream().collect(Collectors.groupingBy(LoanManageRepayment::getUserId));
//		Map<Long, List<LoanManageOverdue>> overdueMap = overdueList.stream().collect(Collectors.groupingBy(LoanManageOverdue::getUserId));
//
//		//取出userId
//		Set<Long> userIds = new HashSet<>();
//		Set<Long> iouId = iouMap.keySet();
//		Set<Long> cloudId = cloudMap.keySet();
//		Set<Long> repaymentId = repaymentMap.keySet();
//		Set<Long> overdueId = overdueMap.keySet();
//		userIds.addAll(iouId);
//		userIds.addAll(cloudId);
//		userIds.addAll(repaymentId);
//		userIds.addAll(overdueId);
//
//		List<ControlBoxVO> vos = new ArrayList<>();
//		List<User> userList = SpringUtil.getBean(IUserService.class).list(Wrappers.<User>lambdaQuery().in(CollUtil.isNotEmpty(userIds),BaseEntity::getId, userIds));
//		for (User user : userList) {
//			Long userId = user.getId();
//			//根据userId取出value
//			List<LoanManageIou> iouList = iouMap.get(userId);
//			List<CloudFinancing> cloudsList = cloudMap.get(userId);
//			List<LoanManageRepayment> repaymentsList = repaymentMap.get(userId);
//			List<LoanManageOverdue> overduesList = overdueMap.get(userId);
//
//			BigDecimal iouAmount = BigDecimal.ZERO;
//			BigDecimal cloudsAmount = BigDecimal.ZERO;
//			BigDecimal repaymentsAmount = BigDecimal.ZERO;
//			BigDecimal overdueAmount = BigDecimal.ZERO;
//
//			Integer iouSize = 0;
//			Integer cloudsSize = 0;
//			Integer overdueSize = 0;
//			if (CollUtil.isNotEmpty(iouList)) {
//				iouAmount = iouList.stream().map(LoanManageIou::getIouAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//				iouSize = iouList.size();
//			}
//			if (CollUtil.isNotEmpty(cloudsList)) {
//				cloudsAmount = cloudsList.stream().map(CloudFinancing::getLoanMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
//				cloudsSize = cloudsList.size();
//			}
//			if(CollUtil.isNotEmpty(repaymentsList)){
//				repaymentsAmount = repaymentsList.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//			}
//			if(CollUtil.isNotEmpty(overduesList)){
//				overdueAmount = overduesList.stream().map(LoanManageOverdue::getShouldPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//				overdueSize = overduesList.size();
//			}
//			//融资金额
//			BigDecimal amount = iouAmount.add(cloudsAmount);
//			//用户姓名
//			ControlBoxVO vo = ControlBoxVO.builder()
//				.userId(String.valueOf(userId))
//				.userName(user.getName())//用户名
//				.financingAmountMon(amount)//借款金额
//				.financingNum(iouSize + cloudsSize)//借款笔数
//				.repaymentAmountMon(repaymentsAmount)//还款金额
//				.overdueAmountMon(overdueAmount)//逾期金额
//				.overdueNum(overdueSize)//逾期笔数
//				.build();
//			vos.add(vo);
//		}
//		return vos;
//	}

//	@Override
//	public Map<String, BigDecimal> getAbbrMapById(Set<Long> userIdList, Map<Long, String> enterpriseMap) {
//		HashMap<String, BigDecimal> map = new HashMap<>();
//		for (Long userId : userIdList) {
//			String abbr = enterpriseMap.get(userId);
//			if(StringUtil.isNotBlank(abbr)){
//				//借据表
//				List<LoanManageIou> iouList = baseMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
//					.eq(LoanManageIou::getUserId, userId)
//					.ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
//				BigDecimal iouAmount = iouList.stream().map(LoanManageIou::getIouAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//				List<String> status = Func.toStrList("4,9,10,11");
//				//云信表
//				List<CloudFinancing> cloudFinancingList = cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery()
//					.in(BaseEntity::getStatus, status)
//					.eq(CloudFinancing::getApplyUser, userId));
//				BigDecimal cloudAmount = cloudFinancingList.stream().filter(e-> e.getLoanMoney() != null).map(CloudFinancing::getLoanMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
//				BigDecimal add = iouAmount.add(cloudAmount);
//				map.put(abbr,add);
//			}
//		}
//		return map;
//	}

//	@Override
//	public Map<String, Integer> getAbbrMapNumById(Set<Long> userIdList, Map<Long, String> enterpriseMap) {
//		HashMap<String, Integer> map = new HashMap<>();
//		for (Long userId : userIdList) {
//			String abbr = enterpriseMap.get(userId);
//			if(StringUtil.isNotBlank(abbr)){
//				//借据表
//				List<LoanManageIou> iouList = baseMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
//					.eq(LoanManageIou::getUserId, userId)
//					.ne(BaseEntity::getStatus, IouEnum.INVALID.getCode()));
//				Set<Long> iouUserIdList = iouList.stream().map(LoanManageIou::getUserId).collect(Collectors.toSet());
//				int iouNum = iouUserIdList.size();
//				List<String> status = Func.toStrList("4,9,10,11");
//				//云信表
//				List<CloudFinancing> cloudFinancingList = cloudFinancingMapper.selectList(Wrappers.<CloudFinancing>lambdaQuery()
//					.in(BaseEntity::getStatus, status)
//					.eq(CloudFinancing::getApplyUser, userId));
//				Set<Long> cloudUserIdList = cloudFinancingList.stream().filter(e-> e.getLoanMoney() != null).map(CloudFinancing::getApplyUser).collect(Collectors.toSet());
//				int cloudNum = cloudUserIdList.size();
//				Integer num = iouNum+cloudNum;
//				map.put(abbr,num);
//			}
//		}
//		return map;
//	}

    @Override
    public List<LoanManageIou> queryloanManageIous(List<String> financeNos) {
        return baseMapper.queryloanManageIous(financeNos);
    }

    @Override
    public IPage<LoanManageIouVO> disbursementRecord(Long companyId, Query query) {
        List<FinanceApply> financeApplies = SpringUtil.getBean(IFinanceApplyService.class).lambdaQuery()
                .eq(FinanceApply::getCapitalId, companyId)
                .in(FinanceApply::getGoodsType, productDirector.getAllType()).list();
        List<Long> userIds = StreamUtil.map(financeApplies, FinanceApply::getUserId);
        if (cn.hutool.core.util.ObjectUtil.isEmpty(financeApplies)) {
            return Condition.getPage(query);
        }
        Map<Long, String> userNameMap = UserUtils.mapUserName(userIds);
        List<String> financeNo = StreamUtil.map(financeApplies, FinanceApply::getFinanceNo);
        //通过获取 融资编号获取userId
        Map<String, FinanceApply> stringLongMap = StreamUtil.toMap(financeApplies, FinanceApply::getFinanceNo, e -> e);

        IPage<LoanManageIou> loanManageIouIPage = SpringUtil.getBean(ILoanManageIouService.class).lambdaQuery().in(LoanManageIou::getFinanceNo, financeNo).page(Condition.getPage(query));
        List<LoanManageIou> list = loanManageIouIPage.getRecords();
        List<LoanManageIouVO> loanManageIouVOS = LoanManageIouWrapper.build().listVO(list);
        List<LoanManageIouVO> collect = loanManageIouVOS.stream().map(loanManageIouVO -> {
            FinanceApply financeApply = stringLongMap.get(loanManageIouVO.getFinanceNo());
            //获取名称
            Long aLong = financeApply.getUserId();
            loanManageIouVO.setAmount(financeApply.getAmount());
            if (cn.hutool.core.util.ObjectUtil.isNotEmpty(aLong)) {
                loanManageIouVO.setUserName(userNameMap.get(aLong));
            }
            return loanManageIouVO;
        }).collect(Collectors.toList());
        IPage<LoanManageIouVO> loanManageIouVOPage = LoanManageIouWrapper.build().pageVO(loanManageIouIPage);
        loanManageIouVOPage.setRecords(collect);
        return loanManageIouVOPage;
    }

    @Override
    public IPage<LoanManageRepaymentVO> repaymentList(Long companyId, Query page) {

        List<FinanceApply> list = SpringUtil.getBean(IFinanceApplyService.class).lambdaQuery().eq(FinanceApply::getCapitalId, companyId).list();
        if (cn.hutool.core.util.ObjectUtil.isEmpty(list)) {
            return Condition.getPage(page);
        }
        List<String> map = StreamUtil.map(list, FinanceApply::getFinanceNo);
        List<LoanManageIou> loanManageIous = SpringUtil.getBean(ILoanManageIouService.class).lambdaQuery().in(LoanManageIou::getFinanceNo, map).list();
        if (cn.hutool.core.util.ObjectUtil.isEmpty(loanManageIous)) {
            return Condition.getPage(page);
        }
        List<String> iouNo = StreamUtil.map(loanManageIous, LoanManageIou::getIouNo);
        IPage<LoanManageRepayment> loanManageRepaymentIPage = SpringUtil.getBean(ILoanManageRepaymentService.class).lambdaQuery().in(LoanManageRepayment::getIouNo, iouNo).page(Condition.getPage(page));
        Page<LoanManageRepaymentVO> loanManageRepaymentVOPage = LoanManageRepaymentWrapper.build().pageVO(loanManageRepaymentIPage);
        List<LoanManageRepaymentVO> records = loanManageRepaymentVOPage.getRecords();
        if (cn.hutool.core.util.ObjectUtil.isEmpty(records)) {
            return Condition.getPage(page);
        }
        List<Long> operatorUserIds = StreamUtil.map(records, LoanManageRepayment::getOperatorUserId);
        List<Long> map1 = StreamUtil.map(records, LoanManageRepayment::getUserId);
        map1.addAll(operatorUserIds);
        Map<Long, String> userNameMap = UserUtils.mapUserName(map1);
        records.forEach(repaymentVo -> {
            String voucher = repaymentVo.getVoucher();

            if (cn.hutool.core.util.ObjectUtil.isNotEmpty(voucher)) {
                List<Attach> attachList = SpringUtil.getBean(IAttachService.class)
                        .lambdaQuery()
                        .in(Attach::getId, Func.toLongList(voucher)).list();
                repaymentVo.setAttachList(attachList);
            }

            BigDecimal interest = repaymentVo.getInterest();
            BigDecimal add = interest.add(repaymentVo.getServiceCharge()).add(repaymentVo.getPenaltyInterest());
            repaymentVo.setTotalAmount(add);
            repaymentVo.setUserName(userNameMap.get(repaymentVo.getOperatorUserId()));
            repaymentVo.setCustomerName(userNameMap.get(repaymentVo.getUserId()));
        });

        loanManageRepaymentVOPage.setRecords(records);
        return loanManageRepaymentVOPage;
    }

    @Override
    public Boolean removeIou(String financeNo) {
        return remove(this.lambdaQuery().eq(LoanManageIou::getFinanceNo, financeNo));
    }

    @Override
    public LoanManageIou getByIouNo(String iouNo) {
        return getOne(Wrappers.<LoanManageIou>lambdaQuery().eq(LoanManageIou::getIouNo, iouNo));
    }

    @Override
    public List<LoanManageIou> listByIouNo(List<String> iouNOList) {
        return list(Wrappers.<LoanManageIou>lambdaQuery().in(LoanManageIou::getIouNo, iouNOList));
    }

    @Override
    public boolean canClose(LoanManageIou loanManageIou) {
        return loanManageIou.getStatus().equals(IouEnum.WAIT_FINISH.getCode())
                || loanManageIou.getStatus().equals(IouEnum.OVERDUE_WAIT_FINISH.getCode())
                || loanManageIou.getStatus().equals(IouEnum.INVALID.getCode());
    }

    @Override
    public List<String> getIouNos(String time, Integer goodsType) {
        return listObjs(Wrappers.<LoanManageIou>lambdaQuery()
                .select(LoanManageIou::getIouNo)
                .eq(LoanManageIou::getGoodsType, goodsType)
                .likeRight(LoanManageIou::getLoanTime, time), e -> (String) e);
    }

//    @Override
//    public Set<Long> getNewCustomer(List<LoanInfoDTO> loanInfoDTOS, List<LoanManageIou> loanManageIouList, LocalDate time) {
//        //本时间区间内的订单之前 曾下单过的用户
//        List<Long> allBeforeOrderUserId = loanManageIouList.stream().filter(e -> e.getLoanTime().isBefore(time))
//                .map(LoanManageIou::getUserId).distinct().collect(Collectors.toList());
//        //只要之前没有下单过就是新用户
//        return loanInfoDTOS.stream().filter(order -> !allBeforeOrderUserId.contains(order.getUserId()))
//                .map(LoanManageIou::getUserId).collect(Collectors.toSet());
//    }

    @Override
    public List<LoanManageIou> listByTimeBefore(LocalDate time) {
        return list(Wrappers.<LoanManageIou>lambdaQuery().select(LoanManageIou::getLoanTime
                        , LoanManageIou::getId
                        , LoanManageIou::getUserId)
                .lt(LoanManageIou::getLoanTime, time));
    }

    @Override
    public Set<Long> getCustomerNewCountByTime(String time, List<Integer> goodsType, String tenantId) {
        //查出time 最早的时间
        LoanManageIou one = getOne(Wrappers.<LoanManageIou>lambdaQuery()
                .likeRight(LoanManageIou::getLoanTime, time)
                .ne(LoanManageIou::getStatus, IouEnum.INVALID.getCode())
                .orderByAsc(LoanManageIou::getLoanTime)
                .eq(LoanManageIou::getTenantId, tenantId)
                .last("limit 1"));
        if (one == null) {
            return Collections.emptySet();
        }
        //新下单的用户
        return baseMapper.newCustomerCount(time, one.getLoanTime(), goodsType, tenantId);
    }

    @Override
    public List<LoanManageIou> listCustomerNewByTime(String queryTime, List<Integer> goodsTypeList, String tenantId) {
        //查出time 最早的时间
        LoanManageIou one = getOne(Wrappers.<LoanManageIou>lambdaQuery()
                .likeRight(LoanManageIou::getLoanTime, queryTime)
                .ne(LoanManageIou::getStatus, IouEnum.INVALID.getCode())
                .in(LoanManageIou::getGoodsType, goodsTypeList)
                .orderByAsc(LoanManageIou::getLoanTime)
                .eq(LoanManageIou::getTenantId, tenantId)
                .last("limit 1"));
        if (one == null) {
            return Collections.emptyList();
        }
        //新下单的用户
        return baseMapper.selectCustomerNewByTime(queryTime, one.getLoanTime(), goodsTypeList, tenantId).stream()
                .sorted(Comparator.comparing(LoanManageIou::getGoodsType)).collect(Collectors.toList());
    }

    @Override
    public List<LoanManageIou> listNewByTime(String queryTime, List<Integer> goodsTypeList, String tenantId) {
        //新下单的用户
        return baseMapper.newLoansDtoList(queryTime, goodsTypeList, tenantId).stream()
                .sorted(Comparator.comparing(LoanManageIou::getGoodsType)).collect(Collectors.toList());
    }

    /**
     * 连表查询返回vo
     * @param loanManageIou
     * @return
     */
    @Override
    public List<LoanManageIouVO> voList(LoanManageIou loanManageIou) {
        return baseMapper.voList(loanManageIou);
    }

    @Override
    public BigDecimal getIouAmountByTime(String timeStr) {
        String tenantId = AuthUtil.getTenantId();
        List<LoanManageIou> IouYearList = baseMapper.newLoansList(timeStr, null, tenantId);
        return IouYearList.stream().map(LoanManageIou::getIouAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    public LoanManageIou getByFinancingNo(String financeNo) {
        return lambdaQuery().eq(LoanManageIou::getFinanceNo, financeNo).one();
    }


    @Override
    public LoanManageIouVO adjustmentDetail(Long id) {
        LoanManageIou loanManageIou = baseMapper.selectById(id);
        if (Objects.isNull(loanManageIou)) {
            return null;
        }
        LoanManageIouVO loanManageIouVO = LoanManageIouWrapper.build().entityVO(loanManageIou);
        String financingNo = loanManageIou.getFinanceNo();
        // 查询融资申请信息
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financingNo));
        loanManageIouVO.setFinanceApply(financeApply);
        User user = UserUtils.getUserById(financeApply.getUserId());
        loanManageIouVO.setUserName(user.getName());
        // 查询还款账户
        EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(financeApply.getGoodsId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), financeApply.getUserId()).getData();
        if (ObjectUtil.isNotEmpty(enterpriseQuotaVO)) {
            loanManageIouVO.setCapitalName(enterpriseQuotaVO.getCapitalName());
            loanManageIouVO.setBankAccount(enterpriseQuotaVO.getBankCardNo());
        }
        // 查询还款计划
        //List<LoanManageRepaymentPlan> loanManageRepaymentPlanList = loanManageRepaymentPlanService.getListByIouNo(loanManageIouVO.getIouNo());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlanList = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, loanManageIouVO.getIouNo())
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod));
        loanManageIouVO.setLoanManageRepaymentPlanList(LoanManageRepaymentPlanWrapper.build().listVO(loanManageRepaymentPlanList));
        // 查询统一社会代码
        CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoService.getByCustomerId(user.getId());
        if (Objects.nonNull(customerBusinessInfo)) {
            loanManageIouVO.setCreditCode(customerBusinessInfo.getCreditCode());
        }
        // 查询发票
        //BillInvoice invoice = invoiceService.getByFinanceNo(financeApply.getFinanceNo());
        // 查询还款明细
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.listAllRelationLoanRepayment(financingNo);
        List<LoanManageRepaymentVO> loanManageRepaymentVOS = LoanManageRepaymentWrapper.build().listVO(loanManageRepayments);
        loanManageIouVO.setLoanManageRepayments(loanHistoryRepaymentPlanList(loanManageRepaymentVOS));
//		if (Objects.nonNull(invoice)) {
//			loanManageIouVO.setInvoiceNo(invoice.getInvoiceNo());
//		}
        return loanManageIouVO;
    }

    private List<LoanManageRepaymentVO> loanHistoryRepaymentPlanList(List<LoanManageRepaymentVO> loanManageRepaymentVOS) {
        BigDecimal totalAmount = loanManageRepaymentVOS.stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal interest = loanManageRepaymentVOS.stream().map(LoanManageRepayment::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principal = loanManageRepaymentVOS.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = loanManageRepaymentVOS.stream().map(LoanManageRepayment::getServiceCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal penaltyInterest = loanManageRepaymentVOS.stream().map(LoanManageRepayment::getPenaltyInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        return loanManageRepaymentVOS.stream().peek(loanManageRepayment -> {
            loanManageRepayment.setMonthlyServiceFee(serviceFee);
            loanManageRepayment.setMonthlyPrincipal(principal);
            loanManageRepayment.setMonthlyInterest(interest);
            loanManageRepayment.setMonthlySupply(totalAmount);
            loanManageRepayment.setMonthlyPenaltyInterest(penaltyInterest);
        }).collect(Collectors.toList());
    }

    @Override
    public LoanManageIou saveLoanManageIou(FinanceApply financeApply) {
        LoanManageIou loanManageIou = LoanManageIou.builder()
                .financeNo(financeApply.getFinanceNo())
                .userId(financeApply.getUserId())
                .goodsType(financeApply.getGoodsType())
                .iouAmount(financeApply.getAmount())
                .financeApplyId(financeApply.getId())
                .loanTime(LocalDate.now())
                .capitalId(financeApply.getCapitalId())
                .goodsId(financeApply.getGoodsId())
                .period(financeApply.getLoadTerm())
                .expireTime(financeApply.getExpireTime().toLocalDate())
                .iouNo(CodeUtil.generateCode(CodeEnum.IOU_STOCK_CODE))
                .periodUnit(financeApply.getLoadTermUnit())
                .repaymentType(financeApply.getRepaymentType())
                .firstRepaymentDate(getFirstRepaymentDate(financeApply.getLoadTermUnit(), LocalDate.now(), financeApply.getLoadTerm()))
                .build();
        save(loanManageIou);
        return loanManageIou;
    }

    @Override
    public LoanManageIou saveLoanManageIou(FinanceApply financeApply, LocalDate startTime, LocalDate refundTime) {
        LoanManageIou loanManageIou = LoanManageIou.builder()
                .financeNo(financeApply.getFinanceNo())
                .userId(financeApply.getUserId())
                .goodsType(financeApply.getGoodsType())
                .iouAmount(financeApply.getAmount())
//                // 使用银行放款的金额
//                .iouAmount(financeApply.getLendingAmount())
                .financeApplyId(financeApply.getId())
                .loanTime(LocalDate.now())
                .capitalId(financeApply.getCapitalId())
                .goodsId(financeApply.getGoodsId())
                .period(financeApply.getLoadTerm())
                .expireTime(financeApply.getExpireTime().toLocalDate())
                .iouNo(CodeUtil.generateCode(CodeEnum.IOU_STOCK_CODE))
                .periodUnit(financeApply.getLoadTermUnit())
                .repaymentType(financeApply.getRepaymentType())
                .firstRepaymentDate(refundTime)
                .loanTime(startTime)
                .expireTime(refundTime)
                .build();
        save(loanManageIou);
        return loanManageIou;
    }

    /**
     * 客户贷款报表
     *
     * @return
     */
    //TODO   报表迁移
//	@Override
//	public List<CreditReport> customerCreditReport(Integer year, String month, Integer type, Long userId, String tentId) {
//		List<String> timeList = new ArrayList<>(32);
//		/*if (year == -1) {
//			year = cn.hutool.core.date.DateUtil.year(new Date());
//		}*/
//		timeList = getDateList(month, year);
//
//
//		ArrayList<CreditReport> creditReports = new ArrayList<>();
//		for (String time : timeList) {
//
//			List<LoanManageIou> newLoanList = new ArrayList<>();
//			List<LoanManageRepayment> payList = new ArrayList<>();
//			List<LoanManageOverdue> overDueList = new ArrayList<>();
//			List<LoanManageRepaymentPlan> payable = new ArrayList<>();
//
//			if (type == 1 || type == 2 || type == -1) {
//				//新增贷款
//				newLoanList = loanManageIouMapper.newLoansByNameList(time, type, userId, tentId);
//				//查询待还
//				payable = loanManageRepaymentPlanMapper.payableByUserIdList(time, type, userId, tentId);
//
//				//已还完笔数
//				payList = loanManageRepaymentMapper.payLoanByNameList(time, type, userId, tentId);
//				//逾期订单
//				overDueList = loanManageOverdueMapper.overDueList(time, type, userId, tentId);
//			}
//
//			List<CloudFinancing> newCloudList = new ArrayList<>();
//			List<CloudAssets> unPayCloudList = new ArrayList<>();
//			List<CloudAssets> overDueCloud = new ArrayList<>();
//			List<CloudPaymentList> payCloudList = new ArrayList<>();
//			List<CloudFinancing> cloudUserList = new ArrayList<>();
//			if (type == 3 || type == -1) {
//				//查询融资企业云信新增信贷
//				CreditReportDto dto = new CreditReportDto();
//				dto.setQueryTime(time);
//				dto.setUserId(userId);
//				dto.setTenantId(tentId);
//				newCloudList = cloudFinancingMapper.newLoansList(dto);
//
//
//				unPayCloudList = cloudAssetsMapper.unCloudpayByNameList(time, userId, tentId);
//
//				payCloudList = cloudPaymentListMapper.payByNameList(time, userId, tentId);
//				//云信逾期订单
//				overDueCloud = cloudAssetsMapper.overdueList(time, userId, tentId);
//
//			}
//
//			//当月待还笔数
//			int umPayNum = payable.size() + unPayCloudList.size();
//			//当月已还笔数
//			int payNum = payCloudList.size() + payList.size();
//			//放款金额(元)
//			BigDecimal iouAmount = newLoanList.stream().map(LoanManageIou::getIouAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal cloudAmount = newCloudList.stream().map(CloudFinancing::getLoanMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal loanTotal = cloudAmount.add(iouAmount);
//
//			//新增贷款数量
//			int newLoanNum = (newLoanList.size()) + (newCloudList.size());
//
//			//还款金额(元)
//			BigDecimal payLoan = payList.stream().map(LoanManageRepayment::getPrincipal).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal payCloud = payCloudList.stream().map(CloudPaymentList::getActualAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			//还款汇总
//			BigDecimal payTotal = payLoan.add(payCloud);
//
//			//逾期金额
//			BigDecimal overDue = overDueList.stream().map(LoanManageOverdue::getShouldPrincipal).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal overCloud = overDueCloud.stream().map(CloudAssets::getCloudBillAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//			BigDecimal overDueMoney = overDue.add(overCloud);
//
//			//应收和代采实还利息
//			BigDecimal payInterest = payList.stream().map(LoanManageRepayment::getInterest).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//
//			CreditReport creditReport = CreditReport.builder()
//				.queryTime(time)//时间
//				.numberOfLoans(newLoanNum)//新增放款笔数
//				.numberOfUnPay(umPayNum)//未还款笔数
//				.numberOfPay(payNum)//已还款笔数
//				.loanAmount(loanTotal)//放款金额汇总
//				.payAmount(payTotal)//已还款金额汇总
//				.overDueMoney(overDueMoney)//逾期金额
//				.payInterest(payInterest)//实还利息
//				.build();
//			creditReports.add(creditReport);
//		}
//
//		return creditReports;
//	}
    private LocalDate getFirstRepaymentDate(Integer loadTermUnit, LocalDate startTime, Integer loanTerm) {
        LocalDate firstRepaymentDate;
        if (GoodsEnum.TERM.getCode().equals(loadTermUnit)) {
            firstRepaymentDate = startTime.plusMonths(1);
            if (firstRepaymentDate.getDayOfMonth() > 28) {
                return firstRepaymentDate.withDayOfMonth(28);
            }
        } else {
            firstRepaymentDate = startTime.plusDays(loanTerm);
        }
        return firstRepaymentDate;
    }
}
