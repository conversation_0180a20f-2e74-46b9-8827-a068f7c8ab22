/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.BillPayStatusEnum;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.utils.BorrowAndReturnUtils;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.feign.RemoteCustomerGoodsService;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.expense.entity.BillInvoiceDetail;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IBillInvoiceDetailService;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.loan.entity.*;
import org.springblade.loan.enums.HistoryAlterationEnum;
import org.springblade.loan.enums.LoanAlterationEnum;
import org.springblade.loan.expense.ExpenseCalculate;
import org.springblade.loan.expense.ExpenseCalculateStrategy;
import org.springblade.loan.expense.req.RepaymentExpenseReq;
import org.springblade.loan.expense.req.SettleExpenseReq;
import org.springblade.loan.expense.resp.RepaymentExpenseResp;
import org.springblade.loan.expense.util.RepaymentUtil;
import org.springblade.loan.feign.RemoteRepaymentBiz;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.mapper.LoanManageRepaymentMapper;
import org.springblade.loan.mapper.LoanManageRepaymentPlanMapper;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.*;
import org.springblade.loan.vo.front.FrontRepaymentPlanVo;
import org.springblade.loan.vo.front.PrepaymentVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.loan.wrapper.RepaymentFeeWrapper;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.GoodsExpenseRule;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.utils.GoodsFeeRulesUtil;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.service.IGoodsExpenseRuleService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.moudle.pubproduct.service.impl.ProductFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 还款计划表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-09
 */
@Service
@AllArgsConstructor
@Slf4j
public class LoanManageRepaymentPlanServiceImpl extends BaseServiceImpl<LoanManageRepaymentPlanMapper, LoanManageRepaymentPlan> implements ILoanManageRepaymentPlanService {
    private final LoanManageIouMapper loanManageIouMapper;
    private final FinanceApplyMapper financeApplyMapper;
    private final RemoteCustomerGoodsService customerGoodsService;
    private final RemoteEnterpriseQuotaService enterpriseQuotaService;
    private final IExpenseOrderService billExpenseOrderService;
    private final IGoodsExpenseRuleService goodsExpenseRuleService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final LoanManageRepaymentPlanMapper loanManageRepaymentPlanMapper;
    private final LoanManageRepaymentMapper loanManageRepaymentMapper;
    private final IExpenseOrderDetailService platformExpensesService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IRepaymentFeeService repaymentFeeService;
    private final ExpenseCalculateStrategy expenseCalculateStrategy;
    private final ProductDirector productDirector;
    private final ProductFactory productFactory;
    private final IBillInvoiceDetailService billInvoiceDetailService;
    private final RemoteRepaymentBiz remoteRepaymentBiz;


    @Override
    public HashMap<String, Object> getMapByIouNo(String iouNo) {

        HashMap<String, Object> resultMap = Maps.newHashMap();
        // 还款计划集合，按第几期数排序
        List<LoanManageRepaymentPlan> repaymentPlans = getListByIouNo(iouNo);
        if (Func.isEmpty(repaymentPlans)) {
            return resultMap;
        }
        LoanManageIou iouEntity = loanManageIouMapper.selectOne(Wrappers.<LoanManageIou>lambdaQuery()
                .eq(LoanManageIou::getIouNo, iouNo));
        resultMap.put("repaymentPlanList", repaymentPlans);
        // 借据总金额
        resultMap.put("iouAmount", iouEntity.getIouAmount());
        //应还罚息总额
        BigDecimal punishInterestTotal = BigDecimal.ZERO;
        //剩余应还本金总额
        BigDecimal surplusPrincipalTotal = BigDecimal.ZERO;
        //剩余应还利息总额
        BigDecimal surplusInterestTotal = BigDecimal.ZERO;
        //剩余应还罚息总额
        BigDecimal surplusPunishInterestTotal = BigDecimal.ZERO;
        //剩余应还服务费总额
        BigDecimal surplusServiceChargeTotal = BigDecimal.ZERO;
        // 计算总额

        //应还罚息
        resultMap.put("punishInterestTotal", punishInterestTotal);
        //剩余应还本金
        resultMap.put("surplusPrincipalTotal", surplusPrincipalTotal);
        //剩余应还利息
        resultMap.put("surplusInterestTotal", surplusInterestTotal);
        //剩余应还罚息
        resultMap.put("surplusPunishInterestTotal", surplusPunishInterestTotal);
        //剩余应还服务费
        resultMap.put("surplusServiceChargeTotal", surplusServiceChargeTotal);
        return resultMap;
    }

    @Override
    public Integer insertBatch(List<LoanManageRepaymentPlan> list) {
        return baseMapper.insertBatchSomeColumn(list);
    }

    @Override
    public List<LoanManageRepaymentPlan> getListByIouNo(String iouNo) {

        // 根据借据单号查询还款计划，按期数升序
        List<LoanManageRepaymentPlan> repaymentPlanList = lambdaQuery()
                .eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod).list();

        // 过滤逾期的还款计划
        List<LoanManageRepaymentPlan> overdueList = repaymentPlanList
                .stream()
                .filter(RepaymentUtil::isOverdue)
                .collect(Collectors.toList());
        // 如果没有逾期的，就不需要计算逾期费用，直接返回
        if (overdueList.size() <= 0) {
            return repaymentPlanList;
        }
        // 计算逾期利息
        Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqList(overdueList);
        // 填充数据
        repaymentPlanList.forEach(loanManageRepaymentPlan -> {
            RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(loanManageRepaymentPlan.getId());
            if (Objects.nonNull(repaymentExpenseReq)) {
                ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(RepaymentConstant.RepaymentTypeEnum.OVERDUE_DEDUCTION.getCode());
                RepaymentExpenseResp repaymentExpenseResp = expenseCalculate.calculate(repaymentExpenseReq);
                loanManageRepaymentPlan.setPenaltyInterest(repaymentExpenseResp.getShouldOverdueInterest());
            }
        });
        return repaymentPlanList;
    }

    @Override
    public List<LoanManageRepaymentPlan> getListByIou(String iouNo) {
        if (StringUtil.isNotBlank(iouNo)) {
            // 根据借据单号查询还款计划，按期数升序
            List<LoanManageRepaymentPlan> repaymentPlanList = lambdaQuery()
                    .eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                    .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                    .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                    .orderByAsc(LoanManageRepaymentPlan::getPeriod).list();

            // 过滤逾期的还款计划
            return repaymentPlanList;
        }
        return Collections.EMPTY_LIST;
    }


    private List<PrepaymentVO> getPrepaymentList(List<LoanManageRepaymentPlan> repaymentPlanList) {

        // 留下支持提前还款并且未逾期，不是当期还的还款计划，根据还款类型分组
        Map<Integer, List<LoanManageRepaymentPlan>> map = repaymentPlanList.stream()
                .filter(loanManageRepaymentPlan ->
                        loanManageRepaymentPlan.getRepaymentTime().isAfter(LocalDate.now())
                                && !loanManageRepaymentPlan.getPrepaymentType().equals(GoodsEnum.NO_SUPPORT.getCode()))
                .collect(Collectors.groupingBy(LoanManageRepaymentPlan::getPrepaymentType));

        //排除待结清和提前结清以外的状态
        List<LoanManageRepaymentPlan> plans = map.get(GoodsEnum.Full_REPAYMENT.getCode());
        List<Integer> list = Arrays.asList(FinanceApplyStatusEnum.UN_SETTLED.getCode()
                , FinanceApplyStatusEnum.ADVANCE_SETTLED_UN_PAY.getCode()
                , FinanceApplyStatusEnum.ADVANCE_SETTLED_EXAMINE.getCode());
        List<LoanManageRepaymentPlan> planList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(plans)) {
            planList = plans.stream().filter(e -> list.contains((financeApplyMapper.selectById(e.getFinanceApplyId())).getStatus())).collect(Collectors.toList());
        }
        map.put(GoodsEnum.Full_REPAYMENT.getCode(), planList);

        // 封装响应数据结构
        return map.entrySet().stream().map(entry -> {
            List<LoanManageRepaymentPlan> value = entry.getValue();
            return PrepaymentVO.builder()
                    .prepaymentType(entry.getKey())
                    .ids(Func.join(StreamUtil.map(value, LoanManageRepaymentPlan::getId)))
                    .num(StreamUtil.groupBy(value, LoanManageRepaymentPlan::getIouNo).size())
                    .iouNo(Func.join(StreamUtil.map(value, LoanManageRepaymentPlan::getIouNo)))
                    .build();
        }).collect(Collectors.toList());
    }


    //查询本期应还，新增计算逾期天数
    private List<FrontRepaymentPlanVo> getUnPaymentsListByRepaymentType(List<LoanManageRepaymentPlan> repaymentPlanList) {
        //获取当天的日期
        LocalDate nowDate = LocalDate.now();
        // 获取当月最后一天
        LocalDate currentMonthLastDay = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        // 过滤掉还款时间不在当月的
        List<LoanManageRepaymentPlan> localDateRepaymentPlanList = StreamUtil.filter(repaymentPlanList, loanManageRepaymentPlan -> loanManageRepaymentPlan.getRepaymentTime().isBefore(currentMonthLastDay));
        // 构建费用计算请求对象
        Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqList(repaymentPlanList);
        // 根据还款时间分组
        Map<LocalDate, List<LoanManageRepaymentPlan>> repaymentPlanMap = StreamUtil.groupBy(localDateRepaymentPlanList, LoanManageRepaymentPlan::getRepaymentTime);

        List<FrontRepaymentPlanVo> unPaymentList = Lists.newArrayList();
        List<Long> repaymentIds = repaymentPlanMap.values().stream().flatMap(List::stream).map(LoanManageRepaymentPlan::getId).collect(Collectors.toList());
        Map<Long, List<LoanManageRepayment>> payedRepaymentMap = loanManageRepaymentTermService.mapRepaymentByPlanIds(repaymentIds);
        Map<Long, List<RepaymentPlanFee>> planFeeByPlanMap = repaymentPlanFeeService.mapPlanFeeByPlanIds(repaymentIds);
        for (Map.Entry<LocalDate, List<LoanManageRepaymentPlan>> entry : repaymentPlanMap.entrySet()) {
            // 计算当前日期所有还款计划的还款总金额
            List<LoanManageRepaymentPlan> plans = entry.getValue();
            if (CollUtil.isEmpty(plans)) {
                continue;
            }
            //查询待还金额
            ArrayList<BigDecimal> amountList = new ArrayList<>();

            for (LoanManageRepaymentPlan plan : plans) {
                List<LoanManageRepayment> repaymentByPlanId = payedRepaymentMap.get(plan.getId());
                if (ObjectUtil.isEmpty(repaymentByPlanId)) {
//                    Integer repaymentType = RepaymentUtil.getRepaymentType(plan.getRepaymentTime());
//                    ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(repaymentType);
//                    RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(plan.getId());
//                    RepaymentExpenseResp repaymentExpenseResp = expenseCalculate.calculate(repaymentExpenseReq);
                    LoanManageRepayment repayment = getTotalAmount(plan, planFeeByPlanMap.getOrDefault(plan.getId(), Collections.emptyList()));
                    BigDecimal unPayAmount = repayment.getActualAmount();
                    amountList.add(unPayAmount);

                } else {
                    AddAmountVO addAmountVO = new AddAmountVO();
                    BigDecimal actualAmount = repaymentByPlanId.stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    AddAmountVO addAmount = getAddAmountVO(plan, repaymentByPlanId.get(0), actualAmount, addAmountVO);
                    amountList.add(addAmount.getUnPayAmount());
                }
            }
            BigDecimal totalAmount = amountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

            String ids = Func.join(StreamUtil.map(entry.getValue(), LoanManageRepaymentPlan::getId));
            //计算逾期天数
            LocalDate date = entry.getKey();
            long overdueDays = date.until(nowDate, ChronoUnit.DAYS);
            overdueDays = overdueDays > 0 ? overdueDays : 0;

            FrontRepaymentPlanVo repaymentPlanVo = FrontRepaymentPlanVo.builder()
                    .repaymentTime(date)
                    .overdue(date.isBefore(LocalDate.now()))
                    .shouldAmountTotal(totalAmount)
                    .ids(ids)
                    .overdueDays(overdueDays)
                    .build();

            unPaymentList.add(repaymentPlanVo);
        }
        //时间顺序排序
        Collections.sort(unPaymentList, Comparator.comparing(FrontRepaymentPlanVo::getRepaymentTime, (t1, t2) -> t1.compareTo(t2)));
        return unPaymentList;
    }

    public LoanManageRepayment getTotalAmount(LoanManageRepaymentPlan plan) {
        //还款计划关联费用
        List<RepaymentPlanFee> planFees = repaymentPlanFeeService.getPlanFeeByPlanIds(plan.getId());
        return getTotalAmount(plan, planFees);
    }

    /**
     * 获取总还款额度（无部分还款情况）
     *
     * @param plan
     * @return
     */

    public LoanManageRepayment getTotalAmount(LoanManageRepaymentPlan plan, List<RepaymentPlanFee> planFees) {
        //本金
        BigDecimal principal = plan.getPrincipal();
        //利息
        BigDecimal interest = plan.getInterest();

        //还款类型，1-分期付款，2-随借随还
        Integer repaymentType = plan.getRepaymentType();
//        if (repaymentType == 1) {
//            interest = plan.getInterest();
//        } else {
//            interest = repaymentExpenseResp.getShouldInterest();
//        }
        //罚息
//        BigDecimal penaltyInterest = plan.getPenaltyInterest();
//        if (ObjectUtil.isEmpty(penaltyInterest)) {
//            penaltyInterest = BigDecimal.ZERO;
//        }
//        //提前结清
//        BigDecimal advanceSettleServiceFee = repaymentExpenseResp.getAdvanceSettleServiceFee();
//        if (ObjectUtil.isEmpty(advanceSettleServiceFee)) {
//            advanceSettleServiceFee = BigDecimal.ZERO;
//        }
//        //提前还款
//        BigDecimal prepaymentServiceFee = repaymentExpenseResp.getPrepaymentServiceFee();
//        if (ObjectUtil.isEmpty(prepaymentServiceFee)) {
//            prepaymentServiceFee = BigDecimal.ZERO;
//        }
        //还款计划关联费用
//        List<RepaymentPlanFee> planFees = repaymentPlanFeeService.getPlanFeeByPlanIds(plan.getId());
        BigDecimal feeAmount = planFees.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal amount = principal.add(interest).add(penaltyInterest).add(advanceSettleServiceFee).add(prepaymentServiceFee).add(feeAmount);
        BigDecimal amount = principal.add(interest).add(feeAmount);
        LoanManageRepayment repayment = LoanManageRepaymentVO.builder()
                .actualAmount(amount)//待还总额
                .principal(principal)
                .interest(interest)
//                .penaltyInterest(penaltyInterest)
//                .serviceCharge((advanceSettleServiceFee).add(prepaymentServiceFee)).build();
                .build();
        return repayment;
//        return null;
    }

    private List<String> geTallowDelayList() {
        List<LoanManageRepaymentPlan> repaymentPlanList = baseMapper.selectRepaymentUseDelay(AuthUtil.getUserId());
        List<String> iouNos = new ArrayList<>();
        if (CollUtil.isEmpty(repaymentPlanList)) {
            return Collections.emptyList();
        }
        List<Long> goodsIds = repaymentPlanList.stream().map(LoanManageRepaymentPlan::getGoodsId).distinct().collect(Collectors.toList());
        List<Long> financeIds = repaymentPlanList.stream().map(LoanManageRepaymentPlan::getFinanceApplyId).collect(Collectors.toList());
        List<FinanceApply> financeApplyList = financeApplyMapper.selectListIds(financeIds);
        Map<Long, FinanceApply> financeApplyMap = financeApplyList.stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
        Map<Long, Product> productMap = productDirector.selectList(goodsIds).stream().collect(Collectors.toMap(Product::getId, e -> e));
        repaymentPlanList.forEach(e -> {
            Product goods = productMap.get(e.getGoodsId());
            if (GoodsEnum.IS_DELAY.getCode().equals(goods.getIsDelay())) {
                long day = e.getRepaymentTime().toEpochDay() - LocalDate.now().toEpochDay();
                if (day >= goods.getDelayBeforeDayMin() && day <= goods.getDelayBeforeDayMax()) {
                    //过滤非待结清状态
                    //LoanManageIou iou = loanManageIouMapper.selectLoan(e.getIouNo());
                    //FinanceApply byFinanceNoApply = financeApplyMapper.getByFinanceNoApply(iou.getFinanceNo());
                    FinanceApply byFinanceNoApply = financeApplyMap.get(e.getFinanceApplyId());
                    if (FinanceApplyStatusEnum.UN_SETTLED.getCode().equals(byFinanceNoApply.getStatus())) {
                        iouNos.add(e.getIouNo());
                    }
                }
            }
            //iouNos.add(e.getIouNo());
        });
        return iouNos.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 还款数据详情-给支付方式字段赋值
     *
     * @param repaymentTermVOList 还款数据详情-列表
     * @return 还款数据详情-列表
     */
    @Override
    public List<RepaymentTermVO> listPayModeFinanceNo(List<RepaymentTermVO> repaymentTermVOList) {
        //获取还款数据-融资编号
        List<String> financeNoList = repaymentTermVOList.parallelStream().map(RepaymentTermVO::getFinanceNo).collect(Collectors.toList());
        //根据融资编号集合，查询对应的产品---支付方式数据
        List<Map<String, String>> map = financeApplyMapper.selectPayModeFinanceNoMap(financeNoList);
        Map<String, Integer> genderCount = new HashMap<>();
        //判断value中是否不存在（不存在默认线下支付）
        for (Map<String, String> kv : map) {
            String key = null;
            Integer value = null;
            key = kv.get("key");
            if (kv.containsKey("value")) {
                value = Integer.parseInt(kv.get("value"));
            } else {
                value = 1;
            }
            genderCount.put(key, value);
        }
        for (RepaymentTermVO repaymentTermVO : repaymentTermVOList) {
            repaymentTermVO.setPayMode(Integer.parseInt(genderCount.get(repaymentTermVO.getFinanceNo()).toString()));
        }
        return repaymentTermVOList;
    }

    @Override
    public List<RepaymentTermVO> selectTermByIdsPartRepayment(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        if (CollectionUtils.isEmpty(loanManageRepaymentPlans)) {
            return Collections.emptyList();
        }
//        Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqList(loanManageRepaymentPlans);
        List<Long> financeIds = loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getFinanceApplyId).collect(Collectors.toList());
        List<FinanceApply> financeApplyList = financeApplyMapper.selectListIds(financeIds);
        Map<Long, FinanceApply> financeApplyMap = financeApplyList.stream().collect(Collectors.toMap(FinanceApply::getId, e -> e));
        Map<String, LoanManageIou> loanManageIouMap = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery().in(LoanManageIou::getFinanceApplyId, financeIds)).stream()
                .collect(Collectors.toMap(LoanManageIou::getIouNo, e -> e));
        // 查询资方名称和还款账户后4位
        Map<Long, String> repaymentAccountSuffixMap = enterpriseQuotaService.getRepaymentAccountSuffixMap(StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId), loanManageRepaymentPlans.get(0).getUserId()).getData();

        List<RepaymentTermVO> result = Lists.newArrayList();

        for (LoanManageRepaymentPlan loanManageRepaymentPlan : loanManageRepaymentPlans) {
            LoanManageIou loanManageIou = loanManageIouMap.get(loanManageRepaymentPlan.getIouNo());
//            ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(RepaymentUtil.getRepaymentType(loanManageRepaymentPlan.getRepaymentTime()));
//            RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(loanManageRepaymentPlan.getId());
//            RepaymentExpenseResp repaymentExpenseResp = expenseCalculate.calculate(repaymentExpenseReq);

            //实还金额
            List<LoanManageRepayment> repaymentList = loanManageRepaymentTermService.getPayedRepaymentByPlanId(loanManageRepaymentPlan.getId());
            //实还金额
            BigDecimal actualAmount = repaymentList.stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //根据还款记录查询关联还款记录费用
            List<LoanManageRepaymentVO> LoanManageRepaymentVOs = repaymentFeeService.getPayRepaymentFeeByRepayments(repaymentList);

            //待还还明细
            AddAmountVO addAmountVO = new AddAmountVO();
            addAmountVO = getFeeDetailsByAddAmount(
                    loanManageRepaymentPlan,
                    CollUtil.isEmpty(repaymentList) ? null : repaymentList.get(0),
                    actualAmount,
//                    repaymentExpenseResp,
                    addAmountVO);

            FinanceApply financeApply = financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            RepaymentTermVO repaymentTermVO = RepaymentTermVO.builder()
                    .iouNo(loanManageRepaymentPlan.getIouNo())
                    .financeNo(financeApply.getFinanceNo())
                    .repaymentType(loanManageRepaymentPlan.getRepaymentType())
                    .id(loanManageRepaymentPlan.getId())
                    .loanAmount(loanManageIou.getIouAmount())
                    .loanTime(loanManageIou.getLoanTime())
                    .repaymentTime(loanManageRepaymentPlan.getRepaymentTime())
                    .repaymentAccount(repaymentAccountSuffixMap.get(loanManageRepaymentPlan.getGoodsId()))
                    .shouldAmount(loanManageRepaymentPlan.getPrincipal())
                    .actualAmount(actualAmount)
                    .unPayAmount(addAmountVO.getUnPayAmount())
//                    .repaymentExpenseResp(repaymentExpenseResp)
                    .addAmountVO(addAmountVO)
                    .repaymentList(LoanManageRepaymentVOs)
                    .goodsId(loanManageRepaymentPlan.getGoodsId())
                    .period(loanManageRepaymentPlan.getPeriod())
                    .build();
            result.add(repaymentTermVO);
        }
        return result;
    }

    /**
     * 计算部分还款未还和新增费用
     *
     * @param loanManageRepaymentPlan 还款计划
     * @param repayment               最后一次还款成功记录
     * @param actualAmount            实还总额
     * @param addAmountVO             承载数据对象
     * @return
     */
    @Override
    public AddAmountVO getAddAmountVO(LoanManageRepaymentPlan loanManageRepaymentPlan,
                                      LoanManageRepayment repayment,
                                      BigDecimal actualAmount,
                                      AddAmountVO addAmountVO) {
        //上一次应还费用
        if (ObjectUtil.isEmpty(repayment)) {
            return addAmountVO;
        }
        //查询还款记录未还的关联费用
        List<RepaymentPlanFee> unPayFeeList = getUnPayRepaymentPlanFees(repayment);

        BigDecimal principal = repayment.getPrincipal();
        BigDecimal interest = repayment.getInterest();
        BigDecimal penaltyInterest = repayment.getPenaltyInterest();
        BigDecimal serviceCharge = repayment.getServiceCharge();

        //上次实还
        BigDecimal actualPrincipal = repayment.getActualPrincipal();
        BigDecimal actualInterest = repayment.getActualInterest();
        BigDecimal actualPenaltyInterest = repayment.getActualPenaltyInterest();
        BigDecimal actualServiceCharge = repayment.getActualServiceCharge();


        //剩余未还
        BigDecimal surplusPrincipal;
        //分期还款与随借本金剩余本金计算方式不同
        if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())) {
            surplusPrincipal = principal.subtract(actualPrincipal);
        } else {
            List<LoanManageRepayment> repaymentByPlanId = loanManageRepaymentTermService.getPayedRepaymentByPlanId(loanManageRepaymentPlan.getId());
            BigDecimal actualPrincipalBySettlement = repaymentByPlanId.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            surplusPrincipal = loanManageRepaymentPlan.getPrincipal().subtract(actualPrincipalBySettlement);
        }
        BigDecimal surplusInterest = interest.subtract(actualInterest);
        BigDecimal surplusPenaltyInterest = penaltyInterest.subtract(actualPenaltyInterest);
        BigDecimal surplusServiceCharge = serviceCharge.subtract(actualServiceCharge);

        //上一次还款日期
        LocalDate repaymentTime = repayment.getRepaymentTime().toLocalDate();
        FinanceApply financeApply = financeApplyMapper.selectById(loanManageRepaymentPlan.getFinanceApplyId());

        //自定义计算器参数   产品计算公式，金额，上一次还款时间，plan，finally good iou
        //获取产品配置费用公式
        Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType = getExpenseRuleGroupByExpenseType(financeApply.getGoodsId());

        //判断还款类型，得出新的利息
        addAmountVO = getAddFee(
                addAmountVO,
                loanManageRepaymentPlan,
                repaymentTime,
                surplusPrincipal,
                financeApply,
                expenseRuleGroupByExpenseType);

        BigDecimal newInterest = surplusInterest.add(addAmountVO.getAddInterest());
        BigDecimal newPenaltyInterest = surplusPenaltyInterest.add(addAmountVO.getAddPenaltyInterest());
        BigDecimal newServiceCharge = surplusServiceCharge.add(addAmountVO.getAddServiceCharge());
        //未还关联费用
        BigDecimal newFee = unPayFeeList.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unPayAmount = surplusPrincipal.add(newInterest).add(newPenaltyInterest).add(newServiceCharge).add(newFee);

        addAmountVO.setNewInterest(newInterest);
        addAmountVO.setNewPenaltyInterest(newPenaltyInterest);
        addAmountVO.setNewServiceCharge(newServiceCharge);
        addAmountVO.setSurplusPrincipal(surplusPrincipal);
        addAmountVO.setRepaymentPlanFeeList(unPayFeeList);
        addAmountVO.setUnPayAmount(unPayAmount);
        return addAmountVO;
    }

    /**
     * 获得未还的还款计划
     *
     * @param repayment 最近一次还款记录
     * @return
     */
    @Override
    public List<RepaymentPlanFee> getUnPayRepaymentPlanFees(LoanManageRepayment repayment) {
        List<RepaymentFee> payRepaymentFeeList = repaymentFeeService.getPayRepaymentFeeList(repayment.getId());
        return payRepaymentFeeList.stream().map(fee -> {
            List<RepaymentPlanFee> repaymentPlanByRepaymentId = loanManageRepaymentTermService.getRepaymentPlanByRepaymentId(fee.getId());
            RepaymentPlanFee planFee = repaymentPlanByRepaymentId.get(0);
            planFee.setAmount(fee.getShouldAmount().subtract(fee.getActualAmount()));
            return planFee;
        }).collect(Collectors.toList());
    }

    /**
     * 获取新增费用金额（部分还款）
     *
     * @param loanManageRepaymentPlan       还款计划
     * @param repaymentTime                 上次还款时间
     * @param surplusPrincipal              剩余本金
     * @param financeApply                  融资信息
     * @param expenseRuleGroupByExpenseType 费用计算公式
     * @return
     */
    public AddAmountVO getAddFee(AddAmountVO addAmountVO,
                                 LoanManageRepaymentPlan loanManageRepaymentPlan,
                                 LocalDate repaymentTime,
                                 BigDecimal surplusPrincipal,
                                 FinanceApply financeApply,
                                 Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType) {

        addAmountVO.setAddInterest(BigDecimal.ZERO);
        addAmountVO.setAddPenaltyInterest(BigDecimal.ZERO);
        addAmountVO.setAddServiceCharge(BigDecimal.ZERO);
        if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())) {
            //分期还款，利息不变
            addAmountVO.setAddInterest(BigDecimal.ZERO);
            //提前结清服务费
            if (LocalDate.now().isBefore(loanManageRepaymentPlan.getRepaymentTime())) {
                diySettlement(addAmountVO, loanManageRepaymentPlan, repaymentTime, surplusPrincipal, financeApply, expenseRuleGroupByExpenseType);
            }
        } else {
            //随借随还 计息天数
            diyInterestBySurplus(addAmountVO, loanManageRepaymentPlan, repaymentTime, surplusPrincipal, financeApply, expenseRuleGroupByExpenseType);
            //计算提前还款服务费
            if (LocalDate.now().isBefore(loanManageRepaymentPlan.getRepaymentTime())) {
                diyPrepayment(addAmountVO, loanManageRepaymentPlan, repaymentTime, surplusPrincipal, financeApply, expenseRuleGroupByExpenseType);
            }
        }
        //判断是否逾期，得出新的罚息
        if (loanManageRepaymentPlan.getRepaymentTime().isBefore(LocalDate.now())) {
            //逾期罚息
            diyOverdue(addAmountVO, loanManageRepaymentPlan, repaymentTime, surplusPrincipal, financeApply, expenseRuleGroupByExpenseType);
        }
        return addAmountVO;
    }

    @Override
    public AddAmountVO diyOverdue(AddAmountVO addAmountVO, LoanManageRepaymentPlan loanManageRepaymentPlan, LocalDate repaymentTime, BigDecimal surplusPrincipal, FinanceApply financeApply, Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType) {
        GoodsExpenseRule goodsExpenseRule = expenseRuleGroupByExpenseType.get(ExpenseConstant.ExpenseTypeEnum.OVERDUE_INTEREST.getCode());
        if (ObjectUtil.isNotEmpty(goodsExpenseRule)) {
            Long overdueDays = LocalDate.now().toEpochDay() - repaymentTime.toEpochDay();
            //构建费用计算器dto
            ExpenseRuleDTO expenseRuleDTO = getCalculatorDto(surplusPrincipal, overdueDays > 0 ? new BigDecimal(overdueDays) : BigDecimal.ZERO, loanManageRepaymentPlan, financeApply);
            BigDecimal addPenaltyInterest = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(goodsExpenseRule), expenseRuleDTO).get(0);
            addAmountVO.setAddPenaltyInterest(addPenaltyInterest);
        }
        return addAmountVO;
    }

    @Override
    public AddAmountVO diyInterestBySurplus(AddAmountVO addAmountVO, LoanManageRepaymentPlan loanManageRepaymentPlan, LocalDate repaymentTime, BigDecimal surplusPrincipal, FinanceApply financeApply, Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType) {
        GoodsExpenseRule goodsExpenseRuleInterest = expenseRuleGroupByExpenseType.get(ExpenseConstant.ExpenseTypeEnum.INTEREST.getCode());
        if (ObjectUtil.isNotEmpty(goodsExpenseRuleInterest)) {
            long InterestAccrualDays = LocalDate.now().toEpochDay() - repaymentTime.toEpochDay();
            InterestAccrualDays = InterestAccrualDays > 0 ? InterestAccrualDays : 0L;
            if (LocalDate.now().toEpochDay() - repaymentTime.toEpochDay() > 0L) {
                //取出产品t+天数
                Integer tDay = 0;
                if (GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode().equals(loanManageRepaymentPlan.getGoodsType())) {
                    tDay = productDirector.detailBase(loanManageRepaymentPlan.getGoodsId()).getInterestDay();
                }
                InterestAccrualDays = InterestAccrualDays + tDay;
            }
            //构建费用计算器dto
            ExpenseRuleDTO expenseRuleDTO = getCalculatorDto(surplusPrincipal, InterestAccrualDays > 0 ? new BigDecimal(InterestAccrualDays) : BigDecimal.ZERO, loanManageRepaymentPlan, financeApply);
            BigDecimal addInterest = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(goodsExpenseRuleInterest), expenseRuleDTO).get(0);
            addAmountVO.setAddInterest(addInterest);
        }
        return addAmountVO;
    }

    @Override
    public AddAmountVO diyPrepayment(AddAmountVO addAmountVO, LoanManageRepaymentPlan loanManageRepaymentPlan, LocalDate repaymentTime, BigDecimal surplusPrincipal, FinanceApply financeApply, Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType) {
        GoodsExpenseRule goodsExpenseRule = expenseRuleGroupByExpenseType.get(ExpenseConstant.ExpenseTypeEnum.PREPAYMENT_SERVICE_FEE.getCode());
        if (ObjectUtil.isNotEmpty(goodsExpenseRule)) {
            Long advanceDays = LocalDate.now().toEpochDay() - repaymentTime.toEpochDay();
            //构建费用计算器dto
            ExpenseRuleDTO expenseRuleDTO = getCalculatorDto(surplusPrincipal, advanceDays > 0 ? new BigDecimal(advanceDays) : BigDecimal.ZERO, loanManageRepaymentPlan, financeApply);
            BigDecimal addServiceCharge = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(goodsExpenseRule), expenseRuleDTO).get(0);
            addAmountVO.setAddServiceCharge(addServiceCharge);
        }
        return addAmountVO;
    }


    /**
     * 提前结清费用计算器
     *
     * @param loanManageRepaymentPlan
     * @param repaymentTime
     * @param surplusPrincipal
     * @param financeApply
     * @param expenseRuleGroupByExpenseType
     * @return
     */
    @Override
    public AddAmountVO diySettlement(AddAmountVO addAmountVO, LoanManageRepaymentPlan loanManageRepaymentPlan, LocalDate repaymentTime, BigDecimal surplusPrincipal, FinanceApply financeApply, Map<Integer, GoodsExpenseRule> expenseRuleGroupByExpenseType) {
        GoodsExpenseRule goodsExpenseRule = expenseRuleGroupByExpenseType.get(ExpenseConstant.ExpenseTypeEnum.ADVANCE_SETTLE_SERVICE_FEE.getCode());
        if (ObjectUtil.isNotEmpty(goodsExpenseRule)) {
            //根据应还日期计算利息
            Long advanceDays = loanManageRepaymentPlan.getRepaymentTime().toEpochDay() - repaymentTime.toEpochDay();
            advanceDays = advanceDays > 0 ? advanceDays : 0L;
            //构建费用计算器dto
            ExpenseRuleDTO expenseRuleDTO = getCalculatorDto(surplusPrincipal, advanceDays > 0 ? new BigDecimal(advanceDays) : BigDecimal.ZERO, loanManageRepaymentPlan, financeApply);
            BigDecimal addServiceCharge = GoodsFeeRulesUtil.calculationRules(Collections.singletonList(goodsExpenseRule), expenseRuleDTO).get(0);
            addAmountVO.setAddServiceCharge(addServiceCharge);
        }
        return addAmountVO;
    }


    /**
     * 获取产品对应的费用公式
     *
     * @param goodId
     * @return
     */
    @Override
    public Map<Integer, GoodsExpenseRule> getExpenseRuleGroupByExpenseType(Long goodId) {
        Map<Long, List<GoodsExpenseRule>> longListMap = goodsExpenseRuleService.
                selectByGoodsId(Func.toLongList(String.valueOf(goodId)));
        List<GoodsExpenseRule> goodsExpenseRules = longListMap.get(goodId);
        if (CollectionUtil.isEmpty(goodsExpenseRules)) {
            return MapUtil.newHashMap();
        }
        Map<Integer, GoodsExpenseRule> integerGoodsExpenseRuleMap = StreamUtil.toMap(goodsExpenseRules, GoodsExpenseRule::getExpenseType, obj -> obj);
        return integerGoodsExpenseRuleMap;

    }


    /**
     * 构建费用计算器dto
     *
     * @param surplusPrincipal        剩余本金
     * @param InterestAccrualDays     计息天数
     * @param loanManageRepaymentPlan 还款计划
     * @param financeApply            融资信息
     * @return
     */
    private ExpenseRuleDTO getCalculatorDto(BigDecimal surplusPrincipal, BigDecimal InterestAccrualDays, LoanManageRepaymentPlan loanManageRepaymentPlan, FinanceApply financeApply) {
        ExpenseRuleDTO expenseRuleDTO = new ExpenseRuleDTO();
        //本金
        expenseRuleDTO.setPrincipalRepayment(surplusPrincipal);
        //计息天数
        expenseRuleDTO.setInterestAccrualDays(InterestAccrualDays);
        //剩余应还本金
        expenseRuleDTO.setSurplusPrincipalRepayment(surplusPrincipal);
        //提前还款天数
        expenseRuleDTO.setPrepaymentDays(InterestAccrualDays);
        //借款天数
        LoanManageIou iou = loanManageIouMapper.selectLoan(loanManageRepaymentPlan.getIouNo());
        Long loanDays = iou.getExpireTime().toEpochDay() - iou.getLoanTime().toEpochDay();
        expenseRuleDTO.setLoanDays(new BigDecimal(loanDays));
        //借款期数
        expenseRuleDTO.setLoanPeriods(new BigDecimal(loanManageRepaymentPlan.getPeriod()));
        //年利率
        expenseRuleDTO.setYearRate(financeApply.getAnnualInterestRate().divide(BigDecimal.valueOf(100), 5, CommonConstant.NUMBER_STRATEGY));
        //日利率
        expenseRuleDTO.setDayRate(financeApply.getDailyInterestRate().divide(BigDecimal.valueOf(100), 5, CommonConstant.NUMBER_STRATEGY));
        //服务费率
        expenseRuleDTO.setServiceRate(financeApply.getServiceRate().divide(BigDecimal.valueOf(100), 5, CommonConstant.NUMBER_STRATEGY));
        //增值税率
        expenseRuleDTO.setTaxRate(BigDecimal.ONE);
        //借款本金
        expenseRuleDTO.setLoanPrincipal(surplusPrincipal);
        return expenseRuleDTO;
    }


    public Map<String, SettleExpenseReq> buildSettleExpenseReq(Map<String, List<LoanManageRepaymentPlan>> repaymentPlanMap) {

        List<LoanManageIou> loanManageIouList = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
                .in(LoanManageIou::getIouNo, repaymentPlanMap.keySet()));

        Map<String, LoanManageIou> loanManageIouMap = StreamUtil.toMap(loanManageIouList, LoanManageIou::getIouNo, loanManageIou -> loanManageIou);

        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = repaymentPlanMap.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 费用订单
        List<Long> financeApplyIdList = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getFinanceApplyId);
        List<ExpenseOrder> billExpenseOrderList = billExpenseOrderService.lambdaQuery()
                .in(ExpenseOrder::getFinanceApplyId, financeApplyIdList)
                .list();
        //Map<Long, BillExpenseOrder> expenseOrderMap = StreamUtil.toMap(billExpenseOrderList, BillExpenseOrder::getFinanceApplyId, obj -> obj);
        Map<Long, List<ExpenseOrder>> expenseOrderMap = StreamUtil.groupBy(billExpenseOrderList, ExpenseOrder::getFinanceApplyId);

        // 我的产品
        List<Long> customerGoodsIds = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getCustomerGoodsId);
        List<CustomerGoods> customerGoodsList = customerGoodsService.listByIds(customerGoodsIds).getData();
        Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getId, obj -> obj);
        // 费用规则
        Map<Long, List<GoodsExpenseRule>> goodsExpenseRuleMap = goodsExpenseRuleService.selectByGoodsId(StreamUtil.map(customerGoodsList, CustomerGoods::getGoodsId));
        // 融资申请
        List<FinanceApply> financeApplies = financeApplyMapper.selectBatchIds(financeApplyIdList);
        List<String> financeNoList = StreamUtil.map(financeApplies, FinanceApply::getFinanceNo);
        Map<Long, FinanceApply> financeApplyMap = StreamUtil.toMap(financeApplies, FinanceApply::getId, obj -> obj);
        // 发票增值税
        List<BillInvoiceDetail> invoiceDetails = billInvoiceDetailService.lambdaQuery().in(BillInvoiceDetail::getFinanceNo, financeNoList).list();
        Map<String, String> taxRateMap = StreamUtil.toMap(invoiceDetails, BillInvoiceDetail::getFinanceNo, BillInvoiceDetail::getTaxRate);

        Map<String, SettleExpenseReq> result = Maps.newHashMap();
        for (Map.Entry<String, List<LoanManageRepaymentPlan>> entry : repaymentPlanMap.entrySet()) {
            LoanManageRepaymentPlan loanManageRepaymentPlan = entry.getValue().get(0);
            LoanManageIou loanManageIou = loanManageIouMap.get(loanManageRepaymentPlan.getIouNo());
            List<ExpenseOrder> billExpenseOrders = expenseOrderMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            CustomerGoods customerGoods = customerGoodsMap.get(loanManageRepaymentPlan.getCustomerGoodsId());
            List<GoodsExpenseRule> goodsExpenseRules = goodsExpenseRuleMap.get(customerGoods.getGoodsId());
            FinanceApply financeApply = financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            SettleExpenseReq settleExpenseReq = SettleExpenseReq.builder()
                    .loanManageIou(loanManageIou)
                    .loanManageRepaymentPlans(entry.getValue())
                    .feeAmount(CollUtil.isNotEmpty(billExpenseOrders) ? billExpenseOrders.stream().map(ExpenseOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO)
                    .annualInterestRate(financeApply.getAnnualInterestRate())
                    .serviceRate(financeApply.getServiceRate())
                    .goodsExpenseRules(goodsExpenseRules)
                    //TODO
                    .taxRate(taxRateMap.getOrDefault(financeApply.getFinanceNo(), BigDecimal.ZERO.toString()))
                    .interestDay(financeApply.getInterestDay())
                    .build();

            result.put(entry.getKey(), settleExpenseReq);
        }
        return result;
    }

    /**
     * 构建还款参数
     *
     * @param loanManageRepaymentPlans 本次的还款计划
     * @param loanManageIouList        关联的借款信息
     * @param billExpenseOrderList     关联的费用订单信息
     * @param customerGoodsList        关联的客户产品信息
     * @param financeApplies           关联的融资申请信息
     * @param goodsExpenseRuleMap      关联的费用规则信息
     * @param invoiceDetails           关联的发票信息
     * @return
     */
    private Map<Long, RepaymentExpenseReq> buildRepaymentExpenseReqList(List<LoanManageRepaymentPlan> loanManageRepaymentPlans
            , List<LoanManageIou> loanManageIouList, List<ExpenseOrder> billExpenseOrderList
            , List<CustomerGoods> customerGoodsList
            , List<FinanceApply> financeApplies
            , Map<Long, List<GoodsExpenseRule>> goodsExpenseRuleMap
            , List<BillInvoiceDetail> invoiceDetails
            , List<Product> products) {

        Map<String, LoanManageIou> loanManageIouMap = StreamUtil.toMap(loanManageIouList, LoanManageIou::getIouNo, loanManageIou -> loanManageIou);

        // 费用订单 Map
        Map<Long, List<ExpenseOrder>> expenseOrderMap = StreamUtil.groupBy(billExpenseOrderList, ExpenseOrder::getFinanceApplyId);
        // 我的产品
        Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getId, obj -> obj);
        // 产品信息
        Map<Long, Product> productMap = StreamUtil.toMap(products, Product::getId, e -> e);
        // 融资申请
        Map<Long, FinanceApply> financeApplyMap = StreamUtil.toMap(financeApplies, FinanceApply::getId, obj -> obj);
        // 发票增值税
        Map<String, String> taxRateMap = StreamUtil.filterToStream(invoiceDetails, billInvoiceDetail -> Objects.nonNull(billInvoiceDetail.getTaxRate()))
                .collect(Collectors.toMap(BillInvoiceDetail::getFinanceNo, BillInvoiceDetail::getTaxRate));
        Map<Long, RepaymentExpenseReq> result = Maps.newHashMap();
        for (LoanManageRepaymentPlan loanManageRepaymentPlan : loanManageRepaymentPlans) {
            LoanManageIou loanManageIou = loanManageIouMap.get(loanManageRepaymentPlan.getIouNo());
            List<ExpenseOrder> billExpenseOrders = expenseOrderMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            List<GoodsExpenseRule> goodsExpenseRules = goodsExpenseRuleMap.get(loanManageRepaymentPlan.getGoodsId());
            FinanceApply financeApply = financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            Product product = productMap.get(loanManageRepaymentPlan.getGoodsId());
            CustomerGoods customerGoods = customerGoodsMap.get(loanManageRepaymentPlan.getCustomerGoodsId());
            if (financeApply == null) {
                continue;
            }
            RepaymentExpenseReq repaymentExpenseReq = RepaymentExpenseReq.builder()
                    .loanManageIou(loanManageIou)
                    .loanManageRepaymentPlan(loanManageRepaymentPlan)
                    .product(product)
                    .customerGoods(customerGoods)
                    .financeApply(financeApply)
                    .feeAmount(CollUtil.isNotEmpty(billExpenseOrders) ? billExpenseOrders.stream()
                            .map(ExpenseOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO)
                    .annualInterestRate(financeApply.getAnnualInterestRate())
                    .serviceRate(financeApply.getServiceRate())
                    .goodsExpenseRules(goodsExpenseRules)
                    .taxRate(taxRateMap.getOrDefault(financeApply.getFinanceNo(), BigDecimal.ZERO.toString()))
                    .interestDay(financeApply.getInterestDay())
                    .build();
            result.put(loanManageRepaymentPlan.getId(), repaymentExpenseReq);
        }
        return result;
    }

    /**
     * 构建还款节点参数
     *
     * @param loanManageRepaymentPlans
     * @return
     */
    private Map<Long, RepaymentExpenseReq> buildRepaymentExpenseReqList(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        //借款信息
        List<LoanManageIou> loanManageIouList = loanManageIouMapper.selectList(Wrappers.<LoanManageIou>lambdaQuery()
                .in(LoanManageIou::getIouNo, StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getIouNo)));
        // 费用订单
        List<Long> financeApplyIdList = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getFinanceApplyId);
        List<ExpenseOrder> billExpenseOrderList = billExpenseOrderService.lambdaQuery()
                .ne(ExpenseOrder::getPaymentStatus, BillPayStatusEnum.BILL_CLOSED.getCode())
                .in(ExpenseOrder::getFinanceApplyId, financeApplyIdList)
                .list();
        // 我的产品
        List<Long> customerGoodsIds = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getCustomerGoodsId);
        List<CustomerGoods> customerGoodsList = customerGoodsService.listByIds(customerGoodsIds).getData();
        //产品信息
        List<Long> goodsIds = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId);
        List<Product> products = productDirector.selectList(goodsIds);
        // 融资申请
        List<FinanceApply> financeApplies = financeApplyMapper.selectBatchIds(financeApplyIdList);
        List<String> financeNoList = StreamUtil.map(financeApplies, FinanceApply::getFinanceNo);
        // 费用规则
        Map<Long, List<GoodsExpenseRule>> goodsExpenseRuleMap = goodsExpenseRuleService.selectByGoodsId(StreamUtil.map(financeApplies, FinanceApply::getGoodsId));
        // 发票增值税
        List<BillInvoiceDetail> invoiceDetails = billInvoiceDetailService.lambdaQuery().in(BillInvoiceDetail::getFinanceNo, financeNoList).list();

        return buildRepaymentExpenseReqList(loanManageRepaymentPlans
                , loanManageIouList, billExpenseOrderList
                , customerGoodsList
                , financeApplies
                , goodsExpenseRuleMap
                , invoiceDetails
                , products);
    }


    @Override
    public List<LoanManageRepaymentPlan> listByFinanceApplyId(Long financeApplyId) {
        return lambdaQuery().eq(LoanManageRepaymentPlan::getFinanceApplyId, financeApplyId)
                .orderByDesc(LoanManageRepaymentPlan::getCreateTime).list();
    }


    /**
     * 提前结清待支付状态的详情
     *
     * @param sdf
     * @param financeApply
     * @param settledDetailsVo
     */
    private void getSettledDetailsVo(SimpleDateFormat sdf,
                                     FinanceApply financeApply,
                                     AddAmountVO settledDetailsVo,
                                     RepaymentExpenseResp repaymentExpenseResp) {
        if (FinanceApplyStatusEnum.ADVANCE_SETTLED_UN_PAY.getCode().equals(financeApply.getStatus())) {
            AdvanceSettledApply advanceSettledApply = cn.hutool.extra.spring.SpringUtil.getBean(IAdvanceSettledApplyService.class).settledApplyByFinanceNo(financeApply.getFinanceNo());
            if (ObjectUtil.isNotEmpty(advanceSettledApply)) {
                settledDetailsVo.setNewInterest(advanceSettledApply.getInterest());
                settledDetailsVo.setSurplusPrincipal(advanceSettledApply.getSurplusPrincipal());
                settledDetailsVo.setNewServiceCharge(advanceSettledApply.getPrepaymentServiceFee());
                //更新费用计算器的费用
                repaymentExpenseResp.setTotalAmount(
                        advanceSettledApply.getInterest()
                                .add(advanceSettledApply.getSurplusPrincipal())
                                .add(advanceSettledApply.getPrepaymentServiceFee()));
                repaymentExpenseResp.setShouldInterest(advanceSettledApply.getInterest());
                repaymentExpenseResp.setAdvanceSettleServiceFee(advanceSettledApply.getPrepaymentServiceFee());
                settledDetailsVo.setAdvanceSettledDays(advanceSettledApply.getPrepaymentDay() + "天");
                if (ObjectUtil.isNotEmpty(advanceSettledApply.getPayDeadLine())) {
                    Date date = Date.from(advanceSettledApply.getPayDeadLine().atZone(ZoneId.systemDefault()).toInstant());
                    settledDetailsVo.setPayDeadLineStr(sdf.format(date));
                } else {
                    settledDetailsVo.setPayDeadLineStr("-");
                }
            }
        }

    }

    @Override
    public Map<Long, RepaymentExpenseResp> calculateExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqList(loanManageRepaymentPlans);
        return loanManageRepaymentPlans.stream().collect(Collectors.toMap(LoanManageRepaymentPlan::getId, loanManageRepaymentPlan -> {
            RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(loanManageRepaymentPlan.getId());
            ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(RepaymentUtil.getRepaymentType(loanManageRepaymentPlan.getRepaymentTime()));
            return expenseCalculate.calculate(repaymentExpenseReq);
        }));
    }

    @Override
    public List<LoanManageRepaymentPlan> listUnRepaymentListByIouNo(String iouNo) {
        return lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod)
                .list();
    }

    @Override
    public List<LoanManageRepaymentPlan> listRepaymentListByIouNo(String iouNo) {
        return lambdaQuery().eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod)
                .list();
    }

    /**
     * 获取费用详情（部分还款）
     *
     * @param loanManageRepaymentPlan 还款计划
     * @param loanManageRepayment     最后一次还款记录
     * @param actualAmount            实还
     * @param addAmountVO             承载数据实体
     * @return
     */
    public AddAmountVO getFeeDetailsByAddAmount(LoanManageRepaymentPlan loanManageRepaymentPlan,
                                                LoanManageRepayment loanManageRepayment,
                                                BigDecimal actualAmount,
//                                                RepaymentExpenseResp repaymentExpenseResp,
                                                AddAmountVO addAmountVO) {
        if (actualAmount.compareTo(BigDecimal.ZERO) == 0) {
            LoanManageRepayment repayment = getTotalAmount(loanManageRepaymentPlan);
            BigDecimal unPayAmount = repayment.getActualAmount();
            addAmountVO.setUnPayAmount(unPayAmount);
            //详细还款明细实体
            addAmountVO.setNewInterest(repayment.getInterest());
            addAmountVO.setNewPenaltyInterest(repayment.getPenaltyInterest());
            addAmountVO.setNewServiceCharge(repayment.getServiceCharge());
            addAmountVO.setSurplusPrincipal(repayment.getPrincipal());
            //查询还款计划关联费用
            List<RepaymentPlanFee> feeList = repaymentPlanFeeService.getPlanFeeByPlanIds(loanManageRepaymentPlan.getId());
            addAmountVO.setRepaymentPlanFeeList(feeList);
        } else {
            //部分还款费用计算
            addAmountVO = getAddAmountVO(loanManageRepaymentPlan, loanManageRepayment, actualAmount, addAmountVO);
        }
        return addAmountVO;
    }

    @Override
    public Map<Long, RepaymentExpenseResp> calculateDetailExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans, LoanManageRepayment repayment, Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap) {

        Map<Long, RepaymentExpenseResp> collect = loanManageRepaymentPlans.stream().collect(Collectors.toMap(LoanManageRepaymentPlan::getId, loanManageRepaymentPlan -> {
            RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(loanManageRepaymentPlan.getId());
            LocalDate repaymentTimePlan = loanManageRepaymentPlan.getRepaymentTime();
            Date createTime = repayment.getCreateTime();
            // 提前还款
            LocalDate repaymentTimeCreate = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            //ExpenseCalculate expenseCalculate = ExpenseCalculateStrategy.build(RepaymentUtil.getRepaymentType(repaymentTime));
            Integer repaymentTypeByPayDetail = RepaymentUtil.getRepaymentTypeByPayDetail(repaymentTimePlan, repaymentTimeCreate);
            repaymentExpenseReq.setLoanManageRepayment(repayment);
            ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(repaymentTypeByPayDetail);

            return expenseCalculate.calculate(repaymentExpenseReq);
        }));
        return collect;
    }


    @Override
    public List<LoanManageRepaymentPlan> selectSettledRepaymentPlan(Long financeId) {
        return baseMapper.selectList(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .eq(LoanManageRepaymentPlan::getFinanceApplyId, financeId)
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode())
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode()));
    }

    @Override
    public void updateExAmount(Long id, BigDecimal exInterest) {
        update(Wrappers.<LoanManageRepaymentPlan>lambdaUpdate().eq(LoanManageRepaymentPlan::getId, id).set(LoanManageRepaymentPlan::getExInterest, exInterest));
    }

    @Override
    public List<LoanManageRepaymentPlan> listRepaymentPlanByStatusAndUserId(List<Long> userIds, List<Integer> goodsType,
                                                                            List<Integer> unFinishStatus,
                                                                            RepaymentConstant.RepaymentPlanRepaymentStatusEnum status) {
        //筛选出系统目前加载的产品
        List<Integer> allType = productDirector.getAllType();
        goodsType = goodsType.stream().filter(allType::contains).collect(Collectors.toList());
        if (goodsType.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用新的连表查询方法，过滤needRedeem=false的数据
        return baseMapper.listRepaymentPlanByStatusAndUserIdWithNeedRedeem(
                userIds,
                goodsType,
                unFinishStatus,
                ObjectUtil.isNotEmpty(status) ? status.getCode() : null
        );
//        return lambdaQuery()
//                .eq(ObjectUtil.isNotEmpty(status), LoanManageRepaymentPlan::getRepaymentStatus, status.getCode())
//                .in(CollUtil.isNotEmpty(userIds), LoanManageRepaymentPlan::getUserId, userIds)
//                .ne(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
//                .in(ObjectUtil.isNotEmpty(goodsType), LoanManageRepaymentPlan::getGoodsType, goodsType)
//                .in(LoanManageRepaymentPlan::getStatus, unFinishStatus)
//                .orderByAsc(LoanManageRepaymentPlan::getRepaymentTime)
//                .list();
    }

    @Override
    public List<LoanManageRepaymentPlan> listByUserIdsAndIds(List<Long> userId, List<Long> ids) {
        return list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .in(CollUtil.isEmpty(userId), LoanManageRepaymentPlan::getUserId, userId)
                .in(LoanManageRepaymentPlan::getId, ids));
    }

    @Override
    public void changeRepaymentStatus(List<Long> ids, int repaymentStatus, Integer status) {
        update(Wrappers.<LoanManageRepaymentPlan>lambdaUpdate().in(LoanManageRepaymentPlan::getId, ids)
                .set(LoanManageRepaymentPlan::getStatus, status)
                .set(LoanManageRepaymentPlan::getRepaymentStatus, repaymentStatus));
    }

    @Override
    public List<LoanManageRepaymentPlan> listByIouNoList(List<String> iouNOList) {
        return lambdaQuery().in(LoanManageRepaymentPlan::getIouNo, iouNOList)
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod)
                .list();
    }

    @Override
    public boolean isUsing(List<Long> repaymentPlanIds) {
        int count = count(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode()).in(LoanManageRepaymentPlan::getId, repaymentPlanIds));
        return count
                == repaymentPlanIds.size();
    }

    @Override
    public Map<Long, RepaymentExpenseResp> calculateOverdueExpense(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqPastList(loanManageRepaymentPlans);
        return loanManageRepaymentPlans.stream().collect(Collectors.toMap(LoanManageRepaymentPlan::getId, loanManageRepaymentPlan -> {
            RepaymentExpenseReq repaymentExpenseReq = repaymentExpenseReqMap.get(loanManageRepaymentPlan.getId());
            ExpenseCalculate expenseCalculate = expenseCalculateStrategy.instance(RepaymentUtil.getRepaymentType(loanManageRepaymentPlan.getRepaymentTime()));
            return expenseCalculate.calculate(repaymentExpenseReq);
        }));
    }

    private Map<Long, RepaymentExpenseReq> buildRepaymentExpenseReqPastList(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        if (CollectionUtil.isEmpty(loanManageRepaymentPlans)) {
            return Collections.emptyMap();
        }
        List<LoanManageIou> loanManageIouList = loanManageIouMapper.selectLoanManageIouList(StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getIouNo));

        Map<String, LoanManageIou> loanManageIouMap = StreamUtil.toMap(loanManageIouList, LoanManageIou::getIouNo, loanManageIou -> loanManageIou);

        // 费用订单
        List<Long> financeApplyIdList = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getFinanceApplyId);
        List<ExpenseOrder> billExpenseOrderList = billExpenseOrderService.lambdaQuery()
                .in(ExpenseOrder::getFinanceApplyId, financeApplyIdList)
                .list();
        Map<Long, List<ExpenseOrder>> expenseOrderMap = billExpenseOrderList.stream().collect(Collectors.groupingBy(ExpenseOrder::getFinanceApplyId));

        //Map<Long, BillExpenseOrder> expenseOrderMap = StreamUtil.toMap(billExpenseOrderList, BillExpenseOrder::getFinanceApplyId, obj -> obj);
        List<Long> goodsIds = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId);
        Map<Long, Product> productMap = StreamUtil.toMap(productDirector.selectList(goodsIds), Product::getId, e -> e);
        // 我的产品
        List<Long> customerGoodsIds = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getCustomerGoodsId);
        List<CustomerGoods> customerGoodsList = customerGoodsService.listByIds(customerGoodsIds).getData();

        Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getId, obj -> obj);
        // 费用规则
        Map<Long, List<GoodsExpenseRule>> goodsExpenseRuleMap = goodsExpenseRuleService.selectByGoodsId(StreamUtil.map(customerGoodsList, CustomerGoods::getGoodsId));
        // 融资申请
        List<FinanceApply> financeApplies = financeApplyMapper.selectListIds(financeApplyIdList);
        List<String> financeNoList = StreamUtil.map(financeApplies, FinanceApply::getFinanceNo);
        Map<Long, FinanceApply> financeApplyMap = StreamUtil.toMap(financeApplies, FinanceApply::getId, obj -> obj);
        // 发票增值税
        List<BillInvoiceDetail> invoiceDetails = billInvoiceDetailService.lambdaQuery().in(BillInvoiceDetail::getFinanceNo, financeNoList).list();
        Map<String, String> taxRateMap = StreamUtil.filterToStream(invoiceDetails, billInvoiceDetail -> Objects.nonNull(billInvoiceDetail.getTaxRate()))
                .collect(Collectors.toMap(BillInvoiceDetail::getFinanceNo, BillInvoiceDetail::getTaxRate));

        Map<Long, RepaymentExpenseReq> result = Maps.newHashMap();
        for (LoanManageRepaymentPlan loanManageRepaymentPlan : loanManageRepaymentPlans) {
            LoanManageIou loanManageIou = loanManageIouMap.get(loanManageRepaymentPlan.getIouNo());
            List<ExpenseOrder> billExpenseOrder = expenseOrderMap.get(loanManageRepaymentPlan.getFinanceApplyId());
//            CustomerGoods customerGoods = customerGoodsMap.get(loanManageRepaymentPlan.getCustomerGoodsId());
//            if (ObjectUtil.isEmpty(customerGoods)) {
//                throw new ServiceException("用户产品不存在!");
//            }
            List<GoodsExpenseRule> goodsExpenseRules = goodsExpenseRuleMap.get(loanManageRepaymentPlan.getGoodsId());
            List<GoodsExpenseRule> expenseRules = SpringUtil.getBean(IGoodsExpenseRuleService.class).list(Wrappers.<GoodsExpenseRule>lambdaQuery()
                    .eq(GoodsExpenseRule::getDefaultInterestAccrual, 1));
            FinanceApply financeApply = financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId());
            RepaymentExpenseReq repaymentExpenseReq = RepaymentExpenseReq.builder()
                    .product(productMap.get(loanManageRepaymentPlan.getGoodsId()))
                    .loanManageIou(loanManageIou)
                    .loanManageRepaymentPlan(loanManageRepaymentPlan)
                    .feeAmount(Objects.nonNull(billExpenseOrder) ? billExpenseOrder.stream()
                            .map(ExpenseOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO)
                    .annualInterestRate(financeApply.getAnnualInterestRate())
                    .serviceRate(financeApply.getServiceRate())
                    .goodsExpenseRules(goodsExpenseRules == null ? expenseRules : goodsExpenseRules)
                    .taxRate(taxRateMap.getOrDefault(financeApply.getFinanceNo(), BigDecimal.ZERO.toString()))
                    .build();

            result.put(loanManageRepaymentPlan.getId(), repaymentExpenseReq);
        }
        return result;
    }

    private void noRepayment(LoanManageRepaymentPlanVO planVO) {
        planVO.setLoanManageRepaymentVOList(Collections.emptyList());
        planVO.setActualAmount(BigDecimal.ZERO);
        planVO.setUnPayAmount(planVO.getPrincipal().add(planVO.getInterest()).add(planVO.getPenaltyInterest()));
    }

    private void haveRepayment(LoanManageRepaymentPlanVO planVO, List<Long> repaymentIds) {
        //查询还款成功且非提前结清记录
        List<LoanManageRepayment> repayments = loanManageRepaymentMapper.selectList(Wrappers.<LoanManageRepayment>lambdaQuery()
                .in(LoanManageRepayment::getId, repaymentIds)
                .eq(BaseEntity::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY.getCode())
                .ne(LoanManageRepayment::getRepaymentType, RepaymentConstant.RepaymentTypeEnum.ADVANCE_SETTLE.getCode())
                .orderByDesc(LoanManageRepayment::getCreateTime));

        //实还总额
        BigDecimal actualAmount = repayments.stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<LoanManageRepaymentVO> loanManageRepaymentVOS = LoanManageRepaymentWrapper.build().listVO(repayments);
        //查询还款记录关联费用
        for (LoanManageRepaymentVO loanManageRepaymentVO : loanManageRepaymentVOS) {
            List<RepaymentFee> payRepaymentFeeList = repaymentFeeService.getPayRepaymentFeeList(loanManageRepaymentVO.getId());
            loanManageRepaymentVO.setRepaymentFeeList(RepaymentFeeWrapper.build().listVO(payRepaymentFeeList));
        }
        //统计未还总额
        BigDecimal unPayAmount;
        if (RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode() == planVO.getRepaymentStatus()
                || CollUtil.isEmpty(repayments)) {
            //已结清或者还款成功记录为空不统计未还
            unPayAmount = BigDecimal.ZERO;
        } else {
            AddAmountVO addAmountVO = new AddAmountVO();
            addAmountVO = getAddAmountVO(planVO, repayments.get(0), actualAmount, addAmountVO);
            unPayAmount = addAmountVO.getUnPayAmount();
        }
        planVO.setLoanManageRepaymentVOList(loanManageRepaymentVOS);
        planVO.setActualAmount(actualAmount);
        planVO.setUnPayAmount(unPayAmount);
    }


    private void selectFather(Long oldFinanceId, List<Long> result) {
        //查询数据库中对应id的实体类
        //查询符合条件的对象
        FinanceApply oldFinanceApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getId, oldFinanceId));
        if (oldFinanceApply != null) {
            result.add(oldFinanceApply.getId());
            selectFather(oldFinanceApply.getOldFinanceId(), result);
        }
    }


    private List<LoanHistoryRepaymentPlanVO> loanHistoryRepaymentPlanList(List<LoanHistoryRepaymentPlanVO> repaymentPlanVOList, List<LoanHistoryRepaymentPlanVO> historyRepaymentPlan) {
        if (CollectionUtil.isEmpty(historyRepaymentPlan)) {
            historyRepaymentPlan = Collections.emptyList();
        }
        List<LoanHistoryRepaymentPlanVO> historyRepaymentPlanVOS = historyRepaymentPlan;
        //过滤使用中的
        BigDecimal totalAmount = repaymentPlanVOList.stream().map(LoanHistoryRepaymentPlanVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal interest = repaymentPlanVOList.stream().map(LoanHistoryRepaymentPlanVO::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principal = repaymentPlanVOList.stream().map(LoanHistoryRepaymentPlanVO::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal serviceFee = repaymentPlanVOList.stream().map(LoanHistoryRepaymentPlanVO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        //上一次变更的金额
        BigDecimal lastTotalAmount = historyRepaymentPlan.stream().map(LoanHistoryRepaymentPlanVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lastInterest = historyRepaymentPlan.stream().map(LoanHistoryRepaymentPlanVO::getInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lastPrincipal = historyRepaymentPlan.stream().map(LoanHistoryRepaymentPlanVO::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal lastServiceFee = historyRepaymentPlan.stream().map(LoanHistoryRepaymentPlanVO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

        //动态费用变更情况
        //1.旧订单动态费用
        Map<Long, BigDecimal> oldPlanfeeMap = historyRepaymentPlan.stream()
                .flatMap(loanHistoryRepaymentPlanVO -> loanHistoryRepaymentPlanVO.getRepaymentPlanFeeList().stream())
                .collect(Collectors.groupingBy(RepaymentPlanFee::getExpenseTypeId, Collectors.reducing(BigDecimal.ZERO, repaymentPlanFee -> repaymentPlanFee.getAmount(), BigDecimal::add)));
        //2.新动态费用
        List<AlterResult> newPlanfeeMap = new ArrayList<>();
        repaymentPlanVOList.stream()
                .flatMap(loanHistoryRepaymentPlanVO -> loanHistoryRepaymentPlanVO.getRepaymentPlanFeeList().stream())
                .collect(Collectors.groupingBy(RepaymentPlanFee::getExpenseTypeId)).forEach((expensetypeId, fees) -> {
                    BigDecimal totalPlanfee = fees.stream()
                            .map(RepaymentPlanFee::getAmount)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    AlterResult alterResult = new AlterResult();
                    alterResult.setExpenseTypeId(expensetypeId);
                    alterResult.setTotalAmount(totalPlanfee);
                    if (!CollectionUtils.isEmpty(historyRepaymentPlanVOS) && totalPlanfee.subtract(oldPlanfeeMap.get(expensetypeId)).signum() == -1) {
                        BigDecimal negate = totalPlanfee.subtract(oldPlanfeeMap.get(expensetypeId)).negate();
                        alterResult.setAlterAmount(negate);
                        alterResult.setTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                    } else if (!CollectionUtils.isEmpty(historyRepaymentPlanVOS) && totalPlanfee.subtract(oldPlanfeeMap.get(expensetypeId)).signum() == 0) {
                        alterResult.setTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else if (!CollectionUtils.isEmpty(historyRepaymentPlanVOS)) {
                        alterResult.setAlterAmount(totalPlanfee.subtract(oldPlanfeeMap.get(expensetypeId)));
                        alterResult.setTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                    newPlanfeeMap.add(alterResult);
                });
        repaymentPlanVOList.forEach(repaymentPlan -> {
            repaymentPlan.setMonthlySupply(totalAmount.setScale(2, RoundingMode.HALF_DOWN));
            repaymentPlan.setMonthlyInterest(interest.setScale(2, RoundingMode.HALF_DOWN));
            repaymentPlan.setMonthlyPrincipal(principal.setScale(2, RoundingMode.HALF_DOWN));
            repaymentPlan.setMonthlyServiceCharge(serviceFee.setScale(2, RoundingMode.HALF_DOWN));
            repaymentPlan.setDailyInterestRate(BorrowAndReturnUtils.calculationDayRate(repaymentPlan.getAnnualInterestRate(), 2));
            //比较上一次变更的金额差
            if (!CollectionUtils.isEmpty(historyRepaymentPlanVOS)) {
                if (!LoanAlterationEnum.LOAN_UNCHANGED.getCode().equals(repaymentPlan.getAlterationType())) {
                    if (repaymentPlan.getAnnualInterestRate().subtract(historyRepaymentPlanVOS.get(0).getAnnualInterestRate()).signum() == -1) {
                        BigDecimal negate = repaymentPlan.getAnnualInterestRate().subtract(historyRepaymentPlanVOS.get(0).getAnnualInterestRate()).negate();
                        repaymentPlan.setTrendInterestRate(negate);
                        repaymentPlan.setInterestRateTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                    } else if (repaymentPlan.getAnnualInterestRate().compareTo(historyRepaymentPlanVOS.get(0).getAnnualInterestRate()) == 0) {
                        repaymentPlan.setInterestRateTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else {
                        repaymentPlan.setTrendInterestRate(repaymentPlan.getAnnualInterestRate().subtract(historyRepaymentPlanVOS.get(0).getAnnualInterestRate()));
                        repaymentPlan.setInterestRateTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                    if (repaymentPlan.getMonthlySupply().subtract(lastTotalAmount).signum() == -1) {
                        BigDecimal negate = repaymentPlan.getMonthlySupply().subtract(lastTotalAmount).negate();
                        repaymentPlan.setMonthlySupplyTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                        repaymentPlan.setTrendMonthlySupply(negate);
                    } else if (repaymentPlan.getMonthlySupply().compareTo(lastTotalAmount) == 0) {
                        repaymentPlan.setMonthlySupplyTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else {
                        repaymentPlan.setTrendMonthlySupply(repaymentPlan.getMonthlySupply().subtract(lastTotalAmount));
                        repaymentPlan.setMonthlySupplyTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                    if (repaymentPlan.getMonthlyPrincipal().subtract(lastPrincipal).signum() == -1) {
                        BigDecimal negate = repaymentPlan.getMonthlyPrincipal().subtract(lastPrincipal).negate();
                        repaymentPlan.setTrendMonthlyPrincipal(negate);
                        repaymentPlan.setMonthlyPrincipalTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                    } else if (repaymentPlan.getMonthlyPrincipal().compareTo(lastPrincipal) == 0) {
                        repaymentPlan.setMonthlyPrincipalTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else {
                        repaymentPlan.setTrendMonthlyPrincipal(repaymentPlan.getMonthlyPrincipal().subtract(lastPrincipal));
                        repaymentPlan.setMonthlyPrincipalTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                    if (repaymentPlan.getMonthlyInterest().subtract(lastInterest).signum() == -1) {
                        repaymentPlan.setTrendMonthlyInterest(repaymentPlan.getMonthlyInterest().subtract(lastInterest).negate());
                        repaymentPlan.setMonthlyInterestTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                    } else if (repaymentPlan.getMonthlyInterest().compareTo(lastInterest) == 0) {
                        repaymentPlan.setMonthlyInterestTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else {
                        repaymentPlan.setTrendMonthlyInterest(repaymentPlan.getMonthlyInterest().subtract(lastInterest));
                        repaymentPlan.setMonthlyInterestTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                    if (repaymentPlan.getMonthlyServiceCharge().subtract(lastServiceFee).signum() == -1) {
                        repaymentPlan.setTrendMonthlyServiceCharge(repaymentPlan.getMonthlyServiceCharge().subtract(lastServiceFee).negate());
                        repaymentPlan.setMonthlyServiceChargeTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_DECLINE.getCode());
                    } else if (repaymentPlan.getMonthlyServiceCharge().compareTo(lastServiceFee) == 0) {
                        repaymentPlan.setMonthlyServiceChargeTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_ZERO.getCode());
                    } else {
                        repaymentPlan.setTrendMonthlyServiceCharge(repaymentPlan.getMonthlyServiceCharge().subtract(lastServiceFee));
                        repaymentPlan.setMonthlyServiceChargeTrendType(HistoryAlterationEnum.HISTORY_ALTERATION_RISE.getCode());
                    }
                }
            }
            repaymentPlan.setPlanfeeMap(newPlanfeeMap);
        });
        return repaymentPlanVOList;
    }


    @Override
    public List<LoanManageRepaymentPlan> selectRepaymentList(String iouNo) {
        return baseMapper.selectRepaymentList(iouNo);
    }

    @Override
    public LoanManageRepaymentPlan selectRepaymentById(Long financeId) {
        return baseMapper.selectRepaymentById(financeId);
    }


    /**
     * 查询未付款的计划
     *
     * @param financeIds
     * @return
     */
    @Override
    public List<LoanManageRepaymentPlan> listUnPayLoanManageRepaymentPlans(List<Long> financeIds) {
        List<Integer> unFinishStatus = Arrays.asList(RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode());
        List<LoanManageRepaymentPlan> list = list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                .in(LoanManageRepaymentPlan::getStatus, unFinishStatus)
                .in(LoanManageRepaymentPlan::getFinanceApplyId, financeIds));
        return list;
    }

    @Override
    public List<LoanManageRepaymentPlan> listOverdueLoanManageRepaymentPlans(List<Long> financeIds) {
        List<LoanManageRepaymentPlan> list = list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                .in(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode())
                .in(LoanManageRepaymentPlan::getFinanceApplyId, financeIds));
        return list;
    }

    @Override
    public Integer selectRepaymentPeriodMax(Long financeId) {
        return baseMapper.selectRepaymentPeriodMax(financeId);
    }

    @Override
    public List<LoanManageRepaymentPlan> selectListByIds(List<Long> repaymentPlanIdList) {
        return baseMapper.selectListByIds(repaymentPlanIdList);
    }

    @Override
    public LoanManageRepaymentPlan getRepaymentPlanById(Long repaymentPlanId) {
        return baseMapper.getRepaymentPlanById(repaymentPlanId);
    }

}
