/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.loan.entity.OverdueStage;
import org.springblade.loan.vo.OverdueStageVO;

/**
 * 放款申请 服务类
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
public interface IOverdueStageService extends BaseService<OverdueStage> {


	/**
	 * 根据逾期天数获取对应的等级
	 * @param overdueDay 逾期天数
	 * @return name
	 */
	String getNameByOverdueDay(Integer overdueDay);


	/**
	 * 分页 逾期阶段列表
	 * @param overdueStage 查询对象
	 * @param query 分页参数
	 * @return IPage<OverdueStageVO>
	 */
	IPage<OverdueStageVO> selectOverdueStagePage(OverdueStage overdueStage, Query query);

	/**
	 * 获取最大分期阶段
	 */
	String selectMax();
}
