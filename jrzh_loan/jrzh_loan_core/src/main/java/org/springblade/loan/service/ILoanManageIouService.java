/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.loan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.vo.LoanManageIouNewVo;
import org.springblade.loan.vo.LoanManageIouTypeVO;
import org.springblade.loan.vo.LoanManageIouVO;
import org.springblade.loan.vo.LoanManageRepaymentVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 借据单 服务类
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
public interface ILoanManageIouService extends BaseService<LoanManageIou> {

    /**
     * 自定义分页
     *
     * @param page
     * @param loanManageIou
     * @return
     */
    IPage<LoanManageIouVO> selectLoanManageIouPage(IPage<LoanManageIouVO> page, LoanManageIouVO loanManageIou);


    /**
     * 批量修改5级分类
     *
     * @param ids               id集合
     * @param fiveLevelCategory 五级分类状态
     * @return 是否成功
     */
    boolean updateBatchFiveLevelCategoryById(List<Long> ids, Integer fiveLevelCategory);

    /**
     * 根据融资编号查询
     *
     * @param financeNo
     * @return
     */
    LoanManageIou getByFinancingNo(String financeNo);

    /**
     * 调息借据详情
     */
    LoanManageIouVO adjustmentDetail(Long id);


    /**
     * 保存借据单
     *
     * @param financeApply 融资申请对象
     * @return 借据单对象
     */
    LoanManageIou saveLoanManageIou(FinanceApply financeApply);

    LoanManageIou saveLoanManageIou(FinanceApply financeApply, LocalDate startTime, LocalDate refundTime);

//	/**
//	 * 客户贷款报表
//	 *
//	 * @param year
//	 * @param month
//	 * @param type
//	 * @param userId
//	 * @return
//	 */
//	List<CreditReport> customerCreditReport(Integer year, String month, Integer type, Long userId, String tentId);

    /**
     * 近n天拮据情况
     *
     * @param userId
     * @param recentDay
     * @return
     */
    List<LoanManageIouVO> listWithDayAndUser(Long userId, Integer recentDay);

    /**
     * 近n天客户还款情况
     *
     * @param userId
     * @param recentDay
     * @return
     */
    List<LoanManageRepaymentVO> listWithDayAndUser(Long userId, Integer recentDay, Integer status);

//	/**
//	 * 新增融资订单穿透报表
//	 * @param dto
//	 * @return
//	 */
//    List<CreditPassVO> creditNewCreditReport(CreditReportDto dto);

//	/**
//	 * 新增融资客户穿透报表
//	 * @param dto
//	 * @return
//	 */
//	List<CreditPassVO> creditNewCustomerReport(CreditReportDto dto);

    /**
     * 查询还款单类型与产品展期方式
     *
     * @param iouNo
     * @return
     */
    LoanManageIouTypeVO selectType(String iouNo);

    /**
     * 所有融资客户数
     *
     * @return
     */
    Set<Long> financingCustomersNum();

//	/**
//	 * 驾驶舱融资金额
//	 * @return
//	 */
//	ControlBoxVO controlBoxFinancingAmount();


    /**
     * 根据时间获取借据放款金额
     *
     * @param timeStr
     * @return
     */
    BigDecimal getIouAmountByTime(String timeStr);

    /**
     * 根据时间区域获取借据放款金额
     *
     * @param nowWeek
     * @return
     */
    BigDecimal getIouAmountByTimes(List<String> nowWeek);

//	/**
//	 * 驾驶舱  公司详情
//	 * @param nowMonth
//	 * @return
//	 */
//	List<ControlBoxVO> controlBoxCompanyDetails(String nowMonth);

//	/**
//	 * 根据用户id统计省份融资金额
//	 * @param userIdList
//	 * @param enterpriseMap
//	 * @return
//	 */
//	Map<String, BigDecimal> getAbbrMapById(Set<Long> userIdList, Map<Long, String> enterpriseMap);

//	/**
//	 * 所有放款金额
//	 * @return
//	 */
//	List<CreditPassVO> allLoanAmount();

//	/**
//	 * 根据用户id统计省份融资客户数
//	 * @param userIdList
//	 * @param enterpriseMap
//	 * @return
//	 */
//	Map<String, Integer> getAbbrMapNumById(Set<Long> userIdList, Map<Long, String> enterpriseMap);

    List<LoanManageIou> queryloanManageIous(List<String> financeNos);

    /***
     * 查询 借据信息
     * @param companyId
     * @return
     */
    IPage<LoanManageIouVO> disbursementRecord(Long companyId, Query query);

    /***
     * 还款明细
     * @param companyId
     * @return
     */
    IPage<LoanManageRepaymentVO> repaymentList(Long companyId, Query page);

    /**
     * 移除
     *
     * @param financeNo
     * @return
     */
    Boolean removeIou(String financeNo);

    /**
     * 获取借据单
     *
     * @param iouNo
     * @return
     */
    LoanManageIou getByIouNo(String iouNo);

    /**
     * 获取借据单
     *
     * @param iouNOList
     * @return
     */
    List<LoanManageIou> listByIouNo(List<String> iouNOList);

    /**
     * 是否可关闭借据单
     *
     * @param loanManageIou
     * @return
     */
    boolean canClose(LoanManageIou loanManageIou);

    /**
     * @param time
     * @return
     */
    List<String> getIouNos(String time, Integer goodsType);

//    /**
//     * 新下单用户
//     *
//     * @param time              时间点之前
//     * @return
//     */
//    Set<Long> getNewCustomer(, LocalDate time);

    /**
     * 在时间之前 只取三个字段 id loanTime userId
     *
     * @param time
     * @return
     */
    List<LoanManageIou> listByTimeBefore(LocalDate time);

    /**
     * 新下单用户id
     *
     * @param time 时间点 年：2024/月:2024-01/日:2024-01-29
     */
    Set<Long> getCustomerNewCountByTime(String time, List<Integer> goodsType, String tenantId);
    /**
     * 新下单的订单信息
     *
     * @param queryTime  时间点 年：2024/月:2024-01/日:2024-01-29
     * @param goodsTypeList 产品类型
     * @param tenantId
     * @return
     */
    List<LoanManageIou> listCustomerNewByTime(String queryTime, List<Integer> goodsTypeList, String tenantId);
    /**
     * 新下单的订单信息
     *
     * @param queryTime  时间点 年：2024/月:2024-01/日:2024-01-29
     * @param goodsTypeList 产品类型
     * @param tenantId
     * @return
     */
    List<LoanManageIou> listNewByTime(String queryTime, List<Integer> goodsTypeList, String tenantId);

    /**
     * 连表查询返回vo
     * @param loanManageIou
     * @return
     */
    List<LoanManageIouVO> voList(LoanManageIou loanManageIou);

    /**
     * 统计融资总额及融资用户数量
     * <AUTHOR>
     * @date 2025/3/15 16:22
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> getTotalLoanManageIou();


    /**
     * 获取最近10天新增的融资订单
     * <AUTHOR>
     * @date 2025/3/15 17:44
     * @return java.util.List<org.springblade.loan.vo.LoanManageIouNewVo>
     */
    List<LoanManageIouNewVo> selectLoanManageIouNewList();

}
