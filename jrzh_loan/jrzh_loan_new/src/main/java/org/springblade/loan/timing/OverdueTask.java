package org.springblade.loan.timing;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.OverdueConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.loan.dto.RepaymentPlanReCalParam;
import org.springblade.loan.entity.*;
import org.springblade.loan.service.*;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.repayment.handler.RepaymentPlanStatusNotify;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("overdueTask")
@RequiredArgsConstructor
@Slf4j
public class OverdueTask implements MessageHandler {

    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final ILoanManageOverdueService loanManageOverdueService;
    private final IOverdueStageService overdueStageService;
    private final IFinanceApplyService financeApplyService;
    private final IRepaymentBizService repaymentBizService;
    private final IRepaymentReCalBizService repaymentReCalBizService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final ProductDirector productDirector;
    private final ILoanManageIouService loanManageIouService;
    private final List<RepaymentPlanStatusNotify> loanManageIouServiceList;
    private final RabbitMsgSender rabbitMsgSender;
    private final ApplicationEventPublisher applicationEventPublisher;

    private final ILoanManageRepaymentService loanManageRepaymentService;

    @Transactional(rollbackFor = Exception.class)
    public void save() {
        //未结束的还款计划状态
        List<Integer> unFinishStatus = Arrays.asList(RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE.getCode());
        List<Integer> allType = productDirector.getAllType();
        if (allType.isEmpty()) {
            return;
        }
        // 找到已还完的预期数据
        List<LoanManageOverdue> dbLoanManageOverdueList = loanManageOverdueService.list(Wrappers.<LoanManageOverdue>lambdaQuery()
                .eq(LoanManageOverdue::getStatus, OverdueConstant.OverdueStatus.PAY.getCode())
        );
        List<Long> paidRepaymentPlanIds = CollStreamUtil.toList(dbLoanManageOverdueList, LoanManageOverdue::getRepaymentPlanId);
        // 查询待还款的还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.lambdaQuery()
                .in(LoanManageRepaymentPlan::getGoodsType, allType)
                .ne(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.INVALID.getCode())
                .in(LoanManageRepaymentPlan::getStatus, unFinishStatus)
                .notIn(CollectionUtil.isNotEmpty(paidRepaymentPlanIds), LoanManageRepaymentPlan::getId, paidRepaymentPlanIds)
                .list();
//        loanManageRepaymentPlans = loanManageRepaymentPlans.stream().filter(e -> 1731492504311808002L==e.getGoodsId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(loanManageRepaymentPlans)) {
            return;
        }
        // 过滤已经过期的
        List<LoanManageRepaymentPlan> expireRepaymentPlans = StreamUtil.filter(loanManageRepaymentPlans,
                loanManageRepaymentPlan -> loanManageRepaymentPlan.getRepaymentTime().compareTo(LocalDate.now()) < 0);
        if (CollectionUtils.isEmpty(expireRepaymentPlans)) {
            return;
        }
//        List<Long> financeApplyIdList = StreamUtil.map(expireRepaymentPlans, LoanManageRepaymentPlan::getFinanceApplyId);
//        List<FinanceApply> financeApplies = financeApplyService.listByIds(financeApplyIdList);
//        financeApplies.forEach(financeApply -> financeApply.setStatus(FinanceApplyStatusEnum.OVERDUE_UN_SETTLED.getCode()));
        // 将过期的设置未逾期未还状态
        expireRepaymentPlans.forEach(expireRepaymentPlan -> {
            expireRepaymentPlan.setOverdue(RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode());
            expireRepaymentPlan.setStatus(RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode());
        });
        //保存逾期记录
        saveOverdueRecord(expireRepaymentPlans);
        loanManageRepaymentPlanService.updateBatchById(expireRepaymentPlans);
        //逾期通知
        Map<Integer, List<LoanManageRepaymentPlan>> collect = expireRepaymentPlans.stream().collect(Collectors.groupingBy(LoanManageRepaymentPlan::getGoodsType));
        for (Integer goodsType : collect.keySet()) {
            for (RepaymentPlanStatusNotify repaymentPlanStatusNotify : loanManageIouServiceList) {
                if (repaymentPlanStatusNotify.support().getCode().equals(goodsType)) {
                    repaymentPlanStatusNotify.overDue(collect.get(goodsType));
                }
            }
        }
    }

    /**
     * 更新逾期费用
     */
    public void updateOverDueFee() {
//        repaymentPlanFeeService.list(Wrappers.<RepaymentPlanFee>lambdaQuery()
//                .eq(RepaymentPlanFee::getRepaymentStatus)
//                .eq(RepaymentPlanFee::getFeeNode, ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode()))
        List<Integer> status = Arrays.asList(OverdueConstant.OverdueStatus.SMS.getCode()
                , OverdueConstant.OverdueStatus.ARTIFICIAL.getCode());
        List<LoanManageOverdue> list = loanManageOverdueService
                .list(Wrappers.<LoanManageOverdue>lambdaQuery()
                        .select(LoanManageOverdue::getRepaymentPlanId)
                        .eq(LoanManageOverdue::getStatus, status));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans =
                loanManageRepaymentPlanService.listByIds(list.stream().map(LoanManageOverdue::getRepaymentPlanId).distinct().collect(Collectors.toList()));
        addOverDueFee(loanManageRepaymentPlans);
    }

    /**
     * 保存逾期记录
     *
     * @param expireRepaymentPlans
     */
    /**
     * 保存逾期记录
     *
     * @param expireRepaymentPlans
     */
    private void saveOverdueRecord(List<LoanManageRepaymentPlan> expireRepaymentPlans) {
        List<OverdueStage> overdueStageList = overdueStageService.list(Wrappers.<OverdueStage>lambdaQuery()
                .orderByAsc(OverdueStage::getMinDay)
        );
        String maxOverdueStageName = overdueStageService.selectMax();
        // 判断还款方式，分期的情况每期都会有逾期记录，随借随还的情况只有一条逾期记录
        List<List<LoanManageRepaymentPlan>> partition = Lists.partition(expireRepaymentPlans, 500);
        for (List<LoanManageRepaymentPlan> loanManageRepaymentPlanList : partition) {
            List<LoanManageOverdue> loanManageOverdueList = new CopyOnWriteArrayList<>();
            List<Long> repaymentPlanIdList = StreamUtil.map(loanManageRepaymentPlanList, LoanManageRepaymentPlan::getId);
            // 逾期的还款记录
            List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.selectOverdueHavePaid(repaymentPlanIdList);
            // 还款记录map
            Map<Long, LoanManageRepayment> loanManageRepaymentsMap = loanManageRepayments.stream().collect(Collectors.toMap(LoanManageRepayment::getRepaymentPlanId, obj -> obj, (oldVal, newVal) -> {
                oldVal.setActualPrincipal(oldVal.getActualPrincipal().add(newVal.getActualPrincipal()));
                oldVal.setActualInterest(oldVal.getActualInterest().add(newVal.getActualInterest()));
                oldVal.setActualAmount(oldVal.getActualAmount().add(newVal.getActualAmount()));
                return oldVal;
            }));
            List<LoanManageOverdue> dbLoanManageOverdues = loanManageOverdueService.list(Wrappers.<LoanManageOverdue>lambdaQuery()
                    .in(LoanManageOverdue::getRepaymentPlanId, repaymentPlanIdList)
            );
            Map<Long, LoanManageOverdue> dbLoanManageOverdueMap = CollStreamUtil.toMap(dbLoanManageOverdues, LoanManageOverdue::getRepaymentPlanId, obj -> obj);
            for (LoanManageRepaymentPlan loanManageRepaymentPlan : loanManageRepaymentPlanList) {
                LoanManageRepayment loanManageRepayment = loanManageRepaymentsMap.get(loanManageRepaymentPlan.getId());
                LoanManageOverdue dbEntity = dbLoanManageOverdueMap.get(loanManageRepaymentPlan.getId());
                LoanManageOverdue loanManageOverdue;
                if (ObjectUtil.isNotEmpty(dbEntity)) {
                    loanManageOverdue = this.updateLoanManageOverdue(dbEntity, loanManageRepayment, overdueStageList, maxOverdueStageName);
                } else {
                    loanManageOverdue = this.buildLoanManageOverdue(loanManageRepaymentPlan, loanManageRepayment, overdueStageList, maxOverdueStageName);
                }
                // 判断是否还完
                BigDecimal repaidPrincipal = loanManageOverdue.getRepaidPrincipal();
                if (loanManageRepaymentPlan.getPrincipal().compareTo(repaidPrincipal) <= 0) {
                    loanManageRepaymentPlan.setStatus(RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE.getCode());
                    loanManageRepaymentPlan.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
                    loanManageOverdue.setStatus(OverdueConstant.OverdueStatus.PAY.getCode());
                }
                loanManageOverdueList.add(loanManageOverdue);
            }
            loanManageOverdueService.saveOrUpdateBatch(loanManageOverdueList);
        }
        //添加逾期费用
        addOverDueFee(expireRepaymentPlans);
    }

    /**
     * 添加逾期费用 并开启计费
     *
     * @param expireRepaymentPlans
     */
    private void addOverDueFee(List<LoanManageRepaymentPlan> expireRepaymentPlans) {
        //按产品分组->
        Map<Long, List<LoanManageRepaymentPlan>> userLoanManageRepaymentPlanMap = expireRepaymentPlans.stream().filter(e -> ObjectUtil.isNotEmpty(e.getGoodsType()))
                .collect(Collectors.groupingBy(LoanManageRepaymentPlan::getGoodsId));

        String tenantId = expireRepaymentPlans.get(0).getTenantId();
        for (Long goodsIds : userLoanManageRepaymentPlanMap.keySet()) {
            List<LoanManageRepaymentPlan> repaymentInfoDTOS1 = userLoanManageRepaymentPlanMap.get(goodsIds);
//            if(goodsIds==1731492504311808002L){
            //丢到mq中慢慢运行
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType("overdueTask")
                    .tenantId(tenantId)
                    .msg(JSONUtil.toJsonStr(repaymentInfoDTOS1.stream().map(e -> e.getId().toString()).collect(Collectors.joining(","))))
                    .seconds(0)
                    .extendParam(new HashMap<>())
                    .build());
//            }

        }
    }

    /**
     * 执行逾期费用更新
     *
     * @param delayMessage 消息内容
     */
    @Override
    public void handler(DelayMessage delayMessage) {
        List<Long> repaymentPlanIds = Func.toLongList(delayMessage.getMsg());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIds);
        List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentBizService.getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans);
        addOverDueFeeByUserId(repaymentInfoDTOS);
    }

    private void addOverDueFeeByUserId(List<RepaymentInfoDTO> repaymentInfoDTOS1) {
        //费用计算
        List<Integer> reCalNode = Collections.singletonList(ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode());
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .currentNode(ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode())
                .reCalGenFeeNode(reCalNode)
                .reCalGenFee(true)
                .feeNode(reCalNode)
                .collectFeedOnly(true).build();
        List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentReCalBizService.reCalRepaymentInfoDTO(build, repaymentInfoDTOS1);
        List<RepaymentPlanFee> repaymentPlanFees = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getRepaymentPlanFeeList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(repaymentPlanFees)) {
            List<Long> existRepaymentPlanFee = repaymentPlanFeeService.list(Wrappers.<RepaymentPlanFee>lambdaQuery().select(RepaymentPlanFee::getId).in(RepaymentPlanFee::getId, repaymentPlanFees.stream().map(RepaymentPlanFee::getId).collect(Collectors.toList())))
                    .stream().map(RepaymentPlanFee::getId).collect(Collectors.toList());
            Map<Boolean, List<RepaymentPlanFee>> collect = repaymentPlanFees.stream()
                    .collect(Collectors.partitioningBy(e -> existRepaymentPlanFee.contains(e.getId())));
            //更新逾期金额
            if (CollUtil.isNotEmpty(collect.get(true))) {
                //防止数据量太大更新失败
                Map<Long, List<RepaymentPlanFee>> userRepaymentPlanFee = collect.get(true).stream().collect(Collectors.groupingBy(RepaymentPlanFee::getUserId));
                for (Long aLong : userRepaymentPlanFee.keySet()) {
                    List<RepaymentPlanFee> repaymentPlanFeeList = userRepaymentPlanFee.get(aLong);
                    for (RepaymentPlanFee repaymentPlanFee : repaymentPlanFeeList) {
                        //不更新该字段
                        repaymentPlanFee.setExpenseOrderDetailStr(null);
                    }
                    repaymentPlanFeeService.updateBatchById(repaymentPlanFeeList);
                }

            }
            //新增逾期金额
            if (CollUtil.isNotEmpty(collect.get(false))) {
                //防止数据量太大更新失败
                Map<Long, List<RepaymentPlanFee>> userRepaymentPlanFee = collect.get(false).stream().collect(Collectors.groupingBy(RepaymentPlanFee::getUserId));
                for (Long aLong : userRepaymentPlanFee.keySet()) {
                    List<RepaymentPlanFee> repaymentPlanFeeList = userRepaymentPlanFee.get(aLong);
                    repaymentPlanFeeService.saveBatch(repaymentPlanFeeList);
                }
            }
        }
    }

    /**
     * 立即将融资单未逾期的全部进行逾期
     */
    public void back(String arg) {
        String[] split = arg.split(",");
        String iouNo = split[0];
        String overdueTime = split[1];
        Integer day = Integer.parseInt(overdueTime);
        //未结束的还款计划状态
        List<Integer> unFinishStatus = Arrays.asList(RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode());
        // 查询待还款的还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.lambdaQuery()
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                .in(LoanManageRepaymentPlan::getStatus, unFinishStatus)
                .eq(LoanManageRepaymentPlan::getIouNo, iouNo)
                .list();
        if (CollectionUtil.isEmpty(loanManageRepaymentPlans)) {
            return;
        }
        Map<String, List<LoanManageRepaymentPlan>> map = loanManageRepaymentPlans.stream().collect(Collectors.groupingBy(LoanManageRepaymentPlan::getTenantId));
        for (String tenantId : map.keySet()) {
            TenantBroker.runAs(tenantId, tenantId1 -> {
                List<LoanManageRepaymentPlan> tenantLoanManageRepaymentPlans = map.get(tenantId);
                tenantLoanManageRepaymentPlans.forEach(e -> {
                    e.setStartTime(e.getStartTime().minusDays(day));
                    e.setRepaymentTime(e.getRepaymentTime().minusDays(day));
                });
                loanManageRepaymentPlanService.updateBatchById(tenantLoanManageRepaymentPlans);
                //借据单往前挪
                LoanManageIou byIouNo = loanManageIouService.getByIouNo(iouNo);
                byIouNo.setLoanTime(byIouNo.getLoanTime().minusDays(day));
                byIouNo.setExpireTime(byIouNo.getExpireTime().minusDays(day));
                loanManageIouService.updateById(byIouNo);
            });
        }
    }

    public void overDueFinance(String arg) {
        String[] split = arg.split(",");
        String financeNo = split[0];
        String overdueTime = split[1];
        Integer day = Integer.parseInt(overdueTime);
        LocalDateTime localDateTime = LocalDateTime.now().minusDays(day);
        financeApplyService.update(Wrappers.<FinanceApply>lambdaUpdate().eq(FinanceApply::getFinanceNo, financeNo)
                .set(FinanceApply::getExpireTime, localDateTime));
    }

    /**
     * 立即将融资单未逾期的全部进行逾期
     */
    public void back2(String arg) {
        String[] split = arg.split(",");
        String repaymentId = split[0];
        String overdueTime = split[1];
        LocalDate repaymentTime = LocalDate.parse(overdueTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        //未结束的还款计划状态
        List<Integer> unFinishStatus = Arrays.asList(RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode());
        // 查询待还款的还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.lambdaQuery()
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode())
                .in(LoanManageRepaymentPlan::getStatus, unFinishStatus)
                .eq(LoanManageRepaymentPlan::getId, Func.toLong(repaymentId))
                .list();
        if (CollectionUtil.isEmpty(loanManageRepaymentPlans)) {
            return;
        }
        Long financeApplyId = loanManageRepaymentPlans.get(0).getFinanceApplyId();
        FinanceApply financeApply = financeApplyService.getById(financeApplyId);
        loanManageRepaymentPlans.forEach(e -> {
            e.setOverdue(RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode());
            e.setStatus(RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode());
            e.setRepaymentTime(repaymentTime);
        });
        //保存逾期记录
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put(financeApplyId.toString(), financeApply.getFinanceNo());
        saveOverdueRecord(loanManageRepaymentPlans);
        financeApplyService.changeStatus(Collections.singletonList(financeApplyId), FinanceApplyStatusEnum.OVERDUE_UN_SETTLED.getCode());
        loanManageRepaymentPlanService.updateBatchById(loanManageRepaymentPlans);
    }

    public void updateOverdueStage() {
        int code = OverdueConstant.CollectionStatus.PAY.getCode();
        List<LoanManageOverdue> loanManageOverdueList = loanManageOverdueService.lambdaQuery()
                .ne(LoanManageOverdue::getStatus, code)
                .list();

        if (CollectionUtils.isEmpty(loanManageOverdueList)) {
            return;
        }
        Map<String, List<LoanManageOverdue>> map = loanManageOverdueList.stream().collect(Collectors.groupingBy(LoanManageOverdue::getTenantId));
        for (String tenantId : map.keySet()) {
            TenantBroker.runAs(tenantId, tenantId1 -> {
                List<LoanManageOverdue> loanManageOverdues = map.get(tenantId);
                loanManageOverdues.forEach(loanManageOverdue -> {
                    //loanManageOverdue.setOverdueDay(loanManageOverdue.getOverdueDay() + 1);
                    long overdueDay = ChronoUnit.DAYS.between(loanManageOverdue.getRepaymentTime(), LocalDateTime.now());
                    loanManageOverdue.setOverdueDay(Integer.parseInt(String.valueOf(overdueDay)));
                    String overdueStage = overdueStageService.getNameByOverdueDay(loanManageOverdue.getOverdueDay());
                    loanManageOverdue.setOverdueStage(overdueStage);
                });

                loanManageOverdueService.updateBatchById(loanManageOverdues);
            });
        }

    }

    private LoanManageOverdue updateLoanManageOverdue(LoanManageOverdue dbEntity, LoanManageRepayment loanManageRepayment, List<OverdueStage> overdueStageList, String maxOverdueStageName) {
        long overdueDay = ChronoUnit.DAYS.between(dbEntity.getRepaymentTime(), LocalDateTime.now());
        dbEntity.setOverdueDay(Integer.parseInt(String.valueOf(overdueDay)));
        String overdueStage = overdueStageService.getNameByOverdueDay(dbEntity.getOverdueDay());
        dbEntity.setOverdueStage(overdueStage);
        if (ObjectUtil.isNotEmpty(loanManageRepayment)) {
            dbEntity.setRepaidPrincipal(loanManageRepayment.getActualPrincipal());
            dbEntity.setRepaidInterest(loanManageRepayment.getActualInterest());
            dbEntity.setRepaidOverdueInterest(loanManageRepayment.getActualPenaltyInterest());
        }
        return dbEntity;
    }

    private LoanManageOverdue buildLoanManageOverdue(LoanManageRepaymentPlan loanManageRepaymentPlan, LoanManageRepayment loanManageRepayment, List<OverdueStage> overdueStageList, String maxOverdueStageName) {
        // 逾期天数
        long overdueDay = ChronoUnit.DAYS.between(loanManageRepaymentPlan.getRepaymentTime(), LocalDateTime.now());
        Integer intOverdueDay = Integer.valueOf(String.valueOf(overdueDay));
        // 逾期阶段
        String overdueStageName = maxOverdueStageName;
        for (OverdueStage overdueStage : overdueStageList) {
            if (cn.hutool.core.util.ObjectUtil.isAllNotEmpty(overdueStage.getMinDay(), overdueStage.getMaxDay())
                    && intOverdueDay >= overdueStage.getMinDay() && intOverdueDay <= overdueStage.getMaxDay()) {
                overdueStageName = overdueStage.getName();
            }
        }
        String overdueStage = overdueStageService.getNameByOverdueDay(intOverdueDay);
        LoanManageOverdue loanManageOverdue = LoanManageOverdue.builder()
                .collectionNo(CodeUtil.generateCode(CodeEnum.COLLECTION_CODE))
                .period(loanManageRepaymentPlan.getPeriod())
                .financeNo(loanManageRepaymentPlan.getFinanceNo())
                .iouNo(loanManageRepaymentPlan.getIouNo())
                .repaymentTime(loanManageRepaymentPlan.getRepaymentTime())
                .userId(loanManageRepaymentPlan.getUserId())
                .overdueDay(intOverdueDay)
                .repaidOverdueInterest(BigDecimal.ZERO)
                .repaidInterest(BigDecimal.ZERO)
                .repaidPrincipal(BigDecimal.ZERO)
                .overdueStage(overdueStageName)
                .shouldPrincipal(loanManageRepaymentPlan.getPrincipal())
                .repaymentPlanId(loanManageRepaymentPlan.getId())
                .build();
        loanManageOverdue.setOverdueStage(overdueStage);
        // 随借随还最开始可能会还一部分金额后导致逾期，分期的情况还款是一次性还完，所以初始化的时候金额都为
        if (ObjectUtil.isNotEmpty(loanManageRepayment)) {
            loanManageOverdue.setRepaidPrincipal(loanManageRepayment.getActualPrincipal());
            loanManageOverdue.setRepaidInterest(loanManageRepayment.getActualInterest());
            loanManageOverdue.setRepaidOverdueInterest(loanManageRepayment.getActualPenaltyInterest());
        }
        return loanManageOverdue;
    }

}