package org.springblade.loan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.OverdueConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.common.utils.BorrowAndReturnUtils;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.expense.dto.*;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.handler.ProductExpenseOrderDetailUtils;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.factory.OrderLevelServerFactory;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.OrderLevelServerService;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.loan.dto.*;
import org.springblade.loan.entity.*;
import org.springblade.loan.enums.AdvanceSettledEnum;
import org.springblade.loan.enums.PayModeEnum;
import org.springblade.loan.expense.resp.RepaymentExpenseResp;
import org.springblade.loan.expense.util.RepaymentUtil;
import org.springblade.loan.mapper.AdvanceSettledApplyMapper;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.*;
import org.springblade.loan.vo.front.FrontRepaymentPlanVo;
import org.springblade.loan.vo.front.PreRepaymentVO;
import org.springblade.loan.vo.front.PrepaymentVO;
import org.springblade.loan.vo.front.UnPaymentListVO;
import org.springblade.loan.wrapper.LoanManageIouWrapper;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.loan.wrapper.RepaymentFeeWrapper;
import org.springblade.loan.wrapper.RepaymentPlanFeeWrapper;
import org.springblade.message.enums.MessageSceneEnum;
import org.springblade.message.utils.MessageNotifyUtil;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.product.common.dto.CapitalPayMethodAndCapitalType;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.constant.ExpenseTypeEnum;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.repayment.publisher.RepaymentSuccessPublisher;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentCreateDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.repayment.dto.RepaymentOrderDTO;
import org.springblade.repayment.handler.LoanManageIouSettleNotifyHandler;
import org.springblade.repayment.handler.RepaymentNotifyHandler;
import org.springblade.resource.constant.IncomeDetailConstant;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.entity.IncomeDetail;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.service.IIncomeDetailService;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springblade.common.enums.FinanceApplyStatusEnum.*;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-15  10:16
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class RepaymentBizImplService implements IRepaymentBizService {
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IIncomeDetailService incomeDetailService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;
    private final IRepaymentFeeService repaymentFeeService;
    private final ProductDirector productDirector;
    private final ILoanManageIouService loanManageIouService;
    private final IFinanceApplyService financeApplyService;
    private final RemoteEnterpriseQuotaService enterpriseQuotaService;
    private final IAdvanceSettledApplyService advanceSettledApplyService;
    private final IParamService paramService;
    private final ILoanManageOverdueService loanManageOverdueService;
    private final IProductExpenseService productExpenseService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private final FinanceApplyMapper financeApplyMapper;
    private final AdvanceSettledApplyMapper advanceSettledApplyMapper;
    private final IBusinessProcessProductService businessProcessProductService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final IRepaymentPlanFeeJsonService repaymentPlanFeeJsonService;
    private final IAttachService attachService;
    private final IRepaymentReCalBizService repaymentReCalBizService;
    private final IManualOperationLoanService manualOperationLoanService;
    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final List<RepaymentNotifyHandler> repaymentNotifyHandlerList;
    private final List<LoanManageIouSettleNotifyHandler> loanManageIouSettleNotifyHandlerList;
    private final OrderLevelServerFactory orderLevelServerFactory;
    private final RepaymentSuccessPublisher repaymentSuccessPublisher;

    /**
     * 还款中 或已支付
     */
    private final static List<Integer> PAYING_OR_PAYED_STATUS = Arrays.asList(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode()
            , RepaymentConstant.RepaymentStatusEnum.PAY.getCode());

    private List<LoanInfoDTO> listLoanInfoDTO(List<String> iouNOList, BigDecimal amount, List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        List<LoanInfoDTO> loanInfoDTOS = BeanUtil.copyToList(loanManageIouService.listByIouNo(iouNOList), LoanInfoDTO.class);
        if (CollUtil.isEmpty(loanInfoDTOS)) {
            return loanInfoDTOS;
        }
        if (CollUtil.isEmpty(loanManageRepaymentPlans)) {
            return loanInfoDTOS;
        }
        Map<String, List<RepaymentInfoDTO>> repaymentInfoDtoMap = getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans).stream()
                .collect(Collectors.groupingBy(LoanManageRepaymentPlan::getIouNo));
        for (LoanInfoDTO loanInfoDTO : loanInfoDTOS) {
            List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentInfoDtoMap.get(loanInfoDTO.getIouNo());
            if (CollUtil.isEmpty(repaymentInfoDTOS)) {
                initLoanInfo(loanInfoDTO, repaymentInfoDTOS);
                continue;
            }
            loanInfoDTO.setRepaymentInfoList(repaymentInfoDTOS);
            BigDecimal subPrincipal = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getSubPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subExInterest = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getSubExInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
            //计算借据单情况
            loanInfoDTO.setSubExInterest(subExInterest);
            loanInfoDTO.setSubPrincipal(subPrincipal);
            BigDecimal subOtherFee = repaymentInfoDTOS.stream().map(e -> calSubOtherFee(e.getRepaymentPlanFeeList()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanInfoDTO.getRepaymentType())) {
                //分期=每期利息总和-已还利息
                BigDecimal totalInterest = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getShouldInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal payedInterest = repaymentInfoDTOS.stream().flatMap(e -> e.getValidRepaymentList().stream())
                        .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus()))
                        .map(LoanManageRepayment::getActualInterest)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                loanInfoDTO.setSubInterest(totalInterest.subtract(payedInterest));
            } else {
                //随借随还=当前累计利息 只有一期
                BigDecimal shouldInterest = CollUtil.getFirst(repaymentInfoDTOS).getShouldInterest();
                loanInfoDTO.setSubInterest(shouldInterest);
            }
            //设置产品id
            RepaymentInfoDTO first = repaymentInfoDTOS.get(0);
            loanInfoDTO.setGoodsId(first.getGoodsId());
            //服务费率等
            loanInfoDTO.setInterestDay(first.getTotalInterestAccrualDays());
            loanInfoDTO.setAnnualInterestRate(first.getAnnualInterestRate());
            loanInfoDTO.setServiceFeeRate(first.getServiceFeeRate());
            loanInfoDTO.setInterestDayStrategy(first.getInterestDay());
            //提前借款天数
            Integer prepaymentDays = ProductExpenseOrderDetailUtils.getLoanDay(LocalDate.now(), loanInfoDTO.getExpireTime());
            loanInfoDTO.setPrepaymentDays(prepaymentDays > 0 ? prepaymentDays : 0);
            //逾期天数
            Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(loanInfoDTO.getLoanTime(), LocalDate.now());
            Integer totalOverDueDay = ProductExpenseOrderDetailUtils.getLoanDay(loanInfoDTO.getExpireTime(), LocalDate.now());
            loanInfoDTO.setOverDueDay(totalOverDueDay > 0 ? totalOverDueDay : 0);
            loanInfoDTO.setLoanDays(loanDay);
            loanInfoDTO.setCurrentRepaymentAmount(ObjectUtil.isEmpty(amount) ? loanInfoDTO.getSubPrincipal() : amount);
            List<RepaymentPlanFee> repaymentPlanFeeList = loanInfoDTO.getRepaymentInfoList().stream().flatMap(e -> e.getRepaymentPlanFeeList().stream()).collect(Collectors.toList());
            loanInfoDTO.setRepaymentPlanFee(repaymentPlanFeeList);
        }
        return loanInfoDTOS;
    }

    private static void initLoanInfo(LoanInfoDTO loanInfoDTO, List<RepaymentInfoDTO> repaymentInfoDTOS) {
        loanInfoDTO.setSubExInterest(BigDecimal.ZERO);
        loanInfoDTO.setSubPrincipal(BigDecimal.ZERO);
        loanInfoDTO.setRepaymentInfoList(repaymentInfoDTOS);
        loanInfoDTO.setSubInterest(BigDecimal.ZERO);
        loanInfoDTO.setInterestDay(0);
        loanInfoDTO.setAnnualInterestRate(BigDecimal.ZERO);
        loanInfoDTO.setServiceFeeRate(BigDecimal.ZERO);
        loanInfoDTO.setInterestDayStrategy(0);
        loanInfoDTO.setPrepaymentDays(0);
        loanInfoDTO.setLoanDays(0);
        loanInfoDTO.setCurrentRepaymentAmount(BigDecimal.ZERO);
        loanInfoDTO.setRepaymentPlanFee(Collections.emptyList());
    }

    @Override
    public List<LoanInfoDTO> listLoanInfoDTO(List<String> iouNOList) {
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIouNoList(iouNOList);
        return listLoanInfoDTO(iouNOList, null, loanManageRepaymentPlans);
    }

    @Override
    public List<LoanInfoDTO> listLoanInfoDTOContainInvalid(List<String> iouNOList) {
        if(CollUtil.isEmpty(iouNOList)){
            return Collections.emptyList();
        }
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = listLoanManageRepaymentPlansContainInvalid(iouNOList);
        return listLoanInfoDTO(iouNOList, null, loanManageRepaymentPlans);
    }

    private List<LoanManageRepaymentPlan> listLoanManageRepaymentPlansContainInvalid(List<String> iouNOList) {
        return loanManageRepaymentPlanService
                .list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().in(LoanManageRepaymentPlan::getIouNo, iouNOList)
                        .orderByAsc(LoanManageRepaymentPlan::getPeriod));
    }

    @Override
    public LoanInfoDTO getLoanInfoDTO(String iouNo) {
        return CollUtil.getFirst(listLoanInfoDTO(Collections.singletonList(iouNo)));
    }

    @Override
    public LoanInfoDTO getLoanInfoDTOByFinanceNo(String financeNo) {
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financeNo);
        return getLoanInfoDTO(loanManageIou.getIouNo());
    }

    @Override
    public LoanInfoDTO getLoanInfoDTO(String iouNo, Integer currentNode, List<Long> feeNode) {
        return null;
    }

    @Override
    public UnPaymentListVO selectUnRepaymentList(Long userId) {
        List<Long> userIds = Collections.singletonList(userId);
        //查询还在使用中状态的还款计划
        List<Integer> unFinishStatus = getUnfinishStatus();
        List<Integer> goodTypes = Arrays.asList(GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode(), GoodsEnum.ORDER_FINANCING.getCode());
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService
                .listRepaymentPlanByStatusAndUserId(userIds, goodTypes, unFinishStatus,
                        RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING);
        if (CollectionUtils.isEmpty(loanManageRepaymentPlans)) {
            return null;
        }
        //融资情况
        List<Long> financeIds = loanManageRepaymentPlans.stream()
                .map(LoanManageRepaymentPlan::getFinanceApplyId).distinct().collect(Collectors.toList());
        Map<Long, FinanceApply> financeApplyMap = financeApplyService.listByIds(financeIds).stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
        //产品信息
        List<Long> goodsIds = loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getGoodsId).distinct().collect(Collectors.toList());
        Map<Long, Product> productMap = productDirector.selectList(goodsIds).stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
        //查询还款情况

        List<RepaymentInfoDTO> repaymentInfoDTOS = getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans);
//        repaymentInfoDTOS = repaymentInfoDTOS.stream().filter(e -> e.getFinanceNo().equals("J43045327774687")).collect(Collectors.toList());
        //重新计算计算逾期费用
        // 获取当月最后一天
        LocalDate currentMonthLastDay = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //排除一下已经申请提前结清的还款计划 融资订单查询状态不是提前结清待支付和提前结清审核中的
        Set<Long> financeApplies = financeApplyMap.entrySet().stream()
                .filter(entry -> {
                    FinanceApply apply = entry.getValue();
                    return apply != null && !Arrays.asList(ADVANCE_SETTLED_EXAMINE.getCode(), ADVANCE_SETTLED_UN_PAY.getCode()).contains(apply.getStatus());
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        // 过滤掉还款时间不在当月的
        List<RepaymentInfoDTO> localDateRepaymentPlanList = StreamUtil.filter(repaymentInfoDTOS, loanManageRepaymentPlan -> loanManageRepaymentPlan.getRepaymentTime()
                .compareTo(currentMonthLastDay) <= 0);
        //移除列表中已经提前结清的还款计划
        List<RepaymentInfoDTO> filteredList = localDateRepaymentPlanList.stream()
                .filter(dto -> financeApplies.contains(dto.getFinanceApplyId()))
                .collect(Collectors.toList());
        //仅计算逾期利息 以及已生成的费用
//        localDateRepaymentPlanList = calOverDueOnlyRepaymentInfoDTO(localDateRepaymentPlanList);
        return UnPaymentListVO.builder()
                // 待还款列表
                .unPaymentList(getUnPaymentsListByRepaymentType(filteredList))
                // 提前还款列表
                .prepayments(getPrepaymentList(repaymentInfoDTOS, financeApplyMap))
                //展期列表
                .allowDelayList(geTallowDelayList(repaymentInfoDTOS, financeApplyMap, productMap))
                .build();
    }

    /**
     * 仅计算逾期利息
     *
     * @param localDateRepaymentPlanList
     * @return
     */
    private RepaymentInfoDTO calOverDueOnlyRepaymentInfoDTO(RepaymentInfoDTO localDateRepaymentPlanList) {
        if (ObjectUtil.isEmpty(localDateRepaymentPlanList)) {
            return localDateRepaymentPlanList;
        }
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .currentNode(ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode())
                .feeNode(Collections.singletonList(ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode())).build();
        List<RepaymentInfoDTO> repaymentInfoDTOS = repaymentReCalBizService.reCalRepaymentInfoDTO(build, Collections.singletonList(localDateRepaymentPlanList));
        return CollUtil.getFirst(repaymentInfoDTOS);
    }

    /**
     * 仅计算逾期利息
     *
     * @param localDateRepaymentPlanList
     * @return
     */
    private List<RepaymentInfoDTO> calOverDueOnlyRepaymentInfoDTO(List<RepaymentInfoDTO> localDateRepaymentPlanList) {
        if (CollUtil.isEmpty(localDateRepaymentPlanList)) {
            return localDateRepaymentPlanList;
        }
        LocalDate repaymentTime = LocalDate.now();
        List<RepaymentInfoDTO> collect = localDateRepaymentPlanList.stream()
                .filter(e -> e.hasOverDue(repaymentTime)).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return localDateRepaymentPlanList;
        }
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .useExpenseCal(true)
                .reCalGenFee(true)
                .currentNode(ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode())
                .feeNode(Collections.singletonList(ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode())).build();
        repaymentReCalBizService.reCalRepaymentInfoDTO(build, collect);
        return localDateRepaymentPlanList;
    }

    private static List<Integer> getUnfinishStatus() {
        List<Integer> unFinishStatus = Arrays.asList(RepaymentConstant.RepaymentPlanStatusEnum.CURRENT.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode(),
                RepaymentConstant.RepaymentPlanStatusEnum.UN_PAY.getCode());
        return unFinishStatus;
    }

    @Override
    public List<RepaymentTermVO> advanceRepaymentList(List<Long> repaymentPlanIdList, Long userId) {
        // 查询还款情况
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listByIds(repaymentPlanIdList);
//        loanManageRepaymentPlans = loanManageRepaymentPlans.stream().filter(e -> e.getFinanceNo().equals("J55077725154645")).collect(Collectors.toList());
        LocalDate now = LocalDate.now();
        //构建动态费用
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .reCalGenFee(true)
                .useExpenseCal(true)
                .feeNode(Collections.singletonList(ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode()))
                .currentNode(ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode()).build();
        List<RepaymentInfoDTO> repaymentInfoDTOS = getRepaymentInfoDTOS(now, loanManageRepaymentPlans, build);
        // 删除已经逾期的
        repaymentInfoDTOS.removeIf(RepaymentUtil::isOverdue);
        // 重新计算当前已经生成的信息


        // 从还款计划中获取产品id
        List<Long> goodsIdList = StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId);
        // 查询还款账户
        Map<Long, String> repaymentAccountSuffixMap = enterpriseQuotaService.getRepaymentAccountSuffixMap(goodsIdList, userId).getData();

        //Map<Long, RepaymentExpenseReq> repaymentExpenseReqMap = buildRepaymentExpenseReqList(loanManageRepaymentPlanMapper.selectBatchIds(repaymentPlanIdList));
        List<Long> financeIds = loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getFinanceApplyId).distinct().collect(Collectors.toList());
        List<FinanceApply> financeApplyList = financeApplyService.listByIds(financeIds);
        Map<Long, FinanceApply> financeApplyMap = financeApplyList.stream().collect(Collectors.toMap(BaseEntity::getId, finance -> finance));

        List<RepaymentTermVO> repaymentTermVOList = new ArrayList<>();
        for (RepaymentInfoDTO loanManageRepaymentPlan : repaymentInfoDTOS) {
            Map<Long, List<RepaymentFee>> repaymentFeeMap = loanManageRepaymentPlan.getRepaymentFeeList().stream()
                    .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus()))
                    .collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));
            //实还金额
//            List<LoanManageRepayment> repaymentList = SpringUtil.getBean(ILoanManageRepaymentService.class).findPayByIous(loanManageRepaymentPlan.getIouNo());
            List<LoanManageRepayment> repaymentList = loanManageRepaymentPlan.getValidRepaymentList().stream().filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus())).collect(Collectors.toList());
            List<LoanManageRepaymentVO> repaymentVOList = LoanManageRepaymentWrapper.build().listVO(repaymentList);
            repaymentVOList.forEach(repaymentVO -> {
                List<RepaymentFee> repaymentFees = repaymentFeeMap.get(repaymentVO.getId());
//                List<RepaymentFee> payRepaymentFeeList = repaymentFeeService.getPayRepaymentFeeList(repaymentVO.getId());
                if (ObjectUtil.isNotEmpty(repaymentFees)) {
                    repaymentVO.setRepaymentFeeList(RepaymentFeeWrapper.build().listVO(repaymentFees));
                }
            });
            BigDecimal actualPrincipal = repaymentList.stream().map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            LoanManageIou loanManageIou = loanManageRepaymentPlan.getLoanManageIou();
            String repaymentAccountSuffix = repaymentAccountSuffixMap.get(loanManageRepaymentPlan.getGoodsId());

            RepaymentTermVO repaymentTermVO = RepaymentTermVO.builder()
                    .financeNo(financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId()) == null ? null : financeApplyMap.get(loanManageRepaymentPlan.getFinanceApplyId()).getFinanceNo())
                    .loanAmount(loanManageIou.getIouAmount())
                    .loanTime(loanManageIou.getLoanTime())
                    .repaymentTime(loanManageRepaymentPlan.getRepaymentTime())
                    .repaymentAccount(repaymentAccountSuffix)
                    .id(loanManageRepaymentPlan.getId())
                    .shouldAmount(loanManageRepaymentPlan.getPrincipal())
                    .actualAmount(actualPrincipal)
                    .unPayAmount(loanManageRepaymentPlan.getSubPrincipal())
                    .repaymentList(repaymentVOList)
                    .build();
            repaymentTermVOList.add(repaymentTermVO);
        }
        CollectionUtil.sort(repaymentTermVOList, Comparator.comparing(RepaymentTermVO::getLoanTime));

        return listPayModeFinanceNo(repaymentTermVOList);
    }

    @Override
    public List<RepaymentTermVO> advanceSettleList(List<String> iouList) {
        //1.查询借据情况
        List<LoanInfoDTO> loanInfoDTOS = listLoanInfoDTO(iouList);
        if (CollUtil.isEmpty(loanInfoDTOS)) {
            return Collections.emptyList();
        }
        //获取还款情况
//        loanInfoDTOS = loanInfoDTOS.stream().filter(e -> e.getFinanceNo().equals("J99452036624141")).collect(Collectors.toList());
        //2.信息查询
        // 查询资方名称和还款账户后4位
        Map<Long, String> repaymentAccountSuffixMap = enterpriseQuotaService.getRepaymentAccountSuffixMap(StreamUtil.map(loanInfoDTOS, LoanInfoDTO::getGoodsId), loanInfoDTOS.get(0).getUserId()).getData();
        //融资订单查询 k:id v:融资信息
        Map<Long, FinanceApply> financeApplyMap = financeApplyService.listByIds(StreamUtil.map(loanInfoDTOS, LoanInfoDTO::getFinanceApplyId))
                .stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
        //3.待支付提前结清订单存在 进行回显订单,不存在进行计算 k:借据编号 v:提前结清订单
        List<String> financeNo = StreamUtil.map(loanInfoDTOS, LoanInfoDTO::getFinanceNo);
        List<AdvanceSettledApply> settledApplyList = advanceSettledApplyService.listAdvanceSettledApplyByFinanceNo(financeNo);
        Map<String, AdvanceSettledApply> settledApplyMap = settledApplyList.stream().collect(Collectors.toMap(AdvanceSettledApply::getIouNo, e -> e));
        //区分已存在结算订单和没有结算订单的数据
        Map<Boolean, List<LoanInfoDTO>> loanInfoDTOListMap = loanInfoDTOS.stream().collect(Collectors.partitioningBy(loanInfoDTO -> {
            FinanceApply financeApply = financeApplyMap.get(loanInfoDTO.getFinanceApplyId());
            return isExistSettleOrder(settledApplyMap, loanInfoDTO, financeApply);
        }));
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .feeNode(Collections.singletonList(ExpenseConstant.FeeNodeEnum.ADVANCE_SETTLE.getCode()))
                .reCalGenFee(false)
                .useExpenseCal(true)
                .collectFeedOnly(true)
                .currentNode(ExpenseConstant.FeeNodeEnum.ADVANCE_SETTLE.getCode())
                .build();
        //如果有需要进行计算的 进行费用计算 仅计算结清费用 其余的按计划剩余的收
        if (CollUtil.isNotEmpty(loanInfoDTOListMap.get(false))) {
            //进行重新计算赋值 更新当前整笔的费用
            repaymentReCalBizService.reCalLoanInfoDTO(loanInfoDTOListMap.get(false), build);
        }
        //赋值完后进行收集
        List<RepaymentTermVO> result = loanInfoDTOS.stream().map(loanInfoDTO -> {
            FinanceApply financeApply = financeApplyMap.get(loanInfoDTO.getFinanceApplyId());
            if (isExistSettleOrder(settledApplyMap, loanInfoDTO, financeApply)) {
                return buildSettleRepaymentTermVOByAdvanceSettledApply(settledApplyMap.get(loanInfoDTO.getIouNo()), loanInfoDTO,
                        financeApply, repaymentAccountSuffixMap.get(financeApply.getId()));
            } else {
                return buildSettleRepaymentTermVOByCal(loanInfoDTO,
                        financeApply, repaymentAccountSuffixMap.get(financeApply.getId()));
            }
        }).collect(Collectors.toList());
        //设置支付方式
        List<RepaymentTermVO> repaymentTermVOS = listPayModeFinanceNo(result);
        return repaymentTermVOS;
    }

    private static boolean isExistSettleOrder(Map<String, AdvanceSettledApply> settledApplyMap, LoanInfoDTO loanInfoDTO, FinanceApply financeApply) {
        // 获取订单状态（提前结清待支付）
        Integer advanceSettledUnPayCode = FinanceApplyStatusEnum.ADVANCE_SETTLED_UN_PAY.getCode();
        // 获取订单状态（提前结清）
        Integer advanceSettledCode = FinanceApplyStatusEnum.ADVANCE_SETTLED.getCode();
        // 状态
        Integer status = financeApply.getStatus();
        // 提前结清待支付/提前结清 的状态均为已结算订单
        return settledApplyMap.containsKey(loanInfoDTO.getIouNo()) && (advanceSettledUnPayCode.equals(status) || (advanceSettledCode.equals(status)));
    }

    /**
     * 提前结清试算
     *
     * @param loanInfoDTO  申请信息
     * @param financeApply 融资信息
     * @return
     */
    private RepaymentTermVO buildSettleRepaymentTermVOByCal(LoanInfoDTO loanInfoDTO,
                                                            FinanceApply financeApply, String accountPre) {
        AddAmountVO addAmountVO = new AddAmountVO();
        //拿出最近的一条还款计划
        List<RepaymentInfoDTO> usingRepaymentPlan = loanInfoDTO.listUsingRepaymentInfo();
        List<RepaymentPlanFee> repaymentPlanFeeList = loanInfoDTO.getRepaymentPlanFee();
        RepaymentInfoDTO recentRepaymentPlan = CollUtil.getFirst(usingRepaymentPlan);

        RepaymentExpenseResp repaymentExpenseResp = new RepaymentExpenseResp();
        //没有需要缴费的费用 时 利息取最近的一条还款计划的利息即可
        repaymentExpenseResp.setShouldInterest(recentRepaymentPlan.getShouldInterest());
        repaymentExpenseResp.setSurplusPrincipal(loanInfoDTO.getSubPrincipal());
        BigDecimal advanceSettleServiceFee = repaymentPlanFinanceApplyBizService.getAmountTotalByRepaymentPlanFee(repaymentPlanFeeList, ExpenseConstant.FeeNodeEnum.ADVANCE_SETTLE.getCode());
        repaymentExpenseResp.setAdvanceSettleServiceFee(advanceSettleServiceFee);
        repaymentExpenseResp.setInterestAccrualDay(BigDecimal.valueOf(loanInfoDTO.getInterestDay()));
        repaymentExpenseResp.setPrepaymentDay(BigDecimal.valueOf(loanInfoDTO.getPrepaymentDays()));
        //加入剩余动态费用
        addAmountVO.setRepaymentPlanFeeList(repaymentPlanFeeList);
        return RepaymentTermVO.builder()
                .ids(Func.join(StreamUtil.map(usingRepaymentPlan, LoanManageRepaymentPlan::getId)))
                .loanTime(loanInfoDTO.getLoanTime())
                //剩余本金+剩余费用+本次提前结清费用
                .shouldAmount(loanInfoDTO.getSubPrincipal()
                        .add(loanInfoDTO.getSubExInterest())
                        .add(recentRepaymentPlan.getShouldInterest())
//                        .add(loanInfoDTO.getSubOtherFee())
                        .add(repaymentExpenseResp.getAdvanceSettleServiceFee()))
                .loanAmount(loanInfoDTO.getIouAmount())
                .repaymentAccount(accountPre)
                .iouNo(loanInfoDTO.getIouNo())
                .financeNo(financeApply.getFinanceNo())
                .repaymentExpenseResp(repaymentExpenseResp)
                .status(financeApply.getStatus())
                .addAmountVO(addAmountVO)
                .build();
    }

    private RepaymentTermVO buildSettleRepaymentTermVOByAdvanceSettledApply(AdvanceSettledApply advanceSettledApply,
                                                                            LoanInfoDTO loanInfoDTO, FinanceApply financeApply, String accountPre) {
        List<RepaymentInfoDTO> repaymentInfoDTOS = loanInfoDTO.listUsingRepaymentInfo();
        AddAmountVO settledDetailsVo = new AddAmountVO();
        RepaymentExpenseResp repaymentExpenseResp = new RepaymentExpenseResp();
        settledDetailsVo.setNewInterest(advanceSettledApply.getInterest());
        settledDetailsVo.setSurplusPrincipal(advanceSettledApply.getSurplusPrincipal());
        settledDetailsVo.setNewServiceCharge(advanceSettledApply.getPrepaymentServiceFee());
        //更新费用计算器的费用 =剩余动态费用+ 审批后利息 +剩余本金
        repaymentExpenseResp.setTotalAmount(advanceSettledApply.getInterest().add(advanceSettledApply.getSurplusPrincipal()));
        repaymentExpenseResp.setShouldInterest(advanceSettledApply.getInterest());
        repaymentExpenseResp.setAdvanceSettleServiceFee(advanceSettledApply.getPrepaymentServiceFee());
        settledDetailsVo.setAdvanceSettledDays(advanceSettledApply.getPrepaymentDay() + "天");
        //剩余还款费用
        if (ObjectUtil.isNotEmpty(advanceSettledApply.getPlanFee())) {
            List<RepaymentPlanFee> list = JSONUtil.toList(advanceSettledApply.getPlanFee(), RepaymentPlanFee.class);
            settledDetailsVo.setRepaymentPlanFeeList(list);
//            BigDecimal reduce = list.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //当手动输入了提前结清金额则使用手输的值
            BigDecimal reduce = list.stream().map(fee -> {
                if (fee.getFix() != null && CommonConstant.YES.equals(fee.getFix()) && fee.getFixAmount() != null) {
                    return fee.getFixAmount();
                } else {
                    return fee.getAmount();
                }
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            repaymentExpenseResp.setTotalAmount(repaymentExpenseResp.getTotalAmount().add(reduce));
        }

        if (ObjectUtil.isNotEmpty(advanceSettledApply.getPayDeadLine())) {
            settledDetailsVo.setPayDeadLineStr(LocalDateTimeUtil.format(advanceSettledApply.getPayDeadLine(), "yyyy-MM-dd HH:mm:ss"));
        } else {
            settledDetailsVo.setPayDeadLineStr("-");
        }
        return RepaymentTermVO.builder()
                .ids(Func.join(StreamUtil.map(repaymentInfoDTOS, LoanManageRepaymentPlan::getId)))
                .loanTime(loanInfoDTO.getLoanTime())
                // 审批的应还金额
                .shouldAmount(repaymentExpenseResp.getTotalAmount())
                .loanAmount(loanInfoDTO.getIouAmount())
                .repaymentAccount(accountPre)
                .iouNo(loanInfoDTO.getIouNo())
                .financeNo(financeApply.getFinanceNo())
                .repaymentExpenseResp(repaymentExpenseResp)
                .status(financeApply.getStatus())
                .addAmountVO(settledDetailsVo)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyAdvanceSettled(AdvanceSettledApplyDTO advanceSettledApplyDTO) {
        String iouNo = advanceSettledApplyDTO.getIouNo();
        //目前借据详情
        LoanManageIou loanManageIou = loanManageIouService.getByIouNo(iouNo);
        Assert.isTrue(ObjectUtil.isNotEmpty(loanManageIou), "未查询到放款记录");
        LoanManageIouVO loanManageIouVO = LoanManageIouWrapper.build().entityVO(loanManageIou);
        //融资单
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, loanManageIou.getFinanceNo()));
        //展期中 调息中 提前结清中不能发起提前结清
        isNotApply(financeApply);

        List<String> iouList = Func.toStrList(iouNo);
        List<RepaymentTermVO> repaymentTermVOS = advanceSettleList(iouList);
        RepaymentTermVO repaymentTermVO = repaymentTermVOS.get(0);

        //提前结清费用
        RepaymentExpenseResp repaymentExpenseResp = repaymentTermVO.getRepaymentExpenseResp();
        BigDecimal prepaymentServiceFee = repaymentExpenseResp.getAdvanceSettleServiceFee();
        prepaymentServiceFee = ObjectUtil.isNotEmpty(prepaymentServiceFee) ? prepaymentServiceFee : BigDecimal.ZERO;

        //剩余本金
        BigDecimal surplusPrincipal = repaymentExpenseResp.getSurplusPrincipal();
        BigDecimal interest = repaymentExpenseResp.getShouldInterest();
        //应还总额
        BigDecimal totalPrepaymentFee = repaymentTermVO.getShouldAmount();
        loanManageIouVO.setTotalByRepaymentFee(totalPrepaymentFee);

        AdvanceSettledApply advance = advanceSettledApplyMapper.selectOne(Wrappers.<AdvanceSettledApply>lambdaQuery().eq(AdvanceSettledApply::getIouNo, iouNo));
        HashMap<String, Object> variables = MapUtil.newHashMap();
        //封装dto
        AdvanceSettledApplyDTO dto = new AdvanceSettledApplyDTO();
        dto.setRepaymentPlanFees(repaymentTermVO.getAddAmountVO().getRepaymentPlanFeeList());
        dto.setIouId(loanManageIouVO.getId());
        dto.setLoanManageIouVO(loanManageIouVO);
        dto.setRepaymentExpenseResp(repaymentExpenseResp);
        dto.setReason(advanceSettledApplyDTO.getReason());
        dto.setPrepaymentServiceFee(prepaymentServiceFee);
        dto.setInterest(interest);
        dto.setTotalAmount(totalPrepaymentFee);
        dto.setSurplusPrincipal(surplusPrincipal);
        variables.put(WfProcessConstant.PROCESS_NO, loanManageIouVO.getIouNo());
        variables.put(WfProcessConstant.ADVANCE_SETTLED, dto);

        //用户信息
        User user = UserUtils.getUserById(financeApply.getUserId());
        String name = "";
        if (ObjectUtil.isNotEmpty(user)) {
            name = user.getName();
        }
        //提前还款时间
        BigDecimal prepaymentDay = repaymentExpenseResp.getPrepaymentDay();

        if (advance != null) {
            advance.setUserId(financeApply.getUserId());
            advance.setFinanceUser(name);
            advance.setStatus(AdvanceSettledEnum.APPLYING.getCode());
            advance.setTotalAmount(totalPrepaymentFee);
            advance.setSurplusPrincipal(surplusPrincipal);
            advance.setPrepaymentServiceFee(prepaymentServiceFee);
            advance.setInterest(interest);
            advance.setPrepaymentDay(prepaymentDay.intValue());
            advance.setReason(advanceSettledApplyDTO.getReason());
            String processInstanceId = businessProcessProductService.startProcess(loanManageIou.getGoodsId(), ProcessTypeEnum.RECEIVE_ADVANCE_SETTLED_APPLY, variables);
            advance.setProcessInstanceId(processInstanceId);
            advance.setPassTime(null);
            advance.setStatus(AdvanceSettledEnum.APPLYING.getCode());
            advance.setRepaymentNo("");
            advance.setPayDeadLine(null);
            LocalDateTime passTime = null;
            LocalDateTime payDeadLine = null;
            advanceSettledApplyMapper.updatePassTimeAndPayDeadLineTime(advance.getId(), passTime, payDeadLine);
            advanceSettledApplyService.updateById(advance);
        } else {

            dto.setUserId(financeApply.getUserId());
            dto.setFinanceUser(name);
            dto.setFinanceNo(loanManageIouVO.getFinanceNo());
            dto.setIouNo(iouNo);
            dto.setTotalAmount(totalPrepaymentFee);
            dto.setPrepaymentServiceFee(prepaymentServiceFee);
            dto.setPrepaymentDay(prepaymentDay.intValue());
            dto.setRepaymentMode(financeApply.getRepaymentMode());
            dto.setReason(advanceSettledApplyDTO.getReason());
            String processInstanceId = businessProcessProductService.startProcess(loanManageIouVO.getGoodsId(), ProcessTypeEnum.RECEIVE_ADVANCE_SETTLED_APPLY, variables);
            dto.setProcessInstanceId(processInstanceId);
            dto.setStatus(AdvanceSettledEnum.APPLYING.getCode());

            //保存
            advanceSettledApplyService.save(dto);
            businessProcessProgressService.saveBusinessProcessProgress(financeApply.getId(), 1, ProcessTypeEnum.RECEIVE_ADVANCE_SETTLED_APPLY.getCode(), dto.getProcessInstanceId());
        }
        //更新融资单的状态
        financeApply.setStatus(ADVANCE_SETTLED_EXAMINE.getCode());
        financeApplyMapper.updateById(financeApply);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepaymentOrderDTO advanceSettleRepayment(List<String> iouList, Integer payMode, String onlinePayRepaymentCode) {
        String iou = iouList.get(0);
        LoanInfoDTO loanInfoDTO = getLoanInfoDTO(iou);
        // 根据借据单查询还款计划
        List<RepaymentInfoDTO> repaymentInfoDTOS = loanInfoDTO.listUsingRepaymentInfo();
        if (CollectionUtils.isEmpty(repaymentInfoDTOS)) {
            return null;
        }
        //最近的还款计划
        RepaymentInfoDTO recentRepaymentPlan = CollUtil.getFirst(repaymentInfoDTOS);
        //获取提前结清记录
        AdvanceSettledApply settle = advanceSettledApplyService.getOne(Wrappers.<AdvanceSettledApply>lambdaQuery().eq(AdvanceSettledApply::getIouNo, iou));
        Assert.isTrue(settle.getStatus().equals(AdvanceSettledEnum.PASSED.getCode()), "当前状态不支持提前结清支付");

        // 根据还款计划id查询还款记录，
        List<LoanManageRepayment> repaymentList = recentRepaymentPlan.getValidRepaymentList();
        if (CollUtil.isNotEmpty(repaymentList)) {
            isPaying(repaymentList);
        }
        //生成借据还款记录
        return saveAdvanceSettledRepayment(settle, loanInfoDTO, payMode, onlinePayRepaymentCode);
    }

    /**
     * 保存提前结清还款记录
     *
     * @param loanInfoDTO            借据情况
     * @param settle                 提起结清记录
     * @param onlinePayRepaymentCode 计算费用map
     * @return
     */
    private RepaymentOrderDTO saveAdvanceSettledRepayment(AdvanceSettledApply settle, LoanInfoDTO loanInfoDTO,
                                                          Integer payMode, String onlinePayRepaymentCode) {
        //最近的还款计划
        RepaymentInfoDTO recentRepaymentPlan = CollUtil.getFirst(loanInfoDTO.listUsingRepaymentInfo());
        List<RepaymentPlanFee> list = StringUtil.isNotBlank(settle.getPlanFee()) ? JSONUtil.toList(settle.getPlanFee(), RepaymentPlanFee.class) : Collections.emptyList();
        //生成动态费用
        RepaymentCreateDTO build = RepaymentCreateDTO.builder().id(recentRepaymentPlan.getId())
                .interest(settle.getInterest())
                .penaltyInterest(BigDecimal.ZERO)
                .serverFee(settle.getPrepaymentServiceFee())
                .onlinePayCode(onlinePayRepaymentCode)
                .repaymentPlanFeeList(list)
                .payMode(payMode)
                .amount(loanInfoDTO.getSubPrincipal())
                .repaymentType(RepaymentConstant.RepaymentTypeEnum.ADVANCE_SETTLE.getCode()).build();
        return createSettleRepayment(build, recentRepaymentPlan, loanInfoDTO);
    }

    private void isNotApply(FinanceApply financeApply) {
        //List<Integer> statusList = Func.toIntList("9,10,11,12,13,14,15,17,19,20,22,24,26,27");
        List<Integer> list = Collections.singletonList(FinanceApplyStatusEnum.UN_SETTLED.getCode());
        com.baomidou.mybatisplus.core.toolkit.Assert.isTrue(list.contains(financeApply.getStatus()), "当前状态不支持提前结清");
    }

    @Override
    public PreRepaymentVO advanceRepaymentExpenseByRule(PrepaymentDTO prepaymentDTO) {
        //获取还款情况
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(prepaymentDTO.getId());

        //只有一期
        RepaymentInfoDTO repaymentInfoDTO = getPreRepaymentInfo(loanManageRepaymentPlan, prepaymentDTO.getAmount());
        R<String> repaymentAccountSuffix = enterpriseQuotaService.getRepaymentAccountSuffix(loanManageRepaymentPlan.getGoodsId(),
                AuthUtil.getUserId());
        return buildPreRepaymentVo(prepaymentDTO, repaymentInfoDTO, repaymentAccountSuffix);
    }

    private static PreRepaymentVO buildPreRepaymentVo(PrepaymentDTO prepaymentDTO, RepaymentInfoDTO repaymentInfoDTO, R<String> repaymentAccountSuffix) {
        PreRepaymentVO preRepaymentVO = PreRepaymentVO.builder()
                .interest(repaymentInfoDTO.getShouldInterest())
                .serviceCharge(BigDecimal.ZERO)
                .principal(prepaymentDTO.getAmount())
                //当期利息+还款金额+剩余的额外利息
                .totalAmount(repaymentInfoDTO.calUnPayTotal(prepaymentDTO.getAmount()))
                .repaymentAccount(Func.toStr(repaymentAccountSuffix.getData()))
                .repaymentPlanFeeList(repaymentInfoDTO.getRepaymentPlanFeeList())
                .build();
        return preRepaymentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepaymentOrderDTO advanceRepayment(PrepaymentDTO prepaymentDTO) {
        Long id = prepaymentDTO.getId();
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(id);
        if (ObjectUtil.isEmpty(loanManageRepaymentPlan)) {
            throw new ServiceException("该还款计划不存在");
        }
        //获取还款情况
        //只有一期
        RepaymentInfoDTO repaymentInfoDTOS = getPreRepaymentInfo(loanManageRepaymentPlan, prepaymentDTO.getAmount());
        R<String> repaymentAccountSuffix = enterpriseQuotaService.getRepaymentAccountSuffix(loanManageRepaymentPlan.getGoodsId(),
                AuthUtil.getUserId());
        buildPreRepaymentVo(prepaymentDTO, repaymentInfoDTOS, repaymentAccountSuffix);
        //创建还款订单
        RepaymentCreateDTO build = RepaymentCreateDTO.builder()
                .onlinePayCode(prepaymentDTO.getOnlinePayRepaymentCode())
                .amount(prepaymentDTO.getAmount())
                .payMode(prepaymentDTO.getPayMode())
                .repaymentPlanFeeList(repaymentInfoDTOS.getRepaymentPlanFeeList())
                .penaltyInterest(repaymentInfoDTOS.calSubPenaltyInterest())
                .serverFee(repaymentInfoDTOS.calSubServiceFee())
                .interest(repaymentInfoDTOS.getShouldInterest()).build();
        return createRepayment(build, loanManageRepaymentPlan, repaymentInfoDTOS);
    }

    /**
     * 是否可以进行还款操作
     *
     * @param financeApply
     * @return
     */
    private Boolean canRepay(FinanceApply financeApply) {
        return !FinanceApplyStatusEnum.OVERDUE_CONSULT_APPLYING.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.OVERDUE_CONSULT_REJECT.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.DELAY_APPLY_EXAMINE.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.DELAY_APPLY_REJECT.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.DELAY_WAIT_CONFIRM.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.DELAY_CONFIRM_EXAMINE.getCode().equals(financeApply.getStatus())
                && !FinanceApplyStatusEnum.DELAY_CONFIRM_REJECT.getCode().equals(financeApply.getStatus())
                && !ADVANCE_SETTLED_EXAMINE.getCode().equals(financeApply.getStatus())
                && !ADVANCE_SETTLED_UN_PAY.getCode().equals(financeApply.getStatus());
    }

    /**
     * 展期列表
     * 符合条件 1.融资订单带结清
     * 2.在产品设置的展期范围内
     * 3.产品允许展期
     * 4.融资订单从未展期过 也就是oldFinanceNo==null
     * 5.融资待结清
     *
     * @param repaymentInfoList 还款计划
     * @param financeApplyMap   融资订单
     * @param productMap        产品信息
     * @return
     */
    private List<String> geTallowDelayList(List<RepaymentInfoDTO> repaymentInfoList, Map<Long, FinanceApply> financeApplyMap, Map<Long, Product> productMap) {
        if (CollUtil.isEmpty(repaymentInfoList)) {
            return Collections.emptyList();
        }

        return repaymentInfoList.stream().filter(e -> {
            Product product = productMap.get(e.getGoodsId());
            if (product == null) {
                return false;
            }
            long day = e.getRepaymentTime().toEpochDay() - LocalDate.now().toEpochDay();
            if (GoodsEnum.NO_DELAY.getCode().equals(product.getIsDelay())) {
                return false;
            }
            boolean canDelayRange = day >= product.getDelayBeforeDayMin() && day <= product.getDelayBeforeDayMax();
            FinanceApply financeApply = financeApplyMap.get(e.getFinanceApplyId());
            return CommonConstant.YES.equals(financeApply.getIsDelay())
                    && StringUtil.isBlank(financeApply.getOldFinanceNo())
                    && FinanceApplyStatusEnum.UN_SETTLED.getCode().equals(financeApply.getStatus()) && canDelayRange;
        }).map(RepaymentInfoDTO::getIouNo).distinct().collect(Collectors.toList());

    }

    /**
     * 提前还款列表
     *
     * @param repaymentInfoList 还款计划
     * @param financeApplyMap   融资信息
     * @return
     */
    private List<PrepaymentVO> getPrepaymentList(List<RepaymentInfoDTO> repaymentInfoList, Map<Long, FinanceApply> financeApplyMap) {
        // 留下支持提前还款并且未逾期，不是当期还的还款计划，根据还款类型分组
        Map<Integer, List<LoanManageRepaymentPlan>> map = repaymentInfoList.stream()
                .filter(loanManageRepaymentPlan ->
                        loanManageRepaymentPlan.getRepaymentTime().isAfter(LocalDate.now())
                                && !loanManageRepaymentPlan.getPrepaymentType().equals(GoodsEnum.NO_SUPPORT.getCode()))
                .collect(Collectors.groupingBy(LoanManageRepaymentPlan::getPrepaymentType));
        //排除待结清和提前结清以外的状态
        List<LoanManageRepaymentPlan> plans = map.get(GoodsEnum.Full_REPAYMENT.getCode());
        List<Integer> list = Arrays.asList(FinanceApplyStatusEnum.UN_SETTLED.getCode()
                , ADVANCE_SETTLED_UN_PAY.getCode()
                , ADVANCE_SETTLED_EXAMINE.getCode());
        List<LoanManageRepaymentPlan> planList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(plans)) {
            planList = plans.stream().filter(e -> {
                FinanceApply financeApply = financeApplyMap.get(e.getFinanceApplyId());
                if (ObjectUtil.isNotEmpty(financeApply)) {
                    return list.contains(financeApply.getStatus());
                }
                return false;
            }).collect(Collectors.toList());
        }
        map.put(GoodsEnum.Full_REPAYMENT.getCode(), planList);

        // 封装响应数据结构
        return map.entrySet().stream().map(entry -> {
            List<LoanManageRepaymentPlan> value = entry.getValue();
            return PrepaymentVO.builder()
                    .prepaymentType(entry.getKey())
                    .ids(Func.join(StreamUtil.map(value, LoanManageRepaymentPlan::getId)))
                    .num(StreamUtil.groupBy(value, LoanManageRepaymentPlan::getIouNo).size())
                    .iouNo(Func.join(StreamUtil.map(value, LoanManageRepaymentPlan::getIouNo)))
                    .build();
        }).collect(Collectors.toList());
    }

    private List<FrontRepaymentPlanVo> getUnPaymentsListByRepaymentType(List<RepaymentInfoDTO> repaymentInfoList) {
        //获取当天的日期
        LocalDate nowDate = LocalDate.now();
        Map<LocalDate, List<RepaymentInfoDTO>> repaymentPlanMap = StreamUtil.groupBy(repaymentInfoList, LoanManageRepaymentPlan::getRepaymentTime);

        List<FrontRepaymentPlanVo> unPaymentList = Lists.newArrayList();
        for (Map.Entry<LocalDate, List<RepaymentInfoDTO>> entry : repaymentPlanMap.entrySet()) {
            // 计算当前日期所有还款计划的还款总金额
            List<RepaymentInfoDTO> plans = entry.getValue();
            if (CollUtil.isEmpty(plans)) {
                continue;
            }
            //查询待还金额
            ArrayList<BigDecimal> amountList = new ArrayList<>();
            for (RepaymentInfoDTO plan : plans) {
                //剩余本金计算
                BigDecimal subPrincipal = plan.getSubPrincipal();
                //剩余动态费用计算
                BigDecimal subOtherFee = calSubOtherFee(plan.getRepaymentPlanFeeList());
                //待还=剩余本金+剩余服务费+剩余利息
                amountList.add(subPrincipal.add(subOtherFee).add(plan.getShouldInterest()));
            }
            BigDecimal totalAmount = amountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

            String ids = Func.join(StreamUtil.map(entry.getValue(), LoanManageRepaymentPlan::getId));
            //计算逾期天数
            LocalDate date = entry.getKey();
            long overdueDays = date.until(nowDate, ChronoUnit.DAYS);
            overdueDays = overdueDays > 0 ? overdueDays : 0;

            FrontRepaymentPlanVo repaymentPlanVo = FrontRepaymentPlanVo.builder()
                    .repaymentTime(date)
                    .overdue(date.isBefore(LocalDate.now()))
                    .shouldAmountTotal(totalAmount)
                    .ids(ids)
                    .overdueDays(overdueDays)
                    .build();
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                unPaymentList.add(repaymentPlanVo);
            }
        }
        //时间顺序排序
        Collections.sort(unPaymentList, Comparator.comparing(FrontRepaymentPlanVo::getRepaymentTime, (t1, t2) -> t1.compareTo(t2)));
        return unPaymentList;
    }

    private BigDecimal calSubOtherFee(List<RepaymentPlanFee> repaymentPlanFeeList) {
        BigDecimal planFee = repaymentPlanFeeList.stream().map(RepaymentPlanFee::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return planFee;
    }

    public List<RepaymentInfoDTO> get() {
        return null;
    }

    @Override
    public List<RepaymentInfoDTO> getRepaymentInfo(Long userId, List<Long> ids) {
        //获取还款情况
        return listRepaymentInfoByUserIdAndIds(Collections.singletonList(userId), ids);
    }

    /**
     * 提前还款
     *
     * @param loanManageRepaymentPlan
     * @param amount
     * @return
     */
    @Override
    public RepaymentInfoDTO getPreRepaymentInfo(LoanManageRepaymentPlan loanManageRepaymentPlan, BigDecimal amount) {
        RepaymentInfoDTO repaymentInfo = getRepaymentInfo(loanManageRepaymentPlan, amount);
        //构建动态费用
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .currentNode(ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode())
                .reCalGenFee(true)
                .feeNode(Arrays.asList(ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode(), ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode()))
                .useExpenseCal(true)
                .build();
        return repaymentReCalBizService.reCalRepaymentInfoDTO(build, repaymentInfo);
    }

    @Override
    public RepaymentInfoDTO getRepaymentInfo(LoanManageRepaymentPlan loanManageRepaymentPlan, BigDecimal amount) {
        //去除掉本次动态费用小于0.01的费用 下次收 或者不收
        BigDecimal min = new BigDecimal("0.01");
        //当期还款期数情况
        LocalDate repaymentTime = LocalDate.now();
        RepaymentInfoDTO repaymentInfoDTO = CollUtil.getFirst(getRepaymentInfoDTOS(repaymentTime, Collections.singletonList(loanManageRepaymentPlan), amount, null));
        //计算金额占比
        //当前利息需要拿还款本金去算
        LocalDate refundDay = repaymentTime.plusDays(repaymentInfoDTO.getInterestDay());
        BigDecimal shouldInterest = BorrowAndReturnUtils.calculatePrincipalAndInvest(amount, ProductExpenseOrderDetailUtils.percentToBigNum(repaymentInfoDTO.getAnnualInterestRate())
                , repaymentInfoDTO.getStartTime(), refundDay, 2, CommonConstant.NUMBER_STRATEGY);
        repaymentInfoDTO.setShouldInterest(min.compareTo(shouldInterest) <= 0 ? shouldInterest : BigDecimal.ZERO);
        return repaymentInfoDTO;
    }

    @Override
    public RepaymentOrderDTO createRepayment(RepaymentCreateDTO repaymentCreateDTO, RepaymentInfoDTO repaymentInfoDTO) {
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(repaymentCreateDTO.getId());
        if (ObjectUtil.isEmpty(loanManageRepaymentPlan)) {
            throw new ServiceException("该还款计划不存在");
        }
        return createRepayment(repaymentCreateDTO, loanManageRepaymentPlan, repaymentInfoDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepaymentOrderDTO createRepayment(RepaymentCreateDTO repaymentCreateDTO) {
        Long id = repaymentCreateDTO.getId();
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(id);
        if (ObjectUtil.isEmpty(loanManageRepaymentPlan)) {
            throw new ServiceException("该还款计划不存在");
        }
        RepaymentInfoDTO repaymentInfoDTO = CollUtil.getFirst(getRepaymentInfoDTOS(LocalDate.now(), Collections.singletonList(loanManageRepaymentPlan)));
        return createRepayment(repaymentCreateDTO, loanManageRepaymentPlan, repaymentInfoDTO);
    }

    private RepaymentOrderDTO createSettleRepayment(RepaymentCreateDTO repaymentCreateDTO, RepaymentInfoDTO loanManageRepaymentPlan
            , LoanInfoDTO loanInfoDTO) {
        if (loanInfoDTO.getSubPrincipal().compareTo(repaymentCreateDTO.getAmount()) < 0) {
            throw new ServiceException("还款本金超过待还本金");
        }
        // 保存还款计划
        RepaymentOrderDTO repaymentOrderDTO = saveRepayment(loanManageRepaymentPlan,
                repaymentCreateDTO, repaymentCreateDTO.getOnlinePayCode());
        return repaymentOrderDTO;
    }

    private RepaymentOrderDTO createRepayment(RepaymentCreateDTO repaymentCreateDTO, LoanManageRepaymentPlan loanManageRepaymentPlan
            , RepaymentInfoDTO repaymentInfoDTO) {
        if (repaymentInfoDTO.getSubPrincipal().compareTo(repaymentCreateDTO.getAmount()) < 0) {
            throw new ServiceException("还款本金超过待还本金");
        }
        Long financeApplyId = loanManageRepaymentPlan.getFinanceApplyId();
        FinanceApply financeApply = financeApplyService.getById(financeApplyId);
        if (ObjectUtil.isNotEmpty(financeApply) && !canRepay(financeApply)) {
            throw new ServiceException("当前节点不可进行还款");
        }
        // 根据还款计划id查询还款记录，如果存在支付中的状态，不能提交
        if (loanManageRepaymentService.hasPaying(repaymentInfoDTO.getRepaymentList())) {
            throw new ServiceException("已有一条还款还未处理完成，无法提交");
        }
        // 保存还款计划
        RepaymentOrderDTO repaymentOrderDTO = saveRepayment(repaymentInfoDTO,
                repaymentCreateDTO, repaymentCreateDTO.getOnlinePayCode());
        return repaymentOrderDTO;
    }

    /**
     * 创建本次还款订单
     *
     * @param loanManageRepaymentPlan 本次还款的计划
     * @param repaymentCreateDTO      还款创建参数
     * @param onlinePayRepaymentCode  线上还款编号
     */
    private RepaymentOrderDTO saveRepayment(RepaymentInfoDTO loanManageRepaymentPlan,
                                            RepaymentCreateDTO repaymentCreateDTO, String onlinePayRepaymentCode) {
        // 保存还款记录
        LoanManageRepayment loanManageRepayment = LoanManageRepayment.builder()
                .repaymentNo(CodeUtil.generateCode(CodeEnum.REPAYMENT_NO))
                .principal(repaymentCreateDTO.getAmount())
                .payMode(repaymentCreateDTO.getPayMode())
                .onlinePayRepaymentCode(onlinePayRepaymentCode)
                .capitalId(loanManageRepaymentPlan.getCapitalId())
                .actualAmount(BigDecimal.ZERO)
                .repaymentPlanId(loanManageRepaymentPlan.getId())
                .iouNo(loanManageRepaymentPlan.getIouNo())
                .goodsType(loanManageRepaymentPlan.getGoodsType())
                .period(loanManageRepaymentPlan.getPeriod())
                .goodsId(loanManageRepaymentPlan.getGoodsId())
                .userId(loanManageRepaymentPlan.getUserId())
                //还款类型计算 不填根据时间进行计算
                .repaymentType(ObjectUtil.isNotEmpty(repaymentCreateDTO.getRepaymentType()) ?
                        repaymentCreateDTO.getRepaymentType()
                        : calRepaymentType(loanManageRepaymentPlan, LocalDate.now()))
                .operatorUserId(loanManageRepaymentPlan.getUserId())
                .interest(repaymentCreateDTO.getInterest())
                .serviceCharge(ObjectUtil.isNotEmpty(repaymentCreateDTO.getServerFee()) ? repaymentCreateDTO.getServerFee() : BigDecimal.ZERO)
                .penaltyInterest(ObjectUtil.isNotEmpty(repaymentCreateDTO.getPenaltyInterest()) ? repaymentCreateDTO.getPenaltyInterest() : BigDecimal.ZERO)
                .actualPrincipal(BigDecimal.ZERO)
                .actualInterest(BigDecimal.ZERO)
                .actualServiceCharge(BigDecimal.ZERO)//无需记录
                .actualPenaltyInterest(BigDecimal.ZERO)//无需记录
                .build();
        loanManageRepayment.setStatus(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode());
        //使用减免利息
        BigDecimal reductionInterest = useReductionInterest(loanManageRepaymentPlan);
        loanManageRepayment.setReductionInterest(reductionInterest);
        loanManageRepaymentService.save(loanManageRepayment);
        Long repaymentId = loanManageRepayment.getId();
        //保存还款费用
        List<RepaymentFee> repaymentFees = loanManageRepaymentService.saveNewRepaymentFee(loanManageRepayment, repaymentCreateDTO.getRepaymentPlanFeeList());
        //使用减免费用
        useRepaymentFeeReductionAmount(repaymentFees);

        //保存还款计划与还款记录中间表
        loanManageRepaymentTermService.saveRepaymentTermList(repaymentId, loanManageRepayment.getIouNo(),
                Collections.singletonList(loanManageRepaymentPlan.getId()));
        //保存还款费用计划json
        if (CollUtil.isNotEmpty(repaymentCreateDTO.getRepaymentPlanFeeList())) {
            repaymentPlanFeeJsonService.saveRepaymentPlanFeeJson(repaymentId, JSONUtil.toJsonStr(repaymentCreateDTO.getRepaymentPlanFeeList()));
        }
        return RepaymentOrderDTO.builder().loanManageRepayment(loanManageRepayment).repaymentFeeList(repaymentFees)
                .repaymentPlanFeeList(repaymentCreateDTO.getRepaymentPlanFeeList()).build();
    }

    private BigDecimal useReductionInterest(RepaymentInfoDTO loanManageRepaymentPlan) {
        BigDecimal reductionAmount = loanManageRepaymentPlan.getReductionAmount();
        //本次需要扣除减免利息 需要将减免利息使用
        if (reductionAmount.compareTo(BigDecimal.ZERO) < 0) {
            IncomeDetail add = incomeDetailService.sub(reductionAmount, IncomeDetailConstant.REDUCTION_INTEREST_AMOUNT, loanManageRepaymentPlan.getId().toString(), "");
            incomeDetailService.saveAll(Collections.singletonList(add));
        }
        return reductionAmount;
    }

    /**
     * 回退减免费用
     *
     * @param loanManageRepayment 费用
     */
    private void cancelReductionInterest(LoanManageRepayment loanManageRepayment) {
        if (loanManageRepayment.getReductionInterest() != null && loanManageRepayment.getReductionInterest().compareTo(BigDecimal.ZERO) < 0) {
            IncomeDetail sub = incomeDetailService.add(loanManageRepayment.getUserId(), loanManageRepayment.getReductionInterest(),
                    IncomeDetailConstant.REDUCTION_INTEREST_AMOUNT, loanManageRepayment.getRepaymentPlanId().toString(), "");
            incomeDetailService.saveAll(Collections.singletonList(sub));
        }
    }

    /**
     * 使用减免费用
     *
     * @param repaymentFees 费用
     */
    private void useRepaymentFeeReductionAmount(List<RepaymentFee> repaymentFees) {
        if (CollUtil.isNotEmpty(repaymentFees)) {
            List<IncomeDetail> reductionAmountIncomeDetailList = repaymentFees.stream()
                    .filter(e -> e.getReductionAmount().compareTo(BigDecimal.ZERO) < 0)
                    .map(e -> incomeDetailService.sub(e.getUserId(), e.getReductionAmount(),
                            IncomeDetailConstant.REDUCTION_FEE_AMOUNT, e.getFeePlanId().toString(), "")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reductionAmountIncomeDetailList)) {
                incomeDetailService.saveAll(reductionAmountIncomeDetailList);
            }
        }
    }

    /**
     * 回退减免费用
     *
     * @param repaymentFees 费用
     */
    private void cancelRepaymentFeeReductionAmount(List<RepaymentFee> repaymentFees) {
        if (CollUtil.isNotEmpty(repaymentFees)) {
            List<IncomeDetail> reductionAmountIncomeDetailList = repaymentFees.stream()
                    .filter(e -> e.getReductionAmount().compareTo(BigDecimal.ZERO) < 0)
                    .map(e -> incomeDetailService.add(e.getUserId(), e.getReductionAmount(),
                            IncomeDetailConstant.REDUCTION_FEE_AMOUNT, e.getFeePlanId().toString(), "")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reductionAmountIncomeDetailList)) {
                incomeDetailService.saveAll(reductionAmountIncomeDetailList);
            }
        }
    }

    private Integer calRepaymentType(LoanManageRepaymentPlan loanManageRepaymentPlan, LocalDate repaymentTime) {
        int compare = loanManageRepaymentPlan.getRepaymentTime().compareTo(repaymentTime);
        if (compare < 0) {
            return RepaymentConstant.RepaymentTypeEnum.OVERDUE_DEDUCTION.getCode();
        }
        if (GoodsEnum.AMORTIZATION_LOAN.getCode().equals(loanManageRepaymentPlan.getRepaymentType())) {
            // 分期 还款时间等于计划时间=当期 还款时间小于计划时间=提前还款
            if (compare == 0) {
                return RepaymentConstant.RepaymentTypeEnum.CURRENT.getCode();
            } else {
                return RepaymentConstant.RepaymentTypeEnum.ADVANCE.getCode();
            }
        } else {
            // 随借随换 提前或当期都是正常还款
            return RepaymentConstant.RepaymentTypeEnum.CURRENT.getCode();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unPay(Long repaymentId) {
        //可操作校验
        if (!isPaying(repaymentId)) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        //设置费用为未支付
        repaymentFeeService.changeStatusByRepaymentId(repaymentId, RepaymentConstant.RepaymentStatusEnum.UN_PAY.getCode());
        //设置还款记录为未支付
        loanManageRepaymentService.changeStatus(Collections.singletonList(repaymentId), RepaymentConstant.RepaymentStatusEnum.UN_PAY.getCode());
        //取消动态费用减免
        List<RepaymentFee> repaymentFees = repaymentFeeService.listByRepaymentIds(Collections.singletonList(repaymentId));
        cancelRepaymentFeeReductionAmount(repaymentFees);
        //取消利息减免
        cancelReductionInterest(loanManageRepaymentService.getById(repaymentId));
        return true;
    }


    private boolean isPaying(Long repaymentId) {
        return loanManageRepaymentService.count(Wrappers.<LoanManageRepayment>lambdaQuery()
                .eq(LoanManageRepayment::getId, repaymentId)
                .eq(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.PAYING.getCode())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean paying(Long repaymentId) {
        //无需操作
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payedRepayment(LoanManageRepaymentDTO loanManageRepaymentDTO) {
        return payedRepayment(loanManageRepaymentDTO, loanManageRepaymentDTO.isSkipAmountValid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payedRepayment(LoanManageRepaymentDTO loanManageRepaymentDTO, Boolean skipAmountValid) {
        //可操作校验
        if (!isPaying(loanManageRepaymentDTO.getId())) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        // 查询还款情况
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(loanManageRepaymentDTO.getId());
        List<RepaymentFee> repaymentFeeList = repaymentFeeService.getRepaymentFeeByRepaymentIds(Collections.singletonList(loanManageRepayment.getId()));
        // 查询借据情况
        LoanInfoDTO loanInfoDTO = getLoanInfoDTO(loanManageRepayment.getIouNo());
        //本次使用的还款计划 暂时只针对还一笔还款计划的情况
        Long repaymentPlanId = loanManageRepayment.getRepaymentPlanId();

        RepaymentInfoDTO repaymentInfoDTO = loanInfoDTO.getRepaymentInfoList().stream()
                .filter(e -> e.getId().equals(repaymentPlanId)).findFirst().get();

        //校验还款金额
        if (skipAmountValid) {
            needFullAmount(loanManageRepaymentDTO, loanManageRepayment, repaymentFeeList);
        }
        // 设置还款水位
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(repaymentPlanId);
        OrderLevelServerService orderLevelServerService = orderLevelServerFactory.getServer(loanManageRepaymentPlan.getGoodsType());
        orderLevelServerService.repaymentOrderLevel(String.valueOf(loanManageRepaymentPlan.getFinanceApplyId()), loanManageRepayment.getPrincipal(), loanManageRepayment.getRepaymentNo());
        //设置支付方式
        loanManageRepaymentDTO.setPayMethod(loanManageRepaymentDTO.getPayMethod() == null ? BillPayNameEnum.OFFLINE_PAY.getStatus() : loanManageRepaymentDTO.getPayMethod());
        if (RepaymentConstant.RepaymentTypeEnum.ADVANCE_SETTLE.getCode() == loanManageRepayment.getRepaymentType()) {
            //支付提前结清订单
            advanceSettledRepaymentPayed(loanManageRepaymentDTO, loanManageRepayment,
                    repaymentFeeList, loanInfoDTO, repaymentPlanId, repaymentInfoDTO);
        } else {
            //支付正常还款订单
            normalRepaymentPayed(loanManageRepaymentDTO, loanManageRepayment, repaymentFeeList,
                    loanInfoDTO, repaymentPlanId, repaymentInfoDTO);
        }
        repaymentSuccessPublisher.pushRepaymentSuccess(loanManageRepaymentPlan.getFinanceApplyId(), loanManageRepayment);
        return true;
    }

    /**
     * 提前结清支付
     *
     * @param loanManageRepaymentDTO
     * @param loanManageRepayment
     * @param repaymentFeeList
     * @param loanInfoDTO
     * @param repaymentPlanId
     * @param repaymentInfoDTO
     */
    private void advanceSettledRepaymentPayed(LoanManageRepaymentDTO loanManageRepaymentDTO, LoanManageRepayment loanManageRepayment, List<RepaymentFee> repaymentFeeList, LoanInfoDTO loanInfoDTO, Long repaymentPlanId, RepaymentInfoDTO repaymentInfoDTO) {
        //还款类型 还款时间更新
        loanManageRepayment.setRepaymentTime(loanManageRepaymentDTO.getRepaymentTime());
        //更新实还金额及订单状态
        loanManageRepayment.setActualAmount(loanManageRepaymentDTO.getActualAmount());
        loanManageRepayment.setActualPrincipal(loanManageRepayment.getPrincipal());
        loanManageRepayment.setActualInterest(loanManageRepayment.getInterest());
        loanManageRepayment.setActualServiceCharge(loanManageRepayment.getServiceCharge());
        loanManageRepayment.setActualPenaltyInterest(loanManageRepayment.getPenaltyInterest());
        loanManageRepayment.setStatus(RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
        loanManageRepayment.setVoucher(loanManageRepaymentDTO.getVoucher());
        loanManageRepayment.setBank(loanManageRepaymentDTO.getBank());
        loanManageRepayment.setBankCardNo(loanManageRepayment.getBankCardNo());
        loanManageRepaymentService.updateById(loanManageRepayment);
        //根据动态费用生成本次的费用单 并进行支付 排除掉资方收取的 资方收取的费用需要在生成费用时 就创建一笔清分订单
        expenseOrderDetailPayed(repaymentFeeList, repaymentInfoDTO, loanManageRepayment, loanManageRepaymentDTO.getPayMethod(), loanManageRepaymentDTO.isSkipExpenseOrderGen(), loanInfoDTO.getFinanceNo());
        //支付后更新还款计划状态 及已还费用 剩余的还款计划都完结掉
//        loanInfoDTO.listUsingRepaymentInfo().forEach(e -> {
//            settleLoanRepaymentPlan(e, RepaymentConstant.RepaymentPlanStatusEnum.ADVANCE_SETTLE.getCode());
//        });
        // 提前结清合并当期于后期的还款计划, listUsingRepaymentInfo这里已经根据期数进行排序了
        this.settleLoanRepaymentPlan(loanInfoDTO.listUsingRepaymentInfo(), RepaymentConstant.RepaymentPlanStatusEnum.ADVANCE_SETTLE.getCode());

        //根据业务类型查找对应消息模板，根据模板发送消息
        MessageNotifyUtil.notifyByTemplate(loanManageRepayment.getUserId(), MessageSceneEnum.RECEIVABLE_Repayment_Suc.getValue());
        // 提前结清处理
        loanInfoDTO.setStatus(IouStatusEnum.ADVANCE_SETTLE.getCode());
        loanManageIouService.updateById(loanInfoDTO);
        doLoanManageIouSettleNotifyByIouStatus(IouStatusEnum.ADVANCE_SETTLE.getCode(), loanInfoDTO);
        //更新提前结清记录
        AdvanceSettledApply advanceSettledApply = advanceSettledApplyService.settledApplyByIouNo(loanInfoDTO.getIouNo());
        if (ObjectUtil.isNotEmpty(advanceSettledApply)) {
            advanceSettledApply.setStatus(AdvanceSettledEnum.ALREADY_PAID.getCode());
            advanceSettledApplyService.updateById(advanceSettledApply);
        }
//        //修改逾期记录
//        loanManageOverdueService.updateOverdueStatus(repaymentPlanId, OverdueConstant.OverdueStatus.PAY.getCode());
        //支付完成通知
        for (RepaymentNotifyHandler repaymentNotifyHandler : repaymentNotifyHandlerList) {
            repaymentNotifyHandler.settlePayed(loanManageRepayment, repaymentFeeList, loanInfoDTO);
        }
    }

    /**
     * 一期的还款订单支付
     *
     * @param loanManageRepaymentDTO
     * @param loanManageRepayment
     * @param repaymentFeeList
     * @param loanInfoDTO
     * @param repaymentPlanId
     * @param repaymentInfoDTO
     */
    private void normalRepaymentPayed(LoanManageRepaymentDTO loanManageRepaymentDTO,
                                      LoanManageRepayment loanManageRepayment, List<RepaymentFee> repaymentFeeList,
                                      LoanInfoDTO loanInfoDTO, Long repaymentPlanId, RepaymentInfoDTO repaymentInfoDTO) {
        //还款类型 还款时间更新
        loanManageRepayment.setRepaymentTime(loanManageRepaymentDTO.getRepaymentTime());

        loanManageRepayment.setRepaymentType(calRepaymentType(repaymentInfoDTO, loanManageRepaymentDTO.getRepaymentTime().toLocalDate()));
        //更新实还金额及订单状态
        loanManageRepayment.setActualAmount(loanManageRepaymentDTO.getActualAmount());
        loanManageRepayment.setActualPrincipal(loanManageRepayment.getPrincipal());
        loanManageRepayment.setActualInterest(loanManageRepayment.getInterest());
        loanManageRepayment.setActualServiceCharge(loanManageRepayment.getServiceCharge());
        loanManageRepayment.setActualPenaltyInterest(loanManageRepayment.getPenaltyInterest());
        loanManageRepayment.setStatus(RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
        loanManageRepayment.setVoucher(loanManageRepaymentDTO.getVoucher());
        loanManageRepayment.setBank(loanManageRepaymentDTO.getBank());
        loanManageRepayment.setBankCardNo(ObjectUtil.isNotEmpty(loanManageRepaymentDTO.getBankCardNo()) ? loanManageRepaymentDTO.getBankCardNo() : loanManageRepayment.getBankCardNo());
        loanManageRepaymentService.updateById(loanManageRepayment);

        // 更新还款费用状态
        repaymentFeeService.changeStatusByRepaymentId(loanManageRepayment.getId(), RepaymentConstant.RepaymentStatusEnum.PAY.getCode());
        //根据动态费用生成本次的费用单 并进行支付 排除掉资方收取的 资方收取的费用需要在生成费用时 就创建一笔清分订单
        expenseOrderDetailPayed(repaymentFeeList, repaymentInfoDTO, loanManageRepayment, loanManageRepaymentDTO.getPayMethod(), loanManageRepaymentDTO.isSkipExpenseOrderGen(), loanInfoDTO.getFinanceNo());
        //支付后更新还款计划状态 及已还费用
        payedUpdateRepaymentPlan(loanInfoDTO, loanManageRepayment, repaymentInfoDTO);
        //根据业务类型查找对应消息模板，根据模板发送消息
        MessageNotifyUtil.notifyByTemplate(loanManageRepayment.getUserId(), MessageSceneEnum.RECEIVABLE_Repayment_Suc.getValue());
        // 判断借据单是否结清
        boolean settle = iouIsSettleByPartRule(loanInfoDTO);
        if (settle) {
            //结清后更新借据单
            settleIouOrder(loanInfoDTO, loanManageRepayment);
        }
        //修改逾期记录
        loanManageOverdueService.updateOverdueStatus(repaymentPlanId, OverdueConstant.OverdueStatus.PAY.getCode());
        //支付完成通知
        for (RepaymentNotifyHandler repaymentNotifyHandler : repaymentNotifyHandlerList) {
            //还款后执行 这样写是因为额度回滚的问题
            if (settle) {
                //结清后执行
                repaymentNotifyHandler.settlePayed(loanManageRepayment, repaymentFeeList, loanInfoDTO);
            } else {
                //还款后执行 这样写是因为额度回滚的问题
                repaymentNotifyHandler.payed(loanManageRepayment, repaymentFeeList, loanInfoDTO, settle, hasOverDueExRepayment(loanInfoDTO, loanManageRepayment));
            }
        }
    }

    /**
     * 是否还有逾期的订单
     *
     * @param loanInfoDTO
     * @param loanManageRepayment
     * @return
     */
    private boolean hasOverDueExRepayment(LoanInfoDTO loanInfoDTO, LoanManageRepayment loanManageRepayment) {
        LocalDate now = LocalDate.now();
        return loanInfoDTO.listUsingRepaymentInfo().stream()
                .anyMatch(e -> !e.getId().equals(loanManageRepayment.getRepaymentPlanId()) && e.hasOverDue(now));
    }

    private void expenseOrderDetailPayed(List<RepaymentFee> repaymentFeeList, RepaymentInfoDTO repaymentInfoDTO, LoanManageRepayment loanManageRepayment, Integer payMethod, boolean skipExpenseOrderGen, String financeNo) {
        List<Long> allExistRepaymentPlanFeeList = repaymentInfoDTO.getAllRepaymentPlanFeeList()
                .stream().map(RepaymentPlanFee::getId).collect(Collectors.toList());
        //从费用计划json中查询出本次的还款计划费用json
        RepaymentPlanFeeJson repaymentPlanFeeJson = repaymentPlanFeeJsonService.getRepaymentPlanFeeJson(loanManageRepayment.getId());
        List<RepaymentPlanFee> repaymentPlanFeeList = ObjectUtil.isNotEmpty(repaymentPlanFeeJson) ? JSONUtil.toList(repaymentPlanFeeJson.getPlanFeeJson(), RepaymentPlanFee.class) :
                Collections.emptyList();
        Integer term = GoodsEnum.AMORTIZATION_LOAN.getCode().equals(repaymentInfoDTO.getRepaymentType()) ? repaymentInfoDTO.getPeriod() : 1;
        if (CollUtil.isEmpty(repaymentFeeList) || CollUtil.isEmpty(repaymentPlanFeeList)) {
            return;
        }
        //保存不存在的
        List<RepaymentPlanFee> repaymentPlanFees = repaymentPlanFeeList.stream().filter(e -> !allExistRepaymentPlanFeeList.contains(e.getId()))
                .map(e -> {
                    e.setPeriod(repaymentInfoDTO.getPeriod());
                    return e;
                })
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(repaymentPlanFees)) {
            repaymentPlanFeeService.saveBatch(repaymentPlanFees);
        }
        if (skipExpenseOrderGen) {
            return;
        }
        List<ExpenseOrderDetail> expenseOrderDetailList = repaymentPlanFeeList.stream().map(e -> {
            ExpenseOrderDetail expenseOrderDetail = JSONUtil.toBean(e.getExpenseOrderDetailStr(), ExpenseOrderDetail.class);
            expenseOrderDetail.setAmount(e.getAmount());
            expenseOrderDetail.setRepaymentTerm(term);
            expenseOrderDetail.setUserId(repaymentInfoDTO.getUserId());
            expenseOrderDetail.setId(null);
            expenseOrderDetail.setBillExpenseNo(null);
            return expenseOrderDetail;
        }).filter(e -> ObjectUtil.isNotEmpty(e) && GoodsEnum.ALONE.getCode().equals(e.getUnified())).collect(Collectors.toList());
        //根据已支付费用生成费用详情->
        if (CollUtil.isEmpty(expenseOrderDetailList)) {
            return;
        }
        //创建费用详情
        CreateExpenseOrderDetailDTO orderDetailDTO = CreateExpenseOrderDetailDTO.builder().expenseOrderDetailList(expenseOrderDetailList)
                .financeNo(repaymentInfoDTO.getFinanceNo()).build();
        List<ExpenseOrderDetail> expenseOrderDetail = productExpenseService.createExpenseOrderDetail(orderDetailDTO);
        //根据父级费用分组 创建费用订单并支付
        Map<String, List<ExpenseOrderDetail>> parenExpenseNameGroup = expenseOrderDetail.stream().collect(Collectors.groupingBy(ExpenseOrderDetail::getParenExpenseName));
        for (String parenExpenseName : parenExpenseNameGroup.keySet()) {
            List<ExpenseOrderDetail> expenseOrderDetails = parenExpenseNameGroup.get(parenExpenseName);
            //创建费用订单
            ExpenseCreateDTO expenseCreateDTO = ExpenseCreateDTO.builder()
                    .bizNo(financeNo)
                    .userId(repaymentInfoDTO.getUserId())
                    .userType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode())
                    .financeNo(financeNo)
                    .billExpenseNo(CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO))
                    .paymentMethod(payMethod).build();
            ExpenseOrder expenseOrder = productExpenseService.expenseOrderCreate(expenseOrderDetails, expenseCreateDTO);
            //支付费用订单 支付时间=还款订单的支付时间
            ExpenseOrderPayedDTO payedDTO = ExpenseOrderPayedDTO.builder()
                    .bank(loanManageRepayment.getBank())
                    .payOrderNo(loanManageRepayment.getOnlinePayRepaymentCode())
                    .account(loanManageRepayment.getBankCardNo())
                    .expenseOrder(expenseOrder)
                    .amount(expenseOrder.getCopeAmount())
                    .billExpenseNo(expenseOrder.getBillExpenseNo())
                    .payTime(loanManageRepayment.getRepaymentTime()).build();
            productExpenseService.expenseOrderPay(payedDTO);
        }
    }

    private Integer getSettleRepaymentPlanStatus(LocalDate repaymentTime, LocalDateTime settleTime) {
        int compare = repaymentTime.compareTo(settleTime.toLocalDate());
        if (compare == 0) {
            return RepaymentConstant.RepaymentPlanStatusEnum.NORMAL.getCode();
        } else if (compare < 0) {
            return RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE.getCode();
        } else {
            return RepaymentConstant.RepaymentPlanStatusEnum.ADVANCE_REPAYMENT.getCode();
        }
    }

    private void settleIouOrder(LoanInfoDTO loanManageIou, LoanManageRepayment loanManageRepayment) {
        int compare = loanManageIou.getExpireTime().compareTo(loanManageRepayment.getRepaymentTime().toLocalDate());
        if (compare == 0) {
            // 正常结清
            loanManageIou.setStatus(IouStatusEnum.NORMAL_SETTLE.getCode());
            loanManageIouService.updateById(loanManageIou);
            doLoanManageIouSettleNotifyByIouStatus(IouStatusEnum.NORMAL_SETTLE.getCode(), loanManageIou);
        } else if (compare < 0) {
            // 逾期结清
            loanManageIou.setStatus(IouStatusEnum.OVERDUE_SETTLE.getCode());
            loanManageIouService.updateById(loanManageIou);
            doLoanManageIouSettleNotifyByIouStatus(IouStatusEnum.OVERDUE_SETTLE.getCode(), loanManageIou);
        } else {
            // 提前结清
            loanManageIou.setStatus(IouStatusEnum.ADVANCE_SETTLE.getCode());
            loanManageIouService.updateById(loanManageIou);
            doLoanManageIouSettleNotifyByIouStatus(IouStatusEnum.ADVANCE_SETTLE.getCode(), loanManageIou);
            //更新提前结清记录
            AdvanceSettledApply advanceSettledApply = advanceSettledApplyService.settledApplyByIouNo(loanManageIou.getIouNo());
            if (ObjectUtil.isNotEmpty(advanceSettledApply)) {
                advanceSettledApply.setStatus(AdvanceSettledEnum.ALREADY_PAID.getCode());
                advanceSettledApplyService.updateById(advanceSettledApply);
            }
        }
    }

    private void doLoanManageIouSettleNotifyByIouStatus(Integer iouStatus, LoanInfoDTO loanManageIou) {
        for (LoanManageIouSettleNotifyHandler loanManageIouSettleNotifyHandler : loanManageIouSettleNotifyHandlerList) {
            if (loanManageIouSettleNotifyHandler.support().getCode().equals(loanManageIou.getGoodsType())) {
                if (IouStatusEnum.NORMAL_SETTLE.getCode() == iouStatus) {
                    loanManageIouSettleNotifyHandler.normalSettle(loanManageIou);
                    return;
                }
                if (IouStatusEnum.OVERDUE_SETTLE.getCode() == iouStatus) {
                    loanManageIouSettleNotifyHandler.overdueSettled(loanManageIou);
                    return;
                }
                if (IouStatusEnum.ADVANCE_SETTLE.getCode() == iouStatus) {
                    loanManageIouSettleNotifyHandler.advanceSettled(loanManageIou);
                    return;
                }
            }
        }
        throw new UnsupportedOperationException("还款完结策略未实现");
    }


    private void payedUpdateRepaymentPlan(LoanInfoDTO loanInfoDTO,
                                          LoanManageRepayment loanManageRepayment,
                                          RepaymentInfoDTO repaymentInfoDTO) {
        //查询未结清且未作废的还款计划
        List<RepaymentInfoDTO> loanManageRepaymentPlans = loanInfoDTO.getRepaymentInfoList()
                .stream().filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e.getRepaymentStatus()
                        && RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode() != e.getStatus()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(loanManageRepaymentPlans)) {
            //剩余本金等于0 则结清
            if (repaymentInfoDTO.getSubPrincipal().compareTo(BigDecimal.ZERO) <= 0) {
                Integer settleRepaymentPlanStatus = getSettleRepaymentPlanStatus(repaymentInfoDTO.getRepaymentTime(), loanManageRepayment.getRepaymentTime());
                settleLoanRepaymentPlan(repaymentInfoDTO, settleRepaymentPlanStatus);
            }
        }
    }

    /**
     * 完结还款计划
     *
     * @param loanManageRepaymentPlan
     */
    @Override
    public LoanManageRepaymentPlan settleLoanRepaymentPlan(LoanManageRepaymentPlan loanManageRepaymentPlan, Integer repaymentPlanStatus) {
        //设置完结状态
        loanManageRepaymentPlan.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
        //更新
        loanManageRepaymentPlanService.changeRepaymentStatus(Collections.singletonList(loanManageRepaymentPlan.getId()), RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode(), repaymentPlanStatus);
        //完结本期还款计划费用
        repaymentPlanFeeService.changeRepaymentStatusByRepaymentId(loanManageRepaymentPlan.getId(), RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
        return loanManageRepaymentPlan;
    }

    /**
     * 提前结清完结还款计划
     * @param repaymentInfoDTOS
     */
    @Override
    public void settleLoanRepaymentPlan(List<RepaymentInfoDTO> repaymentInfoDTOS, Integer repaymentPlanStatus) {
        // 找到最小一起的还款计划，其他的则为全部需要合并的数据
        List<RepaymentInfoDTO> needRemove = new ArrayList<>();
        List<RepaymentInfoDTO> sortedList = repaymentInfoDTOS.stream().filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e.getRepaymentStatus())
                .sorted(Comparator.comparing(LoanManageRepaymentPlan::getPeriod)).collect(Collectors.toList());
        RepaymentInfoDTO needUpdate = sortedList.get(0);
        for (RepaymentInfoDTO infoDTO : sortedList) {
            if (!needUpdate.getId().equals(infoDTO.getId())) {
                needRemove.add(infoDTO);
            }
        }
        needUpdate.setRepaymentStatus(RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
        needUpdate.setStatus(repaymentPlanStatus);
        for (RepaymentInfoDTO dto : needRemove) {
            if (ObjectUtil.isNotEmpty(needUpdate)) {
                needUpdate.setPrincipal(needUpdate.getPrincipal().add(dto.getPrincipal()));
                BigDecimal exInterest = Optional.ofNullable(needUpdate.getExInterest()).orElse(BigDecimal.ZERO);
                needUpdate.setExInterest(exInterest.add(Optional.ofNullable(dto.getExInterest()).orElse(BigDecimal.ZERO)));
                BigDecimal penaltyInterest = Optional.ofNullable(needUpdate.getPenaltyInterest()).orElse(BigDecimal.ZERO);
                needUpdate.setPenaltyInterest(penaltyInterest.add(Optional.ofNullable(dto.getPenaltyInterest()).orElse(BigDecimal.ZERO)));
                BigDecimal reductionInterest = Optional.ofNullable(needUpdate.getPenaltyInterest()).orElse(BigDecimal.ZERO);
                needUpdate.setReductionInterest(reductionInterest.add(Optional.ofNullable(dto.getReductionInterest()).orElse(BigDecimal.ZERO)));
            }
        }
        //设置完结状态
        //更新
        loanManageRepaymentPlanService.updateById(needUpdate);
        loanManageRepaymentPlanService.removeByIds(needRemove.stream().map(RepaymentInfoDTO::getId).collect(Collectors.toList()));
        //完结本期还款计划费用
        List<Long> planIds = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getId).collect(Collectors.toList());
        repaymentPlanFeeService.changeRepaymentStatusByRepaymentId(planIds, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.SETTLE.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appendRepaymentFee(List<ExpenseOrderDetail> expenseOrderDetails, LoanManageRepaymentPlan loanManageRepaymentPlan) {
//        //筛选出累计的
//        List<ExpenseOrderDetail> cumulativeTypeList = expenseOrderDetails.stream().filter(e -> ExpenseRuleFeeCalType.CUMULATIVE_TYPE.getCode().equals(e.getFeeCalType())).collect(Collectors.toList());
//        if (CollUtil.isEmpty(cumulativeTypeList)) {
//            return;
//        }
        List<RepaymentPlanFee> repaymentPlanFeeList = expenseOrderDetails.stream()
                .map(e -> repaymentPlanFinanceApplyBizService.buildRepaymentPlanFee(e.getAmount(), e, loanManageRepaymentPlan.getPeriod(), loanManageRepaymentPlan))
                .collect(Collectors.toList());
        repaymentPlanFeeService.saveBatch(repaymentPlanFeeList);
    }

    @Override
    public List<RepaymentTermVO> term(String repaymentIds, Long userId) {
        //处于使用中的还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .in(LoanManageRepaymentPlan::getId, Func.toLongList(repaymentIds))
                .eq(LoanManageRepaymentPlan::getRepaymentStatus, RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode()));

        List<RepaymentTermVO> repaymentTermVOList = selectTermByIdsPartRepayment(loanManageRepaymentPlans);
        List<RepaymentTermVO> repaymentTermVOS = listPayModeFinanceNo(repaymentTermVOList);
        return repaymentTermVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepaymentOrderDTO repaymentByRule(Long repaymentPlanId, Integer payMode, Long userId, Long operatorId, String onlineCode) {
        //获取还款情况
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(repaymentPlanId);
        // 查询还款计划列表
        if (ObjectUtil.isEmpty(loanManageRepaymentPlan)) {
            throw new ServiceException("还款计划不存在");
        }

        RepaymentInfoDTO repaymentInfoDTOS = getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlan);
        //逾期计算逾期费用
        if (RepaymentConstant.RepaymentPlanOverdueEnum.OVERDUE.getCode().equals(repaymentInfoDTOS.getOverdue())) {
            repaymentInfoDTOS = calOverDueOnlyRepaymentInfoDTO(repaymentInfoDTOS);
        }
        //判断是否有逾期
        boolean flag = false;
        UnPaymentListVO unPaymentListVO = selectUnRepaymentList(userId);
        List<FrontRepaymentPlanVo> unPaymentList = unPaymentListVO.getUnPaymentList();
        for (FrontRepaymentPlanVo frontRepaymentPlanVo : unPaymentList) {
            if (frontRepaymentPlanVo.getOverdueDays() > 0) {
                flag = true;
                break;
            }
        }
        //获取当前逾期的还款计划id集合
        List<Long> overDueIdList = new ArrayList<>();
        for (FrontRepaymentPlanVo frontRepaymentPlanVo : unPaymentList) {
            if (frontRepaymentPlanVo.getOverdueDays() > 0) {
                String ids = frontRepaymentPlanVo.getIds();
                List<Long> list = Func.toLongList(ids);
                overDueIdList.addAll(list);
            }
        }
        if (flag) {
            //逾期还款规则判断
            overDueRule(Collections.singletonList(repaymentPlanId), overDueIdList);
        }
        Long financeApplyId = loanManageRepaymentPlan.getFinanceApplyId();
        //取出融资list
//        List<FinanceApply> financeApplies = financeApplyMapper.selectById(financeApplyId);
        FinanceApply financeApply = financeApplyMapper.selectById(financeApplyId);
//        for (FinanceApply financeApply : financeApplies) {
        if (ObjectUtil.isNotEmpty(financeApply) && !canRepay(financeApply)) {
            throw new ServiceException(String.format("融资编号：%s处于不可还款节点", financeApply.getFinanceNo()));
        }
//        }
        // 根据还款计划id查询还款记录，
        List<LoanManageRepaymentTerm> loanManageRepaymentTerms = repaymentInfoDTOS.getLoanManageRepaymentTerms();
        // 如果不为空，说明提交的还款计划中已经有提交过还款的，需要排除
        if (!CollectionUtils.isEmpty(loanManageRepaymentTerms)) {
            List<LoanManageRepayment> loanManageRepayments = repaymentInfoDTOS.getValidRepaymentList();
            //校验还款记录是否在支付中
            isPaying(loanManageRepayments);
        }
        //创建还款订单
        LoanManageRepaymentPlan plan = loanManageRepaymentPlan;
        RepaymentCreateDTO build = RepaymentCreateDTO.builder()
                .amount(repaymentInfoDTOS.getSubPrincipal())
                .payMode(payMode)
                .repaymentPlanFeeList(repaymentInfoDTOS.getRepaymentPlanFeeList())
                .penaltyInterest(repaymentInfoDTOS.calSubPenaltyInterest())
                .serverFee(repaymentInfoDTOS.calSubServiceFee())
                .onlinePayCode(onlineCode)
                .interest(repaymentInfoDTOS.getShouldInterest()).build();
        RepaymentOrderDTO repayment = createRepayment(build, plan, repaymentInfoDTOS);
        // 返回还款记录id
//        return Func.join(repaymentsList.stream().map(BaseEntity::getId).collect(Collectors.toList()));
        return repayment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByRule(LoanManageRepaymentDTO loanManageRepaymentDTO) {
        Assert.isTrue(!isInvalid(loanManageRepaymentDTO.getStatus()), "已失效状态不能手动修改");
        Boolean status = null;
        // 支付成功
        if (RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return payedRepayment(loanManageRepaymentDTO);
        }
        // 支付失败
        if (RepaymentConstant.RepaymentStatusEnum.PAY_FAIL.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return failRepayment(loanManageRepaymentDTO.getId(), loanManageRepaymentDTO.getFailReason());
        }
        if (RepaymentConstant.RepaymentStatusEnum.UN_PAY.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return unPay(loanManageRepaymentDTO.getId());
        }
        if (RepaymentConstant.RepaymentStatusEnum.PAYING.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return paying(loanManageRepaymentDTO.getId());
        }
        if (RepaymentConstant.RepaymentStatusEnum.REPEAT.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return reCommitRepayment(loanManageRepaymentDTO.getId());
        }
        if (RepaymentConstant.RepaymentStatusEnum.CANCEL.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return cancelRepayment(loanManageRepaymentDTO.getId());
        }
        if (RepaymentConstant.RepaymentStatusEnum.INVALID.getCode().equals(loanManageRepaymentDTO.getStatus())) {
            return invalidRepayment(loanManageRepaymentDTO.getId());
        }
        return status;
    }

    @Override
    public List<RepaymentTermVO> getUnRepaymentByIouNo(String iouNos) {
        LoanInfoDTO loanInfoDTO = getLoanInfoDTO(iouNos);
        // 根据借据单号查询还款计划，按期数升序
        List<RepaymentInfoDTO> loanManageRepaymentPlans = loanInfoDTO.listUsingRepaymentInfo();

        if (CollectionUtils.isEmpty(loanManageRepaymentPlans)) {
            return Collections.emptyList();
        }
        // 查询资方名称和还款账户后4位
        Map<Long, String> repaymentAccountSuffixMap = enterpriseQuotaService
                .getRepaymentAccountSuffixMap(StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId), loanInfoDTO.getUserId()).getData();
        List<RepaymentTermVO> result = Lists.newArrayList();

        for (RepaymentInfoDTO repaymentInfoDTO : loanManageRepaymentPlans) {

            //待还还明细
            AddAmountVO addAmountVO = new AddAmountVO();
            addAmountVO.setUnPayAmount(repaymentInfoDTO.calSubTotal());
            addAmountVO.setNewInterest(repaymentInfoDTO.getShouldInterest());
            addAmountVO.setRepaymentPlanFeeList(repaymentInfoDTO.getRepaymentPlanFeeList());
            addAmountVO.setSurplusPrincipal(repaymentInfoDTO.getSubPrincipal());
            RepaymentTermVO repaymentTermVO = RepaymentTermVO.builder()
                    .iouNo(loanInfoDTO.getIouNo())
                    .financeNo(loanInfoDTO.getFinanceNo())
                    .repaymentType(repaymentInfoDTO.getRepaymentType())
                    .id(repaymentInfoDTO.getId())
                    .loanAmount(loanInfoDTO.getIouAmount())
                    .loanTime(loanInfoDTO.getLoanTime())
                    .repaymentTime(repaymentInfoDTO.getRepaymentTime())
                    .repaymentAccount(repaymentAccountSuffixMap.get(repaymentInfoDTO.getGoodsId()))
                    .shouldAmount(repaymentInfoDTO.getPrincipal())
                    .actualAmount(repaymentInfoDTO.getValidRepaymentList().stream().map(LoanManageRepayment::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .unPayAmount(addAmountVO.getUnPayAmount())
                    .addAmountVO(addAmountVO)
                    .goodsId(loanInfoDTO.getGoodsId())
                    .period(repaymentInfoDTO.getPeriod())
                    .build();
            result.add(repaymentTermVO);
        }
        return result;
    }

    @Override
    public List<ExpenseOrder> createExpenseOrderByRepaymentPlanFeeList(List<ExpenseOrderDetailFinanceVo> expenseOrderDetailFinanceVos, RepaymentOrderDTO repaymentOrderDTO, String financeNo, String bizNo, Integer type, Integer feeNode) {
        if (CollUtil.isEmpty(expenseOrderDetailFinanceVos)) {
            expenseOrderDetailFinanceVos = Collections.emptyList();
        }
        LoanManageRepayment loanManageRepayment = repaymentOrderDTO.getLoanManageRepayment();
        //产品信息
        Product product = productDirector.detailBase(loanManageRepayment.getGoodsId());
        //资方账户信息获取
        CapitalPayMethodAndCapitalType capitalPayModeByGoodsId = billBankCardaRelationService.getCapitalPayModeByGoodsId(product);
        //费用详情创建
        ExpenseCreateDTO build = ExpenseCreateDTO.builder()
                .payOrderNo(loanManageRepayment.getOnlinePayRepaymentCode())
                .repaymentId(loanManageRepayment.getId())
                .userId(loanManageRepayment.getUserId())
                .userType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode())
                .bizNo(bizNo)
                .financeNo(financeNo)
                .build();
        List<ExpenseCreateRespDTO> expenseCreateRespDTOS = manualOperationLoanService.pass(expenseOrderDetailFinanceVos, build, type, feeNode);
        //区分线上线下
        Map<Boolean, List<ExpenseCreateRespDTO>> onlineAndOtherMap = expenseCreateRespDTOS.stream()
                .collect(Collectors.partitioningBy(e -> PayModeEnum.PAY_MODE_ONLINE.getCode().equals(e.getExpenseOrderDetailList().get(0).getCostPayMode())));
        List<ExpenseCreateRespDTO> onlineExpenseOrder = onlineAndOtherMap.get(true);
        List<ExpenseCreateRespDTO> otherExpenseOrder = onlineAndOtherMap.get(false);
        BigDecimal repaymentAmount = loanManageRepayment.getPrincipal().add(loanManageRepayment.getInterest());
        if (PayModeEnum.PAY_MODE_ONLINE.getCode().equals(capitalPayModeByGoodsId.getPlatformCostPayMode())) {
            //资方费用为线上 线上费用存在 将利息和本金加到额外费用中 费用不存在 新增一条只有本金和利息的费用订单
            if (StringUtil.isBlank(capitalPayModeByGoodsId.getMerchantNo())) {
                throw new ServiceException("资方的商户号为空 ");
            }
            if (CollUtil.isEmpty(onlineExpenseOrder)) {
                ExpenseCreateOnlyDTO expenseCreateOnlyDTO = new ExpenseCreateOnlyDTO();
                expenseCreateOnlyDTO.setRepaymentId(loanManageRepayment.getId());
                expenseCreateOnlyDTO.setExAmountMerchantNo(capitalPayModeByGoodsId.getMerchantNo());
                expenseCreateOnlyDTO.setBillExpenseNo(CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO));
                expenseCreateOnlyDTO.setExAmount(repaymentAmount);
                expenseCreateOnlyDTO.setFinanceNo(financeNo);
                expenseCreateOnlyDTO.setBizNo(bizNo);
                expenseCreateOnlyDTO.setRepaymentId(loanManageRepayment.getId());
                expenseCreateOnlyDTO.setPlatName("资方费用");
                expenseCreateOnlyDTO.setGoodsId(product.getId());
                expenseCreateOnlyDTO.setUserId(loanManageRepayment.getUserId());
                expenseCreateOnlyDTO.setUserType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode());
                expenseCreateOnlyDTO.setPayMode(PayModeEnum.PAY_MODE_ONLINE.getCode());
                ExpenseCreateRespDTO expenseCreateRespDTO = productExpenseService.expenseOrderCreate(expenseCreateOnlyDTO);
                onlineExpenseOrder.add(expenseCreateRespDTO);
            } else {
                ExpenseCreateRespDTO expenseCreateRespDTO = CollUtil.getFirst(onlineExpenseOrder);
                expenseCreateRespDTO.setExAmountMerchantNo(capitalPayModeByGoodsId.getMerchantNo());
                productExpenseService.appendExAmountById(onlineExpenseOrder.get(0), repaymentAmount, capitalPayModeByGoodsId.getMerchantNo());
                expenseCreateRespDTO.setExAmount(repaymentAmount);
            }
        } else {
            Optional<ExpenseCreateRespDTO> first = otherExpenseOrder.stream().filter(e -> ExpenseTypeEnum.capital_expense.getCode()
                    .equals(e.getExpenseOrderDetailList().get(0).getExpenseType())).findFirst();
            //资方费用为线下 资方费用存在 将利息和本金加到额外费用中 费用不存在 新增一条只有本金和利息的费用订单
            if (!first.isPresent()) {
                ExpenseCreateOnlyDTO expenseCreateOnlyDTO = new ExpenseCreateOnlyDTO();
                expenseCreateOnlyDTO.setBillExpenseNo(CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO));
                expenseCreateOnlyDTO.setRepaymentId(loanManageRepayment.getId());
                expenseCreateOnlyDTO.setExAmountMerchantNo(capitalPayModeByGoodsId.getMerchantNo());
                expenseCreateOnlyDTO.setExAmount(repaymentAmount);
                expenseCreateOnlyDTO.setPaymentMethod(BillPayNameEnum.OFFLINE_PAY.getStatus());
                expenseCreateOnlyDTO.setRepaymentId(loanManageRepayment.getId());
                expenseCreateOnlyDTO.setFinanceNo(financeNo);
                expenseCreateOnlyDTO.setBizNo(bizNo);
                expenseCreateOnlyDTO.setPlatName("资方费用");
                expenseCreateOnlyDTO.setUserId(loanManageRepayment.getUserId());
                expenseCreateOnlyDTO.setUserType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode());
                expenseCreateOnlyDTO.setGoodsId(product.getId());
                expenseCreateOnlyDTO.setPayMode(PayModeEnum.PAY_MODE_BELOW.getCode());
                otherExpenseOrder.add(productExpenseService.expenseOrderCreate(expenseCreateOnlyDTO));
            } else {
                ExpenseCreateRespDTO expenseCreateRespDTO = first.get();
                expenseCreateRespDTO.setExAmountMerchantNo(capitalPayModeByGoodsId.getMerchantNo());
                productExpenseService.appendExAmountById(expenseCreateRespDTO, repaymentAmount, capitalPayModeByGoodsId.getMerchantNo());
                expenseCreateRespDTO.setExAmount(repaymentAmount);
            }
        }
        return Stream.concat(onlineExpenseOrder.stream(), otherExpenseOrder.stream()).collect(Collectors.toList());
    }

    /**
     * 费用缴费--->还款缴费
     *
     * @param expenseInfoExpenseVOList
     */
    private void buildOffLineRepaymentInfoExpense(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList, LoanManageRepayment loanManageRepayment) {

    }

    /**
     * 费用缴费--->还款缴费
     *
     * @param expenseInfoExpenseVOList
     */
    private void buildOnlineRepaymentInfoExpense(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList, LoanManageRepayment loanManageRepayment, Integer feeNode) {
        List<ExpenseInfoExpenseVO> collect = expenseInfoExpenseVOList.stream()
                .filter(e -> PayModeEnum.PAY_MODE_ONLINE.getCode().equals(e.getPayMode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            //不存在线上费用 为资方创建一条线上费用
            ExpenseInfoExpenseVO capitalExpenseInfo = buildOnlineExpenseInfoVO(loanManageRepayment, feeNode);
            expenseInfoExpenseVOList.add(capitalExpenseInfo);
        } else {
            //将本金利息加入到本次需要支付费用中
            ExpenseInfoExpenseVO expenseInfoExpenseVO = collect.get(0);
        }
        //填充已存在信息

    }

    private ExpenseInfoExpenseVO buildOnlineExpenseInfoVO(LoanManageRepayment loanManageRepayment, Integer feeNode) {
        ExpenseInfoExpenseVO expenseInfoExpenseVO = new ExpenseInfoExpenseVO();
        expenseInfoExpenseVO.setRepaymentId(loanManageRepayment.getRepaymentPlanId());
        return expenseInfoExpenseVO;
    }

    private boolean isInvalid(Integer status) {
        return RepaymentConstant.RepaymentStatusEnum.INVALID.getCode().equals(status);
    }

    /**
     * 校验还款记录是否在支付中
     *
     * @param loanManageRepayments
     */
    private void isPaying(List<LoanManageRepayment> loanManageRepayments) {
        for (LoanManageRepayment loanManageRepayment : loanManageRepayments) {
            if (loanManageRepayment.getStatus().equals(RepaymentConstant.RepaymentStatusEnum.PAYING.getCode())) {
                throw new ServiceException(String.format("本还款计划已有未支付订单，还款单号:%s，无法重复提交", loanManageRepayment.getRepaymentNo()));
            }
        }
    }

    /**
     * 逾期还款规则判断
     *
     * @param repaymentPlanIdList
     * @param overDueIdList
     */
    private void overDueRule(List<Long> repaymentPlanIdList, List<Long> overDueIdList) {
        //如果有逾期，需要先还逾期
        if (repaymentPlanIdList.size() == 1) {
            boolean contains = overDueIdList.contains(repaymentPlanIdList.get(0));
            if (!contains) {
                //throw new ServiceException("请先还逾期订单");
            }
        }

        //至少选中一个逾期订单
        if (repaymentPlanIdList.size() >= 2) {
            boolean contains = false;
            for (Long aLong : repaymentPlanIdList) {
                contains = overDueIdList.contains(aLong);
                if (contains) {
                    break;
                }
            }
            if (!contains) {
                throw new ServiceException("需要至少选中一个逾期订单");
            }
        }
    }

    public List<RepaymentTermVO> listPayModeFinanceNo(List<RepaymentTermVO> repaymentTermVOList) {
        //获取还款数据-融资编号
        List<String> financeNoList = repaymentTermVOList.parallelStream().map(RepaymentTermVO::getFinanceNo).collect(Collectors.toList());
        //根据融资编号集合，查询对应的产品---支付方式数据
        List<Map<String, String>> map = financeApplyMapper.selectPayModeFinanceNoMap(financeNoList);
        Map<String, Integer> genderCount = new HashMap<>();
        //判断value中是否不存在（不存在默认线下支付）
        for (Map<String, String> kv : map) {
            String key = null;
            Integer value = null;
            key = kv.get("key");
            if (kv.containsKey("value")) {
                value = Integer.parseInt(kv.get("value"));
            } else {
                value = 1;
            }
            genderCount.put(key, value);
        }
        for (RepaymentTermVO repaymentTermVO : repaymentTermVOList) {
            repaymentTermVO.setPayMode(Integer.parseInt(genderCount.get(repaymentTermVO.getFinanceNo()).toString()));
        }
        return repaymentTermVOList;
    }

    /**
     * 获取费用详情
     *
     * @param loanManageRepaymentPlan 还款计划
     * @param addAmountVO             承载数据实体
     * @return
     */
    public AddAmountVO getFeeDetailsByAddAmount(RepaymentInfoDTO loanManageRepaymentPlan,
                                                AddAmountVO addAmountVO) {
        BigDecimal unPayAmount = loanManageRepaymentPlan.calSubTotal();
        addAmountVO.setUnPayAmount(unPayAmount);
        //详细还款明细实体
        addAmountVO.setNewInterest(loanManageRepaymentPlan.getShouldInterest());
        addAmountVO.setNewPenaltyInterest(loanManageRepaymentPlan.calSubPenaltyInterest());
        addAmountVO.setNewServiceCharge(loanManageRepaymentPlan.calSubServiceFee());
        addAmountVO.setSurplusPrincipal(loanManageRepaymentPlan.getSubPrincipal());
        //查询还款计划关联费用 排除服务费 和 逾期费用 前端需要
        List<RepaymentPlanFee> repaymentFeeList = loanManageRepaymentPlan.getRepaymentPlanFeeList();
        addAmountVO.setRepaymentPlanFeeList(repaymentFeeList);
        return addAmountVO;
    }

    private List<RepaymentTermVO> selectTermByIdsPartRepayment(List<LoanManageRepaymentPlan> loanManageRepaymentPlans) {
        if (CollectionUtils.isEmpty(loanManageRepaymentPlans)) {
            return Collections.emptyList();
        }
        List<RepaymentInfoDTO> repaymentInfoDTOS = calOverDueOnlyRepaymentInfoDTO(getRepaymentInfoDTOS(LocalDate.now(), loanManageRepaymentPlans));

        List<String> financeNos = repaymentInfoDTOS.stream().map(RepaymentInfoDTO::getFinanceNo).distinct().collect(Collectors.toList());

        Map<String, LoanManageIou> loanManageIouMap = loanManageIouService.list(Wrappers.<LoanManageIou>lambdaQuery().in(LoanManageIou::getFinanceNo, financeNos))
                .stream()
                .collect(Collectors.toMap(LoanManageIou::getIouNo, e -> e));
        // 查询资方名称和还款账户后4位
        Map<Long, String> repaymentAccountSuffixMap = enterpriseQuotaService.getRepaymentAccountSuffixMap(StreamUtil.map(loanManageRepaymentPlans, LoanManageRepaymentPlan::getGoodsId), loanManageRepaymentPlans.get(0).getUserId()).getData();

        List<RepaymentTermVO> result = Lists.newArrayList();

        for (RepaymentInfoDTO loanManageRepaymentPlan : repaymentInfoDTOS) {
            LoanManageIou loanManageIou = loanManageIouMap.get(loanManageRepaymentPlan.getIouNo());
//            //实还金额
            List<LoanManageRepayment> repaymentList = loanManageRepaymentPlan.getValidRepaymentList().stream()
                    .filter(e -> e.getStatus().equals(RepaymentConstant.RepaymentStatusEnum.PAY.getCode()))
                    .collect(Collectors.toList());
//            //实还金额
//            BigDecimal actualAmount = repaymentList.stream().map(LoanManageRepayment::getActualAmount)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//            //根据还款记录查询关联还款记录费用
            List<LoanManageRepaymentVO> LoanManageRepaymentVOs = repaymentFeeService.getPayRepaymentFeeByRepayments(repaymentList);
            //待还还明细
            AddAmountVO addAmountVO = new AddAmountVO();
            addAmountVO = getFeeDetailsByAddAmount(loanManageRepaymentPlan, addAmountVO);

            RepaymentTermVO repaymentTermVO = RepaymentTermVO.builder()
                    .iouNo(loanManageRepaymentPlan.getIouNo())
                    .financeNo(loanManageIou.getFinanceNo())
                    .repaymentType(loanManageRepaymentPlan.getRepaymentType())
                    .id(loanManageRepaymentPlan.getId())
                    .loanAmount(loanManageIou.getIouAmount())
                    .loanTime(loanManageIou.getLoanTime())
                    .repaymentTime(loanManageRepaymentPlan.getRepaymentTime())
                    .repaymentAccount(repaymentAccountSuffixMap.get(loanManageRepaymentPlan.getGoodsId()))
                    .shouldAmount(loanManageRepaymentPlan.getSubPrincipal())
                    .actualAmount(BigDecimal.ZERO)
                    .unPayAmount(addAmountVO.getUnPayAmount())
                    .addAmountVO(addAmountVO)
                    .repaymentList(LoanManageRepaymentVOs)
                    .goodsId(loanManageRepaymentPlan.getGoodsId())
                    .period(loanManageRepaymentPlan.getPeriod())
                    .build();
            result.add(repaymentTermVO);
        }
        return result;
    }

    /**
     * 校验借据单关联还款计划是否全部还完
     *
     * @param loanInfoDTO
     * @return
     */
    private boolean iouIsSettleByPartRule(LoanInfoDTO loanInfoDTO) {
        //当前为最后一期并且本期已结清 则借据单为结清 (外部引用对象repaymentInfoDTO 已更新状态 loanInfoDTO 中的repaymentInfoDTO 也会跟着变)
//        if (repaymentType.equals(GoodsEnum.AMORTIZATION_LOAN.getCode())) {
        long settleCount = loanInfoDTO.getRepaymentInfoList().stream()
                .filter(RepaymentUtil::isSettle)
                .count();
        return settleCount == loanInfoDTO.getRepaymentInfoList().size();
//        }
    }

    /**
     * 检验需要全额还款情况
     *
     * @param loanManageRepaymentDTO
     * @param loanManageRepayment
     */
    private BigDecimal needFullAmount(LoanManageRepaymentDTO loanManageRepaymentDTO, LoanManageRepayment
            loanManageRepayment, List<RepaymentFee> repaymentFeeList) {
        BigDecimal shouldAmount = loanManageRepayment.getPrincipal()
                .add(loanManageRepayment.getInterest())
                .add(loanManageRepayment.getPenaltyInterest())
                .add(loanManageRepayment.getServiceCharge())
                .add(repaymentFeeList.stream().map(RepaymentFee::getShouldAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        //支付测试
        if (isPayTest()) {
            loanManageRepaymentDTO.setActualAmount(shouldAmount);
        }
        //提前结清
        if (RepaymentConstant.RepaymentTypeEnum.ADVANCE_SETTLE.getCode() == loanManageRepayment.getRepaymentType()) {
            Assert.isTrue(loanManageRepaymentDTO.getActualAmount().compareTo(shouldAmount) == 0, "提前结清需全额还款");
        }
        //提前还款
        if (RepaymentConstant.RepaymentTypeEnum.ADVANCE.getCode() == loanManageRepayment.getRepaymentType()) {
            Assert.isTrue(loanManageRepaymentDTO.getActualAmount().compareTo(shouldAmount) == 0, "提前还款需全额还款");
        }
        return shouldAmount;
    }

    /**
     * 支付测试模式
     *
     * @return
     */
    private boolean isPayTest() {
        String value = paramService.getValue("PAY_TEST");
        return !StringUtil.isBlank(value) && Boolean.parseBoolean(value);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean failRepayment(Long repaymentId, String failReason) {
        //可操作校验
        if (!isPaying(repaymentId)) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        //设置费用为未支付
        repaymentFeeService.changeStatusByRepaymentId(repaymentId, RepaymentConstant.RepaymentStatusEnum.PAY_FAIL.getCode());
        //设置还款记录为未支付
        loanManageRepaymentService.update(Wrappers.<LoanManageRepayment>lambdaUpdate()
                .eq(LoanManageRepayment::getId, repaymentId).set(LoanManageRepayment::getStatus, RepaymentConstant.RepaymentStatusEnum.PAY_FAIL.getCode())
                .set(LoanManageRepayment::getFailReason, failReason));
        //取消动态费用减免
        List<RepaymentFee> repaymentFees = repaymentFeeService.listByRepaymentIds(Collections.singletonList(repaymentId));
        cancelRepaymentFeeReductionAmount(repaymentFees);
        //取消利息减免
        cancelReductionInterest(loanManageRepaymentService.getById(repaymentId));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reCommitRepayment(Long repaymentId) {
        //可操作校验
        if (!isPaying(repaymentId)) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        //设置费用为未支付
        repaymentFeeService.changeStatusByRepaymentId(repaymentId, RepaymentConstant.RepaymentStatusEnum.REPEAT.getCode());
        //设置还款记录为未支付
        loanManageRepaymentService.changeStatus(Collections.singletonList(repaymentId), RepaymentConstant.RepaymentStatusEnum.REPEAT.getCode());        //费用记录扣减
        //取消动态费用减免
        List<RepaymentFee> repaymentFees = repaymentFeeService.listByRepaymentIds(Collections.singletonList(repaymentId));
        cancelRepaymentFeeReductionAmount(repaymentFees);
        //取消利息减免
        cancelReductionInterest(loanManageRepaymentService.getById(repaymentId));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelRepayment(Long repaymentId) {
        //可操作校验
        if (!isPaying(repaymentId)) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        //设置费用为未支付
        repaymentFeeService.changeStatusByRepaymentId(repaymentId, RepaymentConstant.RepaymentStatusEnum.CANCEL.getCode());
        //设置还款记录为未支付
        loanManageRepaymentService.changeStatus(Collections.singletonList(repaymentId), RepaymentConstant.RepaymentStatusEnum.CANCEL.getCode());
        //取消动态费用减免
        List<RepaymentFee> repaymentFees = repaymentFeeService.listByRepaymentIds(Collections.singletonList(repaymentId));
        cancelRepaymentFeeReductionAmount(repaymentFees);
        //取消利息减免
        cancelReductionInterest(loanManageRepaymentService.getById(repaymentId));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidRepayment(Long repaymentId) {
        //可操作校验
        if (!isPaying(repaymentId)) {
            throw new ServiceException("当前支付状态不在支付中 ,不可操作");
        }
        //设置费用为未支付
        repaymentFeeService.changeStatusByRepaymentId(repaymentId, RepaymentConstant.RepaymentStatusEnum.INVALID.getCode());
        //设置还款记录为未支付
        loanManageRepaymentService.changeStatus(Collections.singletonList(repaymentId), RepaymentConstant.RepaymentStatusEnum.INVALID.getCode());
        //取消动态费用减免
        List<RepaymentFee> repaymentFees = repaymentFeeService.listByRepaymentIds(Collections.singletonList(repaymentId));
        cancelRepaymentFeeReductionAmount(repaymentFees);
        //取消利息减免
        cancelReductionInterest(loanManageRepaymentService.getById(repaymentId));
        return true;
    }

    @Override
    public List<LoanManageRepaymentPlanVO> repaymentDetails(LoanManageIou iou) {
        //查询借据单所有还款计划
        List<LoanManageRepaymentPlan> planList = loanManageRepaymentPlanService.list(Wrappers.<LoanManageRepaymentPlan>lambdaQuery()
                .eq(LoanManageRepaymentPlan::getIouNo, iou.getIouNo())
                .ne(LoanManageRepaymentPlan::getStatus, RepaymentConstant.RepaymentPlanStatusEnum.INVALID.getCode())
                .orderByAsc(LoanManageRepaymentPlan::getPeriod));
        Map<Long, RepaymentInfoDTO> repaymentInfoDTOMap = getRepaymentInfoDTOS(LocalDate.now(), planList).stream()
                .collect(Collectors.toMap(BaseEntity::getId, e -> e));

        List<LoanManageRepaymentPlanVO> planVOS = BeanUtil.copyToList(planList, LoanManageRepaymentPlanVO.class);
        List<Long> attachIds = repaymentInfoDTOMap.values().stream().flatMap(e -> e.getRepaymentList().stream()).map(LoanManageRepayment::getVoucher)
                .filter(StringUtil::isNotBlank)
                .flatMap(e -> Func.toLongList(e).stream()).collect(Collectors.toList());
        Map<Long, Attach> attachMap = CollUtil.isNotEmpty(attachIds) ? attachService.listByIds(attachIds)
                .stream().collect(Collectors.toMap(BaseEntity::getId, e -> e))
                : MapUtil.newHashMap();
        //查询还款计划关联的还款计划关联费用
        //封装还款计划对应的还款记录
        for (LoanManageRepaymentPlanVO planVO : planVOS) {
            RepaymentInfoDTO repaymentInfoDTO = repaymentInfoDTOMap.get(planVO.getId());
            List<RepaymentPlanFee> allRepaymentPlanFeeList = repaymentInfoDTO.getAllRepaymentPlanFeeList();
            //还款费用记录
            Map<Long, List<RepaymentFee>> repaymentFeeMap = repaymentInfoDTO.getRepaymentFeeList().stream()
                    .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus()))
                    .collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));
            if (CollUtil.isNotEmpty(allRepaymentPlanFeeList)) {
                planVO.setRepaymentPlanFeeVOList(RepaymentPlanFeeWrapper.build().listVO(allRepaymentPlanFeeList.stream().filter(e -> ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode().equals(e.getFeeNode())
                        || ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode().equals(e.getFeeNode())).collect(Collectors.toList())));
            } else {
                planVO.setRepaymentPlanFeeVOList(Collections.emptyList());
            }
            List<LoanManageRepayment> payedRepayment = repaymentInfoDTO.getValidRepaymentList().stream()
                    .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus())).collect(Collectors.toList());
            List<LoanManageRepaymentVO> loanManageRepaymentVOList = BeanUtil.copyToList(payedRepayment, LoanManageRepaymentVO.class);
            //设置支付凭证
            for (LoanManageRepaymentVO loanManageRepaymentVO : loanManageRepaymentVOList) {
                if (ObjectUtil.isNotEmpty(loanManageRepaymentVO.getVoucher())) {
                    loanManageRepaymentVO.setAttachList(Func.toLongList(loanManageRepaymentVO.getVoucher())
                            .stream()
                            .filter(attachMap::containsKey)
                            .map(attachMap::get).collect(Collectors.toList()));
                }
            }
            //设置已还费用
            loanManageRepaymentVOList.forEach(e -> {
                e.setRepaymentFeeList(BeanUtil.copyToList(repaymentFeeMap.get(e.getId()), RepaymentFeeVO.class));
            });
            planVO.setLoanManageRepaymentVOList(loanManageRepaymentVOList);
            //重新设置当前利息为剩余利息
            planVO.setCurrentInterest(planVO.getInterest());
            planVO.setInterest(repaymentInfoDTO.getShouldInterest());
            BigDecimal reduce = payedRepayment.stream().map(LoanManageRepayment::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            planVO.setActualAmount(reduce);
            planVO.setUnPayAmount(repaymentInfoDTO.getSubPrincipal()
                    .add(repaymentInfoDTO.getShouldInterest()));
        }
        return planVOS;
    }

    private List<RepaymentPlanFeeTotal> calUsingRepaymentPlanFeeTotal(List<RepaymentPlanFee> repaymentPlanFeeList) {
        if (CollUtil.isEmpty(repaymentPlanFeeList)) {
            return Collections.emptyList();
        }
        Map<Long, List<RepaymentPlanFee>> repaymentPlanFeeListMap = repaymentPlanFeeList.stream()
                .collect(Collectors.groupingBy(RepaymentPlanFee::getExpenseTypeId));
        return repaymentPlanFeeListMap.keySet().stream().map(expenseTypeId -> {
            //统计
            RepaymentPlanFeeTotal repaymentPlanFeeTotal = BeanUtil.copyProperties(repaymentPlanFeeListMap.get(expenseTypeId).get(0), RepaymentPlanFeeTotal.class);
            //统计金额
            BigDecimal totalAmount = repaymentPlanFeeListMap.get(expenseTypeId).stream().map(RepaymentPlanFee::getPlanNeePayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            repaymentPlanFeeTotal.setAmount(totalAmount);
            repaymentPlanFeeTotal.setOriginAmount(totalAmount);
            return repaymentPlanFeeTotal;
        }).collect(Collectors.toList());

    }

    private List<LoanManageRepaymentPlanVO> repaymentDetailsNew(LoanManageIou iou) {
        LoanInfoDTO loanInfoDTO = getLoanInfoDTO(iou.getIouNo());
        List<RepaymentInfoDTO> repaymentInfoDTOS = loanInfoDTO.getRepaymentInfoList();
        Map<Long, RepaymentInfoDTO> repaymentInfoDTOMap = repaymentInfoDTOS.stream().collect(Collectors.toMap(RepaymentInfoDTO::getId, e -> e));
        List<LoanManageRepaymentPlanVO> planVOS = BeanUtil.copyToList(repaymentInfoDTOS, LoanManageRepaymentPlanVO.class);
        for (LoanManageRepaymentPlanVO planVO : planVOS) {
            RepaymentInfoDTO repaymentInfoDTO = repaymentInfoDTOMap.get(planVO.getId());
            planVO.setLoanManageRepaymentVOList(BeanUtil.copyToList(repaymentInfoDTO.getRepaymentList(), LoanManageRepaymentVO.class));
            Map<Long, List<RepaymentFee>> repaymentFeeMap = repaymentInfoDTO.payedRepaymentFeeList().stream().collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));
            planVO.getLoanManageRepaymentVOList().forEach(e -> e.setRepaymentFeeList(BeanUtil.copyToList(repaymentFeeMap.get(e.getId()), RepaymentFeeVO.class)));
            if (CollUtil.isNotEmpty(repaymentInfoDTO.getRepaymentPlanFeeList())) {
                planVO.setRepaymentPlanFeeVOList(RepaymentPlanFeeWrapper.build().listVO(repaymentInfoDTO.getRepaymentPlanFeeList()));
            } else {
                planVO.setRepaymentPlanFeeVOList(Collections.emptyList());
            }
            List<LoanManageRepayment> payedRepayment = repaymentInfoDTO.getValidRepaymentList().stream()
                    .filter(e -> RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(e.getStatus())).collect(Collectors.toList());
            //重新设置当前利息为剩余利息
            planVO.setInterest(repaymentInfoDTO.getShouldInterest());
            BigDecimal reduce = payedRepayment.stream().map(LoanManageRepayment::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            planVO.setActualAmount(reduce);
            planVO.setUnPayAmount(repaymentInfoDTO.getSubPrincipal().add(repaymentInfoDTO.getShouldInterest()).add(repaymentInfoDTO.getSubExInterest()));
        }
        return planVOS;
    }

    /**
     * 获取还款情况
     *
     * @param userId 用户id
     * @param ids    用户列表
     */
    private List<RepaymentInfoDTO> listRepaymentInfoByUserIdAndIds(List<Long> userId, List<Long> ids) {
        LocalDate now = LocalDate.now();
        //1.获取还款计划
        List<LoanManageRepaymentPlan> repaymentPlanList = loanManageRepaymentPlanService.listByUserIdsAndIds(userId, ids);
        return getRepaymentInfoDTOS(now, repaymentPlanList);
    }

    private RepaymentInfoDTO getRepaymentInfoDTOS(LocalDate repaymentTime, LoanManageRepaymentPlan repaymentPlan) {
        List<RepaymentInfoDTO> repaymentInfoDTOS = getRepaymentInfoDTOS(repaymentTime, Collections.singletonList(repaymentPlan));
        if (CollUtil.isEmpty(repaymentInfoDTOS)) {
            throw new ServiceException("还款计划不存在");
        }
        return CollUtil.getFirst(repaymentInfoDTOS);
    }

    @Override
    public List<RepaymentInfoDTO> getRepaymentInfoDTOS(LocalDate repaymentTime, List<LoanManageRepaymentPlan> repaymentPlanList) {
        return getRepaymentInfoDTOS(repaymentTime, repaymentPlanList, null);
    }

    /**
     * @param repaymentTime
     * @param repaymentPlanList
     * @param reCalDyFeeParam
     * @return
     */
    private List<RepaymentInfoDTO> getRepaymentInfoDTOS(LocalDate repaymentTime, List<LoanManageRepaymentPlan> repaymentPlanList, RepaymentPlanReCalParam reCalDyFeeParam) {
        return getRepaymentInfoDTOS(repaymentTime, repaymentPlanList, null, reCalDyFeeParam);
    }

    /**
     * 统计当前还款计划情况
     *
     * @param repaymentTime     还款时间
     * @param repaymentPlanList 还款计划列表
     * @param amount            本次还款金额
     * @param reCalDyFeeParam   重新试算动态费用参数 不填则取当前的
     * @return
     */
    private List<RepaymentInfoDTO> getRepaymentInfoDTOS(LocalDate repaymentTime, List<LoanManageRepaymentPlan> repaymentPlanList, BigDecimal amount, RepaymentPlanReCalParam reCalDyFeeParam) {
        //有效状态
        List<Integer> validStatus = Arrays.asList(RepaymentConstant.RepaymentStatusEnum.UN_PAY.getCode(),
                RepaymentConstant.RepaymentStatusEnum.PAY.getCode(),
                RepaymentConstant.RepaymentStatusEnum.PAYING.getCode());
        if (CollUtil.isEmpty(repaymentPlanList)) {
            return Collections.emptyList();
        }
//        repaymentPlanList = repaymentPlanList.stream().filter(e -> e.getFinanceNo().equals("J33028961234300")).collect(Collectors.toList());
        //借据单信息
        List<Long> iouIds = repaymentPlanList.stream().map(LoanManageRepaymentPlan::getIouId).distinct().collect(Collectors.toList());
        List<String> iouNos = repaymentPlanList.stream().map(LoanManageRepaymentPlan::getIouNo).distinct().collect(Collectors.toList());
        Map<Long, LoanManageIou> loanManageIouMap = loanManageIouService.listByIds(iouIds).stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
        //2.还款记录获取 获取整笔借据单的 方便在还款计划内统计整笔借据单的情况已还 待还字段
        List<Long> planIds = repaymentPlanList.stream().map(LoanManageRepaymentPlan::getId).collect(Collectors.toList());
        //整笔借据单还款情况
        List<LoanManageRepayment> allRepaymentList = loanManageRepaymentService.listByIouNo(iouNos);
        Map<String, List<LoanManageRepayment>> allRepaymentListMap = allRepaymentList.stream().collect(Collectors.groupingBy(LoanManageRepayment::getIouNo));
        //TODO 整笔情况应当抽出去更好 减少查询
        //整笔借据单还款计划情况 k:借据单号 v:还款计划
        Map<String, List<LoanManageRepaymentPlan>> loanManageRepaymentPlanMap = listLoanManageRepaymentPlansContainInvalid(iouNos)
                .stream().collect(Collectors.groupingBy(LoanManageRepaymentPlan::getIouNo));
        //本次查询的还款情况
        List<LoanManageRepayment> repaymentList = allRepaymentList.stream()
                .filter(e -> planIds.contains(e.getRepaymentPlanId())).collect(Collectors.toList());
        Map<Long, List<LoanManageRepayment>> repaymentListMap = repaymentList.stream()
                .collect(Collectors.groupingBy(LoanManageRepayment::getRepaymentPlanId));
        //3.还款计划费用获取
        List<RepaymentPlanFee> repaymentPlanFeeList = repaymentPlanFeeService.listByRepaymentId(planIds);
        Map<Long, List<RepaymentPlanFee>> repaymentPlanFeeListMap = repaymentPlanFeeList.stream()
                .collect(Collectors.groupingBy(RepaymentPlanFee::getPlanId));
        //本期还款记录和还款计划中间表
        List<LoanManageRepaymentTerm> loanManageRepaymentTermList = loanManageRepaymentTermService.listByPlanIds(planIds);
        Map<Long, List<LoanManageRepaymentTerm>> loanManageRepaymentTermListMap = loanManageRepaymentTermList.stream().collect(Collectors.groupingBy(LoanManageRepaymentTerm::getRepaymentPlanId));
        //3.记录费用
        List<Long> repaymentIds = loanManageRepaymentTermList.stream().map(LoanManageRepaymentTerm::getRepaymentId).collect(Collectors.toList());
        List<RepaymentFee> repaymentFeeList = CollUtil.isNotEmpty(repaymentIds) ? repaymentFeeService.listByRepaymentIds(repaymentIds) : Collections.emptyList();
        Map<Long, List<RepaymentFee>> repaymentFeeListMap = repaymentFeeList.stream()
                .collect(Collectors.groupingBy(RepaymentFee::getRepaymentId));
        // 额外利息统计
        Map<Long, BigDecimal> exInterestMap = getExInterest(planIds);
        // 减免利息
        Map<Long, List<IncomeDetail>> subReductionInterest = getSubReductionInterest(planIds);
        // 减免动态费用
        Map<Long, List<IncomeDetail>> subReductionFeeAmountMap = getSubReductionFeeAmount(repaymentPlanFeeList);
        //数据整合
        List<RepaymentInfoDTO> repaymentInfoDTOList = repaymentPlanList.stream().map(plan -> {
            RepaymentInfoDTO repaymentInfoDTO = BeanUtil.copyProperties(plan, RepaymentInfoDTO.class);
            repaymentInfoDTO.setReductionInterestList(subReductionInterest.get(plan.getId()));
            Long planId = repaymentInfoDTO.getId();
            //剩余额外利息
            repaymentInfoDTO.setSubExInterest(exInterestMap.get(planId));
            //借据单信息
            LoanManageIou loanManageIou = loanManageIouMap.get(repaymentInfoDTO.getIouId());
            repaymentInfoDTO.setLoanManageIou(loanManageIou);
            //还款记录
            List<LoanManageRepayment> loanManageRepayments = repaymentListMap.getOrDefault(planId, Collections.emptyList());
            repaymentInfoDTO.setRepaymentList(loanManageRepayments);
            //有效还款记录
            List<LoanManageRepayment> validLoanManageRepayments = loanManageRepayments.stream()
                    .filter(e -> validStatus.contains(e.getStatus())).collect(Collectors.toList());
            repaymentInfoDTO.setValidRepaymentList(validLoanManageRepayments);
            //计划费用
            List<RepaymentPlanFee> repaymentPlanFeeList1 = repaymentPlanFeeListMap.getOrDefault(planId, Collections.emptyList());
            for (RepaymentPlanFee repaymentPlanFee : repaymentPlanFeeList1) {
                repaymentPlanFee.setReductionFeeList(subReductionFeeAmountMap.getOrDefault(repaymentPlanFee.getId(), new ArrayList<>()));
            }
            repaymentInfoDTO.setAllRepaymentPlanFeeList(repaymentPlanFeeList1);
            //记录与还款计划关联
            List<LoanManageRepaymentTerm> repaymentTermList = loanManageRepaymentTermListMap.getOrDefault(planId, Collections.emptyList());
            repaymentInfoDTO.setLoanManageRepaymentTerms(repaymentTermList);
            //记录费用
            List<RepaymentFee> listFee = new LinkedList<>();
            for (LoanManageRepayment validLoanManageRepayment : validLoanManageRepayments) {
                if (repaymentFeeListMap.containsKey(validLoanManageRepayment.getId())) {
                    listFee.addAll(repaymentFeeListMap.get(validLoanManageRepayment.getId()));
                }
            }
            repaymentInfoDTO.setRepaymentFeeList(listFee);
            //未还的计划费用
            repaymentInfoDTO.setRepaymentPlanFeeList(repaymentReCalBizService.calUnPayRepaymentPlanFee(repaymentPlanFeeList1, listFee));
            //剩余本金
            BigDecimal subPrincipal = calSubPrincipal(validLoanManageRepayments, repaymentInfoDTO.getPrincipal());
            repaymentInfoDTO.setSubPrincipal(subPrincipal);
            //本期计息天数
            Integer loanDay = ProductExpenseOrderDetailUtils.getLoanDay(repaymentInfoDTO.getStartTime(), repaymentTime);
            repaymentInfoDTO.setLoanDay(loanDay);
            repaymentInfoDTO.setInterestAccrualDays(loanDay + repaymentInfoDTO.getInterestDay());
            //提前借款天数
            Integer prepaymentDays = ProductExpenseOrderDetailUtils.getLoanDay(repaymentTime, repaymentInfoDTO.getRepaymentTime());
            repaymentInfoDTO.setPrepaymentDays(prepaymentDays > 0 ? prepaymentDays : 0);
            //逾期天数
            Integer overDueDay = ProductExpenseOrderDetailUtils.getLoanDay(repaymentInfoDTO.getRepaymentTime(), repaymentTime);
            repaymentInfoDTO.setOverdueDays(overDueDay > 0 ? overDueDay : 0);
            //借据单总体情况
            List<LoanManageRepayment> allLoanRepaymentList = allRepaymentListMap.get(repaymentInfoDTO.getIouNo());
            allLoanRepaymentList = CollUtil.isEmpty(allLoanRepaymentList) ? allLoanRepaymentList : Collections.emptyList();
            List<LoanManageRepayment> allLoanManageRepayment = allRepaymentList.stream()
                    .filter(e -> validStatus.contains(e.getStatus())).collect(Collectors.toList());
            //总本金
            repaymentInfoDTO.setLoanAllPrincipal(loanManageIou.getIouAmount());
            //总已付本金
            repaymentInfoDTO.setLoanAllPayedRepaymentPrincipal(allLoanManageRepayment.stream()
                    .map(LoanManageRepayment::getActualPrincipal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            //总计息天数
            Integer totalLoanDay = ProductExpenseOrderDetailUtils.getLoanDay(loanManageIou.getLoanTime(), repaymentTime);
            repaymentInfoDTO.setTotalInterestAccrualDays(totalLoanDay + repaymentInfoDTO.getInterestDay());
            //总提前还款时间
            Integer totalPrepaymentDays = ProductExpenseOrderDetailUtils.getLoanDay(repaymentTime, loanManageIou.getExpireTime());
            repaymentInfoDTO.setTotalPrepaymentDays(totalPrepaymentDays > 0 ? totalPrepaymentDays : 0);
            //总逾期天数
            Integer totalOverDueDay = ProductExpenseOrderDetailUtils.getLoanDay(loanManageIou.getExpireTime(), repaymentTime);
            repaymentInfoDTO.setTotalOverdueDays(totalOverDueDay > 0 ? totalOverDueDay : 0);
            //设置本次还款金额不传则设置为剩余应还本金
            repaymentInfoDTO.setAmount(amount == null ? repaymentInfoDTO.getSubPrincipal() : amount);
            //借据单还款计划期数 剩余待还期数
            List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanMap.get(repaymentInfoDTO.getIouNo());

            repaymentInfoDTO.setTerm((int) loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getRepaymentStatus)
                    .filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.INVALID.getCode() != e).count());
            repaymentInfoDTO.setSubTerm((int) loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getRepaymentStatus)
                    .filter(e -> RepaymentConstant.RepaymentPlanRepaymentStatusEnum.USING.getCode() == e).count());
            return repaymentInfoDTO;
        }).collect(Collectors.toList());
        //如果需要重新待支付的动态费用 则进行重新计算动态费用
        if (ObjectUtil.isNotEmpty(reCalDyFeeParam)) {
            repaymentReCalBizService.reCalRepaymentInfoDTO(reCalDyFeeParam, repaymentInfoDTOList);
        }
        return repaymentInfoDTOList;
    }

    private Map<Long, List<IncomeDetail>> getSubReductionFeeAmount(List<RepaymentPlanFee> repaymentPlanFeeList) {
        Map<Long, List<IncomeDetail>> reductionFeeAmountMap = new HashMap<>();
        if (CollUtil.isEmpty(repaymentPlanFeeList)) {
            return reductionFeeAmountMap;
        }
        List<String> planFeeIdList = repaymentPlanFeeList.stream().map(e -> e.getId().toString()).collect(Collectors.toList());
        List<IncomeDetail> incomeDetails = incomeDetailService.listByTypeAndBizNo(IncomeDetailConstant.REDUCTION_FEE_AMOUNT, planFeeIdList);
        Map<String, List<IncomeDetail>> incomeDetailMap = incomeDetails.stream().collect(Collectors.groupingBy(IncomeDetail::getBizNo));
        for (String planId : planFeeIdList) {
            reductionFeeAmountMap.put(Func.toLong(planId), incomeDetailMap.getOrDefault(planId, new ArrayList<>()));
        }
        return reductionFeeAmountMap;
    }

    private Map<Long, List<IncomeDetail>> getSubReductionInterest(List<Long> planIds) {
        Map<Long, List<IncomeDetail>> listMap = new HashMap<>();
        List<IncomeDetail> incomeDetails = incomeDetailService.listByTypeAndBizNo(IncomeDetailConstant.REDUCTION_INTEREST_AMOUNT, planIds.stream()
                .map(Object::toString).collect(Collectors.toList()));
        Map<String, List<IncomeDetail>> incomeDetailMap = incomeDetails.stream().collect(Collectors.groupingBy(IncomeDetail::getBizNo));
        for (Long planId : planIds) {
            listMap.put(planId, incomeDetailMap.getOrDefault(planId.toString(), new ArrayList<>()));
        }
        return listMap;
    }

    private Map<Long, BigDecimal> getExInterest(List<Long> planIds) {
        Map<Long, BigDecimal> planExInterest = new HashMap<>();
        List<IncomeDetail> incomeDetails = incomeDetailService.listByTypeAndBizNo(IncomeDetailConstant.REPAYMENT_PLAN_EX_INTEREST_AMOUNT, planIds.stream()
                .map(Object::toString).collect(Collectors.toList()));
        Map<String, List<IncomeDetail>> incomeDetailMap = incomeDetails.stream().collect(Collectors.groupingBy(IncomeDetail::getBizNo));
        for (Long planId : planIds) {
            List<IncomeDetail> list = incomeDetailMap.get(planId.toString());
            planExInterest.put(planId, CollUtil.isEmpty(list) ? BigDecimal.ZERO : list.stream().map(IncomeDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return planExInterest;
    }

    private BigDecimal calSubPrincipal(List<LoanManageRepayment> validLoanManageRepayments, BigDecimal principal) {
        if (CollUtil.isEmpty(validLoanManageRepayments)) {
            return principal;
        }
        //已还-正在还
        BigDecimal amount = validLoanManageRepayments.stream().filter(e -> PAYING_OR_PAYED_STATUS.contains(e.getStatus()))
                .map(LoanManageRepayment::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal subtract = principal.subtract(amount);
        return subtract.compareTo(BigDecimal.ZERO) > 0 ? subtract : BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoanManageRepaymentPlan addExInterest(Long id, BigDecimal exInterest) {
        String bizNo = id.toString();
        BigDecimal total = incomeDetailService.getAmountTotal(IncomeDetailConstant.REPAYMENT_PLAN_EX_INTEREST_AMOUNT, bizNo);

        //增加利息
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(id);
        IncomeDetail add = incomeDetailService.add(loanManageRepaymentPlan.getUserId(), exInterest, IncomeDetailConstant.REPAYMENT_PLAN_EX_INTEREST_AMOUNT
                , bizNo, null);
        incomeDetailService.save(add);
        BigDecimal add1 = total.add(exInterest);
        loanManageRepaymentPlanService.updateExAmount(id, add1);
        return loanManageRepaymentPlan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoanManageRepaymentPlan subExInterest(Long id, BigDecimal exInterest) {
        String bizNo = id.toString();
        BigDecimal total = incomeDetailService.getAmountTotal(IncomeDetailConstant.REPAYMENT_PLAN_EX_INTEREST_AMOUNT, bizNo);

        //增加利息
        LoanManageRepaymentPlan loanManageRepaymentPlan = loanManageRepaymentPlanService.getById(id);
        IncomeDetail sub = incomeDetailService.sub(loanManageRepaymentPlan.getUserId(), exInterest, IncomeDetailConstant.REPAYMENT_PLAN_EX_INTEREST_AMOUNT
                , bizNo, null);
        incomeDetailService.save(sub);
        BigDecimal add1 = total.subtract(exInterest);
        loanManageRepaymentPlanService.updateExAmount(id, add1);
        return loanManageRepaymentPlan;
    }

    @Override
    public LoanManageIouVO detail(Long id) {
        LoanManageIou loanManageIou = loanManageIouService.getById(id);
        if (Objects.isNull(loanManageIou)) {
            return null;
        }
        LoanManageIouVO loanManageIouVO = LoanManageIouWrapper.build().entityVO(loanManageIou);
        String financingNo = loanManageIou.getFinanceNo();
        // 查询融资申请信息
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financingNo));
        loanManageIouVO.setFinanceApply(financeApply);
        User user = UserUtils.getUserById(loanManageIou.getUserId());
        loanManageIouVO.setUserName(user.getName());
        // 查询还款账户
        EnterpriseQuotaVO enterpriseQuotaVO = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(loanManageIou.getGoodsId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), loanManageIou.getUserId()).getData();
        loanManageIouVO.setCapitalName(enterpriseQuotaVO.getCapitalName());
        loanManageIouVO.setBankAccount(enterpriseQuotaVO.getBankCardNo());
        // 查询还款计划
        List<LoanManageRepaymentPlanVO> loanManageRepaymentPlanVOS = repaymentDetails(loanManageIouVO);
        loanManageRepaymentPlanVOS.forEach(loanManageRepaymentPlanVO -> {
            if (loanManageRepaymentPlanVO.getRepaymentTime().isBefore(LocalDate.now())) {
                long overdueDay = ChronoUnit.DAYS.between(loanManageRepaymentPlanVO.getRepaymentTime(), LocalDateTime.now());
                loanManageRepaymentPlanVO.setOverdueDay(Integer.parseInt(String.valueOf(overdueDay)));
            }
        });
        loanManageIouVO.setLoanManageRepaymentPlanList(loanManageRepaymentPlanVOS);
        // 查询统一社会代码
        CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoService.getByCustomerId(user.getId());
        if (Objects.nonNull(customerBusinessInfo)) {
            loanManageIouVO.setCreditCode(customerBusinessInfo.getCreditCode());
        }
        // 查询发票
        //BillInvoice invoice = invoiceService.getByFinanceNo(financeApply.getFinanceNo());
        // 查询还款明细
        List<LoanManageRepayment> loanManageRepayments = loanManageRepaymentService.listAllRelationLoanRepayment(financingNo);
        loanManageIouVO.setLoanManageRepayments(LoanManageRepaymentWrapper.build().listVO(loanManageRepayments));
//		if (Objects.nonNull(invoice)) {
//			loanManageIouVO.setInvoiceNo(invoice.getInvoiceNo());
//		}
        return loanManageIouVO;
    }

    @Override
    public IncomeDetail addReductionDetail(IncomeDetailConstant type, Long bizId, BigDecimal amount) {
        return incomeDetailService.sub(amount, type, bizId.toString(), "");
    }

    @Override
    public IncomeDetail addReductionInterest(LoanManageRepaymentPlan loanManageRepaymentPlan, BigDecimal amount) {
        //设置减免利息金额
        loanManageRepaymentPlan.setReductionInterest(loanManageRepaymentPlan.getReductionInterest().subtract(amount));
        return addReductionDetail(IncomeDetailConstant.REDUCTION_INTEREST_AMOUNT, loanManageRepaymentPlan.getId(), amount);
    }

    @Override
    public IncomeDetail addReductionRepaymentPlanFee(RepaymentPlanFee repaymentPlanFee, BigDecimal amount) {
        //设置减免利息金额
        repaymentPlanFee.setPlanReductionAmount(repaymentPlanFee.getPlanReductionAmount().subtract(amount));
        return addReductionDetail(IncomeDetailConstant.REDUCTION_FEE_AMOUNT, repaymentPlanFee.getId(), amount);
    }

    @Override
    public List<IncomeDetail> saveBatchAddReductionDetail(List<IncomeDetail> reductionDetail) {
        incomeDetailService.saveBatch(reductionDetail);
        return reductionDetail;
    }

    @Override
    public List<RepaymentPlanFee> regenRepaymentPlanFee(List<RepaymentPlanFee> repaymentPlanFeeList) {
        repaymentPlanFeeService.saveBatch(repaymentPlanFeeList);
        return repaymentPlanFeeList;
    }

    @Override
    public List<RepaymentPlanFeeTotal> listRepaymentPlanFeeTotal(String iouNo, Integer type) {
        LoanInfoDTO loanInfoDTO = getLoanInfoDTO(iouNo);
        return listRepaymentPlanFeeTotal(loanInfoDTO,type);
    }

    @Override
    public List<RepaymentPlanFeeTotal> listRepaymentPlanFeeTotal(LoanInfoDTO loanInfoDTO, Integer type) {
        //当期
        if (type == 1) {
            RepaymentInfoDTO repaymentInfoDTO = loanInfoDTO.listUsingRepaymentInfo().get(0);
            return calUsingRepaymentPlanFeeTotal(repaymentInfoDTO.getRepaymentPlanFeeList());
        }
        //剩余
        List<RepaymentPlanFee> repaymentPlanFeeList = loanInfoDTO.listUsingRepaymentPlanFee();
        return calUsingRepaymentPlanFeeTotal(repaymentPlanFeeList);
    }

    @Override
    public BigDecimal calPlanInterest(List<RepaymentInfoDTO> repaymentInfoList) {
        return repaymentInfoList.stream().map(RepaymentInfoDTO::getPlanInterest).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
