package org.springblade.loan.mq.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.FinanceApplyStatusEnum;
import org.springblade.common.enums.PlatformExpensesEnum;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.loan.entity.AdvanceSettledApply;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.enums.AdvanceSettledEnum;
import org.springblade.loan.enums.IouEnum;
import org.springblade.loan.mapper.LoanManageIouMapper;
import org.springblade.loan.service.IAdvanceSettledApplyService;
import org.springblade.loan.service.ILoanManageRepaymentService;
import org.springblade.loan.service.IRepaymentPlanFeeService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.handler.MessageHandler;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 提前结清超时未支付，关闭操作
 *
 * <AUTHOR>
 */
@Component("advance_settled_un_pay")
@RequiredArgsConstructor
@TenantIgnore
public class advanceSettledUnPayMessageHandler implements MessageHandler {

    private final LoanManageIouMapper loanManageIouMapper;
    private final FinanceApplyMapper financeApplyMapper;
    private final IAdvanceSettledApplyService advanceSettledApplyService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final IProductExpenseService productExpenseService;
    private final IRepaymentPlanFeeService repaymentPlanFeeService;

    @Override
    public void handler(DelayMessage delayMessage) {
        String financeNo = delayMessage.getMsg();
        LoanManageIou iou = loanManageIouMapper.selectLoanByFinanceNo(financeNo);
        FinanceApply financeApply = financeApplyMapper.getByFinanceNoApply(financeNo);
        if (ObjectUtil.isEmpty(iou) || ObjectUtil.isEmpty(financeApply)) {
            return;
        }
        if (!IouEnum.BEFORE_FINISH.getCode().equals(iou.getStatus())) {
            //更新融资表状态
            financeApply.setStatus(FinanceApplyStatusEnum.UN_SETTLED.getCode());
            financeApplyMapper.updateById(financeApply);
            //更新提前结清状态
            AdvanceSettledApply advanceSettledApply = advanceSettledApplyService.settledApplyByFinanceNo(financeApply.getFinanceNo());
            advanceSettledApply.setStatus(AdvanceSettledEnum.OVERTIME_UNPAID.getCode());
            advanceSettledApplyService.updateById(advanceSettledApply);
            //关闭付款订单
            if (StringUtil.isNotBlank(advanceSettledApply.getRepaymentNo())) {
                List<LoanManageRepayment> list = loanManageRepaymentService.list(Wrappers.<LoanManageRepayment>lambdaQuery()
                        .eq(LoanManageRepayment::getRepaymentNo, advanceSettledApply.getRepaymentNo())
                        .orderByAsc(BaseEntity::getCreateTime));
                if (ObjectUtil.isNotEmpty(list)) {
                    LoanManageRepayment repayment = list.get(0);
                    if (!RepaymentConstant.RepaymentStatusEnum.PAY.getCode().equals(repayment.getStatus())) {
                        repayment.setStatus(RepaymentConstant.RepaymentStatusEnum.INVALID.getCode());
                        loanManageRepaymentService.updateById(repayment);
                    }
                }
            }
            //关闭费用 关闭费用计划
            repaymentPlanFeeService.removeFeeNodeAndFinanceId(Collections.singletonList(ExpenseConstant.FeeNodeEnum.ADVANCE_SETTLE.getCode()), financeApply.getId());
            productExpenseService.cancelUnPayExpenseOrder(financeApply.getFinanceNo(), PlatformExpensesEnum.ADVANCE_SETTLE
                    .getCode());
        }
    }
}
