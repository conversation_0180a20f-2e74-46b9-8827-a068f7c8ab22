module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: ['plugin:vue/essential', 'eslint:recommended'],
  parserOptions: {
    parser: 'babel-eslint',
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',

    /**
     * "off" or 0：停用这个规则
     * "warn" or 1：启用规则，当不满足规则时发出警告，不会导致程序退出
     * "error" or 2：启用规则，当不满足规则时报错，会导致程序退出
     */

    /*

    // 缩进风格
    indent: ['warn', 2],
    // 行尾是否使用分号 <always|never>
    semi: ['warn', 'never'],
    // 字符串引号 <double|single|backtick>
    quotes: ['warn', 'single'],
    // 尾随逗号 <never|always|always-multiline|only-multiline>
    'comma-dangle': ['warn', 'always-multiline'],
    // 箭头函数括号 <always|as-needed>
    'arrow-parens': ['warn', 'as-needed'],
    // 箭头函数体括号 <always|as-needed|never>
    'arrow-body-style': ['warn', 'as-needed'],
    // 箭头函数箭头前后的空格
    'arrow-spacing': [
      'warn',
      {
        before: true,
        after: true,
      },
    ],
    // 双峰驼命名格式 <always|never>
    camelcase: ['warn', { properties: 'always' }],

    */
  },
}
