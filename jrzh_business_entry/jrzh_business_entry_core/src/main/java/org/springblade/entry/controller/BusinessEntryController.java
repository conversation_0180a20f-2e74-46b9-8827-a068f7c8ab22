/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.entry.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.entry.dto.CreateBusinessEntryDTO;
import org.springblade.entry.entity.BusinessEntry;
import org.springblade.entry.service.IBusinessEntryService;
import org.springblade.entry.vo.BusinessEntryVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务录入 控制器
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-entry/web-back/businessEntry")
@Api(value = "业务录入", tags = "业务录入接口")
public class BusinessEntryController extends BladeController {

    private final IBusinessEntryService businessEntryService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入businessEntry")
    public R<BusinessEntry> detail(BusinessEntry businessEntry) {
        BusinessEntry detail = businessEntryService.getOne(Condition.getQueryWrapper(businessEntry));
        return R.data(detail);
    }

    /**
     * 分页查询
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页", notes = "传入businessEntry")
    public R<IPage<BusinessEntryVO>> list(BusinessEntryVO businessEntry, Query query) {
        IPage<BusinessEntryVO> pages = businessEntryService.selectBusinessEntryPage(Condition.getPage(query), businessEntry);
        return R.data(pages);
    }

    /**
     * 新建业务录入
     */
    @PostMapping("/create")
    @ApiOperation(value = "新建业务录入", notes = "传入createBusinessEntryDTO")
    public R<BusinessEntryVO> create(@Valid @RequestBody CreateBusinessEntryDTO createDTO) {
        return businessEntryService.createBusinessEntry(createDTO);
    }

    /**
     * 修改
     */
    @PostMapping("/submit")
    @ApiOperation(value = "修改", notes = "传入businessEntry")
    public R submit(@Valid @RequestBody BusinessEntry businessEntry) {
        return R.status(businessEntryService.updateById(businessEntry));
    }

    /**
     * 更新业务节点
     */
    @PostMapping("/update-node")
    @ApiOperation(value = "更新业务节点", notes = "更新业务节点")
    public R<Boolean> updateBusinessNode(
            @ApiParam(value = "业务录入ID", required = true) @RequestParam Long id,
            @ApiParam(value = "业务节点", required = true) @RequestParam String businessNode,
            @ApiParam(value = "业务节点名称", required = true) @RequestParam String businessNodeName) {
        return businessEntryService.updateBusinessNode(id, businessNode, businessNodeName);
    }

    /**
     * 更新业务状态
     */
    @PostMapping("/update-status")
    @ApiOperation(value = "更新业务状态", notes = "更新业务状态")
    public R<Boolean> updateBusinessStatus(
            @ApiParam(value = "业务录入ID", required = true) @RequestParam Long id,
            @ApiParam(value = "业务状态", required = true) @RequestParam String businessStatus) {
        return businessEntryService.updateBusinessStatus(id, businessStatus);
    }

    /**
     * 更新进度
     */
    @PostMapping("/update-progress")
    @ApiOperation(value = "更新进度", notes = "更新进度")
    public R<Boolean> updateProgress(
            @ApiParam(value = "业务录入ID", required = true) @RequestParam Long id,
            @ApiParam(value = "当前步骤", required = true) @RequestParam Integer currentStep,
            @ApiParam(value = "总步骤数", required = true) @RequestParam Integer totalSteps) {
        return businessEntryService.updateProgress(id, currentStep, totalSteps);
    }

    /**
     * 根据客户ID查询业务录入列表
     */
    @GetMapping("/by-customer/{customerId}")
    @ApiOperation(value = "根据客户ID查询", notes = "根据客户ID查询业务录入列表")
    public R<List<BusinessEntryVO>> getByCustomerId(
            @ApiParam(value = "客户ID", required = true) @PathVariable Long customerId) {
        List<BusinessEntryVO> list = businessEntryService.getByCustomerId(customerId);
        return R.data(list);
    }

    /**
     * 根据业务类型查询业务录入列表
     */
    @GetMapping("/by-type/{businessType}")
    @ApiOperation(value = "根据业务类型查询", notes = "根据业务类型查询业务录入列表")
    public R<List<BusinessEntryVO>> getByBusinessType(
            @ApiParam(value = "业务类型", required = true) @PathVariable String businessType) {
        List<BusinessEntryVO> list = businessEntryService.getByBusinessType(businessType);
        return R.data(list);
    }

    /**
     * 根据业务状态查询业务录入列表
     */
    @GetMapping("/by-status/{businessStatus}")
    @ApiOperation(value = "根据业务状态查询", notes = "根据业务状态查询业务录入列表")
    public R<List<BusinessEntryVO>> getByBusinessStatus(
            @ApiParam(value = "业务状态", required = true) @PathVariable String businessStatus) {
        List<BusinessEntryVO> list = businessEntryService.getByBusinessStatus(businessStatus);
        return R.data(list);
    }

    /**
     * 推进到下一个节点
     */
    @PostMapping("/move-to-next-node")
    @ApiOperation(value = "推进到下一个节点", notes = "推进到下一个节点")
    public R<Boolean> moveToNextNode(
            @ApiParam(value = "业务录入ID", required = true) @RequestParam Long id) {
        return businessEntryService.moveToNextNode(id);
    }

    /**
     * 回退到上一个节点
     */
    @PostMapping("/move-to-previous-node")
    @ApiOperation(value = "回退到上一个节点", notes = "回退到上一个节点")
    public R<Boolean> moveToPreviousNode(
            @ApiParam(value = "业务录入ID", required = true) @RequestParam Long id) {
        return businessEntryService.moveToPreviousNode(id);
    }

    /**
     * 根据业务类型获取所有节点信息
     */
    @GetMapping("/nodes/{businessType}")
    @ApiOperation(value = "获取业务节点", notes = "根据业务类型获取所有节点信息")
    public R<List<String>> getNodesByBusinessType(
            @ApiParam(value = "业务类型", required = true) @PathVariable String businessType) {
        return businessEntryService.getNodesByBusinessType(businessType);
    }
}
