/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.entry.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 业务录入实体类
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@TableName("jrzh_business_entry")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessEntry对象", description = "业务录入")
public class BusinessEntry extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 融资客户ID
     */
    @NotNull(message = "融资客户ID不能为空")
    @ApiModelProperty(value = "融资客户ID")
    private Long customerId;

    /**
     * 融资客户名称
     */
    @NotBlank(message = "融资客户名称不能为空")
    @Size(max = 100, message = "融资客户名称长度不能超过100")
    @ApiModelProperty(value = "融资客户名称")
    private String customerName;

    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 客户资料ID
     */
    @ApiModelProperty(value = "客户资料ID")
    private Long customerMaterialId;

    /**
     * 融资申请ID
     */
    @ApiModelProperty(value = "融资申请ID")
    private Long financeApplyId;

    /**
     * 业务类型：CREDIT-授信，FINANCING-融资，CREDIT_FINANCING-授支一体
     */
    @NotBlank(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型：CREDIT-授信，FINANCING-融资，CREDIT_FINANCING-授支一体")
    private String businessType;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品名称
     */
    @Size(max = 100, message = "产品名称长度不能超过100")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 业务状态：IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消
     */
    @NotBlank(message = "业务状态不能为空")
    @ApiModelProperty(value = "业务状态：IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消")
    private String businessStatus;

    /**
     * 业务节点
     */
    @NotBlank(message = "业务节点不能为空")
    @Size(max = 50, message = "业务节点长度不能超过50")
    @ApiModelProperty(value = "业务节点")
    private String businessNode;

    /**
     * 业务节点名称
     */
    @NotBlank(message = "业务节点名称不能为空")
    @Size(max = 100, message = "业务节点名称长度不能超过100")
    @ApiModelProperty(value = "业务节点名称")
    private String businessNodeName;

    /**
     * 申请金额
     */
    @ApiModelProperty(value = "申请金额")
    private BigDecimal applyAmount;

    /**
     * 批准金额
     */
    @ApiModelProperty(value = "批准金额")
    private BigDecimal approvedAmount;

    /**
     * 当前步骤
     */
    @ApiModelProperty(value = "当前步骤")
    private Integer currentStep;

    /**
     * 总步骤数
     */
    @ApiModelProperty(value = "总步骤数")
    private Integer totalSteps;

    /**
     * 进度百分比
     */
    @ApiModelProperty(value = "进度百分比")
    private BigDecimal progressRate;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500")
    @ApiModelProperty(value = "备注")
    private String remark;
}
