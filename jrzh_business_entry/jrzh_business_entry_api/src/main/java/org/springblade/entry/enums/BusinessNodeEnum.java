/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.entry.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务节点枚举
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Getter
@AllArgsConstructor
public enum BusinessNodeEnum {

    // 授信业务节点
    CREDIT_MATERIAL_SUPPLEMENT("CREDIT_MATERIAL_SUPPLEMENT", "客户授信-补充资料", "CREDIT", 1),
    CREDIT_CONTRACT_SIGN("CREDIT_CONTRACT_SIGN", "客户授信-合同签署", "CREDIT", 2),
    CREDIT_APPROVAL("CREDIT_APPROVAL", "客户授信-审批", "CREDIT", 3),

    // 融资业务节点
    FINANCING_DEMAND("FINANCING_DEMAND", "客户融资-融资需求", "FINANCING", 1),
    FINANCING_FEE_PAYMENT("FINANCING_FEE_PAYMENT", "客户融资-缴纳费用", "FINANCING", 2),
    FINANCING_CONTRACT_SIGN("FINANCING_CONTRACT_SIGN", "客户融资-合同签署", "FINANCING", 3),
    FINANCING_APPROVAL("FINANCING_APPROVAL", "客户融资-审批", "FINANCING", 4),
    FINANCING_DISBURSEMENT("FINANCING_DISBURSEMENT", "客户融资-放款", "FINANCING", 5),

    // 授支一体业务节点
    CF_MATERIAL_SUPPLEMENT("CF_MATERIAL_SUPPLEMENT", "授支一体-补充资料", "CREDIT_FINANCING", 1),
    CF_CREDIT_CONTRACT_SIGN("CF_CREDIT_CONTRACT_SIGN", "授支一体-授信合同签署", "CREDIT_FINANCING", 2),
    CF_CREDIT_APPROVAL("CF_CREDIT_APPROVAL", "授支一体-授信审批", "CREDIT_FINANCING", 3),
    CF_FINANCING_DEMAND("CF_FINANCING_DEMAND", "授支一体-融资需求", "CREDIT_FINANCING", 4),
    CF_FINANCING_CONTRACT_SIGN("CF_FINANCING_CONTRACT_SIGN", "授支一体-融资合同签署", "CREDIT_FINANCING", 5),
    CF_FEE_PAYMENT("CF_FEE_PAYMENT", "授支一体-缴纳费用", "CREDIT_FINANCING", 6),
    CF_FINAL_APPROVAL("CF_FINAL_APPROVAL", "授支一体-最终审批", "CREDIT_FINANCING", 7),
    CF_DISBURSEMENT("CF_DISBURSEMENT", "授支一体-放款", "CREDIT_FINANCING", 8);

    /**
     * 节点编码
     */
    private final String code;

    /**
     * 节点名称
     */
    private final String name;

    /**
     * 业务类型
     */
    private final String businessType;

    /**
     * 节点顺序
     */
    private final Integer order;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static BusinessNodeEnum getByCode(String code) {
        for (BusinessNodeEnum nodeEnum : values()) {
            if (nodeEnum.getCode().equals(code)) {
                return nodeEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(String code) {
        BusinessNodeEnum nodeEnum = getByCode(code);
        return nodeEnum != null ? nodeEnum.getName() : null;
    }

    /**
     * 根据业务类型获取节点列表
     *
     * @param businessType 业务类型
     * @return 节点列表
     */
    public static List<BusinessNodeEnum> getByBusinessType(String businessType) {
        return Arrays.stream(values())
                .filter(node -> node.getBusinessType().equals(businessType))
                .sorted((a, b) -> a.getOrder().compareTo(b.getOrder()))
                .collect(Collectors.toList());
    }

    /**
     * 根据业务类型获取第一个节点
     *
     * @param businessType 业务类型
     * @return 第一个节点
     */
    public static BusinessNodeEnum getFirstNodeByBusinessType(String businessType) {
        return getByBusinessType(businessType).stream()
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据业务类型获取总步骤数
     *
     * @param businessType 业务类型
     * @return 总步骤数
     */
    public static Integer getTotalStepsByBusinessType(String businessType) {
        return (int) Arrays.stream(values())
                .filter(node -> node.getBusinessType().equals(businessType))
                .count();
    }

    /**
     * 获取下一个节点
     *
     * @param currentNodeCode 当前节点编码
     * @return 下一个节点
     */
    public static BusinessNodeEnum getNextNode(String currentNodeCode) {
        BusinessNodeEnum currentNode = getByCode(currentNodeCode);
        if (currentNode == null) {
            return null;
        }

        List<BusinessNodeEnum> nodes = getByBusinessType(currentNode.getBusinessType());
        for (int i = 0; i < nodes.size() - 1; i++) {
            if (nodes.get(i).getCode().equals(currentNodeCode)) {
                return nodes.get(i + 1);
            }
        }
        return null;
    }

    /**
     * 获取上一个节点
     *
     * @param currentNodeCode 当前节点编码
     * @return 上一个节点
     */
    public static BusinessNodeEnum getPreviousNode(String currentNodeCode) {
        BusinessNodeEnum currentNode = getByCode(currentNodeCode);
        if (currentNode == null) {
            return null;
        }

        List<BusinessNodeEnum> nodes = getByBusinessType(currentNode.getBusinessType());
        for (int i = 1; i < nodes.size(); i++) {
            if (nodes.get(i).getCode().equals(currentNodeCode)) {
                return nodes.get(i - 1);
            }
        }
        return null;
    }
}
