package org.springblade.entry.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * AI识别合同信息数据传输对象
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Data
@ApiModel(value = "ContractInfoDTO对象", description = "AI识别合同信息数据传输对象")
public class ContractInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 合同基本信息 ====================

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同标题")
    private String contractTitle;

    @ApiModelProperty(value = "合同类型：1发票，2交割单，3合同，4采购订单，5销售订单")
    private Integer contractType;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "签署日期")
    private LocalDate signDate;

    @ApiModelProperty(value = "合同生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "合同到期日期")
    private LocalDate expireDate;

    @ApiModelProperty(value = "合同版本")
    private String contractVersion;

    @ApiModelProperty(value = "合同备注")
    private String contractRemark;

    // ==================== 买方信息 ====================

    @ApiModelProperty(value = "买方公司名称")
    private String buyerCompanyName;

    @ApiModelProperty(value = "买方统一社会信用代码")
    private String buyerCreditCode;

    @ApiModelProperty(value = "买方营业执照号")
    private String buyerBusinessLicense;

    @ApiModelProperty(value = "买方法定代表人")
    private String buyerLegalPerson;

    @ApiModelProperty(value = "买方联系人")
    private String buyerContactPerson;

    @ApiModelProperty(value = "买方联系电话")
    private String buyerContactPhone;

    @ApiModelProperty(value = "买方邮箱")
    private String buyerEmail;

    @ApiModelProperty(value = "买方传真")
    private String buyerFax;

    @ApiModelProperty(value = "买方注册地址")
    private String buyerRegisteredAddress;

    @ApiModelProperty(value = "买方办公地址")
    private String buyerOfficeAddress;

    @ApiModelProperty(value = "买方开户银行")
    private String buyerBankName;

    @ApiModelProperty(value = "买方银行账号")
    private String buyerBankAccount;

    // ==================== 卖方信息 ====================

    @ApiModelProperty(value = "卖方公司名称")
    private String sellerCompanyName;

    @ApiModelProperty(value = "卖方统一社会信用代码")
    private String sellerCreditCode;

    @ApiModelProperty(value = "卖方营业执照号")
    private String sellerBusinessLicense;

    @ApiModelProperty(value = "卖方法定代表人")
    private String sellerLegalPerson;

    @ApiModelProperty(value = "卖方联系人")
    private String sellerContactPerson;

    @ApiModelProperty(value = "卖方联系电话")
    private String sellerContactPhone;

    @ApiModelProperty(value = "卖方邮箱")
    private String sellerEmail;

    @ApiModelProperty(value = "卖方传真")
    private String sellerFax;

    @ApiModelProperty(value = "卖方注册地址")
    private String sellerRegisteredAddress;

    @ApiModelProperty(value = "卖方办公地址")
    private String sellerOfficeAddress;

    @ApiModelProperty(value = "卖方开户银行")
    private String sellerBankName;

    @ApiModelProperty(value = "卖方银行账号")
    private String sellerBankAccount;

    // ==================== 商品信息列表 ====================

    @ApiModelProperty(value = "商品信息列表")
    private List<ContractGoodsInfo> goodsList;

    // ==================== 金额信息 ====================

    @ApiModelProperty(value = "合同总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "定金金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "定金比例")
    private BigDecimal depositRatio;

    @ApiModelProperty(value = "尾款金额")
    private BigDecimal balanceAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amountExcludingTax;

    @ApiModelProperty(value = "含税金额")
    private BigDecimal amountIncludingTax;

    // ==================== 交货和付款信息 ====================

    @ApiModelProperty(value = "交货日期")
    private LocalDate deliveryDate;

    @ApiModelProperty(value = "交货地点")
    private String deliveryAddress;

    @ApiModelProperty(value = "交货方式")
    private String deliveryMethod;

    @ApiModelProperty(value = "运输方式")
    private String transportMethod;

    @ApiModelProperty(value = "运费承担方")
    private String freightBearer;

    @ApiModelProperty(value = "付款方式")
    private String paymentMethod;

    @ApiModelProperty(value = "付款期限（天）")
    private Integer paymentDays;

    @ApiModelProperty(value = "付款条件")
    private String paymentTerms;

    // ==================== 质量和验收信息 ====================

    @ApiModelProperty(value = "质量标准")
    private String qualityStandard;

    @ApiModelProperty(value = "验收标准")
    private String acceptanceStandard;

    @ApiModelProperty(value = "验收期限（天）")
    private Integer acceptanceDays;

    @ApiModelProperty(value = "质保期（天）")
    private Integer warrantyDays;

    // ==================== 违约和争议解决 ====================

    @ApiModelProperty(value = "违约责任条款")
    private String breachClause;

    @ApiModelProperty(value = "违约金比例")
    private BigDecimal penaltyRate;

    @ApiModelProperty(value = "争议解决方式")
    private String disputeResolution;

    @ApiModelProperty(value = "管辖法院")
    private String jurisdiction;

    // ==================== 其他条款 ====================

    @ApiModelProperty(value = "特殊条款")
    private String specialClause;

    @ApiModelProperty(value = "补充协议")
    private String supplementaryAgreement;

    @ApiModelProperty(value = "不可抗力条款")
    private String forceMajeureClause;

    @ApiModelProperty(value = "保密条款")
    private String confidentialityClause;

    // ==================== 附件信息 ====================

    @ApiModelProperty(value = "合同文件URL")
    private String contractFileUrl;

    @ApiModelProperty(value = "附件列表")
    private List<ContractAttachment> attachmentList;

    // ==================== AI识别相关 ====================

    @ApiModelProperty(value = "AI识别置信度")
    private BigDecimal confidence;

    @ApiModelProperty(value = "识别时间")
    private LocalDateTime recognitionTime;

    @ApiModelProperty(value = "识别状态：1成功，2部分成功，3失败")
    private Integer recognitionStatus;

    @ApiModelProperty(value = "识别错误信息")
    private String recognitionError;

    // ==================== 内部类定义 ====================

    /**
     * 合同商品信息
     */
    @Data
    @ApiModel(value = "ContractGoodsInfo", description = "合同商品信息")
    public static class ContractGoodsInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "商品序号")
        private Integer goodsIndex;

        @ApiModelProperty(value = "商品名称")
        private String goodsName;

        @ApiModelProperty(value = "商品编码")
        private String goodsCode;

        @ApiModelProperty(value = "商品型号")
        private String goodsModel;

        @ApiModelProperty(value = "商品规格")
        private String goodsSpec;

        @ApiModelProperty(value = "商品品牌")
        private String goodsBrand;

        @ApiModelProperty(value = "商品分类")
        private String goodsCategory;

        @ApiModelProperty(value = "计量单位")
        private String unit;

        @ApiModelProperty(value = "数量")
        private BigDecimal quantity;

        @ApiModelProperty(value = "单价")
        private BigDecimal unitPrice;

        @ApiModelProperty(value = "总价")
        private BigDecimal totalPrice;

        @ApiModelProperty(value = "税率")
        private BigDecimal taxRate;

        @ApiModelProperty(value = "含税单价")
        private BigDecimal unitPriceIncludingTax;

        @ApiModelProperty(value = "含税总价")
        private BigDecimal totalPriceIncludingTax;

        @ApiModelProperty(value = "不含税单价")
        private BigDecimal unitPriceExcludingTax;

        @ApiModelProperty(value = "不含税总价")
        private BigDecimal totalPriceExcludingTax;

        @ApiModelProperty(value = "商品描述")
        private String goodsDescription;

        @ApiModelProperty(value = "质量要求")
        private String qualityRequirement;

        @ApiModelProperty(value = "技术标准")
        private String technicalStandard;

        @ApiModelProperty(value = "交货期限")
        private LocalDate deliveryDeadline;

        @ApiModelProperty(value = "备注")
        private String remark;
    }

    /**
     * 合同附件信息
     */
    @Data
    @ApiModel(value = "ContractAttachment", description = "合同附件信息")
    public static class ContractAttachment implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "附件名称")
        private String attachmentName;

        @ApiModelProperty(value = "附件类型")
        private String attachmentType;

        @ApiModelProperty(value = "附件URL")
        private String attachmentUrl;

        @ApiModelProperty(value = "附件大小（字节）")
        private Long attachmentSize;

        @ApiModelProperty(value = "附件描述")
        private String attachmentDescription;

        @ApiModelProperty(value = "上传时间")
        private LocalDateTime uploadTime;
    }
}
