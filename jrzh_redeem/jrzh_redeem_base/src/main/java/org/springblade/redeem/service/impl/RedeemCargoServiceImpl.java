package org.springblade.redeem.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.GoodsConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.enums.purchase.PurchaseEnum;
import org.springblade.common.enums.rabbitmq.RabbitMqStatusEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.feign.ICustomerSuperviseClient;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositSecondaryService;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.service.IExpenseOrderDetailService;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expensecommon.dto.expenseDeposit.ExpenseDepositRefundDTO;
import org.springblade.expensecommon.vo.expenseDeposit.ExpenseDepositRefundBillVO;
import org.springblade.finance.constant.ProcessTypeAndProcessFourEnum;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.IFinanceRepaymentService;
import org.springblade.finance.vo.CostCalculusVO;
import org.springblade.finance.vo.ExpenseOrderDetailFinanceVo;
import org.springblade.ledger.entity.SubLedgerRelation;
import org.springblade.ledger.mapper.SubLedgerRelationMapper;
import org.springblade.loan.dto.RepaymentPlanCal;
import org.springblade.loan.dto.RepaymentPlanReCalParam;
import org.springblade.loan.entity.LoanManageIou;
import org.springblade.loan.entity.LoanManageRepayment;
import org.springblade.loan.entity.RepaymentPlanFee;
import org.springblade.loan.enums.PayModeEnum;
import org.springblade.loan.service.*;
import org.springblade.loan.vo.AgentPurchaseChangeVO;
import org.springblade.loan.vo.StagRecordVO;
import org.springblade.message.dto.MessageSendDto;
import org.springblade.message.service.MessageSendService;
import org.springblade.mq.rabbitmq.DelayMessage;
import org.springblade.mq.rabbitmq.RabbitMsgSender;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.product.common.dto.ExpenseRuleDTO;
import org.springblade.product.common.entity.*;
import org.springblade.product.common.utils.GoodsFeeRulesUtil;
import org.springblade.product.common.vo.GoodsExpenseRelationVO;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.mapper.ExpenseTypeMapper;
import org.springblade.product.expense.service.IExpenseTypeService;
import org.springblade.product.expense.service.IGoodsExpenseRuleService;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.product.expense_relation.service.IGoodsExpenseRelationService;
import org.springblade.product.moudle.billbankcard.service.IBillBankCardaService;
import org.springblade.product.moudle.billbankcard.wrapper.BillBankCardaWrapper;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.moudle.timing.service.IGoodsTimingService;
import org.springblade.redeem.constant.RedeemConstant;
import org.springblade.redeem.dto.*;
import org.springblade.redeem.entity.RedeemCargo;
import org.springblade.redeem.entity.RedeemExpense;
import org.springblade.redeem.entity.RedeemSend;
import org.springblade.redeem.enums.ExtractTypeEnum;
import org.springblade.redeem.enums.RedeemCargoPayEnum;
import org.springblade.redeem.enums.RedeemCargoStatusEnum;
import org.springblade.redeem.enums.RedeemTypeEnum;
import org.springblade.redeem.mapper.RedeemCargoMapper;
import org.springblade.redeem.service.*;
import org.springblade.redeem.vo.*;
import org.springblade.redeem.wrapper.RedeemCargoWrapper;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.service.IRefundService;
import org.springblade.repayment.dto.LoanInfoDTO;
import org.springblade.repayment.dto.RepaymentInfoDTO;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteRegionService;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.warehouse.entity.WarehouseDetails;
import org.springblade.warehouse.handler.WarehouseHandler;
import org.springblade.warehouse.service.IRedemptionWarehouseEnteringService;
import org.springblade.warehouse.service.IWarehouseDetailsService;
import org.springblade.warehouse.service.IWarehouseService;
import org.springblade.warehouse.vo.WarehouseDetailsChildVO;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 赎货表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class RedeemCargoServiceImpl extends BaseServiceImpl<RedeemCargoMapper, RedeemCargo> implements IRedeemCargoService {

    private final IWarehouseDetailsService warehouseDetailsService;
    private final IGoodsExpenseRelationService goodsExpenseRelationService;
    private final IExpenseOrderService billExpenseOrderService;
    /**
     * 产品配置 接口
     */
    private final ProductDirector productDirector;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IGoodsExpenseRuleService goodsExpenseRuleService;
    private final ILoanManageIouService loanManageIouService;
    private final IFinanceRepaymentService financeRepaymentService;
    private final IFinanceApplyService financeApplyService;
    private final IRedeemExpenseService redeemExpenseService;
    private final IAttachService attachService;
    private final IRedeemSendService redeemSendService;
    private final RemoteRegionService regionService;
    private final RemoteUserService userService;
    private final IRedeemObjectionService redeemObjectionService;
    private final IRedemptionWarehouseEnteringService redemptionWarehouseEnteringService;
    private final IWarehouseService warehouseService;
    private final IExpenseOrderService expenseOrderService;
    private final IGoodsTimingService goodsTimingService;
    private final IExpenseDepositSecondaryService cashDepositService;
    private final MessageSendService messageSendService;
    private final IExpenseOrderDetailService platformExpensesService;
    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final IBillBankCardaService billBankCardaService;
    private final IExpenseTypeService expenseTypeService;
    private final ExpenseTypeMapper expenseTypeMapper;
    private final IRedeemAgentPurchaseChangeService redeemAgentPurchaseChangeService;
    private final ILoanManageRepaymentTermService loanManageRepaymentTermService;
    private final IBusinessProcessProductService businessProcessProductService;
    private final IRedeemUserService redeemUserService;
    private final ILoanManageRepaymentService loanManageRepaymentService;
    private final ICustomerSuperviseClient customerSuperviseClient;
    private final RabbitMsgSender rabbitMsgSender;
    private final IRefundService refundService;
    private final SubLedgerRelationMapper subLedgerRelationMapper;
    private final IRepaymentReCalBizService repaymentReCalBizService;
    private final IRepaymentBizService repaymentBizService;
    private final IExpenseDepositService expenseDepositService;
    private final IRepaymentPlanFinanceApplyBizService repaymentPlanFinanceApplyBizService;
    private static final int sum = 0;
    private final int ZERO = 0;
    private final BigDecimal HUNDRED = new BigDecimal("0.01");
    private final Map<String, WarehouseHandler> warehouseHandlerMap;

    @Override
    public IPage<RedeemCargoVO> selectRedeemCargoPage(IPage<RedeemCargoVO> page, RedeemCargoVO redeemCargo) {
        return page.setRecords(baseMapper.selectRedeemCargoPage(page, redeemCargo));
    }

    @Override
    public IPage<RedeemCargoVO> queryPage(RedeemCargoQueryDTO redeemCargo, Query query) {
        IPage<RedeemCargo> page = baseMapper.selectPage(Condition.getPage(query), Condition.getQueryWrapper(redeemCargo, RedeemCargo.class).lambda()
                .orderByDesc(RedeemCargo::getCreateTime));
        IPage<RedeemCargoVO> redeemCargoPage = RedeemCargoWrapper.build().pageVO(page);
        if (page.getTotal() == 0L) {
            return redeemCargoPage;
        }
        List<RedeemCargoVO> records = redeemCargoPage.getRecords();
        List<Long> stokIds = records.stream().map(RedeemCargoVO::getStockId).distinct().collect(Collectors.toList());
        List<Long> userIdList = records.stream().map(RedeemCargoVO::getCreateUser).distinct().collect(Collectors.toList());
        Map<Long, User> longUserMap = userService.getMapInId(userIdList).getData();
        Map<Long, WarehouseDetails> warehouseMap = warehouseDetailsService
                .lambdaQuery()
                .in(WarehouseDetails::getId, stokIds)
                .list()
                .stream()
                .collect(Collectors.toMap(WarehouseDetails::getId, e -> e));
        List<String> redeemNoList = records.stream().map(RedeemCargo::getRedeemNo).collect(Collectors.toList());
        //根据赎货单号查询退换货数量
        Map<String, AgentPurchaseChangeVO> changeGoodsMap = redeemAgentPurchaseChangeService.getChangeGoodsNum(redeemNoList);
        records.forEach(e -> {
            e.setGoodsSpec(warehouseMap.get(e.getStockId()).getGoodsSpec());
            e.setGoodsUnit(warehouseMap.get(e.getStockId()).getGoodsUnitValue());
            e.setPurchasePrice(warehouseMap.get(e.getStockId()).getPurchasePrice());
            e.setFinancingPrice(warehouseMap.get(e.getStockId()).getFinancingPrice());
            e.setPurchasePriceSum(NumberUtil.mul(warehouseMap.get(e.getStockId()).getPurchasePrice(), e.getNum()));
            e.setFinancingPriceSum(NumberUtil.mul(warehouseMap.get(e.getStockId()).getFinancingPrice(), e.getNum()));
            User user = longUserMap.get(e.getCreateUser());
            e.setCreateUserStr(ObjectUtil.isNotEmpty(user) ? user.getName() : "");
            e.setExchangeGoods(changeGoodsMap.get(e.getRedeemNo()).getChangeGoodsNum());
            e.setBackGoodsNum(changeGoodsMap.get(e.getRedeemNo()).getBackGoodsNum());
            if (StrUtil.isNotBlank(warehouseMap.get(e.getStockId()).getGoodsInfo())) {
                e.setGoodsUrl(JsonUtil.parse(warehouseMap.get(e.getStockId()).getGoodsInfo(), GoodsDetailsDTO.class).getLogo());
            }
        });
        return redeemCargoPage.setRecords(records);
    }


    @Override
    public Boolean saveRedeem(RedeemCargoDTO redeemCargoDTO) {
        if (ExtractTypeEnum.WOM_EXTRACT.getKey().equals(redeemCargoDTO.getExtractType())) {
            Assert.notNull(redeemCargoDTO.getRedeemUserDTO().getUsername(), "自提信息不能为空");
        }
        redeemCargoDTO.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_EXAMINEING.getKey());
        redeemCargoDTO.setRedeemNo(CodeUtil.generateCode(CodeEnum.FORECLOSURE_CODE));
        //发起赎货申请流程
        HashMap<String, Object> variables = MapUtil.newHashMap();
        //赎货费用计算
        //创建赎货费用信息
        RedeemCargoCalculationDTO redeemCargoCalculationDTO = new RedeemCargoCalculationDTO();
        redeemCargoCalculationDTO.setNum(redeemCargoDTO.getNum());
        redeemCargoCalculationDTO.setWarehouseId(redeemCargoDTO.getStockId());
        RedeemCargoCalculationVO redeemCargoCalculationVO = calculationRedeem(redeemCargoCalculationDTO);
        if (loanManageRepaymentService.hasPaying(redeemCargoCalculationVO.getRepayments())) {
            throw new ServiceException("已有一条赎货未处理完毕，等待赎货完毕后进行处理");
        }
        RepaymentInfoDTO repaymentInfoDTO = redeemCargoCalculationVO.getRepaymentInfoDTO();
        List<ExpenseOrderDetail> expenseOrderDetails = repaymentInfoDTO.listOrderDetailList();
        //构建还款信息
        CostCalculusVO costCalculusVO = buildCostCalculusVO(repaymentInfoDTO.getAmount(), repaymentInfoDTO.getShouldInterest(), repaymentInfoDTO.getAnnualInterestRate(), repaymentInfoDTO.getDayRate(), expenseOrderDetails);
        variables.put(ProcessConstant.COST_CALCULUS_VO, costCalculusVO);
        variables.put(RedeemConstant.REDEEMNO, redeemCargoDTO.getRedeemNo());
        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
        FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargoDTO.getFinancingNo());
        if (financeApply.getGoodsType().equals(GoodsEnum.ORDER_FINANCING.getCode())) {
            //订单融资类型流程
            redeemCargoDTO.setProcessInstanceId(businessProcessProductService.startProcess(financeApply.getGoodsId(), ProcessTypeAndProcessFourEnum.PURCHASE_REDEMPTION_APPLY_4.getCode(), variables));
        } else if (financeApply.getGoodsType().equals(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())) {
            //代采融资类型流程
            redeemCargoDTO.setProcessInstanceId(businessProcessProductService.startProcess(financeApply.getGoodsId(), ProcessAgencyTypeEnum.AGENCY_APPLY.getCode(), variables));
        }
        redeemCargoDTO.setApplyUser(AuthUtil.getClaimsParam(WebUtil.getRequest(), "personalUserId", Long.class));
        save(redeemCargoDTO);
        //自提
        if (ExtractTypeEnum.WOM_EXTRACT.getKey().equals(redeemCargoDTO.getExtractType())) {
            redeemCargoDTO.getRedeemUserDTO().setCargoId(redeemCargoDTO.getId());
            redeemUserService.save(redeemCargoDTO.getRedeemUserDTO());
        }
        redeemCargoCalculationVO.getManExpenseCulationVoList().forEach(e -> e.setRedeemNo(redeemCargoDTO.getRedeemNo()));
        redeemCargoCalculationVO.getPlaExpenseCulationVoList().forEach(e -> e.setRedeemNo(redeemCargoDTO.getRedeemNo()));
        redeemExpenseService.saveBatch(redeemCargoCalculationVO.getManExpenseCulationVoList());
        redeemExpenseService.saveBatch(redeemCargoCalculationVO.getPlaExpenseCulationVoList());

        //发送消息 TODO
        MessageSendDto messageSend = new MessageSendDto();
        messageSend.setTemplateCode("J123364506447009");
        messageSend.setUserId(financeApply.getUserId().toString());
        HashMap<String, Object> map = MapUtil.newHashMap();
        messageSend.setParam(map);
        messageSendService.sendMessage(messageSend);

        return warehouseDetailsService.operateFormInToRedemption(redeemCargoDTO.getStockId(), redeemCargoDTO.getNum(), false);
    }

    @Override
    public CostCalculusVO buildCostCalculusVO(BigDecimal principal, BigDecimal interest, BigDecimal annualInterest, BigDecimal dayRate, List<ExpenseOrderDetail> expenseOrderDetails) {
        List<ExpenseOrderDetailFinanceVo> expenseOrderDetailFinanceVos = repaymentPlanFinanceApplyBizService.buildExpenseOrderDetailFinanceVo(expenseOrderDetails);
        //创建本金利息数据结构
        RepaymentPlanCal repaymentPlanCal = repaymentPlanFinanceApplyBizService.buildRepaymentPlanCal(principal, interest, annualInterest, dayRate);
        CostCalculusVO costCalculusVO = new CostCalculusVO();
        costCalculusVO.setShowRepaymentPlan(repaymentPlanCal);
        costCalculusVO.setExpenseOrderDetailFinanceVos(expenseOrderDetailFinanceVos);
        return costCalculusVO;
    }

    @Override
    public RedeemCargoCalculationVO calculationRedeem(RedeemCargoCalculationDTO redeemCargoCalculationDTO) {
        RedeemCargoCalculationVO redeemCargoCalculationVO = new RedeemCargoCalculationVO();
        WarehouseDetailsChildVO warehouseDetailsChildVO = getWarehousDetail(redeemCargoCalculationDTO.getWarehouseId());
        String financingCode = warehouseDetailsChildVO.getFinanceNo();
        ExpenseDeposit cashDeposit = expenseDepositService.getCashDeposit(financingCode);
        //借款天数计算
        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financingCode);
        if (loanManageIou == null) {
            throw new ServiceException("业务数据不全,请联系管理员");
        }
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financingCode);
        //本次还款金额=数量*单价
        BigDecimal repaymentAmount = warehouseDetailsChildVO.getFinancingPrice().multiply(BigDecimal.valueOf(redeemCargoCalculationDTO.getNum()));

        LoanInfoDTO loanInfoDTO = repaymentBizService.getLoanInfoDTO(loanManageIou.getIouNo());
        if (loanInfoDTO.getRepaymentInfoList() == null) {
            throw new ServiceException("还款计划不存在,请重新走新单");
        }
        redeemCargoCalculationVO.setRepayments(loanInfoDTO.listLoanManageRepayment());
        // 质押产品走的随借随还因此拿第一条
        RepaymentInfoDTO first = repaymentBizService.getRepaymentInfo(CollUtil.getFirst(loanInfoDTO.getRepaymentInfoList()), repaymentAmount);


        JSONObject attachVar = new JSONObject();
        attachVar.putOnce("num", redeemCargoCalculationDTO.getNum());
        //不需缴纳保证金则不需填充
        if (Objects.nonNull(cashDeposit)) {
            attachVar.putOnce("margin", cashDeposit.getPayableAmount());
        }
        attachVar.putOnce("purchasePrice", warehouseDetailsChildVO.getPurchasePrice());
        attachVar.putOnce("financingPrice", warehouseDetailsChildVO.getFinancingPrice());
        RepaymentPlanReCalParam build = first.hasOverDue(LocalDate.now()) ? overDueRepaymentCal(attachVar) : normalRepaymentCal(attachVar);
        first = repaymentReCalBizService.reCalRepaymentInfoDTO(build, first);
        redeemCargoCalculationVO.setShouldTotal(first.getShouldInterest().add(first.getAmount()).add(first.calSubOtherFee()));
        redeemCargoCalculationVO.setShouldInterest(first.getShouldInterest());
        redeemCargoCalculationVO.setPrincipal(repaymentAmount);
        redeemCargoCalculationVO.setRepaymentInfoDTO(first);
//        // 借款天数
//        Integer period = loanManageIou.getPeriod();
//        //提前还款天数
//        long advanceDays = loanManageIou.getExpireTime().toEpochDay() - LocalDate.now().toEpochDay();
//        //逾期天数计算
//        int overdueDays = DateUtil.between(loanManageIou.getExpireTime(), LocalDate.now()).getDays();
//        overdueDays = Math.max(overdueDays, ZERO);

        // 日利率、年利率、保证金比例、借款本金

//        BigDecimal dailyInterestRate = financeApply.getDailyInterestRate();
//        BigDecimal annualInterestRate = financeApply.getAnnualInterestRate();
//        BigDecimal bondPayProportion = financeApply.getBondPayProportion();
//        BigDecimal amount = financeApply.getAmount();
//        //应还本金计算 赎货数量 * 融资金额
//        BigDecimal principal = new BigDecimal(redeemCargoCalculationDTO.getNum()).multiply(warehouseDetailsChildVO.getFinancingPrice());

//        // 计息天数
//        long interestAccrualDays = LocalDate.now().toEpochDay() - loanManageIou.getLoanTime().toEpochDay() + 1;
//
//        //已还本金
//        BigDecimal overPrincipal = BigDecimal.ZERO;
//        List<LoanManageRepaymentPlan> loanManageRepaymentPlans =
//                loanManageRepaymentPlanService.lambdaQuery()
//                        .eq(LoanManageRepaymentPlan::getFinanceApplyId, financeApply.getId()).list();
//        if (!loanManageRepaymentPlans.isEmpty()) {
//            //剩余本金 = 本金 - 已还本金
//            List<LoanManageRepayment> repayment = loanManageRepaymentTermService.getPayedRepaymentByPlanId(loanManageRepaymentPlans.get(0).getId());
//            BigDecimal actualPrincipal = repayment.stream().map(LoanManageRepayment::getActualPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal shouldPrincipal = loanManageRepaymentPlans.stream().map(LoanManageRepaymentPlan::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
//            overPrincipal = shouldPrincipal.subtract(actualPrincipal);
//        }

//        ExpenseRuleDTO expenseRuleDTO = ExpenseRuleDTO
//                .builder()
//                .principalRepayment(principal)
//                .loanPrincipal(amount)
//                .purchasePrices(warehouseDetailsChildVO.getPurchasePrice())
//                .financingPrices(warehouseDetailsChildVO.getFinancingPrice())
//                .interestAccrualDays(new BigDecimal(interestAccrualDays))
//                .loanDays(new BigDecimal(period))
//                .count(new BigDecimal(redeemCargoCalculationDTO.getNum()))
//                .dayRate(dailyInterestRate.multiply(HUNDRED))
//                .yearRate(annualInterestRate.multiply(HUNDRED))
//                .marginRatio(bondPayProportion.multiply(HUNDRED))
//                .serviceRate(financeApply.getServiceRate().multiply(HUNDRED))
//                .loanPeriods(new BigDecimal(1))
//                //TODO   费用金额 增值税率
//                .prepaymentDays(new BigDecimal(advanceDays).compareTo(BigDecimal.ZERO) >= 0 ? new BigDecimal(advanceDays) : BigDecimal.ZERO)
//                .feeAmount(new BigDecimal(1))
//                .taxRate(new BigDecimal(1))
//                .surplusPrincipalRepayment(overPrincipal)
//                .overdueDays(new BigDecimal(overdueDays))
//                .build();

//        List<Long> manRuleIds = getRuleIds(financeApply.getGoodsId(), ExpenseConstant.FeeTypeEnum.CAPITAL_CHARGE.getCode());
//        List<Long> plaRuleIds = getRuleIds(financeApply.getGoodsId());
//        if (!manRuleIds.isEmpty()) {
//            redeemCargoCalculationVO
//                    .setManExpenseCulationVoList(
//                            expenseCulation(
//                                    goodsExpenseRuleService
//                                            .lambdaQuery()
//                                            .in(GoodsExpenseRule::getId, manRuleIds).list(), expenseRuleDTO, ExpenseConstant.FeeTypeEnum.CAPITAL_CHARGE.getCode(), overdueDays, financeApply.getGoodsId()));
//        }
//
//        if (!plaRuleIds.isEmpty()) {
//            redeemCargoCalculationVO
//                    .setPlaExpenseCulationVoList(
//                            expenseCulation(
//                                    goodsExpenseRuleService.selectExpenseRuleList(financeApply.getGoodsId()), expenseRuleDTO, GoodsEnum.ALONE.getCode(), overdueDays, financeApply.getGoodsId()));
//        }
        redeemCargoCalculationVO.setManExpenseCulationVoList(CollUtil.newArrayList());
        redeemCargoCalculationVO.setPlaExpenseCulationVoList(CollUtil.newArrayList());
        //获取代采还款计划关联费用表
        List<RepaymentPlanFee> feePlanList = first.getRepaymentPlanFeeList();

        if (CollUtil.isNotEmpty(feePlanList)) {
            //产品费用关联
            List<Long> expenseIds = feePlanList.stream().map(RepaymentPlanFee::getExpenseTypeId).collect(Collectors.toList());
            List<GoodsExpenseRelation> goodsExpenseRelations = goodsExpenseRelationService.getBillBankCardaByGoodsIdAndExpenseId(financeApply.getGoodsId(), expenseIds);
            Map<Long, GoodsExpenseRelation> goodsExpenseRelationMap = goodsExpenseRelations.stream().collect(Collectors.toMap(GoodsExpenseRelation::getExpenseTypeId, e -> e));
//            //产品账户关联
//            List<BillBankCardaRelation> cardaRelationList = billBankCardaRelationService.list(Wrappers.<BillBankCardaRelation>lambdaQuery().eq(BillBankCardaRelation::getGoodsId, financeApply.getGoodsId()));
//            Map<Integer, BillBankCardaRelation> cardaRelationMap = StreamUtil.toMap(cardaRelationList, BillBankCardaRelation::getExpenseKey, obj -> obj);

            for (RepaymentPlanFee planFee : feePlanList) {
                ExpenseOrderDetail expenseOrderDetail = JSONUtil.toBean(planFee.getExpenseOrderDetailStr(), ExpenseOrderDetail.class);
//                //查询关联费用信息
//                ExpenseOrderDetail platformExpenses = platformExpensesService.getById(planFee.getRelationExpensesId());
                //构建赎货费用信息
                GoodsExpenseRelation goodsExpenseRelation = goodsExpenseRelationMap.get(planFee.getExpenseTypeId());
                RedeemExpense redeemExpense = transferToRedeemExpensesList(expenseOrderDetail, financeApply.getGoodsId(), planFee.getAmount(), goodsExpenseRelation.getId());
//                redeemExpense.setGoodsExpenseId(goodsExpenseRelation.getId());
//                redeemExpense.setType(goodsExpenseRelation.getType());
                //保存vo
                if (ExpenseConstant.FeeTypeEnum.CAPITAL_CHARGE.getCode().equals(planFee.getExpenseKey())) {
                    redeemCargoCalculationVO.getManExpenseCulationVoList().add(redeemExpense);
                } else {
                    redeemCargoCalculationVO.getPlaExpenseCulationVoList().add(redeemExpense);
                }
            }
        }
        return redeemCargoCalculationVO;
    }

    private static RepaymentPlanReCalParam overDueRepaymentCal(JSONObject attachVar) {
        List<Integer> feeNode = Arrays.asList(ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode(), ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode());
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .feeNode(feeNode)
                .reCalGenFee(true)
                .useExpenseCal(true)
                .needSubRepaymentFeeNode(Collections.singletonList(ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode()))
                .attachVar(attachVar)
                .currentNode(ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode()).build();
        return build;
    }

    private static RepaymentPlanReCalParam normalRepaymentCal(JSONObject attachVar) {
        List<Integer> feeNode = Collections.singletonList(ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode());
        RepaymentPlanReCalParam build = RepaymentPlanReCalParam.builder()
                .feeNode(feeNode)
                .reCalGenFee(true)
                .useExpenseCal(true)
                .attachVar(attachVar)
                .currentNode(ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode()).build();
        return build;
    }

    @Override
    public WarehouseDetailsChildVO fontWarehousDetail(Long warehouseId) {
        if (true) {
            throw new UnsupportedOperationException();
        }
//        WarehouseDetailsChildVO warehouseDetailsChildVO = getWarehousDetail(warehouseId);
//        PurchaseInformation purchaseInformation = purchaseInformationService
//                .getByFinanceNo(warehouseDetailsChildVO.getFinanceNo());
//        Optional.ofNullable(purchaseInformation).ifPresent(e -> {
//            warehouseDetailsChildVO.setExtractType(e.getPickUpManner());
//            warehouseDetailsChildVO.setAddress(e.getReceiveAddress());
//            warehouseDetailsChildVO.setExtractId(e.getId());
//        });
//        //查询质检报告
//
//
//        return warehouseDetailsChildVO;
        return null;
    }

    @Override
    public RedeemConfirmDetailCargoVO redeemConfirmDetail(String redemmNo) {
        if (true) {
            throw new UnsupportedOperationException();
        }
//        RedeemConfirmDetailCargoVO confirmDetailCargoVO = new RedeemConfirmDetailCargoVO();
//        RedeemCargo redeemCargo = lambdaQuery().eq(RedeemCargo::getRedeemNo, redemmNo).one();
//        confirmDetailCargoVO.setRedeemDetailCargoCurrencyVO(redeemDetailCargoCurrency(redeemCargo));
//        return confirmDetailCargoVO;
        return null;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean redeemConfirm(RedeemCargoConfirmDTO redeemCargoConfirmDTO) {

    /// /        //费用订单添加支付凭证
    /// /        List<RedeemCargoVoucherDTO> cargoVoucherDTOList = redeemCargoConfirmDTO.getRedeemCargoVoucherDTOS();
    /// /        //根据费用单号分组
    /// /        Map<String, ExpenseOrder> expenseOrderMap = expenseOrderService.getMapInExpenseNo(StreamUtil.map(cargoVoucherDTOList, RedeemCargoVoucherDTO::getExpenseOrderNo));
    /// /        String bankRepaymentVoucher = redeemCargoConfirmDTO.getBankRepaymentVoucher();
    /// /        //线下支付给对应的费用添加凭证
    /// /        if (CollectionUtil.isNotEmpty(cargoVoucherDTOList)) {
    /// /            for (RedeemCargoVoucherDTO cargoVoucherDTO : cargoVoucherDTOList) {
    /// /                if (Objects.isNull(cargoVoucherDTO.getExpenseOrderNo())) {
    /// /                    bankRepaymentVoucher = cargoVoucherDTO.getRepaymentVoucher();
    /// /                } else {
    /// /                    ExpenseOrder billExpenseOrder = expenseOrderMap.get(cargoVoucherDTO.getExpenseOrderNo());
    /// /                    billExpenseOrder.setPayAttachId(cargoVoucherDTO.getRepaymentVoucher());
    /// /                    expenseOrderService.updateById(billExpenseOrder);
    /// /                }
    /// /            }
    /// /        }
    /// /        //费用订单添加支付凭证
    /// /        RedeemCargo redeemCargo = lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo()).one();
    /// /        RedeemExpense redeemExpense = redeemExpenseService.getOne(Wrappers.<RedeemExpense>lambdaUpdate()
    /// /                .eq(RedeemExpense::getRedeemNo, redeemCargoConfirmDTO.getRedeemNo())
    /// /                .eq(RedeemExpense::getType, GoodsEnum.GOODS_TYPE_FUND.getCode())
    /// /                .last("limit 1"));
    /// /
    /// ///		BillExpenseOrder expenseOrder = expenseOrderService.getById(redeemCargo.getExpenseOrderId());
    /// ///		if (expenseOrder != null && !StrUtil.isNullOrUndefined(redeemCargoConfirmDTO.getPlatformFeeVoucher())) {
    /// ///			expenseOrder.setPayAttachId(redeemCargoConfirmDTO.getPlatformFeeVoucher());
    /// ///			expenseOrderService.updateById(expenseOrder);
    /// ///		}
    /// /        //还款记录添加支付凭证
    /// /        //资方是线上支付
    /// /        if (PayModeEnum.PAY_MODE_BELOW.getCode().equals(redeemExpense.getCostPayMode())) {
    /// /            LoanManageRepayment manageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
    /// /            if (manageRepayment != null && !StrUtil.isNullOrUndefined(bankRepaymentVoucher)) {
    /// /                manageRepayment.setVoucher(bankRepaymentVoucher);
    /// /                loanManageRepaymentService.updateById(manageRepayment);
    /// /            }
    /// /        }
    /// /        FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo());
    /// /        //发起赎货流程
    /// /        HashMap<String, Object> variables = MapUtil.newHashMap();
    /// /        variables.put(RedeemConstant.REDEEMNO, redeemCargo.getRedeemNo());
    /// /        variables.put(WfProcessConstant.PROCESS_NO, CodeUtil.generateCode(CodeEnum.PROCESS_NO));
    /// /        redeemCargo.setProcessInstanceId(startProcess(financeApply.getGoodsId(), ProcessAgencyTypeEnum.AGENCY_CONFIRM.getCode(), variables));
    /// /
    /// /        //赎货状态修改
    /// /        redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_CONFIRM_EXAMINEING.getKey());
    /// /        return updateById(redeemCargo);
//        return null;
//    }
    @Override
    public Boolean sendSave(RedeemSendDTO redeemSendDTO) {
        RedeemCargo redeemCargo = lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemSendDTO.getRedeemNo()).one();
        redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SEND.getKey());
        warehouseDetailsService.subtractRedemption(redeemCargo.getStockId(), redeemCargo.getNum());
        return redeemSendService.save(redeemSendDTO) && updateById(redeemCargo);
    }


    /**
     * 获取库存订单信息
     *
     * @param warehouseId 库存id
     * @return WarehouseDetailsChildVO
     */
    public WarehouseDetailsChildVO getWarehousDetail(Long warehouseId) {
        WarehouseDetails warehouseDetails = warehouseDetailsService.getById(warehouseId);
        WarehouseDetailsChildVO warehouseDetailsChildVO = BeanUtil.copy(warehouseDetails, WarehouseDetailsChildVO.class);
        //查询质检报告
        String testReportAttachId = warehouseDetails.getTestReportAttachId();
        if (StringUtils.isNotBlank(testReportAttachId)) {
            List<Attach> attachListByIds = attachService.getAttachListByIds(Func.toLongList(testReportAttachId));
            warehouseDetailsChildVO.setReportList(attachListByIds);
        }
        //TODO 提货信息
        return warehouseDetailsChildVO
                .setWarehouseAge(DateUtil.between(warehouseDetails.getWarehouseInDate(), LocalDate.now()).getDays())
                .setPurchasePriceSum(NumberUtil.mul(warehouseDetails.getPurchasePrice(), warehouseDetails.getWarehouseNum()))
                .setFinancingPriceSum(NumberUtil.mul(warehouseDetails.getFinancingPrice(), warehouseDetails.getWarehouseNum()))
                .setPrincipalPayable(warehouseDetailsChildVO.getFinancingPriceSum())
                .setGoodLogo(StrUtil.isNotBlank(warehouseDetails.getGoodsInfo()) ? JsonUtil.parse(warehouseDetails.getGoodsInfo(), GoodsDetailsDTO.class).getLogo() : "")
                ;

    }

    /**
     * 赎货费用计算
     *
     * @param expenseRulesList 费用信息
     * @return List<ExpenseCulationVo>
     */
    public List<RedeemExpense> expenseCulation(List<GoodsExpenseRule> expenseRulesList, ExpenseRuleDTO expenseRuleDTO, Integer type, int overdueDays, Long goodsId) {
        if (CollectionUtil.isEmpty(expenseRulesList)) {
            return Collections.emptyList();
        }
        //未逾期去除逾期利息
        if (overdueDays == ZERO) {
            expenseRulesList = expenseRulesList
                    .stream()
                    .filter(expenseRules -> !ExpenseConstant.ExpenseTypeEnum.OVERDUE_INTEREST.getCode().equals(expenseRules.getExpenseType())).collect(Collectors.toList());
        }
        //查询费用规则关联的费用类型
        List<GoodsExpenseRelation> expenseRelations = goodsExpenseRelationService.getByGoodsIdAndRuleId(goodsId, StreamUtil.map(expenseRulesList, GoodsExpenseRule::getId), type);

        List<BillBankCardaRelation> cardaRelationList = billBankCardaRelationService.list(Wrappers.<BillBankCardaRelation>lambdaQuery().eq(BillBankCardaRelation::getGoodsId, goodsId));
        Map<Integer, BillBankCardaRelation> cardaRelationMap = StreamUtil.toMap(cardaRelationList, BillBankCardaRelation::getExpenseKey, obj -> obj);
        List<RedeemExpense> expenseCulationVos = new ArrayList<>();
        Map<Long, GoodsExpenseRule> expenseRuleMap = expenseRulesList.stream().collect(Collectors.toMap(GoodsExpenseRule::getId, expenseRelation -> expenseRelation, (oldVal, newVal) -> oldVal));
        for (GoodsExpenseRelation expenseRelation : expenseRelations) {

            HashMap<Long, BigDecimal> map = MapUtil.newHashMap();
            HashMap<Long, BigDecimal> longBigDecimalHashMap = GoodsFeeRulesUtil.calculationRulesMap(expenseRulesList, expenseRuleDTO, map);
            GoodsExpenseRule rule = expenseRuleMap.get(expenseRelation.getGoodsExpenseRuleId());
            RedeemExpense redeemExpenseVO = new RedeemExpense();
            redeemExpenseVO.setName(rule.getName());
            redeemExpenseVO.setBusinessCategory(type);
            redeemExpenseVO.setExpenseType(ExpenseConstant.FeeTypeEnum.getValueByKey(rule.getExpenseType()));
            redeemExpenseVO.setCalculation(rule.getCalculation());
            redeemExpenseVO.setGoodsId(goodsId);

            //查询费用支付方式
            BillBankCardaRelation billBankCardaRelation = cardaRelationMap.get(expenseRelation.getType());
            redeemExpenseVO.setCostPayMode(Objects.nonNull(billBankCardaRelation) ? billBankCardaRelation.getPlatformCostPayMode() : PayModeEnum.PAY_MODE_BELOW.getCode());
            redeemExpenseVO.setAccountId(Objects.nonNull(billBankCardaRelation) ? billBankCardaRelation.getBillBankCardaId() : null);
            redeemExpenseVO.setGoodsExpenseId(expenseRelation.getId());
            redeemExpenseVO.setType(expenseRelation.getType());

            if (GoodsConstant.CALCULATION_ONE == rule.getCalculation()) {
                redeemExpenseVO.setFeeFormula(GoodsConstant.WRITE);
            } else {
                redeemExpenseVO.setFeeFormula(rule.getFeeFormulaName());
            }
            if (GoodsConstant.CALCULATION_TWO == rule.getCalculation()) {
                redeemExpenseVO.setMoney(longBigDecimalHashMap.get(rule.getId()));
            }
            expenseCulationVos.add(redeemExpenseVO);
        }
//		if (expenseRulesList != null) {
//			//TODO 借款本金 借款单价查询
////			expenseRuleDTO.setLoanPrincipal(new BigDecimal(1));
////			expenseRuleDTO.setServiceRate(new BigDecimal(10));
//			HashMap<Long, BigDecimal> map = MapUtil.newHashMap();
//			HashMap<Long, BigDecimal> longBigDecimalHashMap = GoodsFeeRulesUtil.calculationRulesMap(expenseRulesList, expenseRuleDTO, map);
//
//			Map<Long, List<GoodsExpenseRelation>> finalExpenseRelationMap = expenseRelationMap;
//			Map<Integer, BillBankCardaRelation> finalCardaRelationMap = cardaRelationMap;
//			expenseRulesList.forEach(rule -> {
//				List<GoodsExpenseRelation> goodsExpenseRelation = finalExpenseRelationMap.get(rule.getId());
//				RedeemExpense redeemExpenseVO = new RedeemExpense();
//				redeemExpenseVO.setName(rule.getName());
//				redeemExpenseVO.setBusinessCategory(type);
//				redeemExpenseVO.setExpenseType(ExpenseConstant.FeeTypeEnum.getValueByKey(rule.getExpenseType()));
//				redeemExpenseVO.setCalculation(rule.getCalculation());
//				redeemExpenseVO.setGoodsId(goodsId);
//				if (CollectionUtil.isNotEmpty(goodsExpenseRelation)){
//
//					//查询费用支付方式
//					BillBankCardaRelation billBankCardaRelation = finalCardaRelationMap.get(goodsExpenseRelation.getType());
//					redeemExpenseVO.setCostPayMode(Objects.nonNull(billBankCardaRelation)?billBankCardaRelation.getPlatformCostPayMode():PayModeEnum.PAY_MODE_BELOW.getCode());
//					redeemExpenseVO.setAccountId(Objects.nonNull(billBankCardaRelation)?billBankCardaRelation.getBillBankCardaId():null);
//					redeemExpenseVO.setGoodsExpenseRuleId(goodsExpenseRelation.getGoodsExpenseRuleId());
//					redeemExpenseVO.setType(goodsExpenseRelation.getType());
//				}
//				if (GoodsConstant.CALCULATION_ONE == rule.getCalculation()) {
//					redeemExpenseVO.setFeeFormula(GoodsConstant.WRITE);
//				} else {
//					redeemExpenseVO.setFeeFormula(rule.getFeeFormulaName());
//				}
//				if (GoodsConstant.CALCULATION_TWO == rule.getCalculation()) {
//					redeemExpenseVO.setMoney(longBigDecimalHashMap.get(rule.getId()));
//				}
//				expenseCulationVos.add(redeemExpenseVO);
//			});

        //	}
        return expenseCulationVos;
    }


    /**
     * 获取规则ID集
     *
     * @param productId 产品id
     * @param type      类型 平台费用or资金费用
     * @return List<Long>
     */
    private List<Long> getRuleIds(Long productId, Integer type) {
        List<GoodsExpenseRelationVO> expenseRelationList = goodsExpenseRelationService.listByGoodsId(productId);
        return expenseRelationList
                .stream()
                .filter(s -> s.getType().equals(type))
                .map(GoodsExpenseRelation::getGoodsExpenseRuleId)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<Long> getRuleIds(Long productId) {
        List<GoodsExpenseRelationVO> expenseRelationList = goodsExpenseRelationService.listByGoodsId(productId);
        return expenseRelationList
                .stream()
                .filter(s -> !s.getType().equals(ExpenseConstant.FeeTypeEnum.PLATFORM_CHARGE.getCode()))
                .map(GoodsExpenseRelation::getGoodsExpenseRuleId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public SubLedgerInfoVO getRedeemExpenseAndAccount(String redeemNo) {
        RedeemCargo redeemCargo = lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo).one();
        if (ObjectUtil.isEmpty(redeemCargo)) {
            throw new ServiceException("赎货订单不存在!");
        }
        WarehouseDetails warehouseDetails = warehouseDetailsService.getById(redeemCargo.getStockId());
        if (ObjectUtil.isEmpty(warehouseDetails)) {
            throw new ServiceException("库存订单不存在!");
        }
        //费用信息
        List<RedeemExpense> redeemExpenseList = redeemExpenseService
                .lambdaQuery()
                .eq(RedeemExpense::getRedeemNo, redeemCargo.getRedeemNo())
                .list();
        if (CollectionUtil.isEmpty(redeemExpenseList)) {
            return null;
        }
        //查询当前赎货的费用订单
        Map<String, ExpenseOrder> expenseOrderMap = expenseOrderService.getMapInBillExpenseNo(StreamUtil.map(redeemExpenseList, RedeemExpense::getExpenseOrderNo));

        BigDecimal financingPrice = warehouseDetails.getFinancingPrice();
        Integer num = redeemCargo.getNum();
        BigDecimal capitalAmount = financingPrice.multiply(BigDecimal.valueOf(num));
        //线下支付的费用
        List<RedeemExpense> belowRedeemExpenses = redeemExpenseList.stream()
                .filter(redeemExpense -> PayModeEnum.PAY_MODE_BELOW.getCode().equals(redeemExpense.getCostPayMode()))
                .collect(Collectors.toList());

        //线上支付的费用
        List<RedeemExpense> onlineRedeemExpenses = redeemExpenseList.stream()
                .filter(redeemExpense -> PayModeEnum.PAY_MODE_ONLINE.getCode().equals(redeemExpense.getCostPayMode()))
                .collect(Collectors.toList());
        //线下支付总额
        BigDecimal belowReduce = belowRedeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RedeemExpense belowRedeemExpense : belowRedeemExpenses) {
            if (belowRedeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                belowReduce = belowReduce.add(capitalAmount);
            }
        }
        //线上支付总额
        BigDecimal onlineReduce = onlineRedeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RedeemExpense onlineRedeemExpens : onlineRedeemExpenses) {
            //如果是资方费用需要将本金计算
            if (onlineRedeemExpens.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                onlineReduce = onlineReduce.add(capitalAmount);
                break;
            }
        }
        //查询费用对应的账户
        List<Long> bankCardaIds = StreamUtil.map(redeemExpenseList, RedeemExpense::getAccountId);
        List<BillBankCarda> billBankCardas = billBankCardaService.listByIds(bankCardaIds);
        Map<Long, BillBankCarda> bankCardaMap = StreamUtil.toMap(billBankCardas, BillBankCarda::getId, obj -> obj);
        //查询费用类型
        Map<Integer, ExpenseType> expenseTypeMap = expenseTypeService.getMapByExpenseType();
        //查询资方的支付凭证
        LoanManageRepayment loanManageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());

        //线下支付根据费用类型分组
        Map<Integer, List<RedeemExpense>> belowRedeemExpenseMap = StreamUtil.groupBy(belowRedeemExpenses, RedeemExpense::getType);
        //线下支付数据
        List<BelowPayExpensesVO> redeemCostVOList = Lists.newArrayList();
        for (Map.Entry<Integer, List<RedeemExpense>> entry : belowRedeemExpenseMap.entrySet()) {
            BelowPayExpensesVO redeemCostVO = new BelowPayExpensesVO();
            List<RedeemExpense> redeemExpenses = belowRedeemExpenseMap.get(entry.getKey());
            BigDecimal expenseReduce = redeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            RedeemExpense redeemExpense = redeemExpenses.get(0);
            ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExpense.getExpenseOrderNo());
            if (redeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                expenseReduce = expenseReduce.add(capitalAmount);
                redeemCostVO.setPrincipal(capitalAmount);
            }
            BillBankCarda billBankCarda = bankCardaMap.get(redeemExpense.getAccountId());
            ExpenseType expenseType = expenseTypeMap.get(redeemExpense.getType());
            redeemCostVO.setBillBankCarda(billBankCarda);
            redeemCostVO.setBelowExpenseVoList(redeemExpenses);
            redeemCostVO.setExpenseType(Objects.nonNull(expenseType) ? expenseType.getExpenseName() : null);
            redeemCostVO.setBelowAmount(expenseReduce);
            redeemCostVO.setBelowPayStatus(Objects.nonNull(billExpenseOrder) ? billExpenseOrder.getPaymentStatus() : null);
            redeemCostVO.setExpenseOrderNo(redeemExpense.getExpenseOrderNo());
            //线下支付凭证
            if (Objects.nonNull(billExpenseOrder) && StringUtil.isNotBlank(billExpenseOrder.getPayAttachId())) {
                redeemCostVO.setAttachList(attachService.listByIds(Func.toLongList(billExpenseOrder.getPayAttachId())));
            }
            //资方支付凭证
            if (ObjectUtil.isNotEmpty(loanManageRepayment) && StringUtil.isNotBlank(loanManageRepayment.getVoucher())) {
                redeemCostVO.setAttachList(attachService.listByIds(Func.toLongList(loanManageRepayment.getVoucher())));
            }
            redeemCostVO.setExpenseOrder(billExpenseOrder);
            redeemCostVOList.add(redeemCostVO);
        }
        //线上支付根据费用类型分组
        Map<Integer, List<RedeemExpense>> onlineRedeemExpenseMap = StreamUtil.groupBy(onlineRedeemExpenses, RedeemExpense::getType);
        //线上支付查询虚拟账户
        List<MergePay> mergePays = billBankCardas.stream()
                .map(e -> BillBankCardaWrapper.build().billToMergePay(e))
                .filter(e -> {
                    return StringUtil.isNotBlank(e.getMerchantNo());
                })
                .collect(Collectors.toList());
        Map<Long, MergePay> mergePayMap = StreamUtil.toMap(mergePays, MergePay::getId, mergePay -> mergePay);
        //是否包含资方费用  1.是 .2否
        int IsContainCapitalCost = GoodsEnum.IS_NO_HIGH_QUALITY.getCode();
        //线上支付数据
        List<OnlinePayExpense> redeemAccountVOList = Lists.newArrayList();
        for (Map.Entry<Integer, List<RedeemExpense>> entry : onlineRedeemExpenseMap.entrySet()) {
            OnlinePayExpense redeemAccountVO = new OnlinePayExpense();
            List<RedeemExpense> redeemExpenses = onlineRedeemExpenseMap.get(entry.getKey());
            BigDecimal expenseReduce = redeemExpenses.stream().map(RedeemExpense::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            RedeemExpense redeemExpense = redeemExpenses.get(0);
            ExpenseOrder billExpenseOrder = expenseOrderMap.get(redeemExpense.getExpenseOrderNo());
            if (redeemExpense.getBusinessCategory().equals(PayModeEnum.PAY_MODE_BELOW.getCode())) {
                expenseReduce = expenseReduce.add(capitalAmount);
                redeemAccountVO.setPrincipal(capitalAmount);
                IsContainCapitalCost = GoodsEnum.IS_HIGH_QUALITY.getCode();
            }
            MergePay mergePay = mergePayMap.get(redeemExpense.getAccountId());
            ExpenseType expenseType = expenseTypeMap.get(redeemExpense.getType());
            redeemAccountVO.setMergePay(mergePay);
            redeemAccountVO.setExpenseType(Objects.nonNull(expenseType) ? expenseType.getExpenseName() : null);
            redeemAccountVO.setOnlineAmount(expenseReduce);
            redeemAccountVO.setOnlineExpenseVoList(redeemExpenses);
            redeemAccountVO.setOnlinePayStatus(Objects.nonNull(billExpenseOrder) ? billExpenseOrder.getPaymentStatus() : null);
            redeemAccountVO.setExpenseOrder(billExpenseOrder);
            redeemAccountVOList.add(redeemAccountVO);
        }
        SubLedgerInfoVO redeemCostAccountVO = new SubLedgerInfoVO();
        redeemCostAccountVO.setBelowAmount(belowReduce);
        redeemCostAccountVO.setOnlineAmount(onlineReduce);
        redeemCostAccountVO.setBelowPayExpensesList(redeemCostVOList);
        redeemCostAccountVO.setOnlinePayExpenseList(redeemAccountVOList);
        redeemCostAccountVO.setIsContainCapitalCost(IsContainCapitalCost);
        redeemCostAccountVO.setOnlinePayStatus(Objects.nonNull(redeemCargo.getPaymentStatus()) ? redeemCargo.getPaymentStatus() : 1);
        return redeemCostAccountVO;
    }


    @Override
    public void updateRedeemCargo(String orderId, Map<Integer, List<Long>> redemptionStatusList) {
        if (redemptionStatusList.containsKey(PlatformAccountEnum.RedemptionAccountStatus.FAILURE_ACCOUNT_STATUS.getCode())) {
            SubLedgerRelation subLedgerRelation = subLedgerRelationMapper.selectOne(Wrappers.<SubLedgerRelation>lambdaQuery().eq(SubLedgerRelation::getOrderNo, orderId).last("limit 1"));
            RedeemCargo redeemCargo = baseMapper.selectOne(Wrappers.<RedeemCargo>lambdaQuery().eq(RedeemCargo::getRedeemNo, subLedgerRelation.getRedeemNo()));
            redeemCargo.setPaymentStatus(RedeemCargoPayEnum.PAYMENT_FAILED_STATUS.getCode());
            baseMapper.updateById(redeemCargo);
            //warehouseDetailsService.operateFormInToRedemption(redeemCargo.getStockId(), redeemCargo.getNum(), true);
        }
    }

    @Override
    public RedeemExpense transferToRedeemExpensesList(ExpenseOrderDetail expenseOrderDetail, Long goodsId, BigDecimal amount, Long expenseRelationId) {
        RedeemExpense redeemExpense = new RedeemExpense();
        redeemExpense.setMoney(amount);
        redeemExpense.setName(expenseOrderDetail.getName());
        redeemExpense.setBusinessCategory(expenseOrderDetail.getExpenseType());
        redeemExpense.setExpenseType(ExpenseConstant.ExpenseTypeEnum.getValueByKey(expenseOrderDetail == null ? null : expenseOrderDetail.getExpenseType()));
        redeemExpense.setCalculation(expenseOrderDetail.getCalculation());
        redeemExpense.setFeeFormula(expenseOrderDetail.getFeeFormulaName());

//                BillBankCardaRelation billBankCardaRelation = cardaRelationMap.get(planFee.getExpenseKey());
        redeemExpense.setGoodsId(goodsId);
//                redeemExpense.setCostPayMode(Objects.nonNull(billBankCardaRelation) ? billBankCardaRelation.getPlatformCostPayMode() : PayModeEnum.PAY_MODE_BELOW.getCode());
//                redeemExpense.setAccountId(Objects.nonNull(billBankCardaRelation) ? billBankCardaRelation.getBillBankCardaId() : null);
        redeemExpense.setCostPayMode(expenseOrderDetail.getCostPayMode());
        redeemExpense.setAccountId(expenseOrderDetail.getAccountId());

        redeemExpense.setGoodsExpenseId(expenseRelationId);
        redeemExpense.setType(expenseOrderDetail.getAccountType());
        return redeemExpense;
    }


    //修改代采订单状态
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changOrderStatus(RedeemCargo redeemCargo) {
        FinanceApply financeApply = financeApplyService.lambdaQuery().eq(FinanceApply::getFinanceNo, redeemCargo.getFinancingNo()).one();

        if (ObjectUtil.isEmpty(financeApply)) {
            throw new ServiceException("当前赎货单对应融资数据异常,请联系管理员");
        }
        Product product = productDirector.detailBase(financeApply.getGoodsId());
        boolean isOrderFinancing = product.getType().equals(GoodsTypeEnum.ORDER_FINANCING.getCode());
        //在途数量判断
        Integer num = warehouseDetailsService.getRedemptionNumByFinanceNo(redeemCargo.getFinancingNo());
        List<Integer> status = Arrays.asList(RedeemCargoStatusEnum.REDEEM_CARGO_SEND.getKey(), RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey(), RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey(),
                RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_EXTRACT.getKey(), RedeemCargoStatusEnum.REDEEM_CARGO_RELEASEPLEDGE.getKey());
        List<RedeemCargo> redeemCargoList = lambdaQuery().eq(RedeemCargo::getFinancingNo, redeemCargo.getFinancingNo()).in(RedeemCargo::getStatus, status).list().stream().filter(e -> !e.getRedeemNo().equals(redeemCargo.getRedeemNo())).collect(Collectors.toList());
        if (num > 0) {
            financeApply.setStatus(isOrderFinancing ? ProcessTypeAndProcessFourEnum.PURCHASE_STATUS_SEVEN_4.getCode() : PurchaseEnum.PURCHASE_STATUS_SEVEN.getCode());
        } else if (redemptionWarehouseEnteringService.existReadyToStorageNum(redeemCargo.getFinancingNo())) {
            //待入库数量大于0
            financeApply.setStatus(isOrderFinancing ? ProcessTypeAndProcessFourEnum.PURCHASE_STATUS_SIX_4.getCode() : PurchaseEnum.PURCHASE_STATUS_SIX.getCode());
        } else if (cn.hutool.core.util.ObjectUtil.isNotEmpty(redeemCargoList)) {
            //判断是否有审核中或未签收的
            return;
        } else {
            //到期时间
            LocalDate expireDate = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo()).getExpireTime().toLocalDate();
            //赎货时间
            LocalDate updateDate = redeemCargo.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            if (expireDate.equals(updateDate)) {
                financeApply.setStatus(isOrderFinancing? ProcessTypeAndProcessFourEnum.PURCHASE_STATUS_NINE_4.getCode() : PurchaseEnum.PURCHASE_STATUS_NINE.getCode());
            } else if (updateDate.isAfter(expireDate)) {
                financeApply.setStatus(isOrderFinancing? ProcessTypeAndProcessFourEnum.PURCHASE_STATUS_ELEVEN_4.getCode() : PurchaseEnum.PURCHASE_STATUS_ELEVEN.getCode());
            } else {
                financeApply.setStatus(isOrderFinancing? ProcessTypeAndProcessFourEnum.PURCHASE_STATUS_EIGHT_4.getCode() : PurchaseEnum.PURCHASE_STATUS_EIGHT.getCode());
            }

        }
        financeApplyService.updateById(financeApply);
        warehouseHandlerMap.values().forEach(handler -> {
            handler.redeemCargoSuccess(financeApply.getId());
        });

    }

    @Override
    public WarehouseDetails getWarehouseByRedeemNo(String redeemNo) {
        RedeemCargo redeemCargo = baseMapper.selectOne(Wrappers.<RedeemCargo>lambdaQuery()
                .eq(RedeemCargo::getRedeemNo, redeemNo).last("limit 1"));
        if (Objects.nonNull(redeemCargo)) {
            return warehouseDetailsService.getById(redeemCargo.getStockId());
        }
        return null;
    }

    @Override
    public Boolean overRegister(OverRegisterDTO overRegisterDTO) {
        RedeemCargo redeemCargo = getById(overRegisterDTO.getRedeemId());
        if (RedeemCargoStatusEnum.REDEEM_CARGO_SEND.getKey().equals(redeemCargo.getStatus())) {
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey());
            FinanceApply financeApply = financeApplyService.getByFinanceNo(redeemCargo.getFinancingNo());
            LocalDateTime countdownExpireTime = goodsTimingService.getByExpireTime(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_ACCEPTANCE.getCode());
            redeemCargo.setCountdownExpireTime(countdownExpireTime);

            updateById(redeemCargo);
            RedeemSend redeemSend = redeemSendService.lambdaQuery()
                    .eq(RedeemSend::getRedeemNo, redeemCargo.getRedeemNo())
                    .orderByDesc(BaseEntity::getCreateTime).last("limit 1").one();
            redeemSend.setArrivalAddress(overRegisterDTO.getArrivalAddress());
            redeemSend.setArrivalTime(overRegisterDTO.getArrivalTime());
            redeemSendService.updateById(redeemSend);
            // 发送mq延时消息，超时未操作 代采-待验收状态 则关闭该申请
            Integer seconds = goodsTimingService.getSecondsByGoodsIdAndType(financeApply.getGoodsId(), GoodsTimingEnum.GOODS_TIMING_PURCHASE_ACCEPTANCE.getCode());
            rabbitMsgSender.sendDelayMsg(DelayMessage.builder()
                    .messageType("purchase_redeemCargo_apply")
                    .msg(financeApply.getId().toString())
                    .seconds(seconds)
                    .status(RabbitMqStatusEnum.PLEDGE_REDEEMCARGO_APPLY.getCode())
                    .build());
        }
        return updateById(redeemCargo);
    }

    @Override
    public Boolean signForRegister(SignForRegisterDTO signForRegisterDTO) {
        RedeemCargo redeemCargo = getById(signForRegisterDTO.getId());
        if (RedeemCargoStatusEnum.REDEEM_CARGO_TOBE_EXTRACT.getKey().equals(redeemCargo.getStatus())) {
            redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
            redeemCargo.setReceivedDocument(signForRegisterDTO.getReceivedDocument());
            redeemCargo.setReceivedPeople(signForRegisterDTO.getReceivedPeople());
            redeemCargo.setReceivedTime(signForRegisterDTO.getReceivedTime());
            warehouseDetailsService.subtractRedemption(redeemCargo.getStockId(), redeemCargo.getNum());
            changOrderStatus(redeemCargo);

            //保证金释放
//            refundOfDeposit(redeemCargo.getFinancingNo(), redeemCargo.getRedeemNo());

        }
        return updateById(redeemCargo);
    }

    @Override
    public Boolean objectionHandle(RedeemObjectionDTO redeemObjectionDTO) {
        RedeemCargo redeemCargo = lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemObjectionDTO.getRedeemNo()).one();
        if (RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey().equals(redeemCargo.getStatus())) {
            redeemObjectionDTO.setStatus(redeemObjectionDTO.getHandleStatus());
            redeemCargo.setStatus(redeemObjectionDTO.getHandleStatus());
            redeemObjectionService.save(redeemObjectionDTO);
            if (RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey().equals(redeemObjectionDTO.getHandleStatus())) {
                changOrderStatus(redeemCargo);
            }
        }
        return updateById(redeemCargo);
    }

    @Override
    public Boolean changeStatus(ChangStatusDTO changStatusDTO) {
        RedeemCargo redeemCargo = getById(changStatusDTO.getId());
        if (RedeemTypeEnum.REDEEM_CARGO_DISSENT.getCode().equals(changStatusDTO.getStatus())) {
            //有异议
            if (RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey().equals(redeemCargo.getStatus())) {
                redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey());
            }
        } else if (RedeemTypeEnum.CONFIRM.getCode().equals(changStatusDTO.getStatus())) {
            //确认收货
            if (RedeemCargoStatusEnum.REDEEM_CARGO_SIGN_FOR.getKey().equals(redeemCargo.getStatus())
                    || RedeemCargoStatusEnum.REDEEM_CARGO_DISSENT.getKey().equals(redeemCargo.getStatus())) {
                redeemCargo.setStatus(RedeemCargoStatusEnum.REDEEM_CARGO_COMPLETED.getKey());
                redeemCargo.setReceivedTime(LocalDateTime.now());
                redeemCargo.setReceivedPeople(AuthUtil.getUser().getUserName());
                //保证金释放
//                refundOfDeposit(redeemCargo.getFinancingNo(), redeemCargo.getRedeemNo());
                changOrderStatus(redeemCargo);
            }

        }
        return updateById(redeemCargo);
    }


    public void refundOfDeposit(String financingNo, String redeemNo) {
        ExpenseDepositRefundDTO cashDeposit = cashDepositService.refundOfDepositData(financingNo);
        List<ExpenseDepositRefundBillVO> expenseDepositRefundBillVOList = cashDeposit.getExpenseDepositRefundBillVOList();
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financingNo);
        Product product = productDirector.detailBase(financeApply.getGoodsId());

        BigDecimal sum = new BigDecimal("0");
        for (int i = 0; i < expenseDepositRefundBillVOList.size(); i++) {
            ExpenseDepositRefundBillVO expenseDepositRefundBillVO = expenseDepositRefundBillVOList.get(i);
            if (Integer.valueOf("1").equals(expenseDepositRefundBillVO.getPayRefundType())) {
                sum = sum.add(expenseDepositRefundBillVO.getAmount());
            }
        }

        LoanManageIou loanManageIou = loanManageIouService.getByFinancingNo(financingNo);

        //根据赎货单号查询到还款记录的金额
        RedeemCargo redeemCargo = baseMapper.selectOne(Wrappers.<RedeemCargo>lambdaQuery().eq(RedeemCargo::getRedeemNo, redeemNo));

        LoanManageRepayment manageRepayment = loanManageRepaymentService.getById(redeemCargo.getRepaymentRecordId());
        BigDecimal repaymentTotal = loanManageRepaymentService.list(Wrappers.<LoanManageRepayment>lambdaQuery().eq(LoanManageRepayment::getStatus, 3)
                        .eq(LoanManageRepayment::getIouNo, loanManageIou.getIouNo())).stream().map(LoanManageRepayment::getPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果是信贷结清后退还查询还款记录是否全部还完
        if (product.getBondReleaseMode() == 1) {
            //应还总额
            RepaymentPlanCal repaymentPlanCal = financeRepaymentService.repaymentCalculationFinanceNo(financingNo);
            BigDecimal reduce = repaymentPlanCal.getStagRecords().stream().map(StagRecordVO::getMonthlyPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (reduce.compareTo(repaymentTotal) == 0) {
                //获取退还金额
                Refund refund = new Refund();
                //保证金退款
                refund.setRefundType(2);
                refund.setFinanceNo(financingNo);
                refund.setBillExpenseNo(cashDeposit.getDepositNo());
                refund.setUserId(cashDeposit.getFinancingUserId());
                refund.setRefundAmount(sum);
                refund.setPaymentMethod(cashDeposit.getPayType().toString());
                refund.setCashDepositRate(cashDeposit.getCashDepositRate());
                refund.setBondRefundType(2);
                refundService.refundSave(refund);
            }
            //如果是同还款比例等比还款，客户部分还款，保证金需要按照客户的还款比例进行退还
        } else if (product.getBondReleaseMode() == 2) {
            BigDecimal ratioAmount = manageRepayment.getPrincipal().multiply(cashDeposit.getCashDepositRate()).divide(new BigDecimal("100"));
            //获取退还金额
            Refund refund = new Refund();
            //保证金退款
            refund.setRefundType(2);
            refund.setFinanceNo(financingNo);
            refund.setBillExpenseNo(cashDeposit.getDepositNo());
            refund.setUserId(cashDeposit.getFinancingUserId());
            refund.setRefundAmount(ratioAmount);
            refund.setPaymentMethod(cashDeposit.getPayType().toString());
            refund.setCashDepositRate(cashDeposit.getCashDepositRate());
            refund.setBondRefundType(1);
            SpringUtil.getBean(IRefundService.class).refundSave(refund);
        }
    }
}
