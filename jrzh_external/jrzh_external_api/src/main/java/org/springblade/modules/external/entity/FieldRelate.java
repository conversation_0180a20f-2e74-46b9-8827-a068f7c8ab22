/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.external.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 系统业务字段关联表实体类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jrzh_field_relate")
@ApiModel(value = "FieldRelate对象", description = "系统业务字段关联表")
public class FieldRelate extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 外部系统业务字段
     */
    @ApiModelProperty(value = "外部系统业务字段")
    private String externalSystemField;
    /**
     * 3.0系统业务字段
     */
    @ApiModelProperty(value = "3.0系统业务字段")
    private String internalSystemField;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    /**
     * 系统类型
     * 1:B2B
     */
    @ApiModelProperty(value = "系统类型")
    private Integer systemType;

    /**
     * 外部系统账户id（标识id）
     */
    @ApiModelProperty(value = "外部系统账户id（标识id）")
    private Long memberId;



}
