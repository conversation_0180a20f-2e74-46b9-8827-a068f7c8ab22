/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.product.common.dto.*;
import org.springblade.product.common.entity.Product;

import java.util.List;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/3/29 18:13
 * @Description: 产品信息拓展表
 * @Version: 1.0
 */
@Data
public class ProductVO extends Product {
    private static final long serialVersionUID = 1L;
    /**
     * 资金logo
     */
    private String capitalLogo;
    /**
     * 资方名称
     */
    private String capitalName;
    /**
     * 操作人
     */
    private String operator;
    @ApiModelProperty(value = "标签id")
    private List<Long> labelIds;

    @ApiModelProperty(value = "标签列表")
    private List<GoodsLabelRelationVO> labelList;

    @ApiModelProperty(value = "产品开通流程")
    private List<GoodsOpeningProcessDTO> goodsOpeningProcesses;
    //
    @ApiModelProperty(value = "产品常见问题")
    private List<GoodsQuestionDTO> goodsQuestions;

    @ApiModelProperty(value = "定时器管理")
    private List<GoodsTimingDTO> goodsTimingList;

    @ApiModelProperty(value = "产品合同管理")
    private List<GoodsContractTemplateDTO> goodsContractTemplates;

    @ApiModelProperty(value = "产品资料")
    private List<GoodsMaterialDTO> goodsMaterials;

    @ApiModelProperty(value = "产品费用及关联账户信息")
    private List<GoodsExpenseBankDTO> goodsExpense;

    @ApiModelProperty(value = "保证金监管账户账户")
    private BillBankCardaRelationDTO cashDepositTakeBillBankCardas;

    @ApiModelProperty(value = "产品绑定流程")
    private List<GoodsProcessDTO> goodsProcessList;

//

}
