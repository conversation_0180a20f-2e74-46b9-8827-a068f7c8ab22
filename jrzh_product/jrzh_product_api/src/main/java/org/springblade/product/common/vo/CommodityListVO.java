/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.product.common.dto.AttachDTO;
import org.springblade.product.common.entity.CommodityList;

import java.math.BigDecimal;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GoodsListVO对象", description = "GoodsListVO对象")
public class CommodityListVO extends CommodityList {
	private static final long serialVersionUID = 1L;
	/**
	 * 附件列表
	 */
	private List<AttachDTO> attachList;
	/**
	 * 前端展示分类名字
	 */
	private String commodityCatalogueName;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 供应商logo图标
	 */
	private String supplierLogo;
	/**
	 * 图片地址
	 */
	private String img;
	/**
	 * 操作人
	 */
	private String operatorName;
	/**
	 * 商品规格列表
	 */
	private List<CommoditySpecVO> commoditySpecList;
	/**
	 * 商品对应规格中最低价格
	 */
	private BigDecimal minPrice;
	/**
	 * 商品对应规格中最低价格，拼接 "起"
	 */
	private String minPriceStr;
	/**
	 * 标签，1-全部，2-可代采，3-热门，4-新品
	 */
	private Integer tag;

	/**
	 * 所有规格列表中，最低的价格
	 */
	private BigDecimal allCommoditySpecMinPrice;

	/**
	 * 所有规格列表中，最高的价格
	 */
	private BigDecimal allCommoditySpecMaxPrice;

	/**
	 * 所有规格列表中，最低-最高的价格区间
	 */
	private String allCommoditySpecMinAndMaxPriceStr;
	/**
	 * 当前登录用户，绑定上游供应商状态，1-已绑定，0-未绑定
	 */
	private Integer bindUpStreamSupperStatus;
	/**
	 * 商品的计量单位
	 */
	private String commodityUnit;

	/***
	 * 查询状态 1-热门 2-新品
	 */
	private Integer queryState;

	/**
	 * 拼接最低价格~最高价格
	 *
	 * @return
	 */
	public String getAllCommoditySpecMinAndMaxPriceStr() {
		return getAllCommoditySpecMinPrice() + " ~ " + getAllCommoditySpecMaxPrice();
	}

	/**
	 * 最低价格拼接 起
	 *
	 * @return
	 */
	public String getMinPriceStr() {
		return this.minPrice + "起";
	}

	/**
	 * 未登录时的租户id
	 */
	private String tenantId;


	/**
	 * 最高融资占比
	 */
	@ApiModelProperty("最高融资比例")
	private BigDecimal financingProportion;
}
