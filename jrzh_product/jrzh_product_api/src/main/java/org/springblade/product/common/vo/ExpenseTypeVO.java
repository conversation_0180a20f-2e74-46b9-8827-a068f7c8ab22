/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.common.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.node.INode;
import org.springblade.product.common.entity.ExpenseType;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用类型视图实体类
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExpenseTypeVO对象", description = "费用类型")
public class ExpenseTypeVO extends ExpenseType implements INode<ExpenseTypeVO> {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 父节点ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long parentId;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<ExpenseTypeVO> children;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	@Override
	public List<ExpenseTypeVO> getChildren() {
		if (null == this.children) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	/**
	 * 企业类型
	 */
	@ApiModelProperty(value = "企业类型")
	private String enterpriseTypeStr;

	private Integer type;
	//TODO 待做 不应该出现在这
//	/**
//	 * 平台费用vo
//	 */
//	private	List<PlatformExpensesVO> platformExpensesVOS;

}
