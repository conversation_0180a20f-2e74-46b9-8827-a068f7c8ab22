
package org.springblade.finance.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.excel.FinanceApplyExcel;
import org.springblade.finance.vo.*;
import org.springblade.finance.vo.financeCommon.FinanceApplyCommonVo;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;


/**
 * 融资申请 服务类
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface IFinanceApplyService extends BaseService<FinanceApply> {

    /**
     * 自定义分页
     *
     * @param query           分页对象
     * @param financeApplyDTO 融资申请DTO对象
     * @return page
     */
    IPage<FinanceApplyVO> selectFinanceApplyPage(FinanceApplyDTO financeApplyDTO, Query query);


    /**
     * 根据融资id，查询融资详情数据
     *
     * @param id 融资id
     * @return 融资详情数据
     */
    FinanceApplyCommonVo financeDetail(Long id);

    /**
     * 根据融资ids，查询融资详情列表数据
     *
     * @param ids 融资id列表
     * @return 融资详情列表数据
     */
    List<FinanceApplyCommonVo> financeDetailList(List<Long> ids);

    /**
     * 根据融资编号查询
     *
     * @param financeNo 融资编号
     * @return FinanceApply
     */
    FinanceApply getByFinanceNo(String financeNo);

    /**
     * 流程终止返回金额
     *
     * @param financeApplyId 融资申请id
     */
    void processTerminalReturnAmount(Long financeApplyId);

    /**
     * @param customerGoodsIds
     * @Description: 关闭所有未放款应收账款的融资业务
     * @Author: wujm
     * @Date: 2023/5/27 19:15
     **/
    void closeAllRelatedReceivable(List<Long> customerGoodsIds);

    /**
     * 审批信息
     *
     * @param id 融资申请id
     * @return ApproveInfo
     * @throws ExecutionException   ExecutionException
     * @throws InterruptedException InterruptedException
     * @throws TimeoutException     TimeoutException
     */
    ApproveInfo selectBackApproveInfo(Long id) throws ExecutionException, InterruptedException, TimeoutException;


    /**
     * 借款信息
     *
     * @param id 融资申请id
     * @return LoanInfo
     */
    LoanInfo loanInfo(Long id);

    IPage<FinanceApplyStatusBackVo> getPage(Long userId, Integer type, Query query);

    Boolean changeFinanceStatus(String financeNo, Integer status);

    FinanceApplyInfo selectFinanceApplyInfo(Long id);

    List<Long> listRelationFinanceIds(FinanceApply financeApply);

    List<FinanceApplyExcel> export(FinanceApplyDTO financeApplyDTO);

    /**
     * 判断该用户在该产品下是否进行中的融资
     * @param goodsId
     * @param userId
     * @return
     */
    Boolean hasFinancing(Long goodsId, Long userId);

}
