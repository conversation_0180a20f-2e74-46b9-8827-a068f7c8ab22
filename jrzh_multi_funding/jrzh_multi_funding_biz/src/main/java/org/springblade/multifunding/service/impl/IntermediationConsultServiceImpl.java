package org.springblade.multifunding.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.VerificationCodeSupertube;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.service.impl.BladeServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.*;
import org.springblade.customer.service.*;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.multifunding.dto.AddCapitalSupplementMaterialDto;
import org.springblade.multifunding.dto.IntermediationConsultDto;
import org.springblade.multifunding.dto.IntermediationConsultVoQuery;
import org.springblade.multifunding.entity.CapitalApplyObtainInformation;
import org.springblade.multifunding.entity.CapitalSupplementMaterial;
import org.springblade.multifunding.entity.IntermediationConsult;
import org.springblade.multifunding.enums.CapitalApplyObtainInformationStatusEnum;
import org.springblade.multifunding.enums.CapitalSupplementMaterialStatusEnum;
import org.springblade.multifunding.mapper.IntermediationConsultMapper;
import org.springblade.multifunding.service.CapitalApplyObtainInformationService;
import org.springblade.multifunding.service.CapitalSupplementMaterialService;
import org.springblade.multifunding.service.IntermediationConsultService;
import org.springblade.multifunding.vo.IntermediationConsulVo;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.QualityProducts;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.product.moudle.qualityproduct.service.IQualityProductsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 居间咨询记录
 */
@Service
@RequiredArgsConstructor
public class IntermediationConsultServiceImpl extends BladeServiceImpl<IntermediationConsultMapper, IntermediationConsult> implements IntermediationConsultService {

    private final ICustomerMaterialService customerMaterialService;
    private final CapitalSupplementMaterialService capitalSupplementMaterialService;
    private final CapitalApplyObtainInformationService capitalApplyObtainInformationService;
    private final ICustomerFrontUserTypeService customerFrontUserTypeService;
    private final ICustomerService customerService;
    private final ICustomerInfoService customerInfoService;
    private final IQualityProductsService qualityProductsService;
    private final ProductDirector productDirector;
    private final IntermediationConsultMapper intermediationConsultMapper;

    /**
     * 居间发起咨询
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public Boolean submitConsult(IntermediationConsultDto dto) {
        Long userId = AuthUtil.getUserId();
        String userName = MyAuthUtil.getCompanyName();
        CustomerMaterial customerMaterial = dto.buildCustomerMaterial();
        customerMaterial.setUserId(userId);
        customerMaterialService.save(customerMaterial);
        String userPhone = this.getUserPhone(userId);
        // 客户补充资料id,保存后回填的
        Long customerMaterialId = customerMaterial.getId();
        IntermediationConsult intermediationConsult = IntermediationConsult.builder()
                .consultUserId(userId)
                .consultUserName(userName)
                .goodsId(dto.getGoodsId())
                .customerMaterialId(customerMaterialId)
                .goodsType(dto.getGoodsType())
                .phone(userPhone)
                .build();

        return this.save(intermediationConsult);
    }

    /**
     * 居间发起咨询
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public Boolean submitConsultBack(IntermediationConsultDto dto) {
        // 如果没有id则为新增
        if (ObjectUtil.isEmpty(dto.getId())) {
            CustomerMaterial customerMaterial = dto.buildCustomerMaterial();
            customerMaterial.setUserId(null);
            customerMaterialService.save(customerMaterial);
            // 客户补充资料id,保存后回填的
            Long customerMaterialId = customerMaterial.getId();
            IntermediationConsult intermediationConsult = IntermediationConsult.builder()
                    .consultUserId(null)
                    .consultUserName(dto.getConsultUserName())
                    .goodsId(dto.getGoodsId())
                    .customerMaterialId(customerMaterialId)
                    .goodsType(dto.getGoodsType())
                    .phone(dto.getPhone())
                    .businessType(dto.getConsultBusinessType())
                    .logo(dto.getLogo())
                    .isShowWatermark(dto.getIsShowWatermark())
                    .build();
            return this.save(intermediationConsult);
        } else {
            // 修改
            IntermediationConsult intermediationConsult = this.getById(dto.getId());
            if (ObjectUtil.isEmpty(intermediationConsult)) {
                new ServiceException("咨询记录不存在!");
            }
            CustomerMaterial customerMaterial = dto.buildCustomerMaterial();
            customerMaterial.setId(intermediationConsult.getCustomerMaterialId());
            customerMaterialService.updateById(customerMaterial);
            intermediationConsult.setPhone(dto.getPhone());
            intermediationConsult.setConsultUserName(dto.getConsultUserName());
            intermediationConsult.setBusinessType(dto.getConsultBusinessType());
            intermediationConsult.setLogo(dto.getLogo());
            intermediationConsult.setIsShowWatermark(dto.getIsShowWatermark());
            return this.updateById(intermediationConsult);
        }
    }

    /**
     * 平台端分页
     * @param query
     * @param voQuery
     * @return
     */
    @Override
    public IPage<IntermediationConsulVo> backPage(Query query, IntermediationConsultVoQuery voQuery) {
        IPage<IntermediationConsulVo> iPage = intermediationConsultMapper.backPage(Condition.getPage(query), voQuery);
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return iPage;
        }
        return iPage;
    }

    /**
     * 融资端分页
     * @param query
     * @param voQuery
     * @return
     */
    @Override
    public IPage<IntermediationConsulVo> frontPage(Query query, IntermediationConsultVoQuery voQuery) {
        Long userId = AuthUtil.getUserId();
        if (-1L != userId) {
            voQuery.setConsultUserId(AuthUtil.getUserId());
        }
        IPage<IntermediationConsulVo> iPage = intermediationConsultMapper.backPage(Condition.getPage(query), voQuery);
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return iPage;
        }
        return iPage;
    }

    /**
     * 增加资方需要的额外资料
     * @param dto
     * @return
     */
    @Override
    public Boolean addCapitalSupplementMaterial(AddCapitalSupplementMaterialDto dto) {
        // 保存资方需要的补充资料
        Long intermediationConsultId = dto.getIntermediationConsultId();
        IntermediationConsult intermediationConsult = this.getById(intermediationConsultId);
        if (ObjectUtil.isEmpty(intermediationConsult)) {
            throw new ServiceException("咨询记录有误!");
        }
        String userName = AuthUtil.getUserName();
        CapitalSupplementMaterial capitalSupplementMaterial = CapitalSupplementMaterial.builder()
                .materialName(userName + "_" + "补充资料")
                .materialDesc(dto.getMaterialDesc())
                .intermediationConsultId(intermediationConsultId)
                .goodsId(intermediationConsult.getGoodsId())
                .build();
        capitalSupplementMaterial.setStatus(CapitalSupplementMaterialStatusEnum.UN_SUBMIT.getCode());
        return capitalSupplementMaterialService.save(capitalSupplementMaterial);
    }

    /**
     * 平台端详情
     * @param id
     * @return
     */
    @Override
    public IntermediationConsulVo backDetail(Long id) {
        IntermediationConsulVo vo = intermediationConsultMapper.getVoById(id);
        if (ObjectUtil.isEmpty(vo)) {
            throw new ServiceException("记录不存在");
        }
        CustomerMaterial customerMaterial = customerMaterialService.getById(vo.getCustomerMaterialId());
        vo.setCustomerMaterial(customerMaterial);
        // 这里的数据会根据数据权限进行过滤的
        List<CapitalSupplementMaterial> list = capitalSupplementMaterialService.list(Wrappers.<CapitalSupplementMaterial>lambdaQuery()
                .eq(CapitalSupplementMaterial::getIntermediationConsultId, vo.getId())
        );
        vo.setCapitalSupplementMaterials(list);
        Long userId = AuthUtil.getUserId();
//        // 根据当前数据查询是否有审批通过, 如果通过了则获取融资用户主体个人账户的手机号
//        CapitalApplyObtainInformation information = capitalApplyObtainInformationService.getOne(Wrappers.<CapitalApplyObtainInformation>lambdaQuery()
//                .eq(CapitalApplyObtainInformation::getApplyUserId, userId)
//                .eq(CapitalApplyObtainInformation::getConsultUserId, vo.getConsultUserId())
//                .eq(CapitalApplyObtainInformation::getGoodsId, vo.getGoodsId())
//                .eq(CapitalApplyObtainInformation::getStatus, CapitalApplyObtainInformationStatusEnum.PASSED.getCode())
//        );
//        // 说明该对应的资方审批通过了
//        if (ObjectUtil.isNotEmpty(information)) {
//            // 根据用户去获取主体账号的手机号, 因为这里可能是个人用户也可能是融资企业
//            Long consultUserId = vo.getConsultUserId();
//            // 这里业务调整为了可以不用注册进行，故不需要从个人账户取获取手机号
//            this.fullPhone(vo, consultUserId);
//        }
        return vo;
    }

    /**
     * 资方申请获取用户资料
     * @param id
     * @return
     */
    @Override
    public Boolean applyObtainInformation(Long id) {
        IntermediationConsult intermediationConsult = this.getById(id);
        if (ObjectUtil.isEmpty(intermediationConsult)) {
            throw new ServiceException("咨询记录有误!");
        }
        Long userId = AuthUtil.getUserId();
        // 如果有待处理或已通过的数据则无需新增记录
        List<CapitalApplyObtainInformation> list = capitalApplyObtainInformationService.list(Wrappers.<CapitalApplyObtainInformation>lambdaQuery()
                .eq(CapitalApplyObtainInformation::getApplyUserId, userId)
                .eq(CapitalApplyObtainInformation::getConsultUserId, intermediationConsult.getConsultUserId())
                .eq(CapitalApplyObtainInformation::getGoodsId, intermediationConsult.getGoodsId())
                .in(CapitalApplyObtainInformation::getStatus, CapitalApplyObtainInformationStatusEnum.TO_BE_CONFIRMED.getCode(), CapitalApplyObtainInformationStatusEnum.PASSED.getCode())
        );
        if (CollectionUtil.isNotEmpty(list)) {
            for (CapitalApplyObtainInformation information : list) {
                if (CapitalApplyObtainInformationStatusEnum.TO_BE_CONFIRMED.getCode().equals(information.getStatus())) {
                    throw new ServiceException("已有申请中的数据,无需重复申请!");
                } else if (CapitalApplyObtainInformationStatusEnum.PASSED.getCode().equals(information.getStatus())) {
                    throw new ServiceException("已有获取联系方式,无需重复申请!");
                }
            }
        }
        CapitalApplyObtainInformation capitalApplyObtainInformation = CapitalApplyObtainInformation.builder()
                .applyUserId(userId)
                .consultUserId(intermediationConsult.getConsultUserId())
                .goodsId(intermediationConsult.getGoodsId())
                .build();
        capitalApplyObtainInformation.setStatus(CapitalApplyObtainInformationStatusEnum.TO_BE_CONFIRMED.getCode());
        return capitalApplyObtainInformationService.save(capitalApplyObtainInformation);
    }

    @Override
    public IntermediationConsulVo frontDetail(Long id) {
        IntermediationConsulVo vo = intermediationConsultMapper.getVoById(id);
        if (ObjectUtil.isEmpty(vo)) {
            throw new ServiceException("记录不存在");
        }
        CustomerMaterial customerMaterial = customerMaterialService.getById(vo.getCustomerMaterialId());
        vo.setCustomerMaterial(customerMaterial);
        // 这里的数据会根据数据权限进行过滤的
        List<CapitalSupplementMaterial> list = capitalSupplementMaterialService.list(Wrappers.<CapitalSupplementMaterial>lambdaQuery()
                .eq(CapitalSupplementMaterial::getIntermediationConsultId, vo.getId())
        );
        vo.setCapitalSupplementMaterials(list);
        return vo;
    }

    @Override
    public Object qualityProduct(String tenantId) {
        // 这里只查询居间的数据, 只查6个为原来的数据
        List<QualityProducts> qualityProductsList = qualityProductsService.list(Wrappers.<QualityProducts>lambdaQuery()
                .eq(QualityProducts::getStatus, GoodsEnum.ON_SHELF.getCode())
                .eq(QualityProducts::getProductsType, GoodsEnum.INTERMEDIATION_CONSULT_PRODUCT.getCode())
                .last(" limit 6")
        );
        if (ObjectUtil.isEmpty(qualityProductsList)) {
            return Collections.emptyList();
        }
        List<Long> productsIds = StreamUtil.map(qualityProductsList, e -> Func.toLong(e.getProductsId()));
        List<Product> products = productDirector.selectList(new HashMap<>(), GoodsEnum.INTERMEDIATION_CONSULT_PRODUCT.getCode(), productsIds);
        return products;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConsult(IntermediationConsultDto dto) {
        // 修改
        IntermediationConsult intermediationConsult = this.getById(dto.getId());
        if (ObjectUtil.isEmpty(intermediationConsult)) {
            new ServiceException("咨询记录不存在!");
        }
        CustomerMaterial customerMaterial = dto.buildCustomerMaterial();
        customerMaterial.setId(intermediationConsult.getCustomerMaterialId());
        Boolean updateCustomer = customerMaterialService.updateById(customerMaterial);
        Boolean updateCapital = true;
        List<CapitalSupplementMaterial> capitalSupplementMaterials = dto.getCapitalSupplementMaterials();
        if (CollectionUtil.isNotEmpty(capitalSupplementMaterials)) {
            for (CapitalSupplementMaterial supplementMaterial : capitalSupplementMaterials) {
                if (StringUtil.isNotBlank(supplementMaterial.getMaterialFileJson())) {
                    supplementMaterial.setStatus(CapitalSupplementMaterialStatusEnum.SUBMIT.getCode());
                }
            }
            updateCapital = capitalSupplementMaterialService.updateBatchById(capitalSupplementMaterials);
        }
        return updateCustomer && updateCapital;
    }

    private String getUserPhone(Long consultUserId) {
        CustomerFrontUserType frontUserType = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getUserId, consultUserId)
        );
        if (ObjectUtil.isNotEmpty(frontUserType)) {
            if (VerificationCodeSupertube.PERSONAL.getCode().equals(frontUserType.getType())) {
                // 如果是个人直接查询即可
                Customer customer = customerService.getByCompanyId(consultUserId);
                return customer.getPhone();
            } else if (VerificationCodeSupertube.ENTERPRISE.getCode().equals(frontUserType.getType())) {
                // 如果是企业需先查询企业再查询
                CustomerInfo customerInfo = customerInfoService.getByCompanyId(consultUserId);
                Customer customer = customerService.getById(customerInfo.getCustomerId());
                return customer.getPhone();
            } else {
                // 第三种企业下的人员情况暂未处理
            }
        }
        return null;
    }

}
