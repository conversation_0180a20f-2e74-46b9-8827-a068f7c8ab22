/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.RefundEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.expense.constant.BillConstant;
import org.springblade.expense.feign.IExpenseOrderFeign;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.mapper.RefundMapper;
import org.springblade.refund.service.IRefundActualService;
import org.springblade.refund.service.IRefundBaseService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 退款订单 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Service
@AllArgsConstructor
public class RefundBaseServiceImpl extends BaseServiceImpl<RefundMapper, Refund> implements IRefundBaseService {
    private final RemoteUserService remoteUserService;


    private IPage<Refund> getReFundIPage(Query query, Map<String, Object> ratingRecord) {
        String paymentMethod = null;
        String userName = null;
        //支付方式
        if (ratingRecord.containsKey(BillConstant.PAYMENT_METHOD)) {
            paymentMethod = ratingRecord.get(BillConstant.PAYMENT_METHOD).toString();
            ratingRecord.remove(BillConstant.PAYMENT_METHOD);
        }
        if (ObjectUtil.isNotEmpty(ratingRecord.get("userName"))) {
            userName = ratingRecord.get("userName").toString();
            ratingRecord.remove("userName");
        }
        QueryWrapper<Refund> queryWrapper = Condition.getQueryWrapper(ratingRecord, Refund.class);
        queryWrapper.lambda()
                .orderByDesc(Refund::getCreateTime);
        String finalPaymentMethod = paymentMethod;
        if (StrUtil.isNotBlank(finalPaymentMethod)) {
            queryWrapper.and(wrapper -> wrapper.in("payment_method", finalPaymentMethod));
        }
        if (ObjectUtil.isNotEmpty(userName)) {
            List<User> list = remoteUserService.userLike(userName).getData();
            if (ObjectUtil.isNotEmpty(list)) {
                List<Long> map = StreamUtil.map(list, User::getId);
                queryWrapper.and(wrapper -> wrapper.in("user_id", map));
            } else {
                return Condition.getPage(query);
            }
        }
        IPage<Refund> page = this.page(Condition.getPage(query), queryWrapper);
        return page;
    }

    /***
     * 新增退款费用
     * @param refund
     */
    @Override
    public boolean refundSave(Refund refund) {
        refund.setRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
        refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode());
        return save(refund);
    }

    @Override
    public boolean refundUpdateStatus(Long id, Integer status) {
        return update(Wrappers.<Refund>lambdaUpdate()
                .eq(Refund::getId, id)
                .set(Refund::getStatus, status));
    }


}
