/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.refund.entity.RefundActual;
import org.springblade.refund.mapper.RefundActualMapper;
import org.springblade.refund.service.IRefundActualService;
import org.springblade.refund.vo.RefundActualVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实际退款订单 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Service
public class RefundActualServiceImpl extends BaseServiceImpl<RefundActualMapper, RefundActual> implements IRefundActualService {

    @Override
    public IPage<RefundActualVO> selectRefundActualPage(IPage<RefundActualVO> page, RefundActualVO refundActual) {
        return page.setRecords(baseMapper.selectRefundActualPage(page, refundActual));
    }

    @Override
    public RefundActual getRefundActual(Long refundId) {
        RefundActual one = getOne(Wrappers.<RefundActual>lambdaQuery()
                .eq(RefundActual::getRefundId, refundId));
        return one;
    }

    @Override
    public RefundActual saveRefundActual(RefundActual refundActual) {
        save(refundActual);
        return refundActual;
    }

    @Override
    public RefundActual updateRefundActual(RefundActual refundActual) {
        updateById(refundActual);
        return refundActual;
    }

    @Override
    public Integer refundStatus(Long id) {
        RefundActual one = getOne(Wrappers.<RefundActual>lambdaQuery().eq(RefundActual::getId, id)
                .select(RefundActual::getStatus));
        if (ObjectUtil.isEmpty(one)) {
            throw new IllegalArgumentException("订单不存在");
        }
        return one.getStatus();
    }

    @Override
    public List<RefundActual> listByStatus(Integer status) {
        return list(Wrappers.<RefundActual>lambdaQuery().eq(RefundActual::getStatus, status));
    }


}
