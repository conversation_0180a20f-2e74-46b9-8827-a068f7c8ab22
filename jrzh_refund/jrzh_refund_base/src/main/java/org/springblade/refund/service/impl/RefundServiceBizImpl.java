/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.refund.service.impl;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.RefundEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.expense.service.IBillFinancialFlowBaseService;
import org.springblade.otherapi.core.dto.PayGoodsOrderRefundNotify;
import org.springblade.refund.dto.RefundActualDTO;
import org.springblade.refund.dto.RefundDTO;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;
import org.springblade.refund.entity.RefundActualAccount;
import org.springblade.refund.enums.RefundActualType;
import org.springblade.refund.handler.OriginRefundHandler;
import org.springblade.refund.handler.RefundPayHandler;
import org.springblade.refund.service.IRefundActualAccountService;
import org.springblade.refund.service.IRefundActualService;
import org.springblade.refund.service.IRefundBaseService;
import org.springblade.refund.service.IRefundBizService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 退款订单 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Service
@AllArgsConstructor
public class RefundServiceBizImpl implements IRefundBizService {

    private final IRefundBaseService refundService;
    private final IRefundActualService refundActualService;
    private final List<RefundPayHandler> refundPayHandlerList;
    private final List<OriginRefundHandler> originRefundHandlerList;
    private final IBillFinancialFlowBaseService billFinancialFlowService;
    private final IRefundActualAccountService refundActualAccountService;
    private final String NOTIFY_SUCCESS = "SUCCESS";
    private final String NOTIFY_FAIL = "FAIL";
    private final Integer REFUNDING_STATUS = 1;
    private final Integer REFUND_SUCCESS_STATUS = 2;
    private final Integer REFUND_FAIL_STATUS = 3;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRefund(RefundActualDTO refundActualDTO) {
        //原退款订单
        Long refundId = refundActualDTO.getRefundId();
        Refund refund = refundService.getById(refundId);
        //实际退款信息
        RefundActual refundActual = BeanUtil.copy(refundActualDTO, RefundActual.class);
        refundActual.setRefundActualType(RefundActualType.ORIGINAL_REFUND.getCode());
        Long refundActualAccountId = IdWorker.getId();
        refundActual.setRefundActualAccountId(refundActualAccountId);
        refundActual.setTradeType(refund.getTradeType());
        refundActual.setRefundMethod(Integer.valueOf(refund.getPaymentMethod()));
        refundActual.setAccountName(refundActualDTO.getEnterpriseName());
        refundActual.setOpenName(refundActualDTO.getEnterpriseName());
        refundActual.setBankCode(refund.getBankCode());
        //退款申请
        applyRefund(refund, refundActual);
        //保存实际还款账户
        String refundAccount = refundActualDTO.getEnterpriseName();
        String bankDeposit = refundActualDTO.getBankDeposit();
        RefundActualAccount refundActualAccount = new RefundActualAccount();
        refundActualAccount.setId(refundActualAccountId);
        refundActualAccount.setBankAccount(refundActualDTO.getBankCardNo());
        refundActualAccount.setBankDeposit(bankDeposit);
        refundActualAccount.setRefundActualId(refundActual.getId());
        refundActualAccount.setRefundAccount(refundAccount);
        refundActualAccount.setCreateUser(AuthUtil.getUserId());
        refundActualAccount.setCreateDept(Func.firstLong(AuthUtil.getDeptId()));
        refundActualAccountService.save(refundActualAccount);
        return true;
    }

    @Override
    public Refund cancelRefund(Long id) {
        Refund refund = refundService.getById(id);
        if (RefundEnum.RefundStatusEnum.STATUS_NOT.getCode().equals(refund.getStatus()) || RefundEnum.RefundStatusEnum.REFUND_FAIL.getCode().equals(refund.getStatus())) {
            refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode());
            refundService.changeStatus(Collections.singletonList(id), RefundEnum.RefundStatusEnum.CLOSE.getCode());
        }
        return refund;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RefundDTO applyRefund(Refund refund, RefundActual refundActual) {
        if (!canApplyRefund(refund.getStatus())) {
            throw new ServiceException("退款申请失败");
        }
        //补充实际退款信息
        fillRefundActual(refund, refundActual);
        //更新退款订单和退款实际订单
        refund.setStatus(RefundEnum.RefundStatusEnum.REFUNDING.getCode());
        refundService.updateById(refund);
        //保存实际退款
        refundActualService.saveRefundActual(refundActual);
        //按退款方式进行退款
        if (RefundActualType.ORIGINAL_REFUND.getCode().equals(refundActual.getRefundActualType())) {
            originalRefund(refund, refundActual);
        } else {
            throw new UnsupportedOperationException("未实现");
        }
        if (RefundEnum.RefundStatusEnum.REFUNDING.getCode().equals(refundActual.getStatus())) {
            //退款中
            refundActualService.updateRefundActual(refundActual);
        } else if (RefundEnum.RefundStatusEnum.STATUS_REFUND.getCode().equals(refundActual.getStatus())) {
            //退款成功
            passRefund(refundActual, refund);
        } else if (RefundEnum.RefundStatusEnum.REFUND_FAIL.getCode().equals(refundActual.getStatus())) {
            //退款失败
            failRefund(refundActual, refund);
        }
        RefundDTO refundDTO = BeanUtil.copyProperties(refund, RefundDTO.class);
        refundDTO.setRefundActual(refundActual);
        return refundDTO;
    }

    /**
     * 原路退款订单创建
     *
     * @param refund
     * @param refundActual
     * @return
     */
    private void originalRefund(Refund refund, RefundActual refundActual) {
        for (OriginRefundHandler originRefundHandler : originRefundHandlerList) {
            if (originRefundHandler.support().getStatus().equals(refundActual.getRefundMethod())) {
                originRefundHandler.refundCreate(refund, refundActual);
                return;
            }
        }
        throw new UnsupportedOperationException("原路退款策略类不存在");
    }

    private void fillRefundActual(Refund refund, RefundActual refundActual) {
        if (ObjectUtil.isEmpty(refundActual.getRefundActualType())) {
            refundActual.setRefundActualType(RefundActualType.ORIGINAL_REFUND.getCode());
        }
        if (RefundActualType.ORIGINAL_REFUND.getCode().equals(refundActual.getRefundActualType())) {
            refundActual.setTradeType(refund.getTradeType());
            refundActual.setBankCardNo(refund.getBankCardNo());
            refundActual.setBankDeposit(refund.getBankDeposit());
            refundActual.setBankCode(refund.getBankCode());
        }
        refundActual.setStatus(RefundEnum.RefundStatusEnum.REFUNDING.getCode());
    }

    private Boolean canApplyRefund(Integer status) {
        return RefundEnum.RefundStatusEnum.STATUS_NOT.getCode().equals(status) || RefundEnum.RefundStatusEnum.REFUND_FAIL.getCode().equals(status);
    }

    /**
     * 退款成功
     *
     * @param refundActual
     * @param refund
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void passRefund(RefundActual refundActual, Refund refund) {
        refundActual.setStatus(RefundEnum.RefundStatusEnum.STATUS_REFUND.getCode());
        refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_REFUND.getCode());
        refundService.refundUpdateStatus(refund.getId(), refund.getStatus());
        refundActualService.updateRefundActual(refundActual);
        //TODO 暂时认为退款成功即全部退款成功 执行成功方法
        for (RefundPayHandler refundPayHandler : refundPayHandlerList) {
            if (refundPayHandler.support().getCode().equals(refund.getRefundType())) {
                RefundDTO refundDTO = BeanUtil.copyProperties(refund, RefundDTO.class);
                refundDTO.setRefundActual(refundActual);
                refundPayHandler.refundSuccess(refundDTO);
                return;
            }
        }
    }


    /**
     * 退款失败
     *
     * @param refundActual
     * @param refund
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void failRefund(RefundActual refundActual, Refund refund) {
        refundActual.setStatus(RefundEnum.RefundStatusEnum.REFUND_FAIL.getCode());
        refund.setStatus(RefundEnum.RefundStatusEnum.REFUND_FAIL.getCode());
        refundService.refundUpdateStatus(refund.getId(), refund.getStatus());
        refundActualService.updateRefundActual(refundActual);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String refundNotify(PayGoodsOrderRefundNotify refundNotify, Long refundActualId) {
        //幕等操作
        Integer status = refundActualService.refundStatus(refundActualId);
        if (refundNotify.getStatus().equals(status)) {
            return NOTIFY_SUCCESS;
        }
        if (!RefundEnum.RefundStatusEnum.REFUNDING.getCode().equals(status)) {
            return NOTIFY_SUCCESS;
        }
        //跳过退款中
        if (REFUNDING_STATUS.equals(status)) {
            return NOTIFY_SUCCESS;
        }
        RefundActual refundActual = refundActualService.getById(refundActualId);
        Refund refund = refundService.getById(refundActual.getRefundId());
        return TenantBroker.applyAs(refund.getTenantId(), e -> {
            //退款成功
            if (REFUND_SUCCESS_STATUS.equals(refundNotify.getStatus())) {
                refundActual.setActualRefundAmount(new BigDecimal(refundNotify.getRefundAmount()));
                refundActual.setSystemSerialNo(refundNotify.getSystemSerial());
                passRefund(refundActual, refund);
                return NOTIFY_SUCCESS;
            }
            //退款失败
            if (REFUND_FAIL_STATUS.equals(refundNotify.getStatus())) {
                failRefund(refundActual, refund);
                return NOTIFY_SUCCESS;
            }
            return NOTIFY_FAIL;
        });
    }

    @Override
    public List<RefundActual> listUnPayOrder() {
        return refundActualService.listByStatus(RefundEnum.RefundStatusEnum.REFUNDING.getCode());
    }

    @Override
    public void save(Refund refund) {
        refundService.save(refund);
    }

    @Override
    public void refundSave(Refund refund) {
        refundService.save(refund);
    }
}
