package org.springblade.refund.handler;

import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.RefundEnum;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;
import org.springframework.stereotype.Service;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-01  14:49
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class OffLineOriginRefundHandlerImpl implements OriginRefundHandler {
    @Override
    public BillPayNameEnum support() {
        return BillPayNameEnum.OFFLINE_PAY;
    }

    @Override
    public void refundCreate(Refund refund, RefundActual refundActual) {
        refund.setStatus(RefundEnum.RefundStatusEnum.STATUS_REFUND.getCode());
        refundActual.setStatus(RefundEnum.RefundStatusEnum.STATUS_REFUND.getCode());
        refundActual.setPayRefundOrderNo(CodeUtil.generateCode(CodeEnum.REFUND_NO));
    }

    @Override
    public Integer refundQueryStatus(RefundActual refundActual) {
        return refundActual.getStatus();
    }
}
