package org.springblade.refund.core;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.RefundEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerBankCardPerson;
import org.springblade.customer.feign.IFrontFinancingListService;
import org.springblade.customer.feign.RemoteCustomerBankCardPersonService;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.service.IExpenseOrderService;
import org.springblade.expense.vo.ExpenseOrderVO;
import org.springblade.expense.wrapper.ExpenseOrderWrapper;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;
import org.springblade.refund.entity.RefundActualAccount;
import org.springblade.refund.handler.BaseBillRefund;
import org.springblade.refund.service.IRefundActualAccountService;
import org.springblade.refund.service.IRefundActualService;
import org.springblade.refund.service.IRefundService;
import org.springblade.refund.vo.RefundAndBilExpenseOrderVo;
import org.springblade.refund.vo.RefundVO;
import org.springblade.refund.wrapper.RefundWrapper;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * yjb
 */
@Component("cost_bill_refund")
@RequiredArgsConstructor
public class CostBillRefund extends BaseBillRefund {
    private final IRefundService iRefundService;
    private final RemoteUserService remoteUserService;
    private final IExpenseOrderService billExpenseOrderService;
    private final IAttachService attachService;
    private final IRefundActualService refundActualService;
    private final IRefundActualAccountService refundActualAccountService;
    private final IFrontFinancingListService frontFinancingListService;
    private final FinanceApplyMapper financeApplyMapper;
    private final RemoteCustomerBankCardPersonService customerBankCardPersonService;

    @Override
    public RefundAndBilExpenseOrderVo refundDetail(Refund refund) {
        //费用
        ExpenseOrder billExpenseOrder = billExpenseOrderService.getOne(Wrappers.<ExpenseOrder>lambdaQuery().eq(ExpenseOrder::getBillExpenseNo, refund.getBillExpenseNo()));
        if (Objects.isNull(billExpenseOrder)) {
            return null;
        }
        return settingCompanyInfo(billExpenseOrder, refund);
    }

    //待退款
    private RefundAndBilExpenseOrderVo settingCompanyInfo(ExpenseOrder billExpenseOrder, Refund refund) {
        RefundVO refundVO = RefundWrapper.build().entityVO(refund);
        RefundAndBilExpenseOrderVo refundAndBilExpenseOrderVo = new RefundAndBilExpenseOrderVo();
        ExpenseOrderVO billExpenseOrderVO = ExpenseOrderWrapper.build().entityVO(billExpenseOrder);
        List<Attach> attachList = StringUtil.isNotBlank(billExpenseOrderVO.getPayAttachId())
                ? attachService.listByIds(Func.toLongList(billExpenseOrderVO.getPayAttachId()))
                : Collections.emptyList();
        billExpenseOrderVO.setAttachList(attachList);
        User user = remoteUserService.getUserById(billExpenseOrderVO.getCustomerId(), FeignConstants.FROM_IN).getData();
        if (ObjectUtil.isEmpty(user)) {
            throw new ServiceException("融资用户查询不到，请联系管理员！");
        }
        User financingUser = remoteUserService.getUserById(refund.getUserId(), FeignConstants.FROM_IN).getData();
        RefundActual refundActual = refundActualService.getRefundActual(refund.getId());
        FinanceApply financeApply = financeApplyMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo,billExpenseOrderVO.getFinanceNo()));
        //融资企业名称
        billExpenseOrderVO.setCustomerName(user.getName());
        if (ObjectUtil.isEmpty(financeApply)) {
            throw new ServiceException("融资订单不存在!");
        }
        refundVO.setGoodsId(financeApply.getGoodsId());
        //如果是待退款返回退款金额
        if (Objects.isNull(refundActual)) {
            if (refund.getStatus().equals(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode())) {
                BigDecimal refundAmount = refund.getRefundAmount();
                refundVO.setRefundAmount(refundAmount);
                refundVO.setCompanyName(financingUser == null ? user.getName() : financingUser.getName());
                refundAndBilExpenseOrderVo.setRefund(refundVO);
            }
            refundAndBilExpenseOrderVo.setExpenseOrder(billExpenseOrderVO);
            return refundAndBilExpenseOrderVo;
        }

        return settingRefundedInfo(refundActual, refundVO, refundAndBilExpenseOrderVo, financingUser, billExpenseOrderVO);

    }

    //已退款
    private RefundAndBilExpenseOrderVo settingRefundedInfo(RefundActual refundActual, RefundVO refundVO, RefundAndBilExpenseOrderVo refundAndBilExpenseOrderVo, User financingUser, ExpenseOrderVO billExpenseOrderVO) {
        RefundActualAccount refundActualAccount = refundActualAccountService.getRefundActualAccount(refundActual.getId());
        List<Attach> attaches = attachService.listByIds(Func.toLongList(refundActual.getRefundActualVoucher()));
        User user = remoteUserService.getUserById(refundActual.getCreateUser(), FeignConstants.FROM_IN).getData();
        CustomerBankCardPerson customerBankCardPerson = customerBankCardPersonService.getOneInfo(refundVO.getGoodsId(), refundVO.getUserId()).getData();
        //组装数据
        refundVO.setRefundActualTime(refundActual.getRefundActualTime());
        refundVO.setActualRefundAmount(refundActual.getActualRefundAmount());
        refundVO.setRefundActualVoucher(refundActual.getRefundActualVoucher());
        refundVO.setBankDeposit(refundActualAccount.getBankDeposit());
        refundVO.setRefundAccount(refundActualAccount.getRefundAccount());
        refundVO.setBankAccount(refundActualAccount.getBankAccount());
        refundVO.setCompanyName(financingUser == null ? "" : financingUser.getName());
        refundVO.setAttachRefundList(attaches);
        refundVO.setName(user == null ? "" : user.getName());
        refundAndBilExpenseOrderVo.setRefund(refundVO);
        refundAndBilExpenseOrderVo.setExpenseOrder(billExpenseOrderVO);
        refundAndBilExpenseOrderVo.setCustomerBankCardPerson(customerBankCardPerson);
        return refundAndBilExpenseOrderVo;
    }
}
