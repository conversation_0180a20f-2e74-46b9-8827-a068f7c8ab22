package org.springblade.refund.core;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.RefundEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.feign.RemoteCustomerBankCardPersonService;
import org.springblade.deposit.vo.ExpenseDepositVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.refund.entity.Refund;
import org.springblade.refund.entity.RefundActual;
import org.springblade.refund.entity.RefundActualAccount;
import org.springblade.refund.handler.BaseBillRefund;
import org.springblade.refund.service.IRefundActualAccountService;
import org.springblade.refund.service.IRefundActualService;
import org.springblade.refund.service.IRefundService;
import org.springblade.refund.vo.RefundAndBilExpenseOrderVo;
import org.springblade.refund.vo.RefundVO;
import org.springblade.refund.wrapper.RefundWrapper;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * yjb
 */
@Component("cash_deposit_bill_refund")
@RequiredArgsConstructor
public class CashDepositBillRefund extends BaseBillRefund {

    private final IRefundService refundService;
    private final RemoteUserService remoteUserService;
    private final IAttachService attachService;
    private final IRefundActualService refundActualService;
    private final IRefundActualAccountService refundActualAccountService;
    private final FinanceApplyMapper financeApplyMapper;
    private final RemoteCustomerBankCardPersonService remoteCustomerBankCardPersonService;

    @Override
    public RefundAndBilExpenseOrderVo refundDetail(Refund refund) {
        return settingCompanyInfo(refund);
    }

    private RefundAndBilExpenseOrderVo settingCompanyInfo(Refund refund) {
        RefundVO refundVO = RefundWrapper.build().entityVO(refund);
        RefundAndBilExpenseOrderVo refundAndBilExpenseOrderVo = new RefundAndBilExpenseOrderVo();
        //保证金
        ExpenseDepositVO caseDepositBill = getCaseDepositBill(refund.getBillExpenseNo());
        RefundActual refundActual = refundActualService.getRefundActual(refund.getId());
        User financingUser = remoteUserService.getUserById(refund.getUserId(), FeignConstants.FROM_IN).getData();
        User user = remoteUserService.getUserById(caseDepositBill.getFinancingUserId(), FeignConstants.FROM_IN).getData();
        FinanceApply financeApply = financeApplyMapper.getByFinanceNoApply(caseDepositBill.getFinancingNo());
        if (ObjectUtil.isEmpty(user)) {
            throw new ServiceException("融资用户查询不到,请联系管理员!");
        }
        List<Attach> attaches = StringUtil.isNotBlank(caseDepositBill.getPayedVoucher())
                ? attachService.listByIds(Func.toLongList(caseDepositBill.getPayedVoucher()))
                : Collections.emptyList();
        caseDepositBill.setAttachPaymentList(attaches);
        String cashDepositRate = caseDepositBill.getCashDepositRate().stripTrailingZeros().toPlainString().concat("%");
        caseDepositBill.setCashDepositRateStr(cashDepositRate);
        refundVO.setGoodsId(financeApply.getGoodsId());
        if (Objects.isNull(refundActual)) {
            //如果是待退款返回退款金额
            if (refund.getStatus().equals(RefundEnum.RefundStatusEnum.STATUS_NOT.getCode())) {
                BigDecimal refundAmount = refund.getRefundAmount();
                refundVO.setRefundAmount(refundAmount);
                refundVO.setCompanyName(financingUser == null ? "" : financingUser.getName());
                refundAndBilExpenseOrderVo.setRefund(refundVO);
            }
            refundAndBilExpenseOrderVo.setCashDepositVO(caseDepositBill);
            return refundAndBilExpenseOrderVo;
        }
        return settingRefundedInfo(refundActual, refundVO, refundAndBilExpenseOrderVo, financingUser, caseDepositBill);
    }

    private RefundAndBilExpenseOrderVo settingRefundedInfo(RefundActual refundActual, RefundVO refundVO, RefundAndBilExpenseOrderVo refundAndBilExpenseOrderVo, User financingUser, ExpenseDepositVO caseDepositBill) {
        RefundActualAccount refundActualAccount = refundActualAccountService.getRefundActualAccount(refundActual.getId());
        List<Attach> attaches = attachService.listByIds(Func.toLongList(refundActual.getRefundActualVoucher()));
        User byId = remoteUserService.getUserById(refundActual.getCreateUser(), FeignConstants.FROM_IN).getData();
        //组装数据
        refundVO.setActualRefundAmount(refundActual.getActualRefundAmount());
        refundVO.setRefundActualTime(refundActual.getRefundActualTime());
        refundVO.setRefundActualVoucher(refundActual.getRefundActualVoucher());
        refundVO.setBankDeposit(refundActualAccount.getBankDeposit());
        refundVO.setRefundAccount(refundActualAccount.getRefundAccount());
        refundVO.setBankAccount(refundActualAccount.getBankAccount());
        refundVO.setAttachRefundList(attaches);
        refundVO.setCompanyName(financingUser == null ? "" : financingUser.getName());
        //操作人名称
        refundVO.setName(byId.getName());
        refundAndBilExpenseOrderVo.setRefund(refundVO);
        refundAndBilExpenseOrderVo.setCashDepositVO(caseDepositBill);
        return refundAndBilExpenseOrderVo;
    }

}
