-- 为 jrzh_business_entry 表添加订单ID、客户资料ID、融资申请ID字段
-- 执行时间：2025-09-15
-- 作者：Augment Agent

-- 添加订单ID字段
ALTER TABLE `jrzh_business_entry`
    ADD COLUMN `order_id` bigint(64) DEFAULT NULL COMMENT '订单ID' AFTER `customer_name`;

-- 添加客户资料ID字段
ALTER TABLE `jrzh_business_entry`
    ADD COLUMN `customer_material_id` bigint(64) DEFAULT NULL COMMENT '客户资料ID' AFTER `order_id`;

-- 添加融资申请ID字段
ALTER TABLE `jrzh_business_entry`
    ADD COLUMN `finance_apply_id` bigint(64) DEFAULT NULL COMMENT '融资申请ID' AFTER `customer_material_id`;

-- 添加索引以提高查询性能
ALTER TABLE `jrzh_business_entry`
    ADD INDEX `idx_order_id` (`order_id`),
    ADD INDEX `idx_customer_material_id` (`customer_material_id`),
    ADD INDEX `idx_finance_apply_id` (`finance_apply_id`);

-- 添加外键约束注释（实际外键约束根据业务需要决定是否添加）
-- 注意：以下外键约束仅为注释说明，实际使用时需要根据具体业务场景决定是否启用
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_order` FOREIGN KEY (`order_id`) REFERENCES `jrzh_trading_order_data` (`id`);
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_customer_material` FOREIGN KEY (`customer_material_id`) REFERENCES `jrzh_customer_material` (`id`);
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_finance_apply` FOREIGN KEY (`finance_apply_id`) REFERENCES `jrzh_finance_apply` (`id`);
