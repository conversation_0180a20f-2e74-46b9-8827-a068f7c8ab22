package org.springblade.customer.productopen.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.*;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.dto.CustomerGoodsCoreDTO;
import org.springblade.customer.dto.GroupRouteApplyDTO;
import org.springblade.customer.dto.GroupRouteConfirmDto;
import org.springblade.customer.dto.ProductRouteRunRiskResultDTO;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.ProductRouteGroupApplyEnum;
import org.springblade.customer.productopen.dto.QuotaActiveDTO;
import org.springblade.customer.productopen.dto.QuotaApplyDTO;
import org.springblade.customer.productopen.handler.BusinessProcessFactory;
import org.springblade.customer.productopen.handler.QuotaActivateHandler;
import org.springblade.customer.productopen.handler.QuotaApplyHandler;
import org.springblade.customer.productopen.publisher.RemoveCustomerGoodsPublisher;
import org.springblade.customer.productopen.service.IBusinessProcessProductOpenService;
import org.springblade.customer.service.*;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.RouteCustomerGoodsRespVO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.idempotent.annotation.Idempotent;
import org.springblade.loan.entity.LoanManageOverdue;
import org.springblade.loan.entity.LoanManageRepaymentPlan;
import org.springblade.loan.service.ILoanManageOverdueService;
import org.springblade.loan.service.ILoanManageRepaymentPlanService;
import org.springblade.modules.othersapi.riskordertwo.dto.RiskTwoQuotaProcessDTO;
import org.springblade.process.constant.ProcessResultCode;
import org.springblade.process.service.IBusinessProcessProductService;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.ProductGroupRoute;
import org.springblade.product.moudle.goodstype.service.IGoodsTypeService;
import org.springblade.product.moudle.productgroup.service.IProductGroupService;
import org.springblade.riskmana.api.dto.RatingRecordDTO;
import org.springblade.riskmana.api.entity.RatingRule;
import org.springblade.riskmana.api.entity.RiskmanaApply;
import org.springblade.riskmana.core.handler.RiskOrderApiHandlerFactory;
import org.springblade.riskmana.core.service.IRatingRuleService;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-04-26  10:56
 * @Description: 产品开通服务类
 * @Version: 1.0
 */
@RequiredArgsConstructor
@Service
public class BusinessProcessProductOpenServiceImpl implements IBusinessProcessProductOpenService {
    private final BusinessProcessFactory director;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final ICustomerGoodsService customerGoodsService;
    private final ICustomerGoodsTradeBackgroundService customerGoodsTradeBackgroundService;
    private final ISalesContractService salesContractService;
    private final ICustomerMaterialService customerMaterialService;
    private final IBusinessProcessProgressService businessProcessProgressService;
    private final ILoanManageRepaymentPlanService loanManageRepaymentPlanService;
    private final IFinanceApplyService financeApplyService;
    private final ILoanManageOverdueService loanManageOverdueService;

    private final IProductRouteGroupApplyService productRouteGroupApplyService;
    private final IProductGroupService productGroupService;
    private final IRatingRuleService ratingRuleService;
//    private final IRiskmanaTemplateRunRecordService riskmanaTemplateRunRecordService;
    private final IBusinessProcessProductService businessProcessProductService;
    private final IBusinessProcessService businessProcessService;
    private final ICustomerInfoService customerInfoService;
    private final ICustomerBusinessInfoService customerBusinessInfoService;
    private final RiskOrderApiHandlerFactory riskOrderApiHandlerFactory;
    private final IGoodsTypeService goodsTypeService;
    private final ITradeBackgroundService tradeBackgroundService;
    private final RemoveCustomerGoodsPublisher removeCustomerGoodsPublisher;
//    private final IMultiFundingProductProcessService multiFundingProductProcessService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(value = "quotaApplyLock", param = "#quotaApplyDTO.userId")
    public boolean submitQuotaApplyProcess(QuotaApplyDTO quotaApplyDTO) {
        QuotaApplyHandler quotaApplyHandler = director.instanceOf(quotaApplyDTO.getProcessType(),
                quotaApplyDTO.getEnterpriseType(), QuotaApplyHandler.class);
        quotaApplyHandler.handler(quotaApplyDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(value = "quotaActiveLock", param = "#quotaActiveDTO.userId")
    public boolean submitQuotaActiveProcess(QuotaActiveDTO quotaActiveDTO) {
        QuotaActivateHandler quotaApplyHandler = director.instanceOf(quotaActiveDTO.getProcessType(),
                quotaActiveDTO.getEnterpriseType(), QuotaActivateHandler.class);
        quotaApplyHandler.handler(quotaActiveDTO);
        return true;
    }

    @Override
    public void close(Long goodsId, Long customerGoodsId) {
        Long userId = MyAuthUtil.getUserId();
        CustomerGoods customerGoods = customerGoodsService.getById(customerGoodsId);

        if (ObjUtil.isEmpty(customerGoods)) {
            throw new ServiceException("您还未开通该资金产品!");
        }
        //如果删除的是融资产品组数据，则清除融资产品组的进度
        if (customerGoods.getGroupId() != null && customerGoods.getGoodsId().equals(customerGoods.getGroupId())) {
            removeCustomerGoodsPublisher.pushRemoveCustomerGoods(goodsId, userId);
        }
        //路由申请删除
        if (customerGoods.getGroupId() != null) {
            List<ProductRouteGroupApply> list1 = productRouteGroupApplyService
                    .list(Wrappers.<ProductRouteGroupApply>lambdaQuery()
                            .select(ProductRouteGroupApply::getId)
                            .eq(ProductRouteGroupApply::getApplyUser, customerGoods.getEnterpriseId())
                            .eq(ProductRouteGroupApply::getGroupId, customerGoods.getGroupId()));
            if (CollUtil.isNotEmpty(list1)) {
                productRouteGroupApplyService.removeByIds(list1.stream().map(ProductRouteGroupApply::getId).collect(Collectors.toList()));
            }
            businessProcessProgressService.removeBusinessProcessProgress(customerGoods.getGroupId(), userId);
        }

        if (Objects.equals(customerGoods.getGroupId(), customerGoods.getGoodsId())) {
            // 移除融资产品进度
        }

        //移除额度
        enterpriseQuotaService.remove(Wrappers.<EnterpriseQuota>lambdaQuery()
                .eq(EnterpriseQuota::getEnterpriseId, MyAuthUtil.getUserId())
                .eq(EnterpriseQuota::getGoodsId, goodsId));
        //移除客户产品
        customerGoodsService.removeById(customerGoodsId);
        customerGoodsService.removeOpenInfo(goodsId, MyAuthUtil.getUserId(), customerGoodsId);
        //移除客户产品背景
        List<CustomerGoodsTradeBackground> customerGoodsTradeBackgroundList = customerGoodsTradeBackgroundService.getByCustomerGoodsId(customerGoodsId);
        List<Long> backIds = customerGoodsTradeBackgroundList.stream().map(CustomerGoodsTradeBackground::getTradeBackgroundId).collect(Collectors.toList());
        //移除应收账款
        if (!CollectionUtils.isEmpty(backIds)) {
            salesContractService.deleteCascadeByBackId(backIds);
        }
        //移除还款计划中相关数据，避免还款列表报错
        loanManageRepaymentPlanService.remove(Wrappers.<LoanManageRepaymentPlan>lambdaQuery().eq(LoanManageRepaymentPlan::getGoodsId,goodsId).eq(LoanManageRepaymentPlan::getCustomerGoodsId,customerGoodsId));
        financeApplyService.remove(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply ::getGoodsId,goodsId).eq(FinanceApply::getCustomerGoodsId,customerGoodsId));
        //移除相关联的逾期列表
        List<FinanceApply> financeApplyList= financeApplyService.list(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getCustomerGoodsId,customerGoodsId));
        List<String> financeNos =financeApplyList.stream().map(FinanceApply::getFinanceNo).collect(Collectors.toList());
        // 进行判空
        if (!CollectionUtils.isEmpty(financeNos)){
            loanManageOverdueService.remove(Wrappers.<LoanManageOverdue>lambdaQuery().in(LoanManageOverdue ::getFinanceNo,financeNos));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCustomerMaterial(CustomerMaterial customerMaterial) {
        customerMaterialService.saveOrUpdate(customerMaterial);

        businessProcessProgressService.updateBusinessProcessProgress(customerMaterial.getGoodsId(),
                ProcessProgressEnum.APPLY_QUOTA_SIGN_CONTRACT.getCode(),
                ProcessTypeEnum.APPLY_QUOTA.getCode(), null, MyAuthUtil.getUserId(), null);
        return customerMaterial.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveMultiFundingCustomerMaterial(List<CustomerMaterial> customerMaterialList) {
        customerMaterialList.forEach(customerMaterial -> {
            customerMaterial.setUserId(MyAuthUtil.getUserId());
            // 保存客户资料
            customerMaterialService.saveOrUpdate(customerMaterial);
        });
        return true;
    }

    /**
     * 保存客户资料，更新流程
     * @param customerMaterial
     * @param updateProcess
     * @return*/
    @Transactional(rollbackFor = Exception.class)
    public Long saveCustomerMaterial(CustomerMaterial customerMaterial, boolean updateProcess) {
        if (customerMaterial.getUserId() == null) {
            customerMaterial.setUserId(AuthUtil.getUserId());
        }
        customerMaterialService.saveOrUpdate(customerMaterial);
        if (updateProcess) {
            businessProcessProgressService.updateBusinessProcessProgress(customerMaterial.getGoodsId(),
                    ProcessProgressEnum.APPLY_QUOTA_SIGN_CONTRACT.getCode(),
                    ProcessTypeEnum.APPLY_QUOTA.getCode(), null, customerMaterial.getUserId(), null);
        }
        return customerMaterial.getId();
    }

    /**
     * 产品组路由申请
     *
     * @param groupRouteApplyDTO
     * @return*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Idempotent(key = "#groupRouteApplyDTO.idempotentKey", expireTime = 60)
    public RouteCustomerGoodsRespVO routeCustomerGoodsApply(GroupRouteApplyDTO groupRouteApplyDTO) {
        int count = productGroupService.count(Wrappers.<ProductGroupRoute>lambdaQuery()
                .eq(ProductGroupRoute::getId, groupRouteApplyDTO.getGoodsId())
                .eq(ProductGroupRoute::getStatus, GoodsEnum.ON_SHELF.getCode())
                .gt(ProductGroupRoute::getRelationNum, 0));
        //已经有的情况
        LambdaQueryWrapper<ProductRouteGroupApply> wrapper = Wrappers.<ProductRouteGroupApply>lambdaQuery()
                .eq(ProductRouteGroupApply::getGroupId, groupRouteApplyDTO.getGroupId())
                .eq(ProductRouteGroupApply::getApplyUser, groupRouteApplyDTO.getUserId());
        int exist = productRouteGroupApplyService.count(wrapper);
        if (count <= 0 && exist == 0) {
            throw new ServiceException("产品组不存在");
        }
        if (exist > 0) {
            ProductRouteGroupApply oldProductRoute = productRouteGroupApplyService.getOne(wrapper);
            return runExistRouteApply(oldProductRoute, groupRouteApplyDTO);
        }
        //创建路由订单进行路由
        ProductRouteGroupApply productRouteGroupApply = new ProductRouteGroupApply();
        return runRouteApply(groupRouteApplyDTO, productRouteGroupApply);
    }

    /**
     * 进行路由申请
     */
    private RouteCustomerGoodsRespVO runRouteApply(GroupRouteApplyDTO groupRouteApplyDTO, ProductRouteGroupApply productRouteGroupApply) {
        //数据保存打包
        Long customerMaterialId = saveCustomerMaterial(groupRouteApplyDTO, false);
        //进行数据审批
        Long groupId = groupRouteApplyDTO.getGoodsId();
        // 获取工商信息
        CustomerInfo companyInfo = customerInfoService.getByCompanyId(groupRouteApplyDTO.getUserId());
        CustomerBusinessInfo businessInfo = customerBusinessInfoService.getByCreditCode(companyInfo.getBusinessLicenceNumber());
        //进行风控运行
        List<ProductRouteRunRiskResultDTO> productRouteRunRiskResultList = runRisk(groupRouteApplyDTO, groupId, AuthUtil.getTenantId(), businessInfo);
        Integer goodsType = goodsTypeService.getById(productGroupService.getById(groupId).getGoodsTypeId()).getGoodsType();
        //产品类型为应收账款则需要筛选有贸易企业的产品
        if (Objects.equals(goodsType, GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode())) {
            productRouteRunRiskResultList = productRouteRunRiskResultList.stream()
                    .filter(e->!CollectionUtils.isEmpty(tradeBackgroundService.selectUnUsedTradeBackgroundList(goodsType, e.getProduct().getId())))
                    .collect(Collectors.toList());
        }
        productRouteGroupApply.setApplyUser(groupRouteApplyDTO.getUserId());
        productRouteGroupApply.setGroupId(groupId);
        productRouteGroupApply.setUserType(groupRouteApplyDTO.getUserType());
        productRouteGroupApply.setProductRouteRunRiskResultList(JSONUtil.toJsonStr(productRouteRunRiskResultList));
        //暂时只有订单融资
//        productRouteGroupApply.setGoodsType(GoodsEnum.ORDER_FINANCING.getCode());
        //多种产品类型goodsType   1：应收、5：订单融资
        productRouteGroupApply.setGoodsType(goodsTypeService.getById(productGroupService.getById(groupId).getGoodsTypeId()).getGoodsType());
        //如果不存在返回失败
        if (CollUtil.isEmpty(productRouteRunRiskResultList)) {
            return routeCustomerGoodsApplyFail(productRouteGroupApply);
        }
        //如果只有一条 直接路由成功
        //TODO
/*        if (productRouteRunRiskResultList.size() == 1) {
            Long goodsId = productRouteRunRiskResultList.get(0).getProduct().getId();
            productRouteGroupApply.setApplyGoodsIds(goodsId.toString());
            return routeCustomerGoodsApplySuccess(productRouteGroupApply, goodsId, groupRouteApplyDTO);
        }*/
        //设置为申请中
        routeCustomerGoodsApplying(productRouteGroupApply, productRouteRunRiskResultList);

        productRouteGroupApply.setCustomerMaterialId(customerMaterialId);
        //如果多条 需要开启流程进行选择产品
        int code = ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY.getCode();
        Map<String, Object> variables = getRouteApplyCommonVariables(groupRouteApplyDTO, productRouteGroupApply, productRouteRunRiskResultList, code);
        //设置流程参数
        String processInstanceId = businessProcessProductService.startOrSubmit(groupId, ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY, null, variables, ProcessProgressEnum.PRODUCT_ROUTE_APPLY);
        //保存流程进度
        businessProcessProgressService.saveOrUpdateBusinessProcessProgressUnContainInvalid(groupId, ProcessProgressEnum.PRODUCT_ROUTE_APPLY.getCode(), code, processInstanceId, null, groupRouteApplyDTO.getUserId(), ProcessStatusEnum.APPROVING.getCode());
        productRouteGroupApply.setProcessInstanceId(processInstanceId);
        productRouteGroupApplyService.updateById(productRouteGroupApply);

        return BeanUtil.copyProperties(productRouteGroupApply, RouteCustomerGoodsRespVO.class);
    }

    /**
     * 客户确认并开通所选择产品
     * @param groupRouteConfirm
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO confirmProduct(GroupRouteConfirmDto groupRouteConfirm) {

        ProductRouteGroupApply productRouteGroupApply = productRouteGroupApplyService.getByUserIdAndGroupId(AuthUtil.getUserId(), groupRouteConfirm.getGroupId());

        if (productRouteGroupApply == null) {
            throw new ServiceException("产品路由参数错误");
        }
        productRouteGroupApply.setGoodsId(groupRouteConfirm.getProductId());
        CustomerMaterial customerMaterial = customerMaterialService.getById(groupRouteConfirm.getCustomerMaterialId());

        GroupRouteApplyDTO groupRouteApply = BeanUtil.copyProperties(customerMaterial, GroupRouteApplyDTO.class);
        if (groupRouteApply == null) {
            throw new ServiceException("产品开通补充参数错误");
        }
        groupRouteApply.setGroupId(groupRouteConfirm.getGroupId());

        return routeCustomerGoodsApplySuccess(productRouteGroupApply, groupRouteConfirm.getProductId(), groupRouteApply);
    }

    /**
     * 已存在路由申请情况
     * @param oldProductRoute
     * @param groupRouteApplyDTO
     * @return*/
    private RouteCustomerGoodsRespVO runExistRouteApply(ProductRouteGroupApply oldProductRoute, GroupRouteApplyDTO groupRouteApplyDTO) {
        //如果成功、终止、审批中直接返回
        if (ProductRouteGroupApplyEnum.APPLY_STATUS.SUCCESS.getStatus().equals(oldProductRoute.getStatus())) {
            //数据保存打包
            saveCustomerMaterial(groupRouteApplyDTO, false);
            return routeCustomerGoodsApplySuccess(oldProductRoute, oldProductRoute.getGoodsId(), groupRouteApplyDTO, false);
        }
        if (ProductRouteGroupApplyEnum.APPLY_STATUS.APPLYING.getStatus().equals(oldProductRoute.getStatus()) ||
                ProductRouteGroupApplyEnum.APPLY_STATUS.TERMINATE.getStatus().equals(oldProductRoute.getStatus())) {
            return BeanUtil.copyProperties(oldProductRoute, RouteCustomerGoodsRespVO.class);
        }
        //失败并且已开启流程情况下进行保存资料重提 跟新流程进度
        if (StrUtil.isNotBlank(oldProductRoute.getProcessInstanceId())) {
            //原运行记录
            List<ProductRouteRunRiskResultDTO> list = JSONUtil.toList(oldProductRoute.getProductRouteRunRiskResultList(), ProductRouteRunRiskResultDTO.class);
            saveCustomerMaterial(groupRouteApplyDTO, false);
            int code = ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY.getCode();
            Map<String, Object> variables = getRouteApplyCommonVariables(groupRouteApplyDTO, oldProductRoute, list, code);
            //设置流程参数
            String processInstanceId = businessProcessProductService.startOrSubmit(oldProductRoute.getGroupId(), ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY, oldProductRoute.getProcessInstanceId(), variables, ProcessProgressEnum.PRODUCT_ROUTE_APPLY);
            //保存流程进度
            businessProcessProgressService.saveOrUpdateBusinessProcessProgressUnContainInvalid(oldProductRoute.getGroupId(), ProcessProgressEnum.PRODUCT_ROUTE_APPLY.getCode(), code, processInstanceId, null, groupRouteApplyDTO.getUserId(), ProcessStatusEnum.APPROVING.getCode());
            //运行评估中
            routeCustomerGoodsApplying(oldProductRoute, list);
            return BeanUtil.copyProperties(oldProductRoute, RouteCustomerGoodsRespVO.class);
        }
        //进行路由申请
        return runRouteApply(groupRouteApplyDTO, oldProductRoute);
    }

    /**
     *获取路由申请变量
     */
    private Map<String, Object> getRouteApplyCommonVariables(GroupRouteApplyDTO groupRouteApplyDTO
            , ProductRouteGroupApply productRouteGroupApply, List<ProductRouteRunRiskResultDTO> productRouteRunRiskResultList, Integer processType) {
        Map<String, Object> variables = new HashMap<>();
        List<Product> productList = productRouteRunRiskResultList.stream().map(ProductRouteRunRiskResultDTO::getProduct).collect(Collectors.toList());
        variables.put(ProcessConstant.PROCESS_GOODS_INFO, productList);
        variables.put(ProcessConstant.CUSTOMER_MATERIAL, groupRouteApplyDTO);
        variables.put(ProcessConstant.PRODUCT_ROUTE_GROUP_APPLY, productRouteGroupApply);
        variables.put(ProcessConstant.PROCESS_TYPE, processType);
        variables.put(ProcessConstant.USER_ID, productRouteGroupApply.getApplyUser());
        variables.put(ProcessConstant.ENTERPRISE_TYPE, productRouteGroupApply.getUserType());
        variables.put(WfProcessConstant.PROCESS_NO, businessProcessService.getProcessNo(null));
        variables.put(ProcessConstant.BUSINESS_ID, groupRouteApplyDTO.getGroupId());
        return variables;
    }

    private void routeCustomerGoodsApplying(ProductRouteGroupApply productRouteGroupApply, List<ProductRouteRunRiskResultDTO> productRouteRunRiskResultDTOList) {
        productRouteGroupApply.setStatus(ProductRouteGroupApplyEnum.APPLY_STATUS.APPLYING.getStatus());
        List<Long> applyGoodsIds = productRouteRunRiskResultDTOList.stream().map(e -> e.getProduct().getId()).collect(Collectors.toList());
        productRouteGroupApply.setApplyGoodsIds(StringUtil.join(applyGoodsIds, ","));
        productRouteGroupApplyService.saveOrUpdate(productRouteGroupApply);
    }

    /**
     * 运行风控
     * @param groupRouteApplyDTO
     * @param groupId
     * @param tenantId
     * @param businessInfo
     * @return*/
    private List<ProductRouteRunRiskResultDTO> runRisk(GroupRouteApplyDTO groupRouteApplyDTO, Long groupId, String tenantId, CustomerBusinessInfo businessInfo) {
        //查询产品组的相关产品风控
        List<Product> productList = listByGroupId(groupId);
        for (int i = 0; i < productList.size(); i++) {
            Product product = productList.get(i);
            product.setSort(i);
            product.setIsShow(1);
        }
        return productList.parallelStream().map(e ->
                        TenantBroker.applyAs(tenantId,
                                el -> runRiskByProduct(e, groupRouteApplyDTO, groupId, businessInfo)))
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
    }

    private List<Product> listByGroupId(Long groupId) {
        return productGroupService.listProductByGroupId(groupId);
    }

    /**
     *执行单个产品对应的风控
     */
    private ProductRouteRunRiskResultDTO runRiskByProduct(Product product,
                                                          GroupRouteApplyDTO groupRouteApplyDTO, Long groupId, CustomerBusinessInfo businessInfo) {
        Long scoreTemplateId = product.getScoreTemplateId();
        JSONObject runData = JSONUtil.parseObj(groupRouteApplyDTO.getData());
        RatingRule ratingRule = ratingRuleService.getById(scoreTemplateId);
        RatingRecordDTO ratingRecord = new RatingRecordDTO();
        ratingRecord.setRatingId(scoreTemplateId);
        //设置用户基础信息
        ratingRecord.setEnterpriseType(EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode());
        ratingRecord.setEnpName(businessInfo.getCompanyName());
        ratingRecord.setEnpSocialCode(businessInfo.getCreditCode());
        ratingRecord.setObj(runData);
        ratingRecord.setRatingRule(ratingRule);
        ratingRecord.setUserName(businessInfo.getCompanyName());
        ratingRecord.setWaitSysPass(CommonConstant.YES);
        ratingRecord.setUserType(CustomerTypeEnum.ENTERPRISE.getCode());
        //用3.0的默认风控系统，但直接准入，不做直拒判断
//        RiskmanaApply apply = riskOrderApiHandlerFactory.template(ratingRule.getSupplierNo())
//                .applyQuota(ratingRecord);
        RatingRecordDTO ratingRecordDTO=new RatingRecordDTO();
        RiskTwoQuotaProcessDTO apply = riskOrderApiHandlerFactory.template(ratingRule.getSupplierNo())
                .apply(ratingRecordDTO);
        if (apply.getWhitePass()) {
            ProductRouteRunRiskResultDTO productRouteRunRiskResultDTO = new ProductRouteRunRiskResultDTO();
            productRouteRunRiskResultDTO.setProduct(product);
            productRouteRunRiskResultDTO.setRatingTemplateId(ratingRule.getTemplateId());
            productRouteRunRiskResultDTO.setRuleId(scoreTemplateId);
            return productRouteRunRiskResultDTO;
        }
        //潍柴的风控-产品准入申请和判断，后面需要优化
        /*RiskTwoQuotaProcessDTO apply = riskOrderApiHandlerFactory.template(ratingRule.getSupplierNo())
                .apply(ratingRecord);
        //拒绝条件：1、规则不等于正常 并且 传入值为空 2、命中直拒
        boolean hasReject = apply.getRecordNormVO().getRatingNormRecordList().values().stream()
                .flatMap(Collection::stream).anyMatch(e -> (StrUtil.isEmpty(e.getEnterpriseData())
                        && !CommonConstant.NORM_NORMAL.equals(e.getRuleType())) || CommonConstant.NORM_REJECT.equals(e.getRuleType()));
        if (!hasReject) {
            ProductRouteRunRiskResultDTO productRouteRunRiskResultDTO = new ProductRouteRunRiskResultDTO();
            productRouteRunRiskResultDTO.setProduct(product);
            productRouteRunRiskResultDTO.setRatingTemplateId(ratingRule.getTemplateId());
            productRouteRunRiskResultDTO.setRuleId(scoreTemplateId);
            return productRouteRunRiskResultDTO;
        }*/
        return null;
    }

    /**
     * 产品组路由申请成功
     * @param productRouteGroupApply
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO routeCustomerGoodsApplySuccess(ProductRouteGroupApply productRouteGroupApply, Long goodsId, GroupRouteApplyDTO groupRouteApplyDTO) {
        return routeCustomerGoodsApplySuccess(productRouteGroupApply, goodsId, groupRouteApplyDTO, true);
    }
    private RouteCustomerGoodsRespVO routeCustomerGoodsApplySuccess(ProductRouteGroupApply productRouteGroupApply, Long goodsId, GroupRouteApplyDTO groupRouteApplyDTO, boolean saveCustomerGoods) {
        productRouteGroupApply.setGoodsId(goodsId);
        productRouteGroupApply.setStatus(ProductRouteGroupApplyEnum.APPLY_STATUS.SUCCESS.getStatus());
        productRouteGroupApplyService.saveOrUpdate(productRouteGroupApply);
        //保存具体产品的补充资料
        CustomerMaterial customerMaterial = BeanUtil.copy(groupRouteApplyDTO, CustomerMaterial.class);
        customerMaterial.setGoodsId(goodsId);
        customerMaterial.setId(null);
        saveCustomerMaterial(customerMaterial);
        //开启/修改具体产品的额度申请流程
        int applyQuotaCode = ProcessTypeEnum.APPLY_QUOTA.getCode();
        businessProcessProgressService.saveOrUpdateBusinessProcessProgressUnContainInvalid(goodsId, ProcessProgressEnum.QUOTA_CHANGING_PASS.getCode(), applyQuotaCode, null, CommonConstant.YES, productRouteGroupApply.getApplyUser(), null);
        //开启/修改具体产品的额度激活流程
        int activeCode = ProcessTypeEnum.QUOTA_ACTIVE.getCode();
        businessProcessProgressService.saveOrUpdateBusinessProcessProgressUnContainInvalid(goodsId, ProcessProgressEnum.QUOTA_ACTIVE_SIGN_CONTRACT.getCode(), activeCode, null, CommonConstant.YES, productRouteGroupApply.getApplyUser(), null);
        //保存客户产品
        if (saveCustomerGoods) {
            CustomerGoodsCoreDTO customerGoodsCoreDTO = new CustomerGoodsCoreDTO();
            customerGoodsCoreDTO.setGoodsType(productRouteGroupApply.getGoodsType());
            customerGoodsCoreDTO.setEnterpriseType(productRouteGroupApply.getUserType());
            customerGoodsCoreDTO.setGoodsId(productRouteGroupApply.getGoodsId());
            customerGoodsCoreDTO.setEnterpriseId(productRouteGroupApply.getApplyUser());
            customerGoodsCoreDTO.setGroupId(productRouteGroupApply.getGroupId());
            customerGoodsService.saveCustomerOpenGoods(customerGoodsCoreDTO);
        }
        return BeanUtil.copyProperties(productRouteGroupApply, RouteCustomerGoodsRespVO.class);
    }

    /**
     * 产品组路由申请失败
     *
     * @param productRouteGroupApply
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO routeCustomerGoodsApplyFail(ProductRouteGroupApply productRouteGroupApply) {
        productRouteGroupApply.setStatus(ProductRouteGroupApplyEnum.APPLY_STATUS.FAIL.getStatus());
        productRouteGroupApplyService.saveOrUpdate(productRouteGroupApply);
        return BeanUtil.copyProperties(productRouteGroupApply, RouteCustomerGoodsRespVO.class);
    }

    /**
     * 产品组路由申请终止
     *
     * @param productRouteGroupApply
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO routeCustomerGoodsApplyTerminal(ProductRouteGroupApply productRouteGroupApply) {
        productRouteGroupApply.setStatus(ProductRouteGroupApplyEnum.APPLY_STATUS.FAIL.getStatus());
        productRouteGroupApply.setProcessInstanceId("");
        productRouteGroupApply.setApplyGoodsIds("");
        productRouteGroupApply.setGoodsId(null);
        productRouteGroupApplyService.saveOrUpdate(productRouteGroupApply);
        return BeanUtil.copyProperties(productRouteGroupApply, RouteCustomerGoodsRespVO.class);
    }

    /**
     * 产品组路由申请拒绝
     * @param productRouteGroupApply
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO routeCustomerGoodsApplyReject(ProductRouteGroupApply productRouteGroupApply) {
        productRouteGroupApply.setStatus(ProductRouteGroupApplyEnum.APPLY_STATUS.FAIL.getStatus());
        productRouteGroupApplyService.update(Wrappers.<ProductRouteGroupApply>lambdaUpdate()
                .eq(ProductRouteGroupApply::getId, productRouteGroupApply.getId())
                .set(ProductRouteGroupApply::getStatus, ProductRouteGroupApplyEnum.APPLY_STATUS.FAIL.getStatus()));
        return BeanUtil.copyProperties(productRouteGroupApply, RouteCustomerGoodsRespVO.class);
    }

    /**
     * 获取产品id 已路由情况返回路由产品 未路由情况返回产品组id 及流程type
     * @param groupId
     * @param userId
     * @return*/
    @Override
    public R<JSONObject> getGoodsIdByGroupId(Long groupId, Long goodId, Long userId) {
        ProductRouteGroupApply one = productRouteGroupApplyService.getOne(Wrappers.<ProductRouteGroupApply>lambdaQuery()
                .select(ProductRouteGroupApply::getGoodsId)
                .eq(ProductRouteGroupApply::getGroupId, groupId)
                .eq(ProductRouteGroupApply::getApplyUser, userId));
        JSONObject jsonObject = new JSONObject();
        if (one == null) {
            ProductGroupRoute productGroupRoute = productGroupService.getById(groupId);
            jsonObject.putOnce("goodsId", productGroupRoute.getId());
            jsonObject.putOnce("type", ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY.getCode());
            return R.data(jsonObject);
        }
        if (ObjectUtil.isNotEmpty(one.getGoodsId())) {
            jsonObject.putOnce("goodsId", one.getGoodsId());
            Integer enterpriseType = UserUtils.getEnterpriseType();
            // 如果已经有开通了的产品，并且产品id不为空则判断已经开通的产品组路由路由的产品是不是传入的产品id
            if (ObjectUtil.isNotEmpty(goodId) && !goodId.equals(one.getGoodsId()) && !goodId.equals(groupId)) {
//                throw new ServiceException("该产品组已开通其他产品！");
                return R.fail(ProcessResultCode.PRODUCT_ROUTE_OPEN_OTHER);
            }
            if (EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode() == enterpriseType) {
                jsonObject.putOnce("type", ProcessTypeEnum.APPLY_QUOTA.getCode());
            } else {
                jsonObject.putOnce("type", ProcessTypeEnum.CORE_AUTONOMY_APPLY_QUOTA.getCode());
            }
            return R.data(jsonObject);
        }
        jsonObject.putOnce("goodsId", one.getGroupId());
        jsonObject.putOnce("type", ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY.getCode());
        return R.data(jsonObject);
    }

    /**
     * 获取路由产品状态
     * @param groupId
     * @param userId
     * @return*/
    @Override
    public RouteCustomerGoodsRespVO getGoodsIdByGroupStatus(Long groupId, Long userId) {
        ProductRouteGroupApply one = productRouteGroupApplyService.getOne(Wrappers.<ProductRouteGroupApply>lambdaQuery()
                .select(ProductRouteGroupApply::getId, ProductRouteGroupApply::getGoodsId, ProductRouteGroupApply::getStatus, ProductRouteGroupApply::getGroupId)
                .eq(ProductRouteGroupApply::getGroupId, groupId)
                .eq(ProductRouteGroupApply::getApplyUser, userId));
        return BeanUtil.copyProperties(one, RouteCustomerGoodsRespVO.class);
    }
}
