package org.springblade.customer.timing;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.EnterpriseQuotaStatusEnum;
import org.springblade.common.enums.ProcessProgressEnum;
import org.springblade.common.enums.ProcessStatusEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.customer.entity.CusCapitalQuota;
import org.springblade.customer.entity.CusCapitalQuotaAddition;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.entity.ProductRouteGroupApply;
import org.springblade.customer.service.*;
import org.springblade.finance.external.handler.businessProcessProgress.FinanceBusinessProcessProgressService;
import org.springblade.resource.entity.Param;
import org.springblade.resource.service.IParamService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EnterpriseQuotaTask {

	private final IEnterpriseQuotaService enterpriseQuotaService;
	private final ICustomerGoodsService customerGoodsService;
	private final ICusCapitalQuotaService cusCapitalQuotaService;
	private final ICusCapitalQuotaAdditionService cusCapitalQuotaAdditionService;
	private final IParamService paramService;
	private final FinanceBusinessProcessProgressService financeBusinessProcessProgressService;
	private final IProductRouteGroupApplyService productRouteGroupApplyService;
	private final static String TIMING_TASK_COUNT = "timing_task_count";

	@Scheduled(cron = "0 0 0 * * ? ")
	@Transactional(rollbackFor = Exception.class)
	public void execute() {
		log.info("定时开始执行===================================");

		Map<String, String> parameter = SpringUtil.getBean(IParamService.class).list().stream()
			.filter(o -> o.getParamKey().equals(TIMING_TASK_COUNT))
			.collect(Collectors.toMap(Param::getParamKey, Param::getParamValue,(oldVal, newVal) -> oldVal));
		String timingTaskCount = parameter.get(TIMING_TASK_COUNT);
		List<EnterpriseQuota> list = enterpriseQuotaService.list(Wrappers.<EnterpriseQuota>lambdaQuery()
			.notIn(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.EXPIRE.getCode(),EnterpriseQuotaStatusEnum.DISABLE.getCode()));

		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		Map<String, List<EnterpriseQuota>> map = list.stream().collect(Collectors.groupingBy(EnterpriseQuota::getTenantId));
		for (String tenantId : map.keySet()) {
			TenantBroker.runAs(tenantId, e -> {
				List<EnterpriseQuota> enterpriseQuotas = map.get(tenantId);
				List<EnterpriseQuota> enterpriseQuotaList = enterpriseQuotaService.list(Wrappers.<EnterpriseQuota>lambdaQuery()
						.eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.EXPIRE.getCode()));

				List<EnterpriseQuota> expireList = StreamUtil.filter(enterpriseQuotas,enterpriseQuota ->
						enterpriseQuota.getExpireTime().isBefore(LocalDateTime.now()));

				if (Integer.valueOf(timingTaskCount).equals(1)){
					paramService.update(Wrappers.<Param>lambdaUpdate().set(Param::getParamValue, 2).eq(Param::getParamKey, TIMING_TASK_COUNT));
					updateAcAmount(enterpriseQuotaList);
				}else {
					updateAcAmount(expireList);
				}

				expireList.forEach(enterpriseQuota -> enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.EXPIRE.getCode()));

				enterpriseQuotaService.updateBatchById(expireList);
				customerGoodsService.expire(StreamUtil.map(expireList, EnterpriseQuota::getId));
				for (EnterpriseQuota enterpriseQuota : expireList) {
					ProductRouteGroupApply one = productRouteGroupApplyService.getOne(Wrappers.<ProductRouteGroupApply>lambdaQuery()
							.eq(ProductRouteGroupApply::getGoodsId, enterpriseQuota.getGoodsId())
							.eq(ProductRouteGroupApply::getApplyUser, enterpriseQuota.getEnterpriseId())
					);
					if (ObjectUtil.isNotEmpty(one)) {
						// 此为产品组逻辑, 如果后续非订单融资产品也介入的产品组, 则也需要处理该情况
						financeBusinessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(one.getGroupId(), ProcessProgressEnum.SUPPLEMENT_MATERIAL.getCode(), ProcessTypeEnum.PROCESS_TYPE_PRODUCT_APPLY.getCode(), enterpriseQuota.getProcessInstanceId(), enterpriseQuota.getEnterpriseId(), ProcessStatusEnum.PROCESS_OPEN.getCode(),1,false);
					} else {
						// 此处为原代码, 因type参数固定写的7为核心企业自主申请额度
						financeBusinessProcessProgressService.updateBusinessProcessProgressUnContainInvalid(enterpriseQuota.getGoodsId(),1,7, enterpriseQuota.getProcessInstanceId(), enterpriseQuota.getEnterpriseId(), ProcessStatusEnum.PROCESS_OPEN.getCode(),1,false);
					}

				}
			});
		}

		log.info("定时完成执行===================================");
	}

	private void updateAcAmount(List<EnterpriseQuota> enterpriseQuotaList) {
		Map<Long, List<EnterpriseQuota>> creditAmountMap = enterpriseQuotaList.stream()
			.collect(Collectors.groupingBy(EnterpriseQuota::getCapitalId, HashMap::new, Collectors.toList()));
		List<CusCapitalQuota> cusCapitalQuotas = cusCapitalQuotaService.list();
		for (CusCapitalQuota cusCapitalQuota : cusCapitalQuotas) {
			CusCapitalQuotaAddition capitalQuotaAddition = cusCapitalQuotaAdditionService.getOne(Wrappers.<CusCapitalQuotaAddition>lambdaQuery()
				.eq(CusCapitalQuotaAddition::getCompanyId, cusCapitalQuota.getCompanyId()));
			List<EnterpriseQuota> enterpriseQuotas = creditAmountMap.get(cusCapitalQuota.getCompanyId());
			if (CollectionUtil.isNotEmpty(enterpriseQuotas) && capitalQuotaAddition.getType()!=2) {
				BigDecimal creditAmount = enterpriseQuotas.stream().map(EnterpriseQuota::getCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				BigDecimal acAmount = cusCapitalQuota.getAcAmount();
				cusCapitalQuota.setAcAmount(creditAmount.add(acAmount));
			}
		}
		cusCapitalQuotaService.updateBatchById(cusCapitalQuotas);
	}
}
