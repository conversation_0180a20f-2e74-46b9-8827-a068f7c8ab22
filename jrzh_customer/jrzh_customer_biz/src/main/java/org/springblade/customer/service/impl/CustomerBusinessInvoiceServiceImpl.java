/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeInvoiceVerificationResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerBusinessInvoiceEnum;
import org.springblade.common.enums.InvoiceEnum;
import org.springblade.common.exception.HuaWeiYunOcrException;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.constant.InvoiceStatus;
import org.springblade.customer.dto.CustomerBusinessInvoiceDTO;
import org.springblade.customer.entity.CustomerBusinessInvoice;
import org.springblade.othersapi.ali.handler.AliShumaiInvoiceOcrAndVerifyHandler;
import org.springblade.othersapi.core.enums.InvoiceTypeEnum;
import org.springblade.customer.mapper.CustomerBusinessInvoiceMapper;
import org.springblade.customer.service.ICustomerBusinessInvoiceService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerBusinessInvoiceVO;
import org.springblade.modules.othersapi.dto.InvoiceVertificationDTO;
import org.springblade.modules.othersapi.dto.OcrResponse;
import org.springblade.modules.othersapi.service.IHuaweiApiService;
import org.springblade.otherapi.core.dto.OtherApiInvoiceDto;
import org.springblade.othersapi.core.factory.InvoiceOcrFactory;
import org.springblade.othersapi.core.handler.InvoiceOcrHandler;
import org.springblade.othersapi.core.service.IHuaweiOcrResultService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springblade.customer.constant.InvoiceStatus.INVOICE_VERTIFICATION_STATUS.VERTIFY_FAIL;
import static org.springblade.customer.constant.InvoiceStatus.INVOICE_VERTIFICATION_STATUS.VERTIFY_SUCCEESS;

/**
 * 业务发票 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
@RequiredArgsConstructor
public class CustomerBusinessInvoiceServiceImpl extends BaseServiceImpl<CustomerBusinessInvoiceMapper, CustomerBusinessInvoice> implements ICustomerBusinessInvoiceService {
    private final IHuaweiApiService huaweiApiService;
    private final IHuaweiOcrResultService huaweiOcrResultService;
    private final InvoiceOcrFactory invoiceOcrFactory;
    /**
     * 发票种类为增值税普通发票、增值税电子普通发票、增值税普通发票(卷式)、增值税电子普通发票(通行费) 后6位
     */
    private final static Integer SIZE6 = 6;
    /**
     * 区块链电子发票时此项不可为空(区块链电子发票验真时,填写的是5位校验码)
     */
    private final static Integer SIZE5 = 5;

    @Override
    public IPage<CustomerBusinessInvoiceVO> selectCustomerBusinessInvoicePage(IPage<CustomerBusinessInvoiceVO> page, CustomerBusinessInvoiceVO customerBusinessInvoice) {
        return page.setRecords(baseMapper.selectCustomerBusinessInvoicePage(page, customerBusinessInvoice));
    }

    /**
     * 发票上传
     */
    @Override
    public CustomerBusinessInvoice uploadInvoice(String picUrl) {
        return requestInvoiceOcr(picUrl);
    }

    /**
     * 通过ocr服务供应商与数据库获取发票信息
     */
    @Override
    public CustomerBusinessInvoice requestInvoiceOcr(String picUrl) {
        return requestInvoiceOcr(picUrl, MyAuthUtil.getCompanyId());
    }

    @Override
    public CustomerBusinessInvoice requestInvoiceOcr(String picUrl, String userId) {
        try {
//            // 旧的华为发票ocr功能
//            RecognizeVatInvoiceResponse recognizeVatInvoiceResponse = huaweiApiService.recognizeVatInvoice(picUrl);
//            Object result = recognizeVatInvoiceResponse.getResult();
            OtherApiInvoiceDto result = invoiceOcrFactory.template().ocr(picUrl);
            CustomerBusinessInvoice copy = BeanUtil.copy(result, CustomerBusinessInvoice.class);
            //识别成功保存成功信息
            return saveOrUpdateCustomerInvoice(copy, picUrl, userId);
        } catch (HuaWeiYunOcrException e) {
            //记录识别出错的信息
            CustomerBusinessInvoice customerBusinessInvoice = new CustomerBusinessInvoice();
            customerBusinessInvoice.setProof(picUrl);
            customerBusinessInvoice.setIdentifyStatus(3);
            customerBusinessInvoice.setCompanyId(userId);
            saveInvoice(customerBusinessInvoice);
            throw new ServiceException(e.getMessage());
        }
    }

    private CustomerBusinessInvoice getByPicHash(Long imgHash) {
        CustomerBusinessInvoice one = getOne(Wrappers.<CustomerBusinessInvoice>lambdaQuery()
                .eq(CustomerBusinessInvoice::getHashValue, imgHash).orderByDesc(CustomerBusinessInvoice::getCreateTime).last("limit 1"));
        return one;
    }

    @Override
    public List<CustomerBusinessInvoice> listUnUploadByCompanyId(String companyId) {
        return list(Wrappers.<CustomerBusinessInvoice>lambdaQuery().eq(CustomerBusinessInvoice::getCompanyId, companyId)
                .eq(CustomerBusinessInvoice::getUploadStatus, InvoiceStatus.INVOICE_UPLOAD_STATUS.UN_UPLOAD.getCode()));
    }

    @Override
    public List<CustomerBusinessInvoice> listUnUploadByCompanyIdAndSellerId(String companyId, String sellerId) {
        return list(Wrappers.<CustomerBusinessInvoice>lambdaQuery().eq(CustomerBusinessInvoice::getCompanyId, companyId)
                .eq(CustomerBusinessInvoice::getSellerId, sellerId)
                .eq(CustomerBusinessInvoice::getUploadStatus, InvoiceStatus.INVOICE_UPLOAD_STATUS.UN_UPLOAD.getCode()));
    }

    @Override
    public Boolean deleteNoRealById(Long id) {
        CustomerBusinessInvoice invoice = getById(id);
        if (ObjectUtil.isEmpty(invoice)) {
            throw new ServiceException("发票不存在");
        }
        if (InvoiceStatus.INVOICE_UPLOAD_STATUS.UPLOADED.getCode().equals(invoice.getUploadStatus())) {
            throw new ServiceException("发票已上传至系统，不可删除");
        }
        invoice.setCompanyId(CommonConstant.NO_BODY_CLAIM);
        return updateById(invoice);
    }

    /**
     * 获取业务发票代码
     *
     * @param customerBusinessInvoice 识别结果
     * @return
     */
    private CustomerBusinessInvoice saveOrUpdateCustomerInvoice(CustomerBusinessInvoice customerBusinessInvoice, String url, String userId) {
        CustomerBusinessInvoice oldInvoice = selectByInvoiceNumberAndCode(customerBusinessInvoice.getNumber(), customerBusinessInvoice.getCode());
        //若数据库已存在 判断是否已上传
        if (ObjectUtil.isNotEmpty(oldInvoice)) {
            oldInvoice.setCompanyId(userId);
            oldInvoice.setProof(url);
            //处理发票金额字段
            calInvoiceMoney(customerBusinessInvoice);
            updateById(oldInvoice);
            return oldInvoice;
        }
        customerBusinessInvoice.setCompanyId(userId);
        customerBusinessInvoice.setProof(url);
        //保存新发票
        //处理发票金额字段
        calInvoiceMoney(customerBusinessInvoice);
        this.saveOrUpdate(customerBusinessInvoice);
        return customerBusinessInvoice;
    }

    /**
     * 识别成功后删除识别失败的数据
     *
     * @param picUrl
     */
    private void removeErrorImg(String picUrl) {
        remove(Wrappers.<CustomerBusinessInvoice>lambdaQuery().eq(CustomerBusinessInvoice::getProof, picUrl)
                .eq(CustomerBusinessInvoice::getIdentifyStatus, 2));
    }

    /**
     * 更新数据库发票字段
     *
     * @param customerBusinessInvoice    识别发票信息
     * @param customerBusinessInvoiceOld 数据库发票信息
     */
    private void updateField(CustomerBusinessInvoice customerBusinessInvoice, CustomerBusinessInvoice customerBusinessInvoiceOld) {
        customerBusinessInvoice.setId(customerBusinessInvoiceOld.getId());
        customerBusinessInvoice.setCreateUser(customerBusinessInvoiceOld.getCreateUser());
        customerBusinessInvoice.setCreateTime(customerBusinessInvoiceOld.getCreateTime());
        customerBusinessInvoice.setCreateDept(customerBusinessInvoiceOld.getCreateDept());
        customerBusinessInvoice.setUploadStatus(customerBusinessInvoiceOld.getUploadStatus());
        //处理发票金额字段
        calInvoiceMoney(customerBusinessInvoice);
    }

    @Override
    public void calInvoiceMoney(CustomerBusinessInvoice customerBusinessInvoice) {
        customerBusinessInvoice.setSubtotalDealAmount(dealMoneyField(customerBusinessInvoice.getSubtotalAmount()));
        customerBusinessInvoice.setSubtotalDealTax(dealMoneyField(customerBusinessInvoice.getSubtotalTax()));
        customerBusinessInvoice.setDealTotal(dealMoneyField(customerBusinessInvoice.getTotal()));
    }

    private BigDecimal dealMoneyField(String originMoneyField) {
        String numberByStr = CommonUtil.getNumberByStr(originMoneyField);
        return StrUtil.isEmpty(numberByStr) ? BigDecimal.ZERO : new BigDecimal(numberByStr);
    }


    public String getUserId() {
        if (ObjectUtil.isNotEmpty(MyAuthUtil.getUserId())) {
            return String.valueOf(MyAuthUtil.getUserId());
        } else if (ObjectUtil.isNotEmpty(AuthUtil.getUserId())) {
            return String.valueOf(AuthUtil.getUserId());
        }
        return null;
    }


    private String getInvoiceCacheKey(String userId, String customerData) {
        if (ObjectUtil.isNotEmpty(userId)) {
            return userId.concat(InvoiceEnum.INVOICE_CACHE_KEY).concat(customerData);
        } else {
            return InvoiceEnum.INVOICE_CACHE_KEY.concat(customerData);
        }
    }


    /**
     * 发票验证
     */
    @Override
    public boolean invoiceVertification(CustomerBusinessInvoiceDTO customerBusinessInvoiceDTO) {
        String checkCode = customerBusinessInvoiceDTO.getCheckCode();
//        Assert.isFalse(ObjectUtil.isEmpty(customerBusinessInvoiceDTO.getType()), "发票类型不能为空");
        //查看是否已上传
        CustomerBusinessInvoice customerBusinessInvoice = getByCodeAndNumber(customerBusinessInvoiceDTO.getCode(), customerBusinessInvoiceDTO.getNumber());
        if (ObjectUtil.isNotEmpty(customerBusinessInvoice)) {
            Assert.isTrue(CustomerBusinessInvoiceEnum.UN_UPLOAD.getCode().equals(customerBusinessInvoice.getUploadStatus()), "该发票已上传系统,请勿重新上传");
        }

        InvoiceOcrHandler template = invoiceOcrFactory.template();
        // 如果是阿里数脉则不需要验真
        if (template instanceof AliShumaiInvoiceOcrAndVerifyHandler) {
            // 如果发票存在则直接返回true
            if (ObjectUtil.isNotEmpty(customerBusinessInvoice)) {
                return true;
            }
            OtherApiInvoiceDto ocr = template.ocr(customerBusinessInvoiceDTO.getProof());
            if (ObjectUtil.isNotEmpty(ocr)) {
                return true;
            }
            return false;
        } else {
            //华为云校验
            InvoiceVertificationDTO invoiceVertification = new InvoiceVertificationDTO();
            invoiceVertification.setIssueDate(customerBusinessInvoiceDTO.getIssueDate());
            invoiceVertification.setCode(customerBusinessInvoiceDTO.getCode());
            invoiceVertification.setNumber(customerBusinessInvoiceDTO.getNumber());


            // 根据发票类型填写对应的校验码信息与金额信息
            //截取发票校验码
            invoiceVertification.setCheckCode(subInvoiceCheckCode(checkCode));

            //判断发票类型是否全电发票
            if(InvoiceTypeEnum.fullyDigitalizedInvoiceTypeList().contains(customerBusinessInvoiceDTO.getType())){
                //全电发票取价税合计金额
                invoiceVertification.setSubtotalAmount(CommonUtil.getNumberByStr(customerBusinessInvoiceDTO.getTotal().trim()));
                invoiceVertification.setCode("");
            }else{
                //非全电发票取不含税金额
                invoiceVertification.setSubtotalAmount(CommonUtil.getNumberByStr(customerBusinessInvoiceDTO.getSubtotalAmount().trim()));
            }

            //格式化日期
            invoiceVertification.setIssueDate(replaceChinese(invoiceVertification.getIssueDate(), "-"));
            //发票验证
            RecognizeInvoiceVerificationResponse response = huaweiApiService.invoiceVerification(invoiceVertification);
            //成功则将识别成功的信息赋予发票
            OcrResponse ocrResponse = JSONUtil.toBean(JSONUtil.parseObj(response.getResult()), OcrResponse.class);
            boolean isSusccess = huaweiApiService.isSuccess(ocrResponse);
            Integer vertify = null;
            CustomerBusinessInvoice newInvoice = BeanUtil.copy(customerBusinessInvoiceDTO, CustomerBusinessInvoice.class);
            if (Boolean.TRUE.equals(isSusccess)) {
                setVerifyInfoToCustomerDTO(JSONUtil.parseObj(JSONUtil.toJsonStr(response.getResult())), newInvoice);
                vertify = VERTIFY_SUCCEESS.getCode();
            } else {
                vertify = VERTIFY_FAIL.getCode();
            }
            newInvoice.setIdentifyStatus(vertify);
            newInvoice.setIsVertify(vertify);
            //金额处理
            calInvoiceMoney(newInvoice);
            saveOrUpdate(newInvoice);
            if (!isSusccess) {
                throw new ServiceException(getExceptoionMsg(ocrResponse, isSusccess).replaceAll("[^\u4E00-\u9FA5]", ""));
            }
            return isSusccess;
        }

    }

    public String getExceptoionMsg(OcrResponse ocrResponse, Boolean isSusccess) {
        String code = ocrResponse.getResult_code();
        if (Boolean.FALSE.equals(isSusccess)) {
            String msg;
            InvoiceEnum.InvoiceExceptionEnum invoiceEnum = InvoiceEnum.InvoiceExceptionEnum.getInvoiceDescByType(code);
            if (ObjectUtil.isNotEmpty(invoiceEnum)) {
                msg = invoiceEnum.getDesc();
                msg.replaceAll("[^\u4E00-\u9FA5]", "");
            } else {
                msg = "发票校验失败,传入参数不合法,请确认参数正确性!";
            }
            return msg;
        }
        return null;

    }

    private void setVerifyInfoToCustomerDTO(JSONObject result, CustomerBusinessInvoice customerBusinessInvoice) {
        customerBusinessInvoice.setBuyerId(result.getStr("buyer_id"));
        customerBusinessInvoice.setBuyerName(result.getStr("buyer_name"));
        customerBusinessInvoice.setBuyerBank(result.getStr("buyer_bank"));
        customerBusinessInvoice.setBuyerAddress(result.getStr("buyer_address"));
        customerBusinessInvoice.setSellerName(result.getStr("seller_name"));
        customerBusinessInvoice.setSellerId(result.getStr("seller_id"));
        customerBusinessInvoice.setSellerAddress(result.getStr("seller_address"));
        customerBusinessInvoice.setSellerBank(result.getStr("seller_bank"));
    }

    /**
     * 查出未验真成功的发票
     *
     * @param code
     * @param number
     * @return
     */
    private CustomerBusinessInvoice getErrorPicByCodeAndCheckNumber(String code, String number) {
        return getOne(Wrappers.<CustomerBusinessInvoice>lambdaQuery()
                .eq(CustomerBusinessInvoice::getCode, number)
                .eq(CustomerBusinessInvoice::getNumber, number)
                .ne(CustomerBusinessInvoice::getIdentifyStatus, 1)
                .orderByDesc(CustomerBusinessInvoice::getCreateTime).last("limit 1"));
    }

    private String subInvoiceCheckCode(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        String codeTrim = code.replaceAll(" ", "");
        if (codeTrim.length() < SIZE5) {
            throw new ServiceException("发票校验码不得少于5位");
        }
        int length = codeTrim.length();
        return StrUtil.sub(codeTrim, length - (length < 6 ? SIZE5 : SIZE6), length);
    }

    private boolean platFormVertifaction(CustomerBusinessInvoice o, CustomerBusinessInvoiceDTO dto) {
        if (isOne(o, dto)) {
            Assert.isFalse(VERTIFY_FAIL.getCode().equals(o.getIsVertify()), "验真失败,请检查发票信息");
            return VERTIFY_SUCCEESS.getCode().equals(o.getIsVertify());
        }
        return false;
    }

    private boolean isOne(CustomerBusinessInvoice o, CustomerBusinessInvoiceDTO dto) {
        return ObjectUtil.isNotEmpty(o) && o.getCode().equals(dto.getCode()) &&
                o.getSubtotalAmount().equals(dto.getSubtotalAmount()) &&
                o.getIssueDate().equals(dto.getIssueDate()) &&
                o.getNumber().equals(dto.getNumber());
    }


    /***
     *保存发票
     */
    @Async
    public boolean saveInvoice(CustomerBusinessInvoice customerBusinessInvoice) {
        return this.saveOrUpdate(customerBusinessInvoice);
    }


    /***
     *判断数据在枚举类中是否存在
     */
    public static Boolean judgeEnumValueIsExist(Class<?> tClass, Object enumFeild, Object o) {
        return EnumUtil.getFieldValues((Class<? extends Enum<?>>) tClass, (String) enumFeild).contains(o);
    }

    /**
     * 校验发票信息
     * 1.是否合法
     * 2.是否重复验真
     * 3.是否恶意刷接口
     */
    @SneakyThrows
    public CustomerBusinessInvoice checkInvoiceVertificationDTO(CustomerBusinessInvoiceDTO customerBusinessInvoiceDTO) {
        CustomerBusinessInvoice customerBusinessInvoice = getOne(Wrappers.<CustomerBusinessInvoice
                >lambdaQuery().eq(ObjectUtil.isNotEmpty(customerBusinessInvoiceDTO.getId()), CustomerBusinessInvoice::getId, customerBusinessInvoiceDTO.getId()));
        Assert.isFalse(ObjectUtil.isEmpty(customerBusinessInvoice), "请先上传该发票");
        return customerBusinessInvoice;
    }


    /***
     * 把中文替换为指定字符
     */
    public String replaceChinese(String source, String replacement) {
        if (replacement == null) {
            replacement = "";
        }
        String reg = "[\u4e00-\u9fa5]";
        Pattern pat = Pattern.compile(reg);
        Matcher mat = pat.matcher(source);
        String repickStr = mat.replaceAll(replacement);
        repickStr = repickStr.substring(0, repickStr.length() - 1);
        return repickStr;
    }

    @Override
    public CustomerBusinessInvoice selectByInvoiceNumberAndCode(String invoiceNumber, String code) {
        return getOne(Wrappers.<CustomerBusinessInvoice>lambdaQuery().eq(CustomerBusinessInvoice::getNumber, invoiceNumber)
                .eq(StringUtil.isNoneBlank(code), CustomerBusinessInvoice::getCode, code)
                .orderByDesc(CustomerBusinessInvoice::getCreateTime).last("limit 1"));
    }

    @Override
    public CustomerBusinessInvoice getByCodeAndNumber(String code, String number) {
        return getOne(Wrappers.<CustomerBusinessInvoice>lambdaQuery()
                .eq(StringUtil.isNoneBlank(code), CustomerBusinessInvoice::getCode, code)
                .eq(CustomerBusinessInvoice::getNumber, number).last("limit 1"));
    }

    @Override
    public boolean uploadBefore(List<Long> ids) {
        int count = this.count(Wrappers.<CustomerBusinessInvoice>lambdaQuery()
                .eq(CustomerBusinessInvoice::getUploadStatus, CustomerBusinessInvoiceEnum.UN_UPLOAD.getCode()).in(CustomerBusinessInvoice::getId, ids));
        return ids.size() == count;
    }

    @Override
    public boolean changeUploadStatus(List<Long> ids, Integer status) {
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }
        List<CustomerBusinessInvoice> list = this.listByIds(ids);
        if (list.size() == ids.size()) {
            list.forEach(e -> {
                e.setUploadStatus(status);
            });
            saveOrUpdateBatch(list);
        }
        return false;
    }

}
