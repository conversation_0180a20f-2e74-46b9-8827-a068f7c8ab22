/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.dto.FinanceCustomerDto;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.FinanceCustomerVo;

import java.util.List;
import java.util.Map;

/**
 * 企业信息名称 服务类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
public interface ICustomerInfoService extends BaseService<CustomerInfo> {

    /**
     * 自定义分页
     *
     * @param page
     * @param customerInfo
     * @return
     */
    IPage<CustomerInfoVO> selectCustomerInfoPage(IPage<CustomerInfoVO> page, CustomerInfoVO customerInfo);


    CustomerInfoVO selectByCustomerId(Long customerId);


    CustomerInfoVO selectByCompanyId(Long companyId);

    /**
     * 根据客户id选择脱敏
     */
    CustomerInfoVO selectVagueInfoByCustomerId(Long customerId);

    /***
     * 查询客户实名信息
     * @param isReal
     * @return
     */
    R getCustomerInfoByIsReal(boolean isReal, String userType);

    CustomerInfo isAuthOrAuthing(String licenceNumber);

    Boolean existOpeningCompany(Long customerId);

    /**
     * 更新企业实名信息 并创建企业
     *
     * @param customerInfo
     * @return
     */
    CustomerInfo updateEntInfoAndUserInfo(CustomerInfo customerInfo);


    /**
     * 根据公司征信代码查询企业信息
     *
     * @param code
     * @return
     */
    CustomerInfo getByBusinessLicenceNumber(String code);

    /**
     * 根据公司名称查询实名企业信息
     *
     * @param code       社会统一代码
     * @param authStatus 实名状态 不填代表全部
     * @return
     */
    CustomerInfo getByCompanyCode(String code, Integer authStatus);

    /**
     * 根据公司id查询实名企业信息
     *
     * @param companyId 公司id
     * @return
     */
    CustomerInfo getByCompanyId(Long companyId);

    /**
     * 根据客户id查询企业实名信息
     */
    CustomerInfo getByCustomerId(Long customerId);

    /**
     * 根据公司名称查询实名企业信息
     *
     * @param entName
     */
    CustomerInfo getByCompanyName(String entName);

    /**
     * 主动查询企业实名情况
     *
     * @param customerId 用户id
     * @param id         实名id
     * @return
     */
    R queryEntAuthByCustomerId(Long customerId, Long id);


    /**
     * 物理删除当前企业开通失败的企业流程
     *
     * @param customerId 客户id
     * @return
     */
    Boolean deleteRealByCustomerId(Long customerId);

    /**
     * 查询当前正在开通的企业
     *
     * @param customerId 客户id
     * @return
     */
    CustomerInfo getNotEnterCompanyInfoByCustomerId(Long customerId);

    List<CustomerInfo> getByCompanyIds(List<Long> companyIds);

    /**
     * 通过名称查询企业是否存在
     *
     * @param companyName 企业名称
     * @return
     */
    Boolean existByName(String companyName);

    /**
     * 通过用户id删除实名信息
     */
    Boolean deleteRealByCompanyId(Long companyId);

    /**
     * 設置融资转让权利
     *
     * @param companyId
     */
    void setTradeAuth(Long companyId);

    /**
     * 查询企业实名 条件：用户id,实名id
     */
    CustomerInfo getByCustomerIdAndId(Long companyId, Long id);


    /**
     * 根据企业userId查询省份简写
     *
     * @param enterpriseIds
     * @return
     */
    Map<Long, String> getAbbrMapByEnterpriseIds(List<Long> enterpriseIds);

    /**
     * 根据企业id查询工商信息
     * @param companyId
     * @return
     */
    CustomerBusinessInfo getCustomerBusinessInfoByCompanyId(String companyId);

    List<FinanceCustomerVo> selectFinanceCustomerList(FinanceCustomerDto financeCustomerDto);

    /**
     * 融资企业信息分页查询
     * <AUTHOR>
     * @date 2025/4/25 10:54
     * @param page
     * @param financeCustomerDto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<org.springblade.customer.vo.FinanceCustomerVo>
     */
    IPage<FinanceCustomerVo> selectFinanceCustomerPage(IPage<FinanceCustomerVo> page, FinanceCustomerDto financeCustomerDto);

}
