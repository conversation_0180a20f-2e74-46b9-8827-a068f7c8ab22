/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.customer.dto.EnterpriseQuotaDTO;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.vo.EnterpriseQuotaNewVo;
import org.springblade.customer.vo.EnterpriseQuotaVO;

import java.util.List;

/**
 * 核心企业额度 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
public interface EnterpriseQuotaMapper extends BaseMapper<EnterpriseQuota> {


	/**
	 * 查询核心企业额度列表
	 * @param page 分页对象
	 * @param enterpriseQuotaDTO 企业额度DTO对象
	 * @return IPage<EnterpriseQuotaVO>
	 */
    IPage<EnterpriseQuotaVO> selectCoreEnterpriseQuotaPage(@Param("page") IPage<EnterpriseQuota> page, @Param("enterpriseQuota") EnterpriseQuotaDTO enterpriseQuotaDTO);

	/**
	 * 查询融资企业额度列表
	 * @param page 分页对象
	 * @param enterpriseQuotaDTO 企业额度DTO对象
	 * @return IPage<EnterpriseQuotaVO>
	 */
	IPage<EnterpriseQuotaVO> selectFinancingEnterpriseQuotaPage(@Param("page") IPage<Object> page, @Param("enterpriseQuota") EnterpriseQuotaDTO enterpriseQuotaDTO);

	/**
	 * 查询融资企业额度汇总
	 * <AUTHOR>
	 * @date 2025/7/15 14:55
	 * @param enterpriseQuotaDTO
	 * @return org.springblade.customer.vo.EnterpriseQuotaVO
	 */
	EnterpriseQuotaVO selectFinancingEnterpriseQuotaSum(@Param("enterpriseQuota") EnterpriseQuotaDTO enterpriseQuotaDTO);

	/**
	 * 获取近10条额度记录
	 * <AUTHOR>
	 * @date 2025/3/15 17:53
	 * @return java.util.List<org.springblade.customer.vo.EnterpriseQuotaNewVo>
	 */
	List<EnterpriseQuotaNewVo> selectEnterpriseQuotaNewList();

	List<EnterpriseQuotaVO> selectExportList(@Param("enterpriseQuota") EnterpriseQuotaDTO enterpriseQuotaDTO);

}
