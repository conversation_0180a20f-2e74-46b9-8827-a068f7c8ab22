/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.dto.FrontFinancingListQueryDto;
import org.springblade.customer.entity.FrontFinancingList;
import org.springblade.customer.service.IFrontFinancingListService;
import org.springblade.customer.vo.FrontFinancingListVO;
import org.springblade.system.entity.User;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 融资企业列表信息表 控制器
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK+CommonConstant.WEB_BACK+"/customer/frontfinancinglist")
@Api(value = "融资企业列表信息表", tags = "融资企业列表信息表接口")
public class FrontFinancingListController extends BladeController {



	private final IFrontFinancingListService frontFinancingListService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:detail') or hasRole('administrator')")
	public R<FrontFinancingListVO> detail(Long id) {
		FrontFinancingListVO detail = frontFinancingListService.getFrontFinancingById(id);
		return R.data(detail);
	}

	/**
	 * 分页 融资企业列表信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:list') or hasRole('administrator')")
	public R<IPage<FrontFinancingListVO>> list(FrontFinancingListQueryDto dto, Query query) {
		IPage<FrontFinancingListVO> pages = frontFinancingListService.getPageFinancingList(query, dto);
		return R.data(pages);
	}


	/**
	 * 自定义分页 融资企业列表信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:page') or hasRole('administrator')")
	public R<IPage<FrontFinancingListVO>> page(FrontFinancingListVO frontFinancingList, Query query) {
		IPage<FrontFinancingListVO> pages = frontFinancingListService.selectFrontFinancingListPage(Condition.getPage(query), frontFinancingList);
		return R.data(pages);
	}

	/**
	 * 新增 融资企业列表信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:save') or hasRole('administrator')")
	public R save(@Valid @RequestBody FrontFinancingList frontFinancingList) {
		return R.status(frontFinancingListService.save(frontFinancingList));
	}

	/**
	 * 修改 融资企业列表信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:update') or hasRole('administrator')")
	public R update(@Valid @RequestBody FrontFinancingList frontFinancingList) {
		return R.status(frontFinancingListService.updateById(frontFinancingList));
	}

	/**
	 * 新增或修改 融资企业列表信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入frontFinancingList")
    @PreAuth( "hasPermission('customer:frontfinancinglist:submit') or hasRole('administrator')")
	public R submit(@Valid @RequestBody FrontFinancingList frontFinancingList) {
		return R.status(frontFinancingListService.saveOrUpdate(frontFinancingList));
	}


	/**
	 * 删除 融资企业列表信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth( "hasPermission('customer:frontfinancinglist:remove') or hasRole('administrator')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(frontFinancingListService.deleteLogic(Func.toLongList(ids)));
	}




	/**
	 * 根据status获取list集合
	 * @return
	 */
	@GetMapping("/financing-all")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询所有没有被删除的融资客户", notes = "传入status")
	public R<List<User>> allByStatus(@RequestParam(required = false) Integer status) {
		return R.data(frontFinancingListService.getListByStatus(status));
	}

	/**
	 * 查询所有融资企业信息
	 * @return
	 */
	@GetMapping("/all")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "查询所有融资企业信息")
	@PreAuth("hasPermission('customer:frontfinancinglist:all') or hasRole('administrator')")
	public R<List<FrontFinancingListVO>> all() {
		return R.data(frontFinancingListService.all());
	}

}
