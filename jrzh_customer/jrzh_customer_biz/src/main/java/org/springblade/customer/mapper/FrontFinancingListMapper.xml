<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.FrontFinancingListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="frontFinancingListResultMap" type="org.springblade.customer.entity.FrontFinancingList">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="customer_white" property="customerWhite"/>
        <result column="invitation_code" property="invitationCode"/>
        <result column="customer_score" property="customerScore"/>
        <result column="customer_code" property="customerCode"/>
        <result column="company_id" property="companyId"/>
        <result column="tenant_id" property="tenantId"></result>
    </resultMap>


    <select id="selectFrontFinancingListPage" resultMap="frontFinancingListResultMap">
        select * from jrzh_customer_front_financing_list where is_deleted = 0
    </select>

    <select id="getPageFinancingList" resultType="org.springblade.customer.vo.FrontFinancingListVO">
        SELECT
        cffl.*,
        cfu.account_user,
        cfu.last_visit_time,
        cfu.`name`,
        cfu.phone,
        cfu.`status` AS customerStatus,
        cfu.logo_src,
        1 AS customerLabel
        FROM jrzh_customer_front_financing_list cffl
        LEFT JOIN jrzh_customer_front_user cfu ON cfu.company_id = cffl.company_id
        <where>
            cffl.is_deleted = 0
            AND cfu.is_deleted = 0
            <if test="dto.name != null and dto.name !=''" >
                AND cfu.name LIKE concat('%',#{dto.name},'%')
            </if>
            <if test="dto.phone != null and dto.phone !=''" >
                AND cfu.phone LIKE concat('%',#{dto.phone},'%')
            </if>
            <if test="dto.typeList != null" >
                AND cfu.type IN
                <foreach collection="dto.typeList" item="type" index="index" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
