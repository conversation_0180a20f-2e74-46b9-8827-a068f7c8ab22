/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.exception.ZipUploadException;
import org.springblade.core.boot.props.BladeFileProperties;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.constant.InvoiceStatus;
import org.springblade.customer.dto.CustomerBusinessInvoiceDTO;
import org.springblade.customer.entity.CustomerBusinessInvoice;
import org.springblade.customer.entity.CustomerGoodsTradeBackground;
import org.springblade.customer.entity.SalesContract;
import org.springblade.customer.enums.TradeBackGroundEnum;
import org.springblade.customer.mapper.SalesContractMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerBusinessInvoiceManagerVO;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.customer.vo.TradeBackgroundVO;
import org.springblade.resource.builder.oss.OssBuilder;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.zip.ZipFile;


/**
 * 销售合同 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Service
@AllArgsConstructor
@Slf4j
public class AssetCoreServiceImpl extends BaseServiceImpl<SalesContractMapper, SalesContract> implements IAssetCoreService {

	private final ITradeBackgroundService tradeBackgroundService;
	private final ICustomerBusinessInvoiceService businessInvoiceService;
	private final ICustomerGoodsTradeBackgroundService goodsTradeBackgroundService;
	private final IEnterpriseQuotaService quotaService;
	private final RemoteDeptSearchService remoteDeptSearchService;
	private static final ConcurrentHashMap<String, ZipSaleTask> currentZipTaskPool = new ConcurrentHashMap<>();
	private final OssBuilder ossBuilder;

	@Override
	public void zipUpload(MultipartFile file, String key) {
		//结束之前任务
		ZipSaleTask beforeZip = currentZipTaskPool.get(key);
		if (ObjectUtil.isNotEmpty(beforeZip)) {
			//正常结束
			if (beforeZip.getCount().equals(beforeZip.getCurrentIndex())) {
				currentZipTaskPool.remove(key);
			} else {
				//非正常结束
				stopCurrentTask(key);
			}
		}
		String originalFilename = IdWorker.getId() + file.getOriginalFilename();
		String suffix = FileUtil.getSuffix(originalFilename);
		if (!"zip".equals(suffix)) {
			throw new ServiceException("暂时仅支持Zip格式");
		}
		File fileTransfer = null;
		try {
			//建立文件中转站
			fileTransfer = new File(new BladeFileProperties().getRealPath(), Objects.requireNonNull(originalFilename));
			file.transferTo(fileTransfer);
		} catch (IOException e) {
			e.printStackTrace();
		}
		ZipSaleTask zipSaleTask = new ZipSaleTask(0, 0, true, originalFilename);
		//放入任务池 标识任务开始
		currentZipTaskPool.put(key, zipSaleTask);
		String userId = MyAuthUtil.getCompanyId();
		String tentId = MyAuthUtil.getTentId();
		ThreadUtil.execAsync(()->TenantBroker.runAs(tentId, e -> resolve(key, userId)));
	}

	@Override
	public void stopCurrentTask(String key) {
		ZipSaleTask zipSaleTask = currentZipTaskPool.get(key);
		if (ObjectUtil.isEmpty(zipSaleTask)) {
			throw new ServiceException("未存在任务");
		}
		if (Boolean.FALSE.equals(zipSaleTask.getRunFlag())) {
			throw new ServiceException("任务已停止");
		}
		zipSaleTask.setRunFlag(false);
		//防止任务停止结束
		ThreadUtil.safeSleep(1000);
	}

	@Override
	public R getCurrentUploadTask(String key) {
		//查询是否存在任务
		ZipSaleTask zipSaleTask = currentZipTaskPool.get(key);
		if (ObjectUtil.isEmpty(zipSaleTask)) {
			return R.fail("未存在任务");
		}
		Integer count = zipSaleTask.getCount();
		Integer currentIndex = zipSaleTask.getCurrentIndex();
		if (count.equals(currentIndex)) {
			currentZipTaskPool.remove(key);
			return R.data(new ZipSaleTask(count, currentIndex, false, null));
		}
		return R.data(201, zipSaleTask, "任务进行中");
	}

	@Override
	public List<CustomerBusinessInvoiceManagerVO> listInvoiceManager(String companyId) {
		List<CustomerBusinessInvoiceManagerVO> list = new ArrayList<>();
		if (ObjectUtil.isEmpty(companyId)) {
			throw new ServiceException("登录已过期");
		}
		//未检查的发票
		CustomerBusinessInvoiceManagerVO unInvoiceManager = new CustomerBusinessInvoiceManagerVO();
		HashSet<CustomerBusinessInvoice> unCheckInvoices = new HashSet<>();

		List<CustomerBusinessInvoice> invoiceList = businessInvoiceService.listUnUploadByCompanyId(companyId);
		//没有社会统一代码的发票
		List<CustomerBusinessInvoice> noCodeInvoice = invoiceList.stream().filter(e -> ObjectUtil.isEmpty(e.getSellerId())).collect(Collectors.toList());
		//查询出所有未使用的发票 根据社会统一代码分类
		Map<String, List<CustomerBusinessInvoice>> invoicesMap = invoiceList
			.stream().filter(e -> ObjectUtil.isNotEmpty(e.getSellerId())).collect(Collectors.groupingBy(CustomerBusinessInvoice::getSellerId));
		Long companyIdLong = Func.toLong(companyId);
		//查询所有相关的上游贸易背景
		List<TradeBackgroundVO> backgrounds = tradeBackgroundService.listHeightByCompanyId(companyIdLong, 3, TradeBackGroundEnum.TRADE_BACKGROUND_STATUS.RELATED.getCode());
		Map<Long, TradeBackgroundVO> backgroundsMap = backgrounds.stream().collect(Collectors.toMap(TradeBackgroundVO::getId, e -> e));
		//根据贸易背景查询产品贸易背景
		//核心企业开通的产品 进行分组计算
		if (CollectionUtil.isNotEmpty(backgroundsMap)) {
			List<Long> backIds = new ArrayList(backgroundsMap.keySet());
			List<CustomerGoodsTradeBackground> goodsTradeBackgrounds = goodsTradeBackgroundService.listRelationByBackIds(backIds);
			if (ObjectUtil.isNotEmpty(goodsTradeBackgrounds)) {
				//循环赋值
				for (CustomerGoodsTradeBackground goodsTradeBackground : goodsTradeBackgrounds) {
					Long tradeBackgroundId = goodsTradeBackground.getTradeBackgroundId();
					TradeBackgroundVO backgroundVO = backgroundsMap.get(tradeBackgroundId);
					String companyHeightCode = backgroundVO.getCompanyHeightCode();
					CustomerBusinessInvoiceManagerVO invoiceManagerVO = new CustomerBusinessInvoiceManagerVO();
					EnterpriseQuotaVO quotaVO = quotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseId(goodsTradeBackground.getGoodsId(), 2, companyIdLong);
					if (ObjectUtil.isEmpty(quotaVO)) {
						continue;
					}
					//获取该产品额度
					Dept capital = remoteDeptSearchService.getDeptById(quotaVO.getCapitalId()).getData();
					invoiceManagerVO.setLogo(ObjectUtil.isNotEmpty(capital) ? capital.getLogoSrc() : "");
					invoiceManagerVO.setBackId(tradeBackgroundId);
					invoiceManagerVO.setAvailableAmount(quotaVO.getAvailableAmount());
					invoiceManagerVO.setCompanyHeightName(backgroundVO.getCompanyHeightName());
					invoiceManagerVO.setGoodsId(quotaVO.getGoodsId());
					invoiceManagerVO.setGoodsName(quotaVO.getGoodsName());
					//根据上游贸易伙伴社会代码获取对应发票集合
					List<CustomerBusinessInvoice> invoices = invoicesMap.get(companyHeightCode);
					if (CollectionUtil.isNotEmpty(invoices)) {
						//计算成功数 失败数 发票总金额
						calInvoiceTotal(invoices, invoiceManagerVO);
						list.add(invoiceManagerVO);
					}
					//删除集合中已赋值元素
					invoicesMap.remove(companyHeightCode);
				}
			}
		}
		//非核心企业开通产品发票一律放到一个分组中 未归类发票
		if (ObjectUtil.isNotEmpty(noCodeInvoice)) {
			unCheckInvoices.addAll(noCodeInvoice);
		}
		if (ObjectUtil.isNotEmpty(invoicesMap)) {
			invoicesMap.forEach((k, v) -> {
				unCheckInvoices.addAll(v);
			});
		}
		if (CollectionUtil.isEmpty(unCheckInvoices)) {
			return list;
		}
		unInvoiceManager.setGoodsName("未归类产品");
		unInvoiceManager.setAvailableAmount(BigDecimal.ZERO);
		//设置唯一标识
		unInvoiceManager.setBackId(1008611L);
		unInvoiceManager.setGoodsId(1008611L);
		//计算成功数 失败数 发票总金额
		calInvoiceTotal(new ArrayList<>(unCheckInvoices), unInvoiceManager);
		list.add(unInvoiceManager);
		return list;
	}

	/**
	 * 计算成功数 失败数 发票总金额
	 *
	 * @param invoices         发票集合
	 * @param invoiceManagerVO 发票分组管理信息
	 */
	private void calInvoiceTotal(List<CustomerBusinessInvoice> invoices, CustomerBusinessInvoiceManagerVO
		invoiceManagerVO) {
		BigDecimal subtotalDealAmount = BigDecimal.ZERO;
		Integer successCount = 0;
		Integer failCount = 0;
		if (CollectionUtil.isNotEmpty(invoices)) {
			for (CustomerBusinessInvoice invoice : invoices) {
				if (ObjectUtil.isNotEmpty(invoice.getSubtotalDealAmount())) {
					subtotalDealAmount = subtotalDealAmount.add(invoice.getSubtotalDealAmount());
				}
				if (InvoiceStatus.INVOICE_VERTIFICATION_STATUS.VERTIFY_SUCCEESS.getCode().equals(invoice.getIsVertify())) {
					successCount++;
				} else {
					failCount++;
				}
			}
		}
		invoiceManagerVO.setFailCount(failCount);
		invoiceManagerVO.setSuccessCount(successCount);
		invoiceManagerVO.setTotalSubtotalAmount(subtotalDealAmount);
	}

	@Transactional(rollbackFor = ZipUploadException.class)
	public R resolve(String key, String userId) {
		ZipSaleTask zipSaleTask = currentZipTaskPool.get(key);
		String fileName = zipSaleTask.getFileName();
		List<CustomerBusinessInvoice> invoices = new ArrayList<>(zipSaleTask.getCount());
		//建立文件中转站
		File file = new File(new BladeFileProperties().getRealPath(), Objects.requireNonNull(fileName));
		if (!file.exists()) {
			throw new ServiceException("文件已被系统自动删除，请重新上传");
		}
		try {
			//解析Zip文件内的jpg
			ZipFile zipFile = ZipUtil.toZipFile(file, Charset.forName("GBK"));
			//获取压缩包总数量
			Integer size = zipFile.size();
			zipSaleTask.setCount(size);
			ZipUtil.read(zipFile, zipEntry -> {
				Boolean runFlag = zipSaleTask.getRunFlag();
				//若任务停止 则返回停止成功
				if (Boolean.FALSE.equals(runFlag)) {
					throw new ZipUploadException();
				}
				Integer currentIndex = zipSaleTask.getCurrentIndex();
				String entryName = zipEntry.getName();
				//判断是否为支持类型
				if (!izSupportFile(entryName)) {
					zipSaleTask.setCurrentIndex(currentIndex + 1);
				} else {
					InputStream stream = ZipUtil.getStream(zipFile, zipEntry);
					//上传minio
					BladeFile bladeFile = ossBuilder.template().putFile(entryName, stream);
					String url = bladeFile.getLink();
					//发票识别并验真 识别后将发票塞入invoice中
					regAndVerify(url, invoices, userId);
					zipSaleTask.setCurrentIndex(currentIndex + 1);
				}
			});
		} catch (ZipUploadException e) {
			log.info("解析任务终止，任务文件名:{}", fileName);
			//将发票进行假删除
			for (CustomerBusinessInvoice invoice : invoices) {
				invoice.setCompanyId(CommonConstant.NO_BODY_CLAIM);
			}
			businessInvoiceService.updateBatchById(invoices);
		} catch (IORuntimeException e) {
			e.printStackTrace();
			if (file.exists()) {
				file.delete();
			}
			throw new ServiceException("请保证Zip格式正确，请勿采用修改文件后缀方式创建zip文件");
		} finally {
			//退出时进行删除
			if (file.exists()) {
				file.delete();
			}
		}
		return R.status(true);
	}

	/**
	 * 发票识别并验真
	 *
	 * @param url
	 */
	private void regAndVerify(String url, List<CustomerBusinessInvoice> invoiceList, String userId) {
		try {
			CustomerBusinessInvoice customerBusinessInvoice = businessInvoiceService.requestInvoiceOcr(url, userId);
			if (ObjectUtil.isNotEmpty(customerBusinessInvoice) && ObjectUtil.isNotEmpty(customerBusinessInvoice.getCode())) {
				CustomerBusinessInvoiceDTO invoiceDTO = BeanUtil.copy(Objects.requireNonNull(customerBusinessInvoice), CustomerBusinessInvoiceDTO.class);
				businessInvoiceService.invoiceVertification(invoiceDTO);
				invoiceList.add(invoiceDTO);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 判斷文件名是否符合识别要求
	 *
	 * @param entryName 文件名
	 * @return
	 */
	private boolean izSupportFile(String entryName) {
		String suffix = FileUtil.getSuffix(entryName);
		return "jpeg".equals(suffix) || "jpg".equals(suffix) || "png".equals(suffix) || "bmp".equals(suffix) || "tiff".equals(suffix) || "pdf".equals(suffix) || "ofd".equals(suffix);
	}
}

/**
 * Zip任务类
 */
@Data
@AllArgsConstructor
class ZipSaleTask {
	/**
	 * 总数量
	 */
	private Integer count;
	/**
	 * 当前下标
	 */
	private Integer currentIndex;
	/**
	 * 运行状态
	 */
	private Boolean runFlag;

	public ZipSaleTask(Integer count, Integer currentIndex, Boolean runFlag, String fileName) {
		this.count = count;
		this.currentIndex = currentIndex;
		this.runFlag = runFlag;
		this.fileName = fileName;
	}

	/**
	 * 百分比
	 */
	private BigDecimal percent;
	/**
	 * 文件名
	 */
	private String fileName;

	public BigDecimal getPercent() {
		if (count > 0 && currentIndex > 0) {
			return BigDecimal.valueOf(currentIndex).divide(BigDecimal.valueOf(count), 2, CommonConstant.NUMBER_STRATEGY).multiply(BigDecimal.valueOf(100));
		}
		return BigDecimal.ZERO;
	}
}
