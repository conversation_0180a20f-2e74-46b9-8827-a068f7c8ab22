/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.front;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerService;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业信息名称 控制器
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/customerInfo")
@Api(value = "企业信息名称", tags = "企业信息名称接口")
public class CustomerInfoFrontController extends BladeController {

    private final ICustomerInfoService customerInfoService;
    private final ICustomerService iCustomerService;
    private final RemoteUserService remoteUserService;

    /***
     * 根据customerId 查找客户信息
     */
    @GetMapping("/getById")
    @ApiOperation(value = "获取人员信息", notes = "传入customerId")
    public R<CustomerInfoVO> customerInfo(@RequestParam Long customerId) {
        return R.data(customerInfoService.selectByCustomerId(customerId));
    }


    @GetMapping("/getCustomerInfo")
    public R getCustomerInfo() {
        return R.data(customerInfoService.selectByCustomerId(MyAuthUtil.getId()));
    }

    /***
     *获取经办人-公司经营-法人-证件
     * @param attachId
     * @return
     */
    @GetMapping("getPicByAttachId")
    public R<?> getPicById(@RequestParam String attachId) {
        List<Attach> attachList = SpringUtil.getBean(IAttachService.class).lambdaQuery().in(Attach::getId, Func.toLongList(attachId)).list();
        return R.data(attachList.stream().map(Attach::getLink).collect(Collectors.toList()));
    }


    @GetMapping("searchRealName")
    @ApiOperation(value = "查询是否已经实名")
    public R searchRealName() {
        return R.status(iCustomerService.isRealName());
    }


    //TODO 暂未引入
//	@GetMapping("/searchCompanyIsExsit")
//	@ApiOperation(value = "查询企业是否真实存在")
//	public R searchCompanyIsExsit(@RequestParam String companyName) {
//		return R.status(skyEyeService.getCompanyRealExist(companyName));
//	}


    @GetMapping("searchCompanyStatusInfo")
    public R searchCompanyStatusInfo(@RequestParam boolean isReal, @RequestParam(defaultValue = "1") String userType) {
        return customerInfoService.getCustomerInfoByIsReal(isReal, userType);
    }
    //TODO 待做 换到企业接口去

    /**
     * 查询 企业名称
     *
     * @return 企业名称
     */
    @GetMapping("/getEntName")
    @ApiOperation(value = "获取企业名称")
    public R<String> getEntName() {
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(AuthUtil.getUserId());
        return R.data(customerInfo == null ? "" : customerInfo.getCorpName());
    }

    /***
     * 修改企业头像
     */
    @PostMapping("/updateLogoSrc")
    public R<Boolean> updateLogoSrc(String logoSrc) {
        if (ObjectUtil.isEmpty(AuthUtil.getUserId())) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, ResultCode.CLIENT_UN_AUTHORIZED.getMessage());
        }
        if (StringUtil.isEmpty(logoSrc)) {
            return R.fail("未找到参数");
        }
        Long userId = AuthUtil.getUserId();
        User user = CustomerUserCache.getUserById(userId);
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(userId);
        if (ObjectUtil.isNotEmpty(customerInfo)) {
            customerInfo.setLogo(logoSrc);
            customerInfoService.updateById(customerInfo);
        }
        user.setAvatar(logoSrc);
        return remoteUserService.updateById(user);
    }

}
