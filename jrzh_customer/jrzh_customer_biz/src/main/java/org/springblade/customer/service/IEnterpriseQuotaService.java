/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.customer.dto.CorporateAccountDTO;
import org.springblade.customer.dto.EnterpriseQuotaDTO;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.excel.EnterpriseQuotaExcel;
import org.springblade.customer.vo.EnterpriseQuotaNewVo;
import org.springblade.customer.vo.EnterpriseQuotaVO;
import org.springblade.process.dto.ReceiveUnfreezeApplyDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 融资/核心企业额度 服务类
 *
 * <AUTHOR>
 * @since 2022-02-10
 */
public interface IEnterpriseQuotaService extends BaseService<EnterpriseQuota> {

	/**
	 * 申请额度
	 *
	 * @param enterpriseQuota 企业额度对象
	 * @return boolean
	 */
	boolean applyQuota(EnterpriseQuotaDTO enterpriseQuota);

	/**
	 * 调整额度
	 *
	 * @param enterpriseQuota 企业额度对象
	 * @return boolean
	 */
	boolean modifyQuota(EnterpriseQuotaDTO enterpriseQuota);

	/**
	 * 平台融资企业额度调整
	 *
	 * @param enterpriseQuota
	 * @return
	 */
	boolean financingModifyQuota(EnterpriseQuotaDTO enterpriseQuota);

	/**
	 * 冻结金额
	 *
	 * @param id           企业额度id
	 * @param frozenReason 冻结原因
	 * @return boolean
	 */
	boolean frozenAmount(Long id, String frozenReason);

	/**
	 * 解冻金额
	 *
	 * @param id         企业额度id
	 * @param thawReason 解冻原因
	 * @return boolean
	 */
	boolean thawAmount(Long id, String thawReason);

	/**
	 * 禁用
	 *
	 * @param id            企业额度id
	 * @param disableReason 禁用原因
	 * @return boolean
	 */
	boolean disable(Long id, String disableReason);

	/**
	 * 保存核心企业额度 平台发起
	 *
	 * @param variables 流程变量
	 * @param status
	 * @return Long
	 */
	Long saveEnterpriseQuota(Map<String, Object> variables, Integer status);

	/**
	 * 获取客户有效的产品额度集合
	 *
	 * @param userId         用户id
	 * @param enterpriseType 企业类型 1 融资 2 核心
	 * @return
	 */
	List<EnterpriseQuota> listValidQuota(Long userId, Integer enterpriseType);

	/**
	 * 保存融资企业额度
	 *
	 * @param variables 流程变量
	 * @return boolean
	 */
	Long saveFinancingEnterpriseQuota(Map<String, Object> variables);

	/**
	 * 查询没有额度的核心企业id
	 *
	 * @param companyLowerId 核心企业id集合
	 * @param goodsId        产品id
	 * @return boolean
	 */
	List<Long> getByGoodsIdAndEnterpriseId(List<Long> companyLowerId, Long goodsId);

	/**
	 * 查询没有额度的核心企业id
	 *
	 * @param companyLowerId
	 * @param goodsId
	 * @return
	 */
	List<Long> getByGoodsIdAndEnterpriseId(List<Long> companyLowerId, List<Long> goodsId);

	/**
	 * 激活
	 *
	 * @param id 企业额度id
	 * @return boolean
	 */
	boolean active(Long id);

	/**
	 * 续期
	 *
	 * @param enterpriseQuota 企业额度对象
	 * @return boolean
	 */
	boolean renewal(EnterpriseQuotaDTO enterpriseQuota);

	/**
	 * 扣减额度
	 *
	 * @param goodsId        产品id
	 * @param enterpriseId   核心企业id blade_user id
	 * @param enterpriseType 1 融资 2 核心
	 * @param amount         额度
	 * @return boolean
	 */
	boolean subtractQuota(Long goodsId, Long enterpriseId, Integer enterpriseType, BigDecimal amount);


	/**
	 * 查询核心企业额度列表
	 *
	 * @param enterpriseQuotaDTO 企业额度DTO对象
	 * @param query              分页参数
	 * @return Page
	 */
	IPage<EnterpriseQuotaVO> selectEnterpriseQuotaPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query);

	/**
	 * 查询融资企业额度列表
	 *
	 * @param enterpriseQuotaDTO 企业额度DTO对象
	 * @param query              分页参数
	 * @return Page
	 */
	IPage<EnterpriseQuotaVO> selectFinancingEnterpriseQuotaPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query);

	/**
	 * 统计核心企业所有的额度
	 *
	 * @param enterpriseIdList 企业id集合
	 * @return Map
	 */
	Map<Long, BigDecimal> selectAmountByEnterpriseId(List<Long> enterpriseIdList);
	/**
	 * 查询企业额度信息
	 *
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型 1 融资 2 核心
	 * @param enterpriseId   企业id
	 * @return 企业额度对象
	 */
	EnterpriseQuota getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(Long goodsId, Integer enterpriseType, Long enterpriseId);
	/**
	 * 查询企业额度信息
	 *
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型 1 融资 2 核心
	 * @param enterpriseId   企业id
	 * @return 企业额度对象
	 */
	EnterpriseQuotaVO getByGoodsIdAndEnterpriseTypeAndEnterpriseId(Long goodsId, Integer enterpriseType, Long enterpriseId);

	/**
	 * 根据id查询并根据id分组
	 *
	 * @param idList id集合
	 * @return map id为key，惬意额度对象为value
	 */
	Map<Long, EnterpriseQuota> getMapInId(List<Long> idList);

	/**
	 * getById
	 *
	 * @param id id
	 * @return EnterpriseQuotaVO
	 */
	EnterpriseQuotaVO detail(Long id);

	/**
	 * 启用
	 *
	 * @param id id
	 * @return true or false
	 */
	boolean enable(Long id);

	/**
	 * 将申请中金额返回到可用金额
	 *
	 * @param amount  金额
	 * @param goodsId 产品id
	 * @param userId  用户id
	 * @param type    企业类型
	 */
	void returnApplyAmount(BigDecimal amount, Long goodsId, Long userId, int type);

	/**
	 * 获取回款账款后缀  中国银行(9875)
	 *
	 * @param goodsId 产品id
	 * @param userId  用户id
	 * @return 中国银行(9875)
	 */
	String getRepaymentAccountSuffix(Long goodsId, Long userId);

	/**
	 * 获取回款账款后缀  中国银行(9875)
	 *
	 * @param goodsIdList 产品id
	 * @param userId      用户id
	 * @return Map<Long, String>
	 */
	Map<Long, String> getRepaymentAccountSuffixMap(List<Long> goodsIdList, Long userId);


	/**
	 * 查询核心企业云信额度列表
	 *
	 * @param enterpriseQuotaDTO 企业额度DTO对象
	 * @param query              分页参数
	 * @return Page
	 */
	IPage<EnterpriseQuotaVO> selectEnterpriseQuotaCloudPage(EnterpriseQuotaDTO enterpriseQuotaDTO, Query query);

	/**
	 * 添加额度
	 *
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型
	 * @param userId         用户id
	 * @param amount         金额
	 */
	void addQuota(Long goodsId, Integer enterpriseType, Long userId, BigDecimal amount);

	/**
	 * 添加申请中金额
	 *
	 * @param enterpriseId   企业额度id
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型
	 * @param amount         金额
	 */
	void addApplyAmount(Long goodsId, Long enterpriseId, Integer enterpriseType, BigDecimal amount);

	/**
	 * 添加使用中金额
	 *
	 * @param enterpriseId   企业额度id
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型
	 * @param amount         金额
	 */
	void addUsedAmount(Long goodsId, Long enterpriseId, int enterpriseType, BigDecimal amount);

	/**
	 * 扣减申请中额度
	 *
	 * @param amount         金额
	 * @param goodsId        产品id
	 * @param enterpriseType 企业类型
	 * @param userId         企业额度id
	 */
	void subtractApplyQuota(BigDecimal amount, Long goodsId, int enterpriseType, Long userId);

	/**
	 * 额度扣减
	 *
	 * @param amount          融资金额
	 * @param enterpriseQuota 已开通产品 额度
	 * @param type            1、代采申请中：可用额度   减去 融资金额 然后  申请中的额度 加上 融资金额
	 *                        2、驳回：     申请中额度 减去 融资金额 然后 可用额度 加上 融资金额
	 *                        3、通过：     申请中额度 减去 融资金额 然后 已用额度 加上 融资金额
	 */
	void subtractReceivableAmount(BigDecimal amount, EnterpriseQuota enterpriseQuota, int type);

	/**
	 * 冻结客户可用产品
	 *
	 * @param ids
	 */
	void frozenValidAmountByQuotaIds(List<Long> ids, List<CustomerGoods> customerGoods);

	/**
	 * 融资企业额度历史
	 *
	 * @param id
	 * @return
	 */
	EnterpriseQuotaVO financingQuotaHistory(Long id);

	/**
	 * 核心企业额度历史
	 *
	 * @param id
	 * @return
	 */
	EnterpriseQuotaVO quotaHistory(Long id);

	/**
	 * 核心企业调整额度
	 *
	 * @param enterpriseQuota
	 * @return
	 */
	boolean coreModifyQuota(EnterpriseQuotaDTO enterpriseQuota);

	/**
	 * 查询企业额度
	 *
	 * @param companyId 企业id
	 * @param type      额度类型
	 */
	List<EnterpriseQuota> selectEnterpriseQuota(Long companyId, Integer type);

	/**
	 * 保存对公账户
	 * @param corporateAccountDTO
	 * @return
	 */
	Boolean saveBankAccount(CorporateAccountDTO corporateAccountDTO);

	/**
	 * 产品解冻申请
	 * @param receiveUnfreezeApply
	 * @return
	 */
	void receiveUnfreezeApply(ReceiveUnfreezeApplyDTO receiveUnfreezeApply);

	/**
	 * 解冻额度-业务审批通过
	 * @param processInstanceId
	 * @param variables
	 */
	void handlerUnfreezeGoodsApplyProcessCompleted(String processInstanceId, Map<String, Object> variables);

	/**
	 * 解冻额度-业务审批终止
	 * @param processInstanceId
	 * @param variables
	 */
	void handlerUnfreezeGoodsApplyProcessTerminate(String processInstanceId, Map<String, Object> variables);

	/**
	 * 根据产品列表、企业类型、企业id 获取额度列表
	 * @param goodsIdList
	 * @param enterpriseType
	 * @param enterpriseId
	 * @return
	 */
	List<EnterpriseQuota> getListByGoodsIdsAndEnterpriseTypeAndgEnterpriseId(List<Long> goodsIdList, Integer enterpriseType, Long enterpriseId);


	List<EnterpriseQuotaExcel> export(EnterpriseQuotaDTO enterpriseQuotaDTO);

	/**
	 * 查询所有授信额度
	 * <AUTHOR>
	 * @date 2025/3/15 16:32
	 * @return java.math.BigDecimal
	 */
	BigDecimal getTotalQuota();

	/**
	 * 获取近10条额度记录
	 * <AUTHOR>
	 * @date 2025/3/15 17:55
	 * @return java.util.List<org.springblade.customer.vo.EnterpriseQuotaNewVo>
	 */
	List<EnterpriseQuotaNewVo> selectEnterpriseQuotaNewList();
}
