/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.customer.dto.FrontFinancingListQueryDto;
import org.springblade.customer.entity.FrontFinancingList;
import org.springblade.customer.vo.FrontFinancingListVO;

import java.util.List;

/**
 * 融资企业列表信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
public interface FrontFinancingListMapper extends BaseMapper<FrontFinancingList> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param frontFinancingList
	 * @return
	 */
	List<FrontFinancingListVO> selectFrontFinancingListPage(IPage page, FrontFinancingListVO frontFinancingList);

	IPage<FrontFinancingListVO> getPageFinancingList(IPage<FrontFinancingListVO> page, @Param("dto") FrontFinancingListQueryDto dto);

}
