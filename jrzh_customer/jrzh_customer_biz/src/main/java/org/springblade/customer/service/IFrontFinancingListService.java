/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.customer.dto.FrontFinancingListQueryDto;
import org.springblade.customer.entity.FrontFinancingList;
import org.springblade.customer.vo.FrontFinancingListVO;
import org.springblade.system.entity.User;

import java.util.List;

/**
 * 融资企业列表信息表 服务类
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
public interface IFrontFinancingListService extends BaseService<FrontFinancingList> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param frontFinancingList
	 * @return
	 */
	IPage<FrontFinancingListVO> selectFrontFinancingListPage(IPage<FrontFinancingListVO> page, FrontFinancingListVO frontFinancingList);

	/****
	 * 查询 融资企业数据
	 * @param query 分页数据
	 * @param dto
	 * @return
	 */
	IPage<FrontFinancingListVO> getPageFinancingList(Query query, FrontFinancingListQueryDto dto);

	/****
	 * 查询 融资企业数据
	 * @param page 分页数据
	 * @param queryWrapper
	 * @return
	 */
	IPage<FrontFinancingListVO> getPageCoreFinancingList(IPage<FrontFinancingListVO> page, QueryWrapper<FrontFinancingList> queryWrapper);

	/***
	 * 个人名称 或企业名称信息
	 * @param id
	 * @return
	 */
	FrontFinancingListVO getFrontFinancingById(Long id);

	/****
	 * 查询所有融资企业
	 */
	List<FrontFinancingListVO> selectFinancingContactsList();


	/***
	 * 查询所有融资客户
	 * @param status
	 * @return
	 */
	List<User> getListByStatus(Integer status);

	/**
	 * 查询融资企业基本信息
	 *
	 */
	List<FrontFinancingListVO> all();

	/**
	 * 根据用户 id查询 用户基础信息
	 * @param userId
	 * @return
	 */
	User userDetail(Long userId);

	/**
	 * 删除
	 * @param userId
	 */
	void deleteByUserId(Long userId);

}
