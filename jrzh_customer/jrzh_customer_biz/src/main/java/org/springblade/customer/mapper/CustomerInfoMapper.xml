<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerInfoResultMap" type="org.springblade.customer.entity.CustomerInfo">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="customer_id" property="customerId"/>
        <result column="corp_name" property="corpName"/>
        <result column="business_licence_number" property="businessLicenceNumber"/>
        <result column="regist_pic_attch_id" property="registPicAttchId"/>
        <result column="corporation_name" property="corporationName"/>
        <result column="corporation_id_card_number" property="corporationIdCardNumber"/>
        <result column="corporation_sex" property="corporationSex"/>
        <result column="corporation_country" property="corporationCountry"/>
        <result column="corporation_nation" property="corporationNation"/>
        <result column="corporation_valid_time" property="corporationValidTime"/>
        <result column="corporation_address" property="corporationAddress"/>
        <result column="mobile" property="mobile"/>
        <result column="leagal_no_attach_id" property="leagalNoAttachId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_idcard" property="operatorIdcard"/>
        <result column="operator_sex" property="operatorSex"/>
        <result column="operator_country" property="operatorCountry"/>
        <result column="operator_nation" property="operatorNation"/>
        <result column="operator_valid_time" property="operatorValidTime"/>
        <result column="operator_address" property="operatorAddress"/>
        <result column="operator_phone" property="operatorPhone"/>
        <result column="operator_attach_id" property="operatorAttachId"/>
        <result column="corporation_id_type" property="corporationIdType"/>
        <result column="corporation_face_attachId" property="corporationFaceAttachId"/>
        <result column="corporation_back_attachId" property="corporationBackAttachId"/>
        <result column="operator_back_attachId" property="operatorBackAttachId"/>
        <result column="operator_face_attachId" property="operatorFaceAttachId"/>
        <result column="operation_status" property="operationStatus"/>
        <result column="business_licence_attach_id" property="businessLicenceAttachId"/>
        <result column="operation_from" property="operationFrom"/>
        <result column="operation_to" property="operationTo"/>
        <result column="corporation_email" property="corporationEmail"/>
        <result column="company_id" property="companyId"/>
        <result column="process" property="process"/>
        <result column="task_id" property="taskId"/>
    </resultMap>


    <select id="selectCustomerInfoPage" resultMap="customerInfoResultMap">
        select *
        from jrzh_customer_info
        where is_deleted = 0
    </select>

    <select id="selectFinanceCustomerList" resultType="org.springblade.customer.vo.FinanceCustomerVo">
        select cst.business_licence_number as credit_code
        , cst.corp_name               as name
        , customer_id                 as user_id
        , cst.auth_status
        , cst.corporation_name        as legal_person_name
        , cst.enterprise_code
        , cst.create_time
        , cfu.`name`                  as personal_name
        , cfut.id                     as user_type_id
        from jrzh_customer_info cst
        LEFT JOIN jrzh_customer_front_user cfu on cst.customer_id = cfu.id
        LEFT JOIN jrzh_customer_front_user_type cfut on cst.company_id = cfut.user_id
        <where>
            cst.auth_status = 2
            <if test="financeCustomer.name != null and financeCustomer.name != ''">
                and cst.corp_name like concat('%', #{financeCustomer.name}, '%')
            </if>
            <if test="financeCustomer.legalPersonName != null and financeCustomer.legalPersonName != ''">
                and cst.corporation_name like concat('%', #{financeCustomer.legalPersonName}, '%')
            </if>

            <if test="financeCustomer.creditCode!= null and financeCustomer.creditCode!= ''">
                and cst.business_licence_number = #{financeCustomer.creditCode}
            </if>
            <if test="financeCustomer.personalName!= null and financeCustomer.personalName!= ''">
                and cfu.`name` like concat('%', #{financeCustomer.personalName}, '%')
            </if>
            <if test="financeCustomer.startTime!= null and financeCustomer.startTime!= ''">
                and DATE_FORMAT(cst.create_time, '%Y-%m-%d') >= #{financeCustomer.startTime}
            </if>
            <if test="financeCustomer.endTime!= null and financeCustomer.endTime!= ''">
                and DATE_FORMAT(cst.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{financeCustomer.endTime}
            </if>
        </where>
        order by cst.create_time desc
    </select>

    <select id="selectFinanceCustomerPage" resultType="org.springblade.customer.vo.FinanceCustomerVo">
        select cst.business_licence_number as credit_code
        , cst.corp_name               as name
        , customer_id                 as user_id
        , cst.auth_status
        , cst.corporation_name        as legal_person_name
        , cst.enterprise_code
        , cst.create_time
        , cfu.`name`                  as personal_name
        , cfut.id                     as user_type_id
        from jrzh_customer_info cst
        LEFT JOIN jrzh_customer_front_user cfu on cst.customer_id = cfu.id
        LEFT JOIN jrzh_customer_front_user_type cfut on cst.company_id = cfut.user_id and cfut.type = 2
        <where>
            cst.auth_status = 2 and cst.is_deleted = 0
            <if test="financeCustomer.name != null and financeCustomer.name != ''">
                and cst.corp_name like concat('%', #{financeCustomer.name}, '%')
            </if>
            <if test="financeCustomer.legalPersonName != null and financeCustomer.legalPersonName != ''">
                and cst.corporation_name like concat('%', #{financeCustomer.legalPersonName}, '%')
            </if>

            <if test="financeCustomer.creditCode!= null and financeCustomer.creditCode!= ''">
                and cst.business_licence_number = #{financeCustomer.creditCode}
            </if>
            <if test="financeCustomer.personalName!= null and financeCustomer.personalName!= ''">
                and cfu.`name` like concat('%', #{financeCustomer.personalName}, '%')
            </if>
            <if test="financeCustomer.startTime!= null and financeCustomer.startTime!= ''">
                and DATE_FORMAT(cst.create_time, '%Y-%m-%d') >= #{financeCustomer.startTime}
            </if>
            <if test="financeCustomer.endTime!= null and financeCustomer.endTime!= ''">
                and DATE_FORMAT(cst.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{financeCustomer.endTime}
            </if>
        </where>
        order by cst.create_time desc
    </select>

</mapper>
