/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.customer.entity.FinancingRole;
import org.springblade.customer.vo.*;
import org.springblade.system.entity.Role;

import java.util.List;

/**
 * 融资企业用户类型 服务类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
public interface ICustomerFrontUserTypeService extends BaseService<CustomerFrontUserType> {

	/**
	 * 自定义分页
	 *
	 * @return IPage<CustomerFrontUserTypeVO>
	 */
	IPage<CustomerFrontUserTypeVO> selectCustomerFrontUserTypePage(IPage<CustomerFrontUserTypeVO> page, CustomerFrontUserTypeVO customerFrontUserType);

	/***
	 *
	 * @param id 用户ID
	 * @return List<CustomerFrontUserTypeVO>  返回用户所填数据
	 */
	List<CustomerFrontUserTypeVO> selectByCustomerId(Long id);


	void customerRegister(Customer customer);

	/***
	 * 判断是否为邮箱
	 * @param email 邮箱
	 */
	void isnotNullEmail(String email);

	/***
	 * 解绑用户
	 * @param id 当前企业ID
	 * @return boolean
	 */
	boolean customerUnbound(Long id);

	/***
	 * 企业绑定用户 相当于个人实名认证
	 * @param customerId 登录账号id
	 * @return
	 */
	boolean customerBinding(Long customerId, String name, Long userId, Integer entAuthType);

	/****
	 * 查询当前企业 所有用户
	 * @param companyId 企业ID
	 * @return List<CustomerFrontUserTypeVO>
	 */
	List<CustomerFrontUserTypeVO> queryTypeCustomer(Long companyId);

	/***
	 * 查询融资企业数据
	 * @param customerFrontUserTypeDTO  用户选择企业类型
	 * @return IPage<CustomerFrontUserTypeBackVO>
	 */
	IPage<CustomerFrontUserTypeBackVO> customerFrontUserList(CustomerFrontUserType customerFrontUserTypeDTO, Query query, Integer isCoreOrFront);

	/***
	 * 企业删除 已绑定的用户
	 * @param toLongList 删除已绑定的用户信息
	 * @return boolean
	 */
	boolean enterpriseDeleteCustomer(List<Long> toLongList);

	/***
	 * 企业邀请用户
	 * @param code 邀请码 角色信息 coverCodeId
	 * @param frontName 邀请企业名称 	 * @return
	 */
	boolean enterpriseInvitesCustomer(String code, String frontName);

	/***
	 * 企业发送验证码给用户
	 * @param emil
	 * @return
	 */
	boolean enterpriseInvitCode(String emil);

	/***
	 * 查询所有用户（除了本企业的）
	 * @param name 根据个人名称查询用户
	 * @return
	 */
	List<Customer> selectCustomerAll(String name);

	/****
	 * 企业移交权限
	 * @param typeId  超级管理员id 信息
	 * @param customerId 被转移人账号id
	 * @return
	 */
	boolean customerTransfer(Long typeId, Long customerId);

	/***
	 *
	 * @return
	 */
	List<Role> selectFinancingRoleAll();

	/***
	 * 根据角色id进行查询所有角色信息
	 * @param roleId
	 * @return
	 */
	String selectRoleName(String roleId);

	/***
	 * 根据typeId 获取CustomerFrontUserType 数据
	 * @param roleIds 角色ids
	 * @param typeId  选择的企业ID
	 * @return Boolean
	 */
	Boolean changeRole(String roleIds, String typeId);


	/***
	 * 初始化企业角色管理信息
	 * @return FinancingRole
	 */
	FinancingRole initialization(Long companyId);

	/***
	 * 更换企业LOGO
	 * @param corporateAvatar
	 *  @param typeId 选择企业的ID
	 * @return Boolean
	 */
	Boolean updateCompanyAvatar(String corporateAvatar, Long typeId);

	/***
	 * 查询企业的详情信息
	 * @return CustomerInfoVO
	 */
	CustomerInfoVO getCompanyDetailsByCustomerId();

	/***
	 * 返回用户需要的数据
	 * @param typeId
	 * @return
	 */
	CustomerVO customerSignin(Long typeId, Long id);

	/***
	 * 个人信息数据
	 * @param typeId 登录选择typeid
	 * @param id 登录ID
	 * @return CustomerVO
	 */
	CustomerVO personalCustomer(Long typeId, Long id);

	/***
	 * 查询所有企业信息
	 * @param customerId
	 * @return
	 */
	List<CustomerFrontUserTypeVO> selectEnterprise(Long customerId);

	/***
	 * 修改角色信息
	 * @return
	 */
	Boolean updateRole(String roleIds, String typeId);

	/***
	 * 查询个人账户账号信息
	 * @param id
	 * @return
	 */
	CustomerFrontUserTypePersonalVO personalDetails(Long id);

	/***
	 * 获取企业成员信息
	 * @param valueOf
	 * @param page
	 * @return
	 */
	IPage<CustomerFrontUserBackTypeVO> queryTypeCustomerToPage(Long valueOf, IPage<CustomerFrontUserType> page);

	/***
	 * 对个人手机号码  统一社会代码信息进行脱敏
	 * @param customerId
	 * @return
	 */
	List<CustomerFrontUserTypeVO> selectByCustomerIdAndEncryptionPhone(Long customerId);

	/**
	 * @param customer
	 */
	void checkCustomer(Customer customer);

	/***
	 *
	 * @return
	 */
	boolean verifyCoreEnterprise();

	/***
	 * 融资企业实名
	 * @param customerId  账号ID
	 * @param name 企业名称
	 * @param userId 用户userId
	 * @param entAuthType  2-融资企业 3-核心企业
	 * @return
	 */
	boolean enterpriseBinding(Long customerId, String name, Long userId, Integer enterpriseType, Integer entAuthType);

	/****
	 *
	 * @param label 标签
	 * @param enterpriseType  人员信息 法人 或经办人
	 * @param userId 用户表ID
	 * @return
	 */
	boolean saveEnterpriseadopt(Integer label, Integer enterpriseType, Long userId, Integer entAuthType);


	/**
	 * 实名失败时
	 *
	 * @param typeId
	 * @return
	 */
	boolean removeFrontUserType(Long typeId);

	/***
	 * 实名成功
	 * @param userId
	 */
	void success(Long userId);

	/***
	 * 实名失败
	 * @param userId
	 */
	void fail(Long userId);

	/**
	 * 获取用户列表
	 *
	 * @param rolesIds 角色Ids
	 */
	List<CustomerFrontUserType> listByRoleIds(List<String> rolesIds);

	/**
	 * 获取用户类型
	 *
	 * @param roleId 角色id
	 * @return
	 */
	CustomerFrontUserType getByRoleId(String roleId);

	/***
	 * 获取个人基础资料
	 * @param comapnyId
	 * @return
	 */
	CustomerPersonInfo getCustomerPersonInfo(Long comapnyId);

	/**
	 * 根据角色id删除
	 * @param rolesId
	 */
	void deleteByRoleId(Long rolesId);

	/**
	 * 根据角色id删除
	 * @param userId
	 */
	void deleteByUserId(Long userId);
	/**
	 * 通过userId查询集合
	 * @param userId
	 */
	List<CustomerFrontUserType> listByUserId(Long userId);

	/**
	 * 通过客户表id查询
	 * @param id
	 */
	List<CustomerFrontUserType> listByFornUserTypeId(Long id);

	/**
	 * 查询核心企业季度排名
	 * @return
	 */
	List<CustomerFrontUserTypeBackVO> selectCoreEnterpriseQuarterRanking();

	/**
	 *  获取当前登陆用户信息
	 * <AUTHOR>
	 * @date 2024/10/15 17:24
	 * @return org.springblade.customer.vo.CustomerVO
	 */
	CustomerVO getCustomerInfo();
}
