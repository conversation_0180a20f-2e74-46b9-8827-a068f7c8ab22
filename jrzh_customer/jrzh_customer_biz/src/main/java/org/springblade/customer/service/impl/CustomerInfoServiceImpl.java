/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.TaskService;
import org.flowable.engine.task.Comment;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CustomerEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.constant.AuthStatus;
import org.springblade.customer.constant.SSQPersonAuthStatus;
import org.springblade.customer.constant.UserInfoStatus;
import org.springblade.customer.dto.CustomerAuthInfoDTO;
import org.springblade.customer.dto.FinanceCustomerDto;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.AuthProcess;
import org.springblade.customer.mapper.CustomerInfoMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.IdCardUtils;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.FinanceCustomerVo;
import org.springblade.customer.wrapper.CustomerInfoWrapper;
import org.springblade.othersapi.sky.dto.SupplierBusinessInfo;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 企业信息名称 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Service
@RequiredArgsConstructor
public class CustomerInfoServiceImpl extends BaseServiceImpl<CustomerInfoMapper, CustomerInfo> implements ICustomerInfoService {

    private final IAttachService attachService;
    private final ICustomerBusinessInfoService businessInfoService;
    private final IProcessCoreEnterService processCoreEnterService;
    private final TaskService taskService;

    @Override
    public IPage<CustomerInfoVO> selectCustomerInfoPage(IPage<CustomerInfoVO> page, CustomerInfoVO customerInfo) {
        return page.setRecords(baseMapper.selectCustomerInfoPage(page, customerInfo));
    }



    @Override
    public CustomerInfoVO selectByCustomerId(Long customerId) {
        if (ObjectUtil.isEmpty(customerId)) {
            return null;
        }
        CustomerInfo customerInfo = this.lambdaQuery().eq(CustomerInfo::getCustomerId, customerId).one();
        CustomerInfoVO customerInfoVO = BeanUtil.copyProperties(customerInfo, CustomerInfoVO.class);
        if (ObjectUtil.isNotEmpty(customerInfoVO) && ObjectUtil.isNotEmpty(customerInfoVO.getCorporationIdCardNumber())) {
            customerInfoVO.setCorporationBirthDay(IdCardUtils.cusBirthday(customerInfoVO.getCorporationIdCardNumber()));
        }
        if (ObjectUtil.isNotEmpty(customerInfoVO) && ObjectUtil.isNotEmpty(customerInfoVO.getOperatorIdcard())) {
            customerInfoVO.setOperatorBirthDay(IdCardUtils.cusBirthday(customerInfoVO.getOperatorIdcard()));
        }
        return customerInfoVO;
    }

    @Override
    public CustomerInfoVO selectByCompanyId(Long companyId) {
        if (ObjectUtil.isEmpty(companyId)) {
            return null;
        }
        CustomerInfo customerInfo = this.lambdaQuery().eq(CustomerInfo::getCompanyId, companyId).one();
        CustomerInfoVO customerInfoVO = BeanUtil.copyProperties(customerInfo, CustomerInfoVO.class);
        if (ObjectUtil.isNotEmpty(customerInfoVO) && ObjectUtil.isNotEmpty(customerInfoVO.getCorporationIdCardNumber())) {
            customerInfoVO.setCorporationBirthDay(IdCardUtils.cusBirthday(customerInfoVO.getCorporationIdCardNumber()));
        }
        if (ObjectUtil.isNotEmpty(customerInfoVO) && ObjectUtil.isNotEmpty(customerInfoVO.getOperatorIdcard())) {
            customerInfoVO.setOperatorBirthDay(IdCardUtils.cusBirthday(customerInfoVO.getOperatorIdcard()));
        }
        return customerInfoVO;
    }

    @Override
    public CustomerInfoVO selectVagueInfoByCustomerId(Long customerId) {

        CustomerInfoVO customerInfo = this.selectByCustomerId(customerId);
        if (ObjectUtil.isEmpty(customerInfo)) {
            return customerInfo;
        }
        String corporationAddress = customerInfo.getCorporationAddress();
        String operatorAddress = customerInfo.getOperatorAddress();
        customerInfo.setMobile(DesensitizedUtil.idCardNum(customerInfo.getMobile(), 3, 2))
                .setCorporationAddress(DesensitizedUtil.address(corporationAddress, getAddressVagueLength(corporationAddress)))
                .setCorporationIdCardNumber(DesensitizedUtil.idCardNum(customerInfo.getCorporationIdCardNumber(), 3, 2))
                .setOperatorPhone(DesensitizedUtil.idCardNum(customerInfo.getOperatorPhone(), 3, 2))
                .setOperatorAddress(DesensitizedUtil.address(operatorAddress, getAddressVagueLength(operatorAddress)))
                .setOperatorIdcard(DesensitizedUtil.idCardNum(customerInfo.getOperatorIdcard(), 3, 2));
        return customerInfo;
    }


    public Integer getAddressVagueLength(String address) {
        if (ObjectUtil.isEmpty(address)) {
            return 0;
        }
        if (address.length() > 9) {
            return address.length() - 9;
        } else {
            return 0;
        }
    }

    public boolean isRealName(){
        return SpringUtil.getBean(ICustomerService.class).isRealName();
    }

    @Override
    public R getCustomerInfoByIsReal(boolean isReal, String userType) {

        if (ObjectUtil.isEmpty(MyAuthUtil.getId())) {
            return R.fail(ResultCode.UN_AUTHORIZED, ResultCode.UN_AUTHORIZED.getMessage());
        }
        if (UserInfoStatus.userType.ENT.getCode().equals(userType)) {
            List<CustomerInfo> customerInfos = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().eq(CustomerInfo::getCustomerId, MyAuthUtil.getId()).list();
            CustomerAuthInfoDTO customerAuthInfoDTO = new CustomerAuthInfoDTO();

            if (Boolean.FALSE.equals(isRealName())) {
                customerAuthInfoDTO.setCode(1);
                customerAuthInfoDTO.setMsg("未实名");
                return R.data(customerAuthInfoDTO);
            } else if (customerInfos.size() == 0) {
                customerAuthInfoDTO.setCode(2);
                customerAuthInfoDTO.setMsg("未授权");
                return R.data(customerAuthInfoDTO);
            } else if (isReal && isRealName() && customerInfos.size() > 0) {
                customerAuthInfoDTO.setMsg("获取客户信息成功");
                customerAuthInfoDTO.setCode(3);
                customerAuthInfoDTO.setCustomerInfoVO(SpringUtil.getBean(ICustomerInfoService.class).selectByCustomerId(MyAuthUtil.getId()));
                return R.data(customerAuthInfoDTO);

            } else if (Boolean.FALSE.equals(isReal) && isRealName() && customerInfos.size() > 0) {
                customerAuthInfoDTO.setMsg("获取客户脱敏感信息成功");
                customerAuthInfoDTO.setCode(4);
                customerAuthInfoDTO.setCustomerInfoVO(SpringUtil.getBean(ICustomerInfoService.class).selectVagueInfoByCustomerId(MyAuthUtil.getId()));
                return R.data(customerAuthInfoDTO);
            }


        } else if (UserInfoStatus.userType.PERSON.getCode().equals(userType)) {
            List<CustomerPersonInfo> customerPersonInfos = SpringUtil.getBean(ICustomerPersonInfoService.class).lambdaQuery().eq(CustomerPersonInfo::getCustomerId, MyAuthUtil.getId()).list();
            CustomerAuthInfoDTO customerAuthInfoDTO = new CustomerAuthInfoDTO();


            if (Boolean.FALSE.equals(isRealName())) {
                customerAuthInfoDTO.setCode(1);
                customerAuthInfoDTO.setMsg("未实名");
                return R.data(customerAuthInfoDTO);
            } else if (customerPersonInfos.size() == 0) {
                customerAuthInfoDTO.setCode(2);
                customerAuthInfoDTO.setMsg("未授权");
                return R.data(customerAuthInfoDTO);
            } else if (isReal && isRealName() && customerPersonInfos.size() > 0) {
                customerAuthInfoDTO.setMsg("获取客户信息成功");
                customerAuthInfoDTO.setCode(3);
                customerAuthInfoDTO.setCustomerPersonInfoVO(SpringUtil.getBean(ICustomerPersonInfoService.class).getPersonInfoByCustomerId(MyAuthUtil.getId()));
                return R.data(customerAuthInfoDTO);

            } else if (Boolean.FALSE.equals(isReal) && isRealName() && customerPersonInfos.size() > 0) {
                customerAuthInfoDTO.setMsg("获取客户脱敏感信息成功");
                customerAuthInfoDTO.setCode(4);
                customerAuthInfoDTO.setCustomerPersonInfoVO(SpringUtil.getBean(ICustomerPersonInfoService.class).selectVagueInfoByCustomerId(MyAuthUtil.getId()));
                return R.data(customerAuthInfoDTO);
            }
        }

        return R.status(Boolean.FALSE);
    }

    public CustomerInfo getCurrentCustomerInfo(CustomerInfo customerInfo) {
        //实名认证状态不是认证未通过
        CustomerInfo authCustomerInfo = isAuthOrAuthing(customerInfo.getBusinessLicenceNumber());
        Long customerId = customerInfo.getCustomerId();
        customerInfo.setTenantId(AuthUtil.getTenantId());
        if (ObjectUtil.isNotEmpty(authCustomerInfo)) {
            //若存在且为本人则修改原来的记录
            if (authCustomerInfo.getCustomerId().equals(customerId)) {
                if (AuthStatus.CompanyAuthStatus.AUTH_PASS.getStatus().equals(authCustomerInfo.getAuthStatus())) {
                    throw new ServiceException("该企业已实名，无需重新实名");
                }
                customerInfo.setId(authCustomerInfo.getId());
                customerInfo.setAuthStatus(authCustomerInfo.getAuthStatus());
                customerInfo.setCompanyId(authCustomerInfo.getCompanyId());
                customerInfo.setStatus(authCustomerInfo.getStatus());
                customerInfo.setProcess(authCustomerInfo.getProcess());
                return customerInfo;
            }
            throw new ServiceException("该实名信息已被实名或正在实名中");
        }
        //查询当前用户是否存在开通中企业
        if (existOpeningCompany(customerId)) {
            throw new ServiceException("企业开通流程未结束，请先结束当前开通流程");
        }
        customerInfo.setStatus(AuthStatus.OPEN_STATUS.OPENGING.getStatus());
        //若系统中已存在企业 则获取其企业id
        customerInfo.setCompanyId(getNewCompanyId(customerInfo.getBusinessLicenceNumber()));
        customerInfo.setProcess(AuthProcess.ENT_PROCESS.AUTH_START.getProcess());
        customerInfo.setAuthStatus(AuthStatus.PersonAuthStatus.AUTHING.getStatus());
        return customerInfo;
    }

    /**
     * 查询系统中是否已经存在需要入驻的客户
     *
     * @param businessLicenceNumber
     * @return
     */
    private Long getNewCompanyId(String businessLicenceNumber) {
        //查询是否存在供应商
        ICustomerSupplierService supplierService = SpringUtil.getBean(ICustomerSupplierService.class);
        CustomerSupplier customerSupplier = supplierService.getByUnifiedCode(businessLicenceNumber);
        if (ObjectUtil.isNotEmpty(customerSupplier)) {
            //存在进行解绑 避免跳过实名
            return customerSupplier.getId();
        }
        return IdWorker.getId();
    }

    /**
     * 查询当前企业已实名之外的开通流程
     *
     * @param customerId
     * @return
     */
    @Override
    public Boolean existOpeningCompany(Long customerId) {
        int count = count(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, customerId)
                .ne(CustomerInfo::getEnterStatus, AuthStatus.ENTER_STATUS.ENTER.getStatus()));
        return count > 0;
    }

    @Override
    public R queryEntAuthByCustomerId(Long customerId, Long id) {
        CustomerInfo customerInfo = getById(id);
        if (ObjectUtil.isEmpty(customerInfo)) {
            return R.data(null);
        }
        //回显旧数据供用户修改
        CustomerInfoVO vo = CustomerInfoWrapper.build().entityVO(customerInfo);
        ProcessCoreEnter processCoreEnter = processCoreEnterService.getByCompanyId(customerInfo.getCompanyId());
        if (ObjectUtil.isNotEmpty(processCoreEnter)) {
            List<Comment> commentList = taskService.getProcessInstanceComments(processCoreEnter.getProcessId());
            vo.setCommentList(commentList);
        }
        //回显orc附件
        vo.setAttachMap(listCustomerAttach(vo, true));
        return R.data(vo);
    }

    @Override
    public Boolean deleteRealByCustomerId(Long customerId) {
        if (ObjectUtil.isEmpty(customerId)) {
            throw new ServiceException("客户id不得为空");
        }
        return baseMapper.deleteRealByCustomerId(customerId);
    }

    @Override
    public CustomerInfo getNotEnterCompanyInfoByCustomerId(Long customerId) {
        CustomerInfo one = getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, customerId)
                .eq(CustomerInfo::getEnterStatus, AuthStatus.ENTER_STATUS.NOT_ENTER.getStatus()));
        return one;
    }


    @Override
    public List<CustomerInfo> getByCompanyIds(List<Long> companyIds) {
        return list(Wrappers.<CustomerInfo>lambdaQuery().in(CustomerInfo::getCompanyId, companyIds));
    }

    @Override
    public Boolean existByName(String companyName) {
        return count(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCorpName, companyName)
                .orderByDesc(CustomerInfo::getCreateTime).last("limit 1")) > 0;
    }

    @Override
    public Boolean deleteRealByCompanyId(Long companyId) {
        return baseMapper.deleteRealByCompanyId(companyId);
    }


    /**
     * 设置贸易背景权利
     *
     * @param companyId 企业id
     */
    @Async
    @Override
    public void setTradeAuth(Long companyId) {
        ITradeBackgroundService tradeBackgroundService = SpringUtil.getBean(ITradeBackgroundService.class);
        List<TradeBackground> backgroundList = tradeBackgroundService.listAllByCompanyId(companyId);
        if (CollectionUtil.isEmpty(backgroundList)) {
            return;
        }
        //查询入驻的企业
        Map<Long, Long> companyIdsMap = listAllEnterCompany().stream().collect(HashMap::new, (m, v) -> m.put(v.getCompanyId(), v.getCompanyId()), HashMap::putAll);
        //若贸易双方都入驻，设置其融资转让权利
        if (CollectionUtil.isNotEmpty(companyIdsMap)) {
            List<Long> needAuthBackIds = new ArrayList<>();
            //k:上游id v:背景id
            Map<Long, Long> heightIdsMap = backgroundList.stream().filter(e -> companyId.equals(e.getCompanyLowerId()))
                    .collect(HashMap::new, (m, v) -> m.put(v.getCompanyHeightId(), v.getId()), HashMap::putAll);
            heightIdsMap.forEach((k, v) -> {
                if (companyIdsMap.containsKey(k)) {
                    needAuthBackIds.add(v);
                }
            });
            //k:下游id v:背景id
            Map<Long, Long> lowerIdsMap = backgroundList.stream().filter(e -> companyId.equals(e.getCompanyHeightId()))
                    .collect(HashMap::new, (m, v) -> m.put(v.getCompanyLowerId(), v.getId()), HashMap::putAll);
            lowerIdsMap.forEach((k, v) -> {
                if (companyIdsMap.containsKey(k)) {
                    needAuthBackIds.add(v);
                }
            });
            //赋予权限
            if (CollectionUtil.isNotEmpty(needAuthBackIds)) {
                tradeBackgroundService.changeTransferAbility(needAuthBackIds.stream().distinct().collect(Collectors.toList()), CommonConstant.OPENSTATUS);
            }

        }
    }

    @Override
    public CustomerInfo getByCustomerIdAndId(Long customerId, Long id) {
        return getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getId, id).eq(CustomerInfo::getCustomerId, customerId));
    }


    @Override
    public Map<Long, String> getAbbrMapByEnterpriseIds(List<Long> enterpriseIds) {
        Map<Long, String> map = new HashMap<>();
        for (Long enterpriseId : enterpriseIds) {
            CustomerInfo customerInfo = baseMapper.selectOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCompanyId, enterpriseId));
            String base = customerInfo.getBase();
            if (StringUtil.isNotBlank(base)) {
                map.put(enterpriseId, base);
            }
        }
        return map;
    }

    @Override
    public CustomerBusinessInfo getCustomerBusinessInfoByCompanyId(String companyId) {
        CustomerInfo companyInfo = getByCompanyId(Func.toLong(companyId));
        if (ObjectUtil.isEmpty(companyInfo)) {
            return new CustomerBusinessInfo();
        }
        return businessInfoService.getByCreditCode(companyInfo.getBusinessLicenceNumber());
    }


    private List<CustomerInfo> listAllEnterCompany() {
        return list(Wrappers.<CustomerInfo>lambdaQuery()
                .eq(CustomerInfo::getEnterStatus, AuthStatus.ENTER_STATUS.ENTER.getStatus()));
    }

    /**
     * @param customerInfo
     * @param linkOrName   返回link连接或名字 true 连接 false 名字
     * @return
     */
    private Map<Long, String> listCustomerAttach(CustomerInfo customerInfo, Boolean linkOrName) {
        List<Long> attachIds = new ArrayList<>(5);
        Optional.ofNullable(customerInfo.getBusinessLicenceAttachId()).ifPresent(attachIds::add);
        Optional.ofNullable(customerInfo.getCorporationFaceAttachId()).ifPresent(attachIds::add);
        Optional.ofNullable(customerInfo.getCorporationBackAttachId()).ifPresent(attachIds::add);
        Optional.ofNullable(customerInfo.getOperatorFaceAttachId()).ifPresent(attachIds::add);
        Optional.ofNullable(customerInfo.getOperatorBackAttachId()).ifPresent(attachIds::add);
        Stream<Attach> stream = attachService.listByIds(attachIds).stream();
        if (linkOrName) {
            return stream.collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getLink()), HashMap::putAll);
        }
        return stream.collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getName()), HashMap::putAll);
    }

    /**
     * 根据社会征信代码查询已实名或正在实名的企业实名信息
     *
     * @param licenceNumber 社会征信代码
     * @return
     */
    @Override
    public CustomerInfo isAuthOrAuthing(String licenceNumber) {
        LambdaQueryWrapper<CustomerInfo> wrapper = Wrappers.lambdaQuery();
        return getOne(wrapper.eq(CustomerInfo::getBusinessLicenceNumber, licenceNumber)
                .ne(CustomerInfo::getAuthStatus, AuthStatus.CompanyAuthStatus.NO_AUTH.getStatus()));
    }

    @Override
    public CustomerInfo updateEntInfoAndUserInfo(CustomerInfo customerInfo) {
        //拉取工商实名信息
        String corpName = customerInfo.getCorpName();
        SupplierBusinessInfo supplierBusinessInfo = businessInfoService.saveBusinessInfo(corpName);
        String base = supplierBusinessInfo.getCompanyBasicInfo().getBase();
        customerInfo.setBase(base);
        //设置实名通过
        customerInfo.setAuthStatus(SSQPersonAuthStatus.AUTH_PASS.getCode());
        Integer legalPersonFlag = customerInfo.getLegalPersonFlag();
        if (legalPersonFlag == 1) {
            //法人不需要签署授权书
            customerInfo.setStartSign(2);
        }
        saveOrUpdate(customerInfo);
        Long userId = customerInfo.getCompanyId();
        SpringUtil.getBean(ICustomerFrontUserTypeService.class).enterpriseBinding(customerInfo.getCustomerId(), customerInfo.getCorpName(), userId,
                legalPersonFlag.equals(1) ? CustomerEnum.LEGAL_PERSON.getCode() : CustomerEnum.HANDLED_BY.getCode(), customerInfo.getEntAuthType());
        return customerInfo;
    }

    @Override
    public CustomerInfo getByBusinessLicenceNumber(String code) {
        CustomerInfo one = getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getBusinessLicenceNumber, code)
                .last("limit 1"));
        return one;
    }

    @Override
    public CustomerInfo getByCompanyCode(String code, Integer authStatus) {
        LambdaQueryWrapper<CustomerInfo> wrapper = Wrappers.<CustomerInfo>lambdaQuery()
                .eq(CustomerInfo::getBusinessLicenceNumber, code);
        if (ObjectUtil.isNotEmpty(authStatus)) {
            wrapper.eq(CustomerInfo::getAuthStatus, authStatus);
        }
        CustomerInfo one = getOne(wrapper.last("limit 1"));
        return one;
    }

    @Override
    public CustomerInfo getByCompanyId(Long companyId) {
        CustomerInfo one = getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCompanyId, companyId)
                .last("limit 1"));
        return one;
    }

    @Override
    public CustomerInfo getByCustomerId(Long customerId) {
        CustomerInfo one = getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, customerId)
                .last("limit 1"));
        return one;
    }

    @Override
    public CustomerInfo getByCompanyName(String entName) {
        CustomerInfo one = getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCorpName, entName).last("limit 1"));
        return one;
    }

    public static void main(String[] args) {
        System.out.println(DesensitizedUtil.address(null, 2));
    }

    @Override
    public List<FinanceCustomerVo> selectFinanceCustomerList(FinanceCustomerDto financeCustomerDto) {
        return baseMapper.selectFinanceCustomerList(financeCustomerDto);
    }

    @Override
    public IPage<FinanceCustomerVo> selectFinanceCustomerPage(IPage<FinanceCustomerVo> page, FinanceCustomerDto financeCustomerDto) {
        return baseMapper.selectFinanceCustomerPage(page, financeCustomerDto);
    }

}
