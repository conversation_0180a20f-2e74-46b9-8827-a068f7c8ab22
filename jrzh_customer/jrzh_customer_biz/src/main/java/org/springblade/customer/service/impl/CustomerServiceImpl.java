/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;


import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.auth.utils.TokenUtil;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.UserTypeEnum;
import org.springblade.common.enums.VerificationCodeSupertube;
import org.springblade.common.handler.CustomerRegisterHandler;
import org.springblade.common.utils.ChineseUtil;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.CustomerDTO;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.FrontFinancingList;
import org.springblade.customer.excel.CustomerExcel;
import org.springblade.customer.mapper.CustomerMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerFrontUserTypeBackVO;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.CustomerPersonInfoVO;
import org.springblade.customer.vo.CustomerVO;
import org.springblade.customer.wrapper.CustomerWrapper;
import org.springblade.modules.othersapi.utils.HuaWeiUtils;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.Tenant;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteTenantService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl extends BaseServiceImpl<CustomerMapper, Customer> implements ICustomerService {

    private static final Pattern PATTERN_EMAIL = Pattern.compile("^\\s*\\w+(?:\\.?[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$");

    private static final Pattern PATTERN_PHONE = Pattern.compile("^[1][3,4,5,7,8,9][0-9]{9}$");


    private final Map<String, CustomerRegisterHandler> customerRegisterHandlerMap;


    private static final Pattern PAR = Pattern.compile("^[1][3,4,5,7,8,9][0-9]{9}$");


    private final RemoteDeptSearchService remoteDeptSearchService;


    private final RemoteTenantService remoteTenantService;

    @Override
    public IPage<CustomerVO> selectCustomerPage(IPage<CustomerVO> page, CustomerVO customer) {
        return page.setRecords(baseMapper.selectCustomerPage(page, customer));
    }

    /**
     * 根据id获取租户id
     *
     * @param id
     * @return
     */
    @Override
    @TenantIgnore
    public String getTenantId(Long id) {
        Customer customer = this.getById(id);
        if (ObjectUtil.isNotEmpty(customer)) {
            return customer.getTenantId();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean customerRegister(Customer customer) {
        if (!isPhone(customer.getPhone())) {
            throw new ServiceException("手机号输入不正确,请检查!");
        }
        checkCustomer(customer);
        String password = customer.getPassword();

        customer.setPassword(SecureUtil.md5(password));

        this.save(customer);

        User user = new User();
        //
        user.setTenantId(customer.getTenantId());

        user.setAccount(UUID.randomUUID().toString().substring(0, 10));
        user.setPassword(UUID.randomUUID().toString());
        //企业信息
        user.setName(customer.getName());

        user.setUserType(UserTypeEnum.FINANCING.getCode());
        Dept dept = remoteDeptSearchService.getDeptByName("融资企业", "000000").getData();
        if (Func.isNotEmpty(dept)) {
            user.setDeptId(String.valueOf(dept.getId()));
        }

        customerRegisterHandlerMap.values().forEach(customerRegisterHandlerMap -> {
            boolean result = customerRegisterHandlerMap.registerHandler(user);
            if (result) {
                throw new ServiceException("未知错误!");
            }
        });

        FrontFinancingList frontFinancingList = new FrontFinancingList();
        frontFinancingList.setTenantId(customer.getTenantId());
        frontFinancingList.setStatus(0);
        //编码
        frontFinancingList.setCustomerCode(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
        //设置 企业ID
        frontFinancingList.setCompanyId(user.getId());
        SpringUtil.getBean(IFrontFinancingListService.class).save(frontFinancingList);
        //设置 企业ID
        customer.setCompanyId(user.getId());
        //userID
        customer.setUserId(user.getId());
        updateById(customer);

        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void isnotNullPhone(String phone) {
        if (ObjectUtil.isEmpty(phone)) {
            throw new ServiceException("手机不能为空!");
        }
        Matcher matcher = PATTERN_PHONE.matcher(phone);
        if (!matcher.matches()) {
            throw new ServiceException("手机格式不符合!");
        }
        Customer customer = baseMapper.selectOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, phone));
        if (Func.isNotEmpty(customer)) {
            throw new ServiceException("该手机号已被绑定!");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void isnotNullEmail(String email) {
        if (ObjectUtil.isEmpty(email)) {
            throw new ServiceException("邮箱未输入!");
        }
        Matcher matcher = PATTERN_EMAIL.matcher(email);
        if (!matcher.matches()) {
            throw new ServiceException("邮箱格式不符合!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean userLogin(String account, String password, String tenant, Integer enterpriseType) {
        return analysis(account, password, tenant, enterpriseType);
    }


    @Override
    public void updateZoneDateTimeById(Customer customer) {
        //TODO 重构
//		update(Wrappers.<Customer>lambdaUpdate().in(BaseEntity::getId, customer.getId())
//				.set(Customer::getLastVisitTime, new Date().toInstant().
//						atZone(ZoneId.systemDefault()).toLocalDateTime()));
    }

    @Override
    public void isTenanId(String tenantId) {
        //TODO 重构
		Tenant tenant = remoteTenantService.getByTenantId(tenantId, FeignConstants.FROM_IN).getData();
		if (TokenUtil.judgeTenant(tenant)) {
			throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
		}
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean phoneLogin(String phone, String password, String tenantId, Integer enterpriseType) {
        Customer one = getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, phone).eq(TenantEntity::getTenantId, tenantId));
        if (ObjectUtil.isEmpty(one)) {
            throw new ServiceException("账号或密码错误!");
        }
        return analysis(one.getAccountUser(), password, tenantId, enterpriseType);
    }


    @Override
    public List<CustomerExcel> exportCustomer(Customer customer) {
        return baseMapper.exportCustomer(customer);
    }

    /***
     *
     * @param accountUser 账号
     * @param password   密码
     * @param tenantId  租户ID
     * @param type   融资企业 或核心企业
     */
    private boolean analysis(String accountUser, String password, String tenantId, Integer type) {
        Customer one = this.getOne(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getAccountUser, accountUser)
                .eq(TenantEntity::getTenantId, tenantId)
                .eq(Customer::getType, type));

        if (ObjectUtil.isNull(one)) {
            throw new ServiceException("账号或密码错误!");
        }

        //return one.getPassword().equals(SecureUtil.md5(password));
        return one.getPassword().equals(password);
    }

    private boolean isPhone(String phone) {
        Matcher matcher = PAR.matcher(phone);
        return matcher.matches();
    }

    private void checkCustomer(Customer customer) {

        isnotNullEmail(customer.getEmail());

        //融资企业
        LambdaQueryWrapper<Customer> and = Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getPhone, customer.getPhone())
                .and(wrapper -> wrapper.eq(Customer::getType, 0).or().eq(Customer::getType, 2));
        LambdaQueryWrapper<Customer> and1 = Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getAccountUser, customer.getAccountUser())
                .and(wrapper -> wrapper.eq(Customer::getType, 0).or().eq(Customer::getType, 2));

        Customer customer1 = baseMapper.selectOne(and);
        Customer customer2 = baseMapper.selectOne(and1);
        if (StringUtil.isEmpty(customer.getId())) {
            if (ObjectUtil.isNotEmpty(customer1)) {
                throw new ServiceException("手机号已被注册,请去登录!");
            }
            if (ObjectUtil.isNotEmpty(customer2)) {
                throw new ServiceException("账号已被注册,请重新输入!");
            }
        }
        isChineseCustomer(customer);

    }

    @Override
    public boolean updateCustomerById(Customer entity) {
        checkCustomer(entity);
        return super.updateById(entity);
    }

    @Override
    public boolean checkIsRealNameAuthentication(Long customerId) {
        Customer customer = baseMapper.selectById(MyAuthUtil.getCustomerId());
        return Objects.nonNull(customer) && customer.getStatus().compareTo(0) > 0;
    }

    /**
     * 是否实名认证
     */
    @Override
    public boolean isRealName() {
        Customer customer = this.lambdaQuery().eq(Customer::getId, MyAuthUtil.getId()).one();
        Assert.isFalse(ObjectUtil.isEmpty(customer), "客户不存在");
        return customer.getStatus() > 0;
    }

    @Override
    public Customer getByUserId(Long enterpriseId) {
        return baseMapper.selectOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getUserId, enterpriseId));
    }

    @Override
    public Customer getByCompanyId(Long companyId) {
        return baseMapper.selectOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getCompanyId, companyId));
    }

    @Override
    public void subAccountRegister(CustomerDTO customer) {
    }

    @Override
    public CustomerFrontUserTypeBackVO companyRealName() {
        Long id = MyAuthUtil.getId();
        String companyId = MyAuthUtil.getCompanyId();
        if (ObjectUtil.isEmpty(id) || ObjectUtil.isEmpty(companyId) || "null".equals(id) || "null".equals(companyId)) {
            throw new ServiceException("长时间未操作,请重新登录!");
        }
        Customer byId = getById(id);
        CustomerFrontUserType one2 = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, id).eq(CustomerFrontUserType::getUserId, companyId).one();
        //判断是否已开通企业 1-未实名 2-已实名 -1 已实名未同步数据
        return getCustFrontUserTypeBackVO(byId, one2);
    }

    /**
     * 获取客户类型
     */
    @Override
    public Integer getCustomerType() {
        Customer customer = this.lambdaQuery().eq(Customer::getId, MyAuthUtil.getId()).one();
        return ObjectUtil.isNotEmpty(customer) ? customer.getType() : null;
    }

    /**
     * 判断是否为企业
     */
    @Override
    public boolean isEnterPrise() {
        return getCustomerType() < 2;
    }

    @Override
    public boolean searchIsRealName() {
        return false;
    }

    @Override
    public CustomerVO getPersonal(Long id) {

        FrontFinancingList one = SpringUtil.getBean(IFrontFinancingListService.class).getById(id);
        Customer byId = lambdaQuery().eq(Customer::getCompanyId, one.getCompanyId()).one();
        CustomerVO customerVO = CustomerWrapper.build().entityVO(byId);
        String invitationCode1 = one.getInvitationCode();

        if (!StringUtils.isEmpty(invitationCode1)) {
            Long aLong = Long.valueOf(invitationCode1);
            User user = CustomerUserCache.getUserById(aLong);
            customerVO.setInvitationName(user.getRealName());
        }

        return customerVO;
    }

    @Override
    public List<Customer> listByCustomerIds(List<Long> customerIds) {
        return list(Wrappers.<Customer>lambdaQuery().in(Customer::getCompanyId, customerIds));
    }

    @Override
    public Customer getByNameAndPhone(String invitedContact, String invitedContactPhone) {
        return getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getName, invitedContact).eq(Customer::getPhone, invitedContactPhone).last("limit 1"));
    }

    @Override
    public void removeByUserId(Long userId) {
        remove(Wrappers.<Customer>lambdaQuery().eq(Customer::getUserId, userId));
    }

    @Override
    public JSONObject getDesensitizationPhoneInfo(Long personalUserId) {
        Customer customer = getByUserId(personalUserId);
        JSONObject returnData = new JSONObject();
        String phone = customer.getPhone();
        returnData.put("phone", phone);
        returnData.put("phoneDesensitization", DesensitizedUtil.mobilePhone(phone));
        return returnData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean userLoginApplet(String account, String password, String tenant, Integer enterpriseType) {
        return analysisApplet(account, password, tenant, enterpriseType);
    }

    /***
     *
     * @param accountUser 账号
     * @param password   密码
     * @param tenantId  租户ID
     * @param type   融资企业 或核心企业
     */
    private boolean analysisApplet(String accountUser, String password, String tenantId, Integer type) {
        Customer one = this.getOne(Wrappers.<Customer>lambdaQuery()
                .eq(Customer::getAccountUser, accountUser)
                .eq(TenantEntity::getTenantId, tenantId)
                .eq(Customer::getType, type));

        if (ObjectUtil.isNull(one)) {
            throw new ServiceException("账号或密码错误!");
        }

        return one.getPassword().equals(SecureUtil.md5(password));
    }

    /**
     * 根据kv 获取身份证的有效期
     */
    @SneakyThrows
    public String getIdCardBackInfo(Kv kv) {
        String url = kv.getStr("url");
        JSONObject JsonObect = JSONObject.parseObject(HuaWeiUtils.getIdCard(huaWeiIdCardRequestParams(url)));
        String fromDate = JsonObect.getJSONObject("result").getString("valid_from");
        String toDate = JsonObect.getJSONObject("result").getString("valid_to");
        if (ObjectUtil.isEmpty(fromDate) && ObjectUtil.isEmpty(toDate)) {
            return null;
        }
        return fromDate + "~" + toDate;
    }

    /**
     * 华为云ocr 请求参数封装
     */
    public String huaWeiIdCardRequestParams(String url) {
        HashMap<String, String> paramsMap = new HashMap<>();
        paramsMap.put("url", url);
        return JSONObject.toJSONString(paramsMap);
    }


    private void isChineseCustomer(Customer customer) {
        boolean isPassword = ChineseUtil.isChinese(customer.getPassword());
        boolean isAccount = ChineseUtil.isChinese(customer.getAccountUser());
        if (isPassword || isAccount) {
            throw new ServiceException("不能输入中文!");
        }
    }


    @Override
    public CustomerInfoVO searchCompanyAuthInfo(boolean isReal) {

        if (isReal) {
            return SpringUtil.getBean(ICustomerInfoService.class).selectByCustomerId(MyAuthUtil.getId());
        } else {
            return SpringUtil.getBean(ICustomerInfoService.class).selectVagueInfoByCustomerId(MyAuthUtil.getId());
        }

    }

    @Override
    public CustomerPersonInfoVO searchPersonAuthInfo(boolean isReal) {
        if (isReal) {
            return SpringUtil.getBean(ICustomerPersonInfoService.class).getPersonInfoByCustomerId(MyAuthUtil.getId());
        } else {
            return SpringUtil.getBean(ICustomerPersonInfoService.class).selectVagueInfoByCustomerId(MyAuthUtil.getId());
        }

    }

    /**
     * 个人实名回填信息
     *
     * @param name       个人名称
     * @param customerId 登录账号ID
     */
    private void personalRealName(String name, Long customerId) {
        Customer byId = getById(customerId);
        //0-未实名 1-已实名
        byId.setStatus(1);
        byId.setName(name);
        updateById(byId);
        User user = CustomerUserCache.getUserById(byId.getUserId());
        user.setName(name);
        //TODO 重构
        //userService.updateById(user);
    }


    private CustomerFrontUserTypeBackVO getCustFrontUserTypeBackVO(Customer byId, CustomerFrontUserType customerFrontUserType) {
        //CustomerFrontUserType one = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getCustomerId, byId.getId()).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).one();
        //
        Customer customer = byId;
        CustomerFrontUserType customerFrontUserType1 = customerFrontUserType;
        if (customerFrontUserType1.getType().equals(3) || customerFrontUserType1.getType().equals(1)) {
            ICustomerFrontUserTypeService iCustomerFronType = SpringUtil.getBean(ICustomerFrontUserTypeService.class);
            //后续优化  获取 最高父级
            CustomerFrontUserType one = customerFrontUserType1.getType().equals(3) ? iCustomerFronType.lambdaQuery().eq(CustomerFrontUserType::getUserId, customerFrontUserType.getUserId()).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).one() :
                    iCustomerFronType.lambdaQuery().eq(CustomerFrontUserType::getId, MyAuthUtil.getTypeId()).one();
            customerFrontUserType = one;
            Long customerId = one.getCustomerFrontUserId();
            byId = getById(customerId);
        }
        CustomerFrontUserTypeBackVO copy = BeanUtil.copy(customerFrontUserType, CustomerFrontUserTypeBackVO.class);
        Long userId = customerFrontUserType.getRoleUserId();
        User user = CustomerUserCache.getUserById(userId);

        copy.setName(user.getName());
        copy.setCorporateAvatar(user.getAvatar());
        CustomerInfo customerInfo = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().in(CustomerInfo::getCompanyId, customerFrontUserType1.getUserId()).one();

        if (ObjectUtil.isEmpty(customerInfo)) {
            return copy;
        }
        copy.setCreditCode(customerInfo.getBusinessLicenceNumber());
        //所属者
        copy.setPersonalName(byId.getName());
        copy.setCorporateAvatar(Func.toStr(customerInfo.getLogo(),user.getAvatar()));
        copy.setType(customerFrontUserType1.getType());
        copy.setLegalPersonName(customerInfo.getCorporationName());
        return copy;
    }
}
