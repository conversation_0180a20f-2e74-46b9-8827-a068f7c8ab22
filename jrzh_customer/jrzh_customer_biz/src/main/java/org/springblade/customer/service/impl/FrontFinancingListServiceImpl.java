/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.CustomerEnum;
import org.springblade.common.enums.VerificationCodeSupertube;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.FrontFinancingListQueryDto;
import org.springblade.customer.entity.*;
import org.springblade.customer.mapper.CustomerMapper;
import org.springblade.customer.mapper.FrontFinancingListMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.vo.FrontFinancingListVO;
import org.springblade.customer.wrapper.FrontFinancingListWrapper;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 融资企业列表信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Service
@AllArgsConstructor
public class FrontFinancingListServiceImpl extends BaseServiceImpl<FrontFinancingListMapper, FrontFinancingList> implements IFrontFinancingListService {

    //private final IUserService userService;

    private final CustomerMapper customerMapper;

    private final RemoteUserService remoteUserService;

    private final ICustomerInfoService customerInfoService;


    //角色
    //private final IRoleService roleService;

    @Override
    public IPage<FrontFinancingListVO> selectFrontFinancingListPage(IPage<FrontFinancingListVO> page, FrontFinancingListVO frontFinancingList) {
        return page.setRecords(baseMapper.selectFrontFinancingListPage(page, frontFinancingList));
    }

    @Override
    public IPage<FrontFinancingListVO> getPageFinancingList(Query query, FrontFinancingListQueryDto dto) {
        if (ObjectUtil.isEmpty(dto)) {
            dto = new FrontFinancingListQueryDto();
        }
        dto.setTypeList(Arrays.asList(CustomerEnum.THREE_TYPE.getCode(), CustomerEnum.ZERO_TYPE.getCode()));
        IPage<FrontFinancingListVO> pageVo = baseMapper.getPageFinancingList(Condition.getPage(query), dto);
        return pageVo;
//        FrontFinancingList frontFinancingList = new FrontFinancingList();
//        QueryWrapper<FrontFinancingList> queryWrapper = Condition.getQueryWrapper(frontFinancingList);
//        List<FrontFinancingList> list = list(queryWrapper);
//        Map<Long, List<Customer>> collect = frontFinanList();
//        List<FrontFinancingListVO> frontFinancingListVos = finanListVO(list, collect);
//        if (CollectionUtils.isEmpty(frontFinancingListVos)) {
//            Page<FrontFinancingListVO> resultPage = new Page<>();
//            resultPage.setRecords(frontFinancingListVos);
//            return resultPage;
//        }
//        List<FrontFinancingListVO> financingList = frontFinancingListVos.stream().skip((long) (query.getCurrent() - 1) * (query.getSize()))
//                .limit(query.getSize()).collect(Collectors.toList());
//        Page<FrontFinancingListVO> resultPage = new Page<>();
//        resultPage.setRecords(financingList);
//        resultPage.setTotal(frontFinancingListVos.size());
//        resultPage.setSize(query.getSize());
//        resultPage.setCurrent(query.getCurrent());
//        return resultPage;
    }

    @Override
    public IPage<FrontFinancingListVO> getPageCoreFinancingList(IPage<FrontFinancingListVO> page, QueryWrapper<FrontFinancingList> queryWrapper) {
        List<FrontFinancingList> list = list(queryWrapper);
        Map<Long, List<Customer>> collect = frontFinanList1();
        List<FrontFinancingListVO> frontFinancingListVos = finanListVO(list, collect);
        if (CollectionUtils.isEmpty(frontFinancingListVos)) {
            List<FrontFinancingListVO> frontFinancingListVO = new ArrayList<>();
            page.setRecords(frontFinancingListVO);
            return page;
        }
        page.setRecords(frontFinancingListVos);
        return page;
    }


//	public void saveCoreOrFinancingCompanyType(Long companyId){
//		String enterprise =getEnterprise(companyId,null);
//		if(cn.hutool.core.util.ObjectUtil.isNotEmpty(enterprise)){
//			IFrontFinancingListService frontcingList = org.springblade.core.tool.utils.SpringUtil.getBean(IFrontFinancingListService.class);
//			FrontFinancingList one = frontcingList.lambdaQuery().eq(FrontFinancingList::getCompanyId, companyId).one();
//			if(cn.hutool.core.util.ObjectUtil.isNotEmpty(one)){
//				one.setCompanyType(enterprise);
//				frontcingList.updateById(one);
//			}else {
//				IFrontCoreListService frontCoreListService = org.springblade.core.tool.utils.SpringUtil.getBean(IFrontCoreListService.class);
//				FrontCoreList frontCoreList1 = frontCoreListService.lambdaQuery().eq(FrontCoreList::getCompanyId, companyId).one();
//				if(cn.hutool.core.util.ObjectUtil.isEmpty(frontCoreList1)){
//					return;
//				}
//				frontCoreList1.setCompanyType(enterprise);
//				frontCoreListService.updateById(frontCoreList1);
//			}
//
//		}
//	}

    @Override
    public FrontFinancingListVO getFrontFinancingById(Long id) {

        CustomerFrontUserType customerFrontUserType = SpringUtil.getBean(ICustomerFrontUserTypeService.class).getById(id);
        FrontFinancingListVO frontFinancingListVO = null;
        FrontFinancingList byId = null;
        if (ObjectUtil.isEmpty(customerFrontUserType)) {
            byId = getById(id);
            customerFrontUserType = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getUserId, byId.getCompanyId()).one();
        } else {
            byId = lambdaQuery().eq(FrontFinancingList::getCompanyId, customerFrontUserType.getUserId()).one();
        }
        frontFinancingListVO = FrontFinancingListWrapper.build().entityVO(byId);
        if (ObjectUtil.isEmpty(frontFinancingListVO)) {
            throw new ServiceException("未找到对应数据!");
        }

        Customer customer = SpringUtil.getBean(ICustomerService.class).getById(customerFrontUserType.getCustomerFrontUserId());

        User user1 = UserUtils.getUserById(customerFrontUserType.getUserId());
        //SpringUtil.getBean(IUserService.class).getById(customerFrontUserType.getUserId());

        if (ObjectUtil.isEmpty(frontFinancingListVO)) {
            return null;
        }

        //Customer customer = customerMapper.selectOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getCompanyId,frontFinancingListVO.getCompanyId()));
        if (ObjectUtil.isEmpty(customer)) {
            return frontFinancingListVO;
        }
        frontFinancingListVO.setBelonging(customer.getName());
        frontFinancingListVO.setAccountUser(customer.getAccountUser());
        frontFinancingListVO.setPhone(customer.getPhone());
        frontFinancingListVO.setLastVisitTime(customer.getLastVisitTime());


        EnterpriseAdopt enterpriseAbopt = SpringUtil.getBean(IEnterpriseAdoptService.class).lambdaQuery()
                .eq(EnterpriseAdopt::getUserId, customerFrontUserType.getUserId()).one();
        //标签 企业类型;企业类型  2-融资企业  3-核心企业  1-个人
        if (Integer.valueOf("1").equals(enterpriseAbopt.getLabel())) {
            frontFinancingListVO.setCustomerStatus(customer.getStatus());
        } else {
            //1-开通成功  2-失败 3 -開通中
            frontFinancingListVO.setCustomerStatus(enterpriseAbopt.getProcessStatus());
        }

        String invitationCode = frontFinancingListVO.getInvitationCode();
        frontFinancingListVO.setName(user1.getName());
        //判断当前人是否是个人信息  个人赋值个人头像  企业赋值 企业头像
        if (customerFrontUserType.getType().equals(VerificationCodeSupertube.PERSONAL.getCode())) {
            frontFinancingListVO.setLogoSrc(customer.getLogoSrc());
        } else {
            CustomerInfo customerInfo = customerInfoService.getByCompanyId(user1.getId());
            if(ObjectUtil.isNotEmpty(customerInfo)){
                frontFinancingListVO.setLogoSrc(ObjectUtil.isNotEmpty(customerInfo.getLogo())?customerInfo.getLogo():user1.getAvatar());
            }
        }


        if (StringUtil.isEmpty(invitationCode)) {
            frontFinancingListVO.setInvitationName(null);
        }
        CustomerInfo one = customerInfoService.lambdaQuery().eq(CustomerInfo::getCompanyId, user1.getId()).one();
        if (ObjectUtil.isEmpty(one)) {
            frontFinancingListVO.setOperationStatus(null);
            frontFinancingListVO.setCorporationName(null);
        } else {
            frontFinancingListVO.setOperationStatus(one.getOperationStatus());
            frontFinancingListVO.setCorporationName(one.getCorporationName());
            frontFinancingListVO.setCorporationFaceAttachId(one.getCorporationFaceAttachId());
            frontFinancingListVO.setCorporationBackAttachId(one.getCorporationBackAttachId());
            frontFinancingListVO.setOperatorFaceAttachId(one.getOperatorFaceAttachId());
            frontFinancingListVO.setOperatorBackAttachId(one.getOperatorBackAttachId());
            frontFinancingListVO.setBusinessLicenceAttachId(one.getBusinessLicenceAttachId());
        }

        String invitationCode1 = frontFinancingListVO.getInvitationCode();

        if (!StringUtils.isEmpty(invitationCode1)) {
            Long aLong = Long.valueOf(invitationCode1);
            User user = CustomerUserCache.getUserById(aLong);//userService.getById(aLong);
            frontFinancingListVO.setInvitationName(user.getRealName());
        }

//		RatingRecord ratingRecords = SpringUtil.getBean(IRatingRecordService.class).getOne(Wrappers.<RatingRecord>lambdaQuery().eq(RatingRecord::getClientId, customer.getId())
//			.orderByDesc(BaseEntity::getCreateTime)
//			.last("limit 1"));
//		if (ObjectUtil.isNotEmpty(ratingRecords)) {
//			frontFinancingListVO.setCustomerScore(ratingRecords.getFinalScore());
//		}
        frontFinancingListVO.setCustomerLabel(1);
        if (ObjectUtil.isEmpty(frontFinancingListVO.getCompanyType())) {
            frontFinancingListVO.setCompanyType("规模未知");
        }
        return frontFinancingListVO;
    }


    @Override
    public List<FrontFinancingListVO> selectFinancingContactsList() {
        Map<Long, List<Customer>> longListMap = frontFinanList();
        //查询融资企业 设置为白名单的
        List<FrontFinancingList> list = list();

        return finanListVO(list, longListMap);
    }


    /**
     * 后续这里可能会有表的变更，请注意修改
     *
     * @param status
     * @return
     */
    @Override
    public List<User> getListByStatus(Integer status) {
        //TODO 暂未引入
//		List<User> list = userService.list(Wrappers.<User>lambdaQuery().eq(User::getUserType, 6));
//		for (User user : list) {
//			user.setPassword(null);
//			user.setRoleId(null);
//			user.setDeptId(null);
//			user.setUserType(null);
//			user.setEmail(null);
//			user.setPhone(null);
//			user.setAvatar(null);
//			user.setPostId(null);
//			user.setAccount(null);
//			user.setBirthday(null);
//			user.setSex(null);
//			user.setRealName(null);
//		}
//		return list;
        return null;
    }


    private Map<Long, List<Customer>> frontFinanList() {
        //查询融资企业
        List<Customer> customers = customerMapper.selectList(Wrappers.<Customer>lambdaQuery().and(Wrapper -> Wrapper.eq(Customer::getType, CustomerEnum.THREE_TYPE.getCode()).or().eq(Customer::getType, CustomerEnum.ZERO_TYPE.getCode())));
        if (CollectionUtils.isNotEmpty(customers)) {
            return customers.stream().filter(customer -> Func.isNotEmpty(customer.getCompanyId())).collect(Collectors.groupingBy(Customer::getCompanyId));
        }
        return null;

    }

    private Map<Long, List<Customer>> frontFinanList1() {
        //查询核心企业
        List<Customer> customers = customerMapper.selectList(Wrappers.<Customer>lambdaQuery().and(Wrapper -> Wrapper.eq(Customer::getType, CustomerEnum.ONE_TYPE.getCode())));
        if (CollectionUtils.isNotEmpty(customers)) {
            return customers.stream().filter(customer -> Func.isNotEmpty(customer.getCompanyId())).collect(Collectors.groupingBy(Customer::getCompanyId));
        }
        return null;

    }

    private List<FrontFinancingListVO> finanListVO(List<FrontFinancingList> list, Map<Long, List<Customer>> collect) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }
        //获取所有业务经理
        //TODO 需要重构
        //Map<Long, User> collect2 = userService.lambdaQuery().eq(User::getUserType, 1).list().stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return list.stream().filter(li -> {
                    FrontFinancingListVO frontFinancingListVOS = FrontFinancingListWrapper.build().entityVO(li);
                    List<Customer> customers = collect.get(frontFinancingListVOS.getCompanyId());
                    return !CollectionUtils.isEmpty(customers);
                }
        ).map(li -> {
            FrontFinancingListVO frontFinancingListVOS = FrontFinancingListWrapper.build().entityVO(li);
            List<Customer> customers = collect.get(frontFinancingListVOS.getCompanyId());
            if (CollectionUtils.isEmpty(customers)) {
                return frontFinancingListVOS;
            }
            Customer customer = customers.get(0);
            frontFinancingListVOS.setAccountUser(customer.getAccountUser());
            frontFinancingListVOS.setLastVisitTime(customer.getLastVisitTime());
            frontFinancingListVOS.setName(customer.getName());
            frontFinancingListVOS.setPhone(customer.getPhone());
            frontFinancingListVOS.setCustomerStatus(customer.getStatus());
            frontFinancingListVOS.setLogoSrc(customer.getLogoSrc());
            String invitationCode = frontFinancingListVOS.getInvitationCode();
            if (StringUtil.isEmpty(invitationCode)) {
                frontFinancingListVOS.setInvitationName(null);
            }
            String invitationCode1 = frontFinancingListVOS.getInvitationCode();
            if (!StringUtils.isEmpty(invitationCode1)) {
                Long aLong = Long.valueOf(invitationCode1);
                //User user = collect2.get(aLong);
                //frontFinancingListVOS.setInvitationName(user.getRealName());
            }
//			RatingRecord ratingRecords = SpringUtil.getBean(IRatingRecordService.class).getOne(Wrappers.<RatingRecord>lambdaQuery().eq(RatingRecord::getClientId, customer.getId())
//				.orderByDesc(BaseEntity::getCreateTime)
//				.last("limit 1"));
//			//比较评分是否存在
//			if (ObjectUtil.isNotEmpty(ratingRecords)) {
//				frontFinancingListVOS.setCustomerScore(ratingRecords.getFinalScore());
//			}

            frontFinancingListVOS.setCustomerLabel(1);
            return frontFinancingListVOS;
        }).collect(Collectors.toList());
    }


    /**
     * 查询融资企业基本信息
     *
     * <AUTHOR>
     */
    @Override
    public List<FrontFinancingListVO> all() {
        /***
         * 获取实名状态 1为实名 0为未实名
         */
        List<Customer> customer = SpringUtil.getBean(ICustomerService.class).lambdaQuery()
                .eq(BaseEntity::getStatus, 1)
                .list();
        if (ObjectUtil.isEmpty(customer)) {
            return new ArrayList<>();
        }

        List<Long> collect = customer.stream()
                .map(Customer::getId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<CustomerFrontUserType> customerFrontUserTypes = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().in(CustomerFrontUserType::getCustomerFrontUserId, collect).list();
        if (ObjectUtil.isEmpty(customerFrontUserTypes)) {
            return new ArrayList<>();
        }
        List<Long> collect2 = customerFrontUserTypes.stream().map(CustomerFrontUserType::getUserId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect2)) {
            return new ArrayList<>();
        }
        /**
         * 融资企业基本信息 FrontFinancingList 中的 CompanyId 关联中 用户表中Id， 两个查出来 对比关联然后封装给 vo类 在返回出去
         */
        List<FrontFinancingList> list = this.list(Wrappers.<FrontFinancingList>lambdaQuery().in(FrontFinancingList::getCompanyId, collect2));
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<FrontFinancingListVO> frontFinancingListVOList = FrontFinancingListWrapper.build().listVO(list);

        List<Long> frontListId = frontFinancingListVOList.stream().map(FrontFinancingList::getCompanyId).collect(Collectors.toList());

        List<User> users = remoteUserService.listByUser(frontListId, FeignConstants.FROM_IN).getData();
        if (ObjectUtil.isEmpty(users)) {
            return new ArrayList<>();
        }
        Map<Long, User> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

        List<FrontFinancingListVO> collect1 = frontFinancingListVOList.stream().map(frontFinancingListVO -> {
            User user = userMap.get(frontFinancingListVO.getCompanyId());
            if (ObjectUtil.isEmpty(user)) {
                return frontFinancingListVO;
            }
            frontFinancingListVO.setLogoSrc(user.getAvatar());
            frontFinancingListVO.setName(user.getName());
            return frontFinancingListVO;
        }).collect(Collectors.toList());
        return collect1;
    }

    @Override
    public User userDetail(Long userId) {
        return CustomerUserCache.getUserById(userId);
    }

    @Override
    public void deleteByUserId(Long userId) {
        remove(Wrappers.<FrontFinancingList>lambdaQuery().eq(FrontFinancingList::getCompanyId, userId));
    }


}
