package org.springblade.customer.controller.front;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.DuplicateKeyValid;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.SexEnum;
import org.springblade.common.utils.ChineseUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.sms.SmsTemplate;
import org.springblade.core.sms.model.SmsCode;
import org.springblade.core.sms.model.SmsData;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.businessinfo.vo.CustomerBusinessInfoVO;
import org.springblade.customer.dto.CustomerContactsDTO;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.customer.entity.VerifyPhone;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerTokenUtil;
import org.springblade.customer.util.IdCardUtils;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.*;
import org.springblade.customer.wrapper.CustomerWrapper;
import org.springblade.resource.builder.sms.SmsBuilder;
import org.springblade.resource.endpoint.MailEndpoint;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.service.IParamService;
import org.springblade.resource.utils.SmsUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static org.springblade.resource.utils.SmsUtil.*;

@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/front/customer")
@Api(value = "用户", tags = "用户接口")
@Slf4j

public class CustomerApiController extends BladeController {

    private final SmsBuilder smsBuilder;

    private final MailEndpoint mailEndpoint;

    private final ICustomerService customerService;

    private final JwtProperties jwtProperties;

    private final BladeRedis bladeRedis;

    private final ICustomerAuthService customerAuthService;



    @ApiLog("注册用户验证")
    @PostMapping("/web/register")
    @ApiOperation(value = "注册用户", notes = "账号:accountUser,密码:password")
    public R<Kv> register(
            SmsCode smsCode,
            @ApiParam(value = "密码", required = true) @RequestParam(required = false) String password,
            @ApiParam(value = "确认密码", required = true) @RequestParam(required = false) String newPassword,
            @ApiParam(value = "账号", required = true) @RequestParam(required = false) String accountUser,
            String tenantId
    ) {
        customerService.isTenanId(tenantId);
        if (password.length() < 6) {
            return R.fail("密码长度过短!");
        }
        if (password.equals(newPassword)) {
            boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
            if (validate) {
                Customer customer = new Customer();

                customer.setPassword(password);
                //手机号码
                String phone = smsCode.getPhone();
                customer.setPhone(phone);
                ////0-待实名 1-待授信 2-已授信
                customer.setStatus(0);
                //融资企业新增
                customer.setType(0);
                //账号
                customer.setAccountUser(accountUser);
                //租户ID
                customer.setTenantId(tenantId);
                customerService.customerRegister(customer);
                CustomerVO customerVO = CustomerWrapper.build().entityVO(customer);
                return R.data(CustomerTokenUtil.createAuth(customerVO));
            } else {
                return R.fail("验证码错误!");
            }
        } else {
            return R.fail("密码与确认密码不匹配!");
        }
    }

    @ApiLog("修改密码")
    @PostMapping("/web/updatePassword")
    @ApiOperation(value = "修改密码")
    public R<Kv> updatePassword(String originPassword, String newPassword, String rePassword) {
        if (ChineseUtil.isChinese(newPassword)) {
            throw new ServiceException("不能输入中文!");
        }
        //检查密码长度
        if (newPassword.length() < 6) {
            return R.fail("密码长度过短!");
        }
        Long id = MyAuthUtil.getId();
        if (ObjectUtil.isEmpty(id)) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, ResultCode.CLIENT_UN_AUTHORIZED.getMessage());
        }
        //对比原密码
        Customer customer = customerService.getById(id);
        String password = customer.getPassword();

        //检查密码是否不一致
        String md5OriginPassword = SecureUtil.md5(originPassword);
        if (!password.equals(md5OriginPassword)) {
            return R.fail("原密码错误,请输入正确的原密码!");
        }
        if (!newPassword.equals(rePassword)) {
            return R.fail("密码与确认密码不匹配!");
        }

        String md5Password = SecureUtil.md5(newPassword);
        return R.status(customerService.update(Wrappers.<Customer>lambdaUpdate().eq(BaseEntity::getId, id).set(Customer::getPassword, md5Password)));
    }


    @PostMapping("/web/login")
    @ApiOperation("登录")
    @ApiLog(value = "登录")
    public R<Object> login(
            @Valid @ApiParam(value = "账号", required = true) @RequestParam(required = false) String account,
            @ApiParam(value = "密码", required = true) @RequestParam(required = false) String password
            , String tenantId, @ApiParam(value = "融资企业或核心企业", required = true) @RequestParam(required = false) Integer type
    ) {
        R r = TenantBroker.applyAs(tenantId, tenantId1 -> {
            customerService.isTenanId(tenantId);
            if (!customerService.userLogin(account, password, tenantId, type)) {
                return R.fail("密码错误!");
            }
            Customer customer1 = customerService.getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getAccountUser, account).eq(TenantEntity::getTenantId, tenantId).eq(Customer::getType, type));
            customerService.updateZoneDateTimeById(customer1);
            Map<String, Object> map = new HashMap<>(16);
            List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = TenantBroker.applyAs(tenantId, e -> {
                return SpringUtil.getBean(ICustomerFrontUserTypeService.class).selectByCustomerIdAndEncryptionPhone(customer1.getId());
            });
            if (1 == customerFrontUserTypeVOS.size()) {
                CustomerFrontUserTypeVO customerFrontUserTypeVO = customerFrontUserTypeVOS.get(0);
                CustomerVO customerVO = TenantBroker.applyAs(tenantId, e -> {
                    return SpringUtil.getBean(ICustomerFrontUserTypeService.class).personalCustomer(customerFrontUserTypeVO.getId(), customerFrontUserTypeVO.getCustomerFrontUserId());
                });
                return R.data(CustomerTokenUtil.createAuthCustomer(customerVO));
            }
            map.put("cusomerType", customerFrontUserTypeVOS);
            //String addr = IpUtil.getRealIP(request);
            String uuid = UUID.randomUUID().toString();
            Long id = customer1.getId();
            bladeRedis.setEx(CacheUtil.formatCacheName(uuid, true), id + uuid, 3 * 60L);
            CustomerCheckVo customerCheckVo = new CustomerCheckVo();
            customerCheckVo.setId(id);
            customerCheckVo.setUuid(uuid);
            map.put("customerCheckVo", customerCheckVo);
            return R.data(map);
        });
        return r;
    }

    @PostMapping("/web/phoneLogin")
    @ApiOperation("登录")
    @ApiLog(value = "登录")
    public R<Object> phoneLogin(SmsCode smsCode, String tenantId, Integer type
    ) {
        customerService.isTenanId(tenantId);
        boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
        if (!validate) {
            return R.fail("验证码错误!");
        }
        Customer customer = customerService.getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, smsCode.getPhone()).eq(TenantEntity::getTenantId, tenantId).eq(Customer::getType, type));
        if (null == customer) {
            return R.fail("手机号未注册!");
        }
        customerService.updateZoneDateTimeById(customer);

        Map<String, Object> map = new HashMap<>(16);

        //List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = SpringUtil.getBean(ICustomerFrontUserTypeService.class).selectByCustomerIdAndEncryptionPhone(customer.getId());

        List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = TenantBroker.applyAs(tenantId, e -> {
            return SpringUtil.getBean(ICustomerFrontUserTypeService.class).selectByCustomerIdAndEncryptionPhone(customer.getId());
        });

        if (1 == customerFrontUserTypeVOS.size()) {
            CustomerFrontUserTypeVO customerFrontUserTypeVO = customerFrontUserTypeVOS.get(0);
            CustomerVO customerVO = SpringUtil.getBean(ICustomerFrontUserTypeService.class).personalCustomer(customerFrontUserTypeVO.getId(), customerFrontUserTypeVO.getCustomerFrontUserId());
            return R.data(CustomerTokenUtil.createAuthCustomer(customerVO));
        }
        map.put("cusomerType", customerFrontUserTypeVOS);
        //String addr = IpUtil.getRealIP(request);
        String uuid = UUID.randomUUID().toString();
        Long id = customer.getId();
        bladeRedis.setEx(CacheUtil.formatCacheName(uuid, true), id + uuid, 3 * 60L);
        CustomerCheckVo customerCheckVo = new CustomerCheckVo();
        customerCheckVo.setId(id);
        customerCheckVo.setUuid(uuid);
        map.put("customerCheckVo", customerCheckVo);
        return R.data(map);
    }


    @PostMapping("/updateName")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改企业名称", notes = "传入name")
    public R<Boolean> updateName(@Valid String name) {
        Long id = MyAuthUtil.getUserId();
        boolean update = customerService.update(Wrappers.<Customer>lambdaUpdate().eq(BaseEntity::getId, id).set(Customer::getName, name));
        return R.status(update);
    }

    /***
     * 修改邮箱
     */
    @SneakyThrows
    @PostMapping("/updateEmail")
    @ApiOperation(value = "修改邮箱", notes = "email")
    public R<Boolean> updateEmail(String email, String code) {
        Long id = MyAuthUtil.getId();
        if (ObjectUtil.isEmpty(id)) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, ResultCode.CLIENT_UN_AUTHORIZED.getMessage());
        }
        //判断邮箱格式
        customerService.isnotNullEmail(email);
        boolean validate = mailEndpoint.validateMessage(code);
        if (!validate) {
            return R.fail("验证码错误或验证码已过期");
        }
        //通过后销毁验证码
        mailEndpoint.destroyMessageCode();
        return R.status(customerService.update(Wrappers.<Customer>lambdaUpdate().eq(BaseEntity::getId, id).set(Customer::getEmail, email)));
    }

    /***
     * 修改手机号
     */
    @SneakyThrows
    @PostMapping("/updatePhone")
    @ApiOperation(value = "修改手机号", notes = "phone")
    public R<Boolean> updatePhone(String newPhone, SmsCode smsCode) {
        Long userId = MyAuthUtil.getId();
        //判断手机格式
        customerService.isnotNullPhone(newPhone);
        if (newPhone.equals(smsCode.getPhone())) {
            return R.fail("不能绑定原手机号码");
        }

        boolean validate = smsBuilder.myTemplate(AuthUtil.getTenantId(), "verification").validateMessage(smsCode);
        if (!validate) {
            return R.fail("验证码错误!");
        }
        ICustomerPersonInfoService bean = SpringUtil.getBean(ICustomerPersonInfoService.class);
        CustomerPersonInfo one = bean.lambdaQuery().eq(CustomerPersonInfo::getAccount, MyAuthUtil.getUserPhone()).one();
        one.setAccount(newPhone);
        bean.updateById(one);
        return R.status(customerService.update(Wrappers.<Customer>lambdaUpdate().eq(BaseEntity::getId, userId).set(Customer::getPhone, newPhone)));
    }

    @GetMapping("/oauth/logout")
    @ApiOperation(value = "退出登录")
    public Kv logout() {
        BladeUser user = AuthUtil.getUser();
        if (user != null && jwtProperties.getState()) {
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.removeAccessToken(user.getTenantId(), String.valueOf(user.getUserId()), token);
        }
        return Kv.create().set("success", "true").set("msg", "success");
    }

    @GetMapping("/web/getSecurityDetails")
    @ApiOperation(value = "获取安全中心页面")
    public R<CustomerVO> getSecurityDetails() {
        Long id = MyAuthUtil.getId();
        CustomerVO customerVO = SpringUtil.getBean(ICustomerFrontUserTypeService.class).customerSignin(MyAuthUtil.getTypeId(), id);
        return R.data(customerVO);
    }

    /***
     * 修改头像
     */
    @SneakyThrows
    @PostMapping("/updateLogoSrc")
    @ApiOperation(value = "修改头像", notes = "logoSrc")
    public R<Boolean> getSecurityDetails(String logoSrc) {
        if (ObjectUtil.isEmpty(MyAuthUtil.getId())) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, ResultCode.CLIENT_UN_AUTHORIZED.getMessage());
        }
        if (StringUtil.isEmpty(logoSrc)) {
            return R.fail("未找到参数");
        }
        Customer customer = customerService.getById(MyAuthUtil.getId());
        if (ObjectUtil.isEmpty(customer)) {
            return R.status(false);
        }
        customer.setLogoSrc(logoSrc);
        return R.data(customerService.saveOrUpdate(customer));
    }

    @GetMapping("searchRealName")
    @ApiOperation(value = "查询是否已经实名")
    public R<Boolean> searchRealName() {
        return R.status(customerService.isRealName());
    }

    /**
     * 短信验证码发送
     *
     * @param phone 手机号
     */
    @SneakyThrows
    @PostMapping("/send-validate")
    public R<Object> sendValidate(@RequestParam String phone, @RequestParam String tenantId) {
        //Map<String, String> params = SmsUtil.getValidateParams();
        IParamService paramService = SpringUtil.getBean(IParamService.class);
        SmsTemplate smsTemplate = smsBuilder.myTemplate(tenantId,"verification");
        SmsCode smsCode = new SmsCode();
        String value = paramService.getValue(SMS_CODE_PARAM);
        if (StringUtil.isNotBlank(value) && Boolean.parseBoolean(value)) {
            String id = StringUtil.randomUUID();
//            this.bladeRedis.setEx(CacheUtil.formatCacheName(smsTemplate.cacheKey(phone, id), true), "123456", Duration.ofMinutes(30L));
            this.bladeRedis.setEx(smsTemplate.cacheKey(phone, id), "123456", Duration.ofMinutes(30L));
            smsCode.setId(id).setValue(value);
        }else{
            Map<String, String> params = SmsUtil.getValidateParams();
            smsCode = smsTemplate.sendValidate(new SmsData(params).setKey(PARAM_KEY), phone);
        }

        //SmsCode smsCode = smsBuilder.myTemplate(tenantId, "verification").sendValidate(new SmsData(params).setKey(PARAM_KEY), phone);
        return smsCode.isSuccess() ? R.data(smsCode, SEND_SUCCESS) : R.fail(SEND_FAIL);
    }

    @ApiLog("忘记密码")
    @PostMapping("/web/forgetPassword")
    @ApiOperation(value = "忘记密码")
    public R<Kv> forgetPassword(SmsCode smsCode, String password, String newPassword, String tenantId, String accountUser) {
        if (ChineseUtil.isChinese(newPassword)) {
            throw new ServiceException("不能输入中文!");
        }
        String phone = smsCode.getPhone();


        //判断用户是否存在
        Customer one = customerService.getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, phone).eq(Customer::getTenantId, tenantId));
        if (null == one) {
            return R.fail("手机号不存在!");
        }

//        Customer customer = customerService.getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getAccountUser, accountUser).eq(Customer::getPhone, phone).eq(Customer::getTenantId, tenantId));
//        if (null == customer) {
//            return R.fail("用户不存在!");
//        }

        if (password.length() < 6) {
            return R.fail("密码长度过短!");
        }
        if (!newPassword.equals(password)) {
            return R.fail("确认密码与密码不匹配!");
        }

        boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
        if (!validate) {
            return R.fail("验证码错误!");
        }

        String md5Password = SecureUtil.md5(password);
        return R.status(customerService.update(Wrappers.<Customer>lambdaUpdate().eq(Customer::getPhone, phone).set(Customer::getPassword, md5Password)));
    }

    @PostMapping("/web/overdue")
    @ApiLog("检查token是否过期")
    @ApiOperation(value = "检查token是否过期")
    public R<Object> overdue() {
        //查找是否存在id
        Long id = MyAuthUtil.getId();
        if (Func.isEmpty(id)) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, "");
        }
        return R.fail(ResultCode.SUCCESS, ResultCode.SUCCESS.getMessage());
    }

    @PostMapping("/web/phone")
    @ApiLog("获取phone")
    @ApiOperation(value = "获取phone")
    public R<String> getPhone() {
        //查找是否存在id
        Long customerId = MyAuthUtil.getCustomerId();
        Customer byId = customerService.getById(customerId);
        if (Func.isEmpty(byId)) {
            return R.fail(ResultCode.CLIENT_UN_AUTHORIZED, "");
        }
        String phone = byId.getPhone();
        return R.data(phone);
    }

    //没用到
    @GetMapping("/getUserInfoByCompanyId")
    @ApiLog("通过companyid获取客户信息,客服系统")
    @ApiOperation(value = "通过companyid获取客户信息,客服系统")
    public R<Customer> getUserInfoByCompanyId(Long companyId) {
        Customer customer = customerService.lambdaQuery().eq(Customer::getCompanyId, companyId).one();
        return R.data(customer);
    }


    @GetMapping("/cskf/getContactsInfoByCompanyId")
    @ApiLog("通过融资用户id获取客户联系")
    @ApiOperation(value = "通过融资用户id获取客户联系")
    public R<CustomerContactsDTO> getContactsInfoByCompanyId(@RequestParam Long companyId) {
        Customer customer = customerService.lambdaQuery().eq(Customer::getCompanyId, companyId).one();
        if (ObjectUtil.isEmpty(customer)) {
            return null;
        } else {
            CustomerInfo customerInfo = SpringUtil.getBean(ICustomerInfoService.class).selectByCustomerId(customer.getId());
            CustomerPersonInfo customerPersonInfo = SpringUtil.getBean(ICustomerPersonInfoService.class).getPersonInfoByCustomerId(customer.getId());
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                CustomerBusinessInfoVO customerBusinessInfoVO = new CustomerBusinessInfoVO();
                customerBusinessInfoVO.setCompanyName(customerInfo.getCorpName());
                customerBusinessInfoVO.setCreditCode(customerInfo.getBusinessLicenceNumber());
                CustomerBusinessInfo customerBusinessInfo = SpringUtil.getBean(ICustomerBusinessInfoService.class).getCustomerBusinessInfo(customerBusinessInfoVO);
                CustomerContactsDTO customerContactsDTO = new CustomerContactsDTO();
                customerContactsDTO.setCompany(customerInfo.getCorpName())
                        .setAddress(customerBusinessInfo.getRegLocation())
                        .setCountry(customerInfo.getCorporationCountry())
                        .setEmail(customer.getEmail())
                        .setGender(SexEnum.getSexByCode(SexEnum.class, customerInfo.getCorporationSex()))
                        .setCusbirthday(IdCardUtils.cusBirthday(customerInfo.getCorporationIdCardNumber()))
                        .setPhone(customerBusinessInfo.getPhoneNumber())
                        .setName(customerInfo.getCorpName())
                        .setIdentifynumber(customerInfo.getCorporationIdCardNumber());
                return R.data(customerContactsDTO);
            } else if (ObjectUtil.isNotEmpty(customerPersonInfo)) {
                CustomerContactsDTO customerContactsDTO = new CustomerContactsDTO();
                customerContactsDTO
                        .setAddress(customerPersonInfo.getAddress())
                        .setGender(SexEnum.getSexByCode(SexEnum.class, customerInfo.getCorporationSex()))
                        .setCusbirthday(IdCardUtils.cusBirthday(customerPersonInfo.getIdentity()))
                        .setPhone(customer.getPhone())
                        .setEmail(customer.getEmail())
                        .setName(customerPersonInfo.getName())
                        .setIdentifynumber(customerPersonInfo.getIdentity());
                return R.data(customerContactsDTO);
            }
            return null;
        }
    }

    /***
     * 二阶段修改手机号
     */
    @SneakyThrows
    @PostMapping("/updatePhoneTwo")
    @ApiOperation(value = "修改手机号", notes = "phone")
    public R<Boolean> updatePhoneTwo(SmsCode smsCode, String beforeId) {
        Long customerId = MyAuthUtil.getId();
        String phone = smsCode.getPhone();
        //判断手机格式
        customerService.isnotNullPhone(phone);

        Customer byId = customerService.getById(customerId);
        if (byId.getPhone().equals(phone)) {
            return R.fail("不能绑定原手机号码");
        }

        boolean validate = smsBuilder.myTemplate(AuthUtil.getTenantId(), "verification").validateMessage(smsCode);
        if (!validate) {
            return R.fail("验证码错误!");
        }
        IVerifyPhoneService bean1 = SpringUtil.getBean(IVerifyPhoneService.class);
        VerifyPhone one1 = bean1.lambdaQuery().eq(VerifyPhone::getBeforeId, beforeId).eq(VerifyPhone::getBeforePhone, byId.getPhone()).one();
        if (ObjectUtil.isEmpty(one1)) {
            throw new ServiceException("未通过前手机号码校验!");
        } else {
            bean1.removeById(one1);
        }
        ICustomerPersonInfoService bean = SpringUtil.getBean(ICustomerPersonInfoService.class);

        CustomerPersonInfo one = bean.lambdaQuery().eq(CustomerPersonInfo::getCustomerId, MyAuthUtil.getCustomerId()).one();
        if (ObjectUtil.isNotEmpty(one)) {
            one.setAccount(phone);
            bean.updateById(one);
        }
        return R.status(customerService.update(Wrappers.<Customer>lambdaUpdate().eq(BaseEntity::getId, customerId).set(Customer::getPhone, phone)));
    }

    @SneakyThrows
    @PostMapping("/send-validate-customer")
    public R<Object> sendValidateCustomer(@RequestParam String tenantId) {
        Map<String, String> params = SmsUtil.getValidateParams();
        Long customerId = MyAuthUtil.getCustomerId();
        Customer byId = customerService.getById(customerId);
        SmsCode smsCode = smsBuilder.myTemplate(tenantId, "verification").sendValidate(new SmsData(params).setKey(PARAM_KEY), byId.getPhone());
        return smsCode.isSuccess() ? R.data(smsCode, SEND_SUCCESS) : R.fail(SEND_FAIL);
    }

    @SneakyThrows
    @ApiLog("校验手机号")
    @PostMapping("/checkCustomerPhone")
    public R<Boolean> sendValidateCustomer(SmsCode smsCode, String tenantId) {
        Long customerId = MyAuthUtil.getCustomerId();
        Customer byId = customerService.getById(customerId);
        smsCode.setPhone(byId.getPhone());
        boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
        if (!validate) {
            throw new ServiceException("验证码错误");
        }

        VerifyPhone verifyPhone = new VerifyPhone();
        verifyPhone.setBeforeId(smsCode.getId());
        verifyPhone.setBeforePhone(smsCode.getPhone());
        SpringUtil.getBean(IVerifyPhoneService.class).saveVerifyPhone(verifyPhone);
        return R.status(validate);
    }

    @SneakyThrows
    @ApiLog("查看身份证信息")
    @GetMapping("/selectIdentityCard")
    public R<List<String>> selectIdentityCard() {
        Long customerId = MyAuthUtil.getCustomerId();
        Customer customer = SpringUtil.getBean(ICustomerService.class)
                .lambdaQuery().eq(Customer::getId, customerId).one();
        CustomerPersonInfo one = SpringUtil.getBean(ICustomerPersonInfoService.class).lambdaQuery().eq(CustomerPersonInfo::getCustomerId, customer.getCompanyId()).one();
        if (ObjectUtil.isEmpty(one)) {
            throw new ServiceException("未获取到个人信息!");
        }
        String identityBackfileAttachid = one.getIdentityBackfileAttachid();
        String identityFacefileAttachid = one.getIdentityFacefileAttachid();
        List<String> list = new ArrayList<>();
        list.add(identityBackfileAttachid);
        list.add(identityFacefileAttachid);
        List<Attach> list1 = SpringUtil.getBean(IAttachService.class).lambdaQuery().in(BaseEntity::getId, list).list();
        if (ObjectUtil.isEmpty(list1)) {
            return null;
        }
        List<String> collect = list1.stream().map(Attach::getLink).collect(Collectors.toList());
        return R.data(collect);
    }

    @SneakyThrows
    @ApiLog("查询当前账户是否已实名已认证企业")
    @GetMapping("/companyRealName")
    @PreAuth("hasPermission('front:customer:companyRealName') or hasRole('admin') or hasRole('financing_admin')")
    public R<CustomerFrontUserTypeBackVO> companyRealName() {
        CustomerFrontUserTypeBackVO customerFrontUserTypeVO = customerService.companyRealName();
        return R.data(customerFrontUserTypeVO);
    }

    @SneakyThrows
    @ApiLog("查询手机号是否已经注册")
    @GetMapping("/checkPhone")
    public R<Boolean> checkPhone(String phone) {
        Customer one = SpringUtil.getBean(ICustomerService.class).getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, phone));
        return R.status(ObjectUtil.isEmpty(one));
    }

    @SneakyThrows
    @ApiLog("查询账号是否已经注册")
    @GetMapping("/checkAccount")
    public R<Boolean> checkAccount(String account) {
        Customer one = SpringUtil.getBean(ICustomerService.class).getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getAccountUser, account));
        return R.status(ObjectUtil.isEmpty(one));
    }

    @ApiLog("提交基础信息")
    @PostMapping("/postCustomerPersonInfo")
    @DuplicateKeyValid("操作过快")
    public R postCustomerPersonInfo(@RequestBody CustomerPersonInfoVO customerPersonInfo) {
        if (ObjectUtil.isNotEmpty(customerPersonInfo.getId())) {
            throw new ServiceException("id必须为空");
        }
        Long customerId = MyAuthUtil.getCustomerId();
        if (ObjectUtil.isEmpty(customerId)) {
            throw new ServiceException("登录过期");
        }
        Customer one = customerService.getById(customerId);
        customerPersonInfo.setIdentityEffective(customerPersonInfo.getValidFrom() + "-" + customerPersonInfo.getValidTo());
        customerPersonInfo.setCustomerId(AuthUtil.getUserId());
        customerPersonInfo.setAccount(one.getPhone());
        return customerAuthService.commitPersonAuthInfo(customerPersonInfo);
    }

    @SneakyThrows
    @ApiLog("校验手机号码")
    @GetMapping("/checkSmsBuilderPhone")
    public R<Boolean> checkPhone(String tenantId, SmsCode smsCode) {
        boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
        return validate ? R.success(VALIDATE_SUCCESS) : R.fail(VALIDATE_FAIL);
    }

    @ApiLog("查看当前登录用户手机号脱敏")
    @GetMapping("/desensitizationPhoneInfo")
    public R desensitizationPhoneInfo() {
        Long personalUserId = MyAuthUtil.getPersonalUserId();
        if (ObjectUtil.isEmpty(personalUserId)) {
            return R.status(true);
        }
        return R.data(customerService.getDesensitizationPhoneInfo(personalUserId));
    }
}
