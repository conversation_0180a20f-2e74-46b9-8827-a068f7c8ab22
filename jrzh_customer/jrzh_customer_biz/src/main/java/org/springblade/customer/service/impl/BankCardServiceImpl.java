/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.bank.entity.Bank;
import org.springblade.bank.service.IBankService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.common.enums.CustomerTypeEnum;
import org.springblade.common.enums.EnterpriseQuotaStatusEnum;
import org.springblade.common.enums.VerificationCodeSupertube;
import org.springblade.common.enums.dept.DeptCommonConstant;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.SaveCustomerBankCardDTO;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.CustomerBankCardPersonEnum;
import org.springblade.customer.mapper.BankCardMapper;
import org.springblade.customer.publisher.AllGroupPlanCallPublisher;
import org.springblade.customer.service.*;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.BankCardVO;
import org.springblade.customer.wrapper.BankCardWrapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.modules.bank.handler.BankServiceApiHandler;
import org.springblade.modules.bank.handler.BankServiceApiHandlerFactory;
import org.springblade.modules.bank.service.IHuaweiBankCardService;
import org.springblade.modules.othersapi.dto.BankCardInfoVO;
import org.springblade.otherapi.core.constant.ApiSupplier;
import org.springblade.othersapi.core.handler.CompanyPaymentCertificationHandler;
import org.springblade.othersapi.core.utils.OtherApiUtils;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 添加账户 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Service
@AllArgsConstructor
public class BankCardServiceImpl extends BaseServiceImpl<BankCardMapper, BankCard> implements IBankCardService {

    private final IBankService bankService;
    private final IHuaweiBankCardService huaweiBankCardService;
    private final RemoteDeptSearchService remoteDeptSearchService;
    private final ICustomerInfoService customerInfoService;
    private final ICustomerPersonInfoService customerPersonInfoService;

    private final RemoteUserService remoteUserService;
    private final ICustomerBankCardPersonService customerBankCardPersonService;
    private final ICustomerGoodsService customerGoodsService;
    //private final IDeptService deptService;
    private final BankServiceApiHandlerFactory bankServiceApiHandlerFactory;
    private final AllGroupPlanCallPublisher allGroupPlanCallPublisher;

    private final Map<String, CompanyPaymentCertificationHandler> companyPaymentCertificationHandlerMap;

    @Override
    public IPage<BankCardVO> selectBankCardPage(IPage<BankCardVO> page, BankCardVO bankCard) {
        return page.setRecords(baseMapper.selectBankCardPage(page, bankCard));
    }

    /**
     * 新增银行卡数据
     *
     * @param bankCard
     * @return
     */
    @Override
    public boolean saveBackCard(BankCard bankCard) {
        Long userId = MyAuthUtil.getUserId();
        if (ObjectUtil.isNotEmpty(userId)) {
            bankCard.setUserId(userId);
            User user = remoteUserService.getUserById(MyAuthUtil.getUserId(), FeignConstants.FROM_IN).getData();
            bankCard.setEnterpriseName(user.getName());
        } else {
            throw new ServiceException("银行卡添加失败");
        }
        int count = count(Wrappers.<BankCard>lambdaQuery().eq(BankCard::getUserId, userId)
                .eq(BankCard::getBankCardNo, bankCard.getBankCardNo()));
        if (count > 0) {
            throw new ServiceException("新增失败,银行卡号已存在");
        }
        if (bankCard.getBankCardNo().length() < 4) {
            throw new ServiceException("错误的银行卡号!");
        }
        Boolean isOpen = OtherApiUtils.isOpenByApiNo(ApiSupplier.HUAWEI_BANK_SERVICE.getCode());
        if (isOpen) {
            BankCardInfoVO cardInfoVO = huaweiBankCardService.getByCardNo(bankCard.getBankCardNo());
            if (ObjectUtil.isEmpty(cardInfoVO)) {
                throw new ServiceException("错误的银行卡号!");
            }
            bankCard.setCardName(cardInfoVO.getCardName());
            bankCard.setBankCode(cardInfoVO.getBankCode());
            bankCard.setBankDeposit(cardInfoVO.getBankName());
            bankCard.setAbbr(cardInfoVO.getAbbr());
            bankCard.setCardType(cardInfoVO.getCardType());
            bankCard.setCardBin(cardInfoVO.getCardBin());
            bankCard.setBinLen(cardInfoVO.getBinLen());
        } else {
            Bank bank = bankService.getById(bankCard.getBankId());
            bankCard.setBankName(bank.getName());
        }
        bankCard.setBankArea("44/4403");
        String[] split = bankCard.getBankArea().split("/");
        bankCard.setBankArea(split[split.length - 1]);
        //实名校验
        String code = ApiSupplier.BEST_SIGN_BANK_SERVICE.getCode();
        Boolean bestBankOpen = OtherApiUtils.isOpenByApiNo(code);
        if (bestBankOpen) {
            BankServiceApiHandler bankServiceApiHandler = bankServiceApiHandlerFactory.template(code);
            Integer customerType = MyAuthUtil.getCustomerType();
            Map<String, Object> query = new HashMap<>();
            Boolean verify = false;
            if (CustomerTypeEnum.PERSONAL.getCode().equals(customerType)) {
                CustomerPersonInfo personInfo = customerPersonInfoService.getByCustomerId(AuthUtil.getUserId());
                verify = bankServiceApiHandler.personBankVerify(personInfo.getName(), bankCard.getBankCardNo());
            } else {
                CustomerInfo customerInfo = customerInfoService.getByCompanyId(AuthUtil.getUserId());
//                SSQAccountAudit ssqAccountAudit = new SSQAccountAudit();
//                ssqAccountAudit.setAccount(customerInfo.getCompanyId().toString());
//                ssqAccountAudit.setBankCardName(customerInfo.getCorpName());
//                ssqAccountAudit.setBankCard(bankCard.getBankCardNo());
//                ssqAccountAudit.setBankName(bankCard.getBankDeposit());
//                ssqAccountAudit.setBankAreaCode(bankCard.getBankArea());
//                ssqAccountAudit.setBraBankName(bankCard.getBankName());
//                verify = bankServiceApiHandler.entBankVerify(ssqAccountAudit);
                Map<String, Object> param = new HashMap<>();
                param.put("companyId", customerInfo.getCompanyId());
                param.put("corpName", customerInfo.getCorpName());
                param.put("bankCard", bankCard.getBankCardNo());
                param.put("bankName", bankCard.getBankDeposit());
                param.put("bankAreaCode", bankCard.getBankArea());
                param.put("braBankName", bankCard.getBankName());
                verify = companyPaymentCertificationHandlerMap.get(code).bankVerify(param);


            }
            if (!verify) {
                throw new ServiceException("请核实卡号信息是否正确及卡号所属人是否为您本人");
            }
        }
        save(bankCard);
        return true;
    }

    /***
     * 查询所有银行卡
     * @return
     */
    @Override
    public List<BankCardVO> backCardAll() {
        Long userId = MyAuthUtil.getUserId();
        List<BankCardVO> bankCardVOS = backCardAll(userId);
        if (ObjectUtil.isNotEmpty(bankCardVOS)) {
            bankCardVOS = bankCardVOS.stream().map(bankCardVO -> {
                bankCardVO.setBankCardNo(DesensitizedUtil.bankCard(bankCardVO.getBankCardNo()));
                return bankCardVO;
            }).collect(Collectors.toList());
        }
        return bankCardVOS;

    }

    /***
     * 接触绑定
     * @param id
     * @return
     */
    @Override
    public boolean relieveBackCar(Long id) {
        return removeById(id);
    }

    /***
     * 查询当前最高企业手机号码  与企业名称
     */
    @Override
    public BankCardVO enterprisePhoneAndName() {
        Long userId = MyAuthUtil.getUserId();
        CustomerFrontUserType one = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getUserId, userId).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).one();
        if (ObjectUtil.isEmpty(one)) {
            //个人账户
            CustomerFrontUserType customerFrontUserType = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getUserId, userId).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.PERSONAL.getCode()).one();
            String companyName = MyAuthUtil.getCompanyName();
            Customer one1 = SpringUtil.getBean(ICustomerService.class).lambdaQuery().eq(BaseEntity::getId, customerFrontUserType.getCustomerFrontUserId()).one();
            BankCardVO bankCardVO = new BankCardVO();
            bankCardVO.setPhone(one1.getPhone());
            bankCardVO.setEnterpriseName(companyName);
            return bankCardVO;
        }
        Long customerFrontUserId = one.getCustomerFrontUserId();
        Customer one1 = SpringUtil.getBean(ICustomerService.class).lambdaQuery().eq(BaseEntity::getId, customerFrontUserId).one();
        String companyName = MyAuthUtil.getCompanyName();
        BankCardVO bankCardVO = new BankCardVO();
        bankCardVO.setPhone(one1.getPhone());
        bankCardVO.setEnterpriseName(companyName);
        return bankCardVO;
    }

    @Override
    public List<BankCardVO> backCardFinAll(Long userId) {
        List<BankCardVO> bankCardVOS = backCardAll(userId);
        return bankCardVOS;
    }

    @Override
    public List<BankCardVO> backCardByBankName(String bankName) {
        Long userId = MyAuthUtil.getUserId();
        List<BankCardVO> bankCardVOS = null;
        if (StringUtil.isBlank(bankName)) {
            bankCardVOS = backCardAllByName(userId, null);
        } else {
            //根据资方名称查询
            Dept capital = remoteDeptSearchService.getDeptByName(bankName, AuthUtil.getTenantId()).getData();
            if (ObjectUtil.isEmpty(capital)) {
                throw new ServiceException("资方不存在");
            }
            List<String> keyword = new ArrayList<>();
            if (capital.getCapitalType().equals(DeptCommonConstant.DeptCapitalTypeEnum.CAPITAL_TYPE_BANK.getCode())) {
                keyword.addAll(Collections.singletonList(bankName));
            }
            String capitalKeyword = capital.getCapitalKeyword();
            if (StringUtil.isNotBlank(capitalKeyword)) {
                keyword.addAll(Func.toStrList(capitalKeyword));
            }
            bankCardVOS = backCardAllByName(userId, keyword);
        }
        if (ObjectUtil.isNotEmpty(bankCardVOS)) {
            bankCardVOS = bankCardVOS.stream().map(bankCardVO -> {
                String bankCardNo = bankCardVO.getBankCardNo();
                bankCardVO.setBankCardNoCutOut(bankCardNo.substring(bankCardNo.length() - 4));
                bankCardVO.setBackName(bankCardVO.getBankDeposit());
                return bankCardVO;
            }).collect(Collectors.toList());
        }
        return bankCardVOS;
    }

    @Override
    public BankCard getByGoodsIdAndUserId(Long goodsId, Long userId) {
        return baseMapper.getByGoodsIdAndUserId(goodsId, userId);
    }

    @Override
    public BankCard getByCardNo(String bankCardNo, Long userId) {
        return getOne(Wrappers.<BankCard>lambdaQuery().eq(BankCard::getBankCardNo, bankCardNo).eq(BankCard::getUserId, userId));
    }

    private List<BankCardVO> backCardAllByName(Long userId, List<String> keyword) {
        List<BankCard> list = this.lambdaQuery().eq(BankCard::getUserId, userId)
                .and(CollUtil.isNotEmpty(keyword), e -> {
                    keyword.forEach(k -> {
                        e.like(BankCard::getBankDeposit, k).or();
                    });
                }).list();
        return BankCardWrapper.build().listVO(list);
    }

    /**
     * 保存用户产品的还款账户
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCustomerBankCard(SaveCustomerBankCardDTO dto) {
        Long userId = AuthUtil.getUserId();
        BankCard dbBankCard = this.getOne(Wrappers.<BankCard>lambdaQuery()
                .eq(BankCard::getId, dto.getBankCardId())
                .eq(BankCard::getUserId, userId)
        );
        if (ObjectUtil.isEmpty(dbBankCard)) {
            throw new ServiceException("银行账户有误");
        }
        Boolean updateOther = this.updateOther(dto, userId, dbBankCard);
        return updateOther;
    }

    private Boolean updateOther(SaveCustomerBankCardDTO dto, Long userId, BankCard bankCard) {
        Integer goodsType = dto.getGoodsType();
        Long goodsId = dto.getGoodsId();
        Long bankId = bankCard.getBankId();
        CustomerBankCardPerson dbCustomerBankCardPerson = customerBankCardPersonService.getOne(Wrappers.<CustomerBankCardPerson>lambdaQuery()
                .eq(CustomerBankCardPerson::getGoodsId, goodsId)
                .eq(CustomerBankCardPerson::getUserId, userId)
                .eq(CustomerBankCardPerson::getStatus, CustomerBankCardPersonEnum.STATUS.THIS.getCode())
        );
        if (ObjectUtil.isNotEmpty(dbCustomerBankCardPerson) && CommonConstant.YES.equals(dbCustomerBankCardPerson.getIsBankAccount())) {
            throw new ServiceException("行方返回的银行账户不允许变更!");
        }
        // 判断是否有借款记录, 如果有则不允许变更
        IFinanceApplyService financeApplyService = SpringUtil.getBean(IFinanceApplyService.class);
        Boolean hasFinancing = financeApplyService.hasFinancing(dto.getGoodsId(), userId);
        if (hasFinancing) {
            throw new ServiceException("存在融资中数据,不允许变更");
        }
        customerBankCardPersonService.updateTisToOld(goodsId, userId);
        // 变更为历史数据
        CustomerBankCardPerson customerBankCardPerson = new CustomerBankCardPerson();
        customerBankCardPerson.setBankId(bankId);
        customerBankCardPerson.setBankDeposit(bankCard.getBankDeposit());
        customerBankCardPerson.setBankCardNo(bankCard.getBankCardNo());
        customerBankCardPerson.setUserId(userId);
        customerBankCardPerson.setGoodsId(goodsId);
        customerBankCardPerson.setGoodsType(goodsType);
        customerBankCardPerson.setIsBankAccount(CommonConstant.NO);
        customerBankCardPerson.setIsLock(CommonConstant.YES);
        customerBankCardPersonService.saveOrUpdate(customerBankCardPerson);
        CustomerGoods customerGoods = customerGoodsService.getByGoodsIdAndCustomerId(goodsId, userId);
        if (ObjectUtil.isEmpty(customerGoods)) {
            throw new ServiceException("该用户暂未开通该产品");
        }
        // 更新客户产品状态
        customerGoodsService.updateStatus(customerGoods.getId(), CustomerGoodsEnum.FINANCING.getCode());
        IEnterpriseQuotaService enterpriseQuotaService = SpringUtil.getBean(IEnterpriseQuotaService.class);
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
        if (ObjectUtil.isNotEmpty(bankId)) {
            Bank bank = bankService.getById(bankId);
            enterpriseQuota.setBankUnionCode(bank.getBankNo());
        } else {
            enterpriseQuota.setBankUnionCode(bankCard.getBankCode());
        }
        enterpriseQuota.setBank(bankCard.getBankDeposit());
        enterpriseQuota.setBankId(bankId);
        enterpriseQuota.setBankCardNo(bankCard.getBankCardNo());
        enterpriseQuota.setCity(bankCard.getBankArea());
        enterpriseQuota.setStatus(EnterpriseQuotaStatusEnum.VALID.getCode());
        enterpriseQuotaService.updateById(enterpriseQuota);
        String tenantId = AuthUtil.getTenantId();
        ThreadUtils.runAsync(() -> {
            TenantBroker.runAs(tenantId, e -> {
                Thread.sleep(5000);
                allGroupPlanCallPublisher.pushAllGroupPlanCall(userId, goodsId, tenantId);
            });
        });
        return true;
    }

    private List<BankCardVO> backCardAll(Long userId) {
        List<BankCard> list = this.lambdaQuery().eq(BankCard::getUserId, userId).list();
        return BankCardWrapper.build().listVO(list);
    }

}
