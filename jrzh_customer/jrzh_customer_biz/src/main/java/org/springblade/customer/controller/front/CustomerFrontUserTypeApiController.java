package org.springblade.customer.controller.front;


import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.UsualLog;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.RoleEnum;
import org.springblade.common.enums.VerificationCodeSupertube;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.sms.model.SmsCode;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.Customer;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.service.ICustomerFrontUserTypeService;
import org.springblade.customer.service.ICustomerService;
import org.springblade.customer.util.CustomerTokenUtil;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerCheckVo;
import org.springblade.customer.vo.CustomerFrontUserTypeVO;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.CustomerVO;
import org.springblade.customer.wrapper.CustomerWrapper;
import org.springblade.resource.builder.sms.SmsBuilder;
import org.springblade.system.entity.User;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springblade.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/front/customerFrontUserType")
@Api(value = "用户类型", tags = "用户类型接口")
@Slf4j
public class CustomerFrontUserTypeApiController extends BladeController {

	private final ICustomerFrontUserTypeService customerFrontUserTypeService;

	private final SmsBuilder smsBuilder;

	private final ICustomerService customerService;

	private final JwtProperties jwtProperties;

	private final BladeRedis bladeRedis;


	@UsualLog("注册用户验证")
	@PostMapping("/web/register")
	@ApiOperation(value = "注册用户", notes = "账号:accountUser,密码:password")
	public R<Kv> register(
		SmsCode smsCode,
		@ApiParam(value = "密码", required = true) @RequestParam(required = false) String password,
		@ApiParam(value = "确认密码", required = true) @RequestParam(required = false) String newPassword,
		@ApiParam(value = "账号", required = true) @RequestParam(required = false) String accountUser,
		String tenantId
	) {

		customerService.isTenanId(tenantId);

		Customer customer = new Customer();
		customer.setPassword(password);
		//手机号码
		String phone = smsCode.getPhone();
		customer.setPhone(phone);
		//0-待实名 1-已实名
		customer.setStatus(0);
		//融资企业新增
		customer.setType(0);
		//账号
		customer.setAccountUser(accountUser);
		//租户ID
		customer.setTenantId(tenantId);
		R r = TenantBroker.applyAs(tenantId,tenantId1 -> {
			customerFrontUserTypeService.checkCustomer(customer);
			if (password.equals(newPassword)) {
				boolean validate = smsBuilder.myTemplate(tenantId, "verification").validateMessage(smsCode);
				if (validate) {
					customerFrontUserTypeService.customerRegister(customer);
					CustomerVO customerVO = CustomerWrapper.build().entityVO(customer);
					CustomerFrontUserType one = customerFrontUserTypeService.lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, customerVO.getId()).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.PERSONAL.getCode()).one();
					customerVO.setTypeId(one.getId());
					return R.data(CustomerTokenUtil.createAuth(customerVO));
				} else {
					return R.fail("验证码错误!");
				}
			} else {
				return R.fail("密码与确认密码不匹配!");
			}
		});
		return r;
	}

	@UsualLog("查询当前账户子用户信息")
	@PostMapping("/selectCustomer")
	@ApiOperation(value = "查询当前账户子用户信息", notes = "customerId 登录id")
	public R<List<CustomerFrontUserTypeVO>> register() {
		Long customerId = MyAuthUtil.getId();
		String tenantId = MyAuthUtil.getTentId();
		customerService.isTenanId(tenantId);
		List<CustomerFrontUserTypeVO> customerFrontUserType = customerFrontUserTypeService.selectByCustomerId(customerId);

		return R.data(customerFrontUserType);
	}

	@UsualLog("选择用户")
	@PostMapping("/selecustomerSigninct")
	@ApiOperation(value = "选择用户", notes = "用户类型id")
	public R<Kv> customerSignin(Long typeId, CustomerCheckVo customerCheckVo) {
		if (ObjectUtil.isEmpty(MyAuthUtil.getCustomerId())) {
			if (ObjectUtil.isEmpty(customerCheckVo.getUuid())) {
				throw new ServiceException("长时间未操作,请重新登录!");
			}
			//String addr = IpUtil.getRealIP(request);
			String o = bladeRedis.get(CacheUtil.formatCacheName(customerCheckVo.getUuid(), true));
			if (ObjectUtil.isNotEmpty(o)) {
				if (!o.equals(customerCheckVo.getId() + customerCheckVo.getUuid())) {
					throw new ServiceException("非法操作,请重新登录!");
				}
				bladeRedis.del(CacheUtil.formatCacheName(customerCheckVo.getUuid(), true));
			} else {
				throw new ServiceException("非法操作,请重新登录!");
			}

		}
		String tenantId = customerService.getTenantId(customerCheckVo.getId());
		if (StrUtil.isNotBlank(tenantId)) {
			R r = TenantBroker.applyAs(tenantId, tenantId1 -> {
				CustomerVO customerVO = customerFrontUserTypeService.customerSignin(typeId, customerCheckVo.getId());
				return R.data(CustomerTokenUtil.createAuthCustomer(customerVO));
			});
			return r;
		}else {
			CustomerVO customerVO = customerFrontUserTypeService.customerSignin(typeId, customerCheckVo.getId());
			return R.data(CustomerTokenUtil.createAuthCustomer(customerVO));
		}

	}

	/**
	 * 获取当前登陆账号信息
	 * <AUTHOR>
	 * @date 2024/10/15 16:51
	 * @return org.springblade.core.tool.api.R<org.springblade.core.tool.support.Kv>
	 */
	@GetMapping("/userinfo")
	public R<Kv> getUserInfo(){
		Kv authInfo = Kv.create();
		CustomerVO customer = customerFrontUserTypeService.getCustomerInfo();

		String roleId = customer.getRoleId();
		String roleName = customer.getRoleAlias();
		if (customer.getType().equals(VerificationCodeSupertube.PERSONAL.getCode()) || customer.getType().equals(VerificationCodeSupertube.ENTERPRISE.getCode())) {
			roleName = RoleEnum.ADMIN.getName();
		}
		authInfo.set(TokenConstant.TENANT_ID, customer.getTenantId())
				.set("customerId", customer.getId())
				.set("userId", customer.getUserId())
				.set(TokenConstant.USER_ID, customer.getUserId())
				.set("subUserId", customer.getSubUserId())
				.set("id", customer.getId())
				.set("logoSrc", customer.getLogoSrc())
				.set("phone", customer.getPhone())
				.set("email", customer.getEmail())
				.set("name", customer.getName())
				.set("type", customer.getType())
				.set("companyId", customer.getCompanyId())
				.set("accountUser", customer.getAccountUser())
				.set("status", customer.getStatus())
				.set("corporateAvatar", customer.getCorporationName())
				.set("typeId", customer.getTypeId())
				.set("identityCard", customer.getIdentityCard())
				.set("companyName", customer.getCompanyName())
				.set("roleName", customer.getRoleName())
				.set("personalUserId", customer.getPersonalUserId())
				.set("roleId", roleId)
				.set("label", customer.getLabel())
				.set("toDemo", customer.getToDemo())
				.set(TokenConstant.USER_TYPE,customer.getLabel())
				.set(TokenConstant.TENANT_ID, customer.getTenantId())
				.set(TokenConstant.ACCOUNT, customer.getAccountUser())
				.set(TokenConstant.ROLE_ID, roleId)
				.set(TokenConstant.ROLE_NAME, roleName);

		return R.data(authInfo);
	}

	@UsualLog("企业用户移交权限")
	@PostMapping("/customerTransfer")
	@ApiOperation(value = "企业用户移交权限", notes = "用户类型id,customerId 用户ID,companyId企业ID")
	@PreAuth(" hasRole('admin')")
	public R<Boolean> customerTransfer(Long typeId, Long customerId) {
		boolean b = customerFrontUserTypeService.customerTransfer(typeId, customerId);
		return R.data(b);
	}

	/*@UsualLog("企业用户绑定权限")
	@PostMapping("/customerBinding")
	@ApiOperation(value = "企业用户绑定权限", notes = "customerId 用户ID")
	public  R<Boolean>  customerBinding(Long customerId,String name){
		boolean b = customerFrontUserTypeService.customerBinding(customerId,name);
		return R.data(b);
	}*/

	@UsualLog("查询当前企业的所有用户")
	@PostMapping("/currentSelectCustomer")
	@ApiOperation(value = "查询当前企业的所有用户", notes = "companyId 企业ID")
	@PreAuth("hasPermission('front:customerFrontUserType:currentSelectCustomer') or hasRole('admin') or hasRole('financing_admin')")
	public R<List<CustomerFrontUserTypeVO>> queryTypeCustomer() {
		String companyId = MyAuthUtil.getCompanyId();
		List<CustomerFrontUserTypeVO> b = customerFrontUserTypeService.queryTypeCustomer(Long.valueOf(companyId));
		return R.data(b);
	}

	@UsualLog("企业清除用户")
	@PostMapping("/enterpriseDeleteCustomer")
	@ApiOperation(value = "企业清除用户", notes = "companyId 企业ID")
	@PreAuth("hasPermission('front:customerFrontUserType:enterpriseDeleteCustomer') or hasRole('admin') or hasRole('financing_admin')")
	public R<Boolean> enterpriseDeleteCustomer(@ApiParam(value = "主键集合", required = true) @RequestParam String typeIds) {
		boolean b = customerFrontUserTypeService.enterpriseDeleteCustomer(Func.toLongList(typeIds));
		return R.data(b);
	}

	@UsualLog("个人绑定企业")
	@PostMapping("/enterpriseInvitesCustomer")
	@ApiOperation(value = "个人绑定企业", notes = "code 邀请编码 frontName 邀请人公司名称")
	public R<Boolean> enterpriseInvitesCustomer(String code, String frontName) {
		boolean b = customerFrontUserTypeService.enterpriseInvitesCustomer(code, frontName);
		return R.data(b);
	}

	@UsualLog("查询所有用户(除了本企业的)")
	@PostMapping("/selectCustomerAll")
	@ApiOperation(value = "name", notes = "name个人姓名")
	public R<List<Customer>> selectCustomerAll(String name) {
		List<Customer> customers = customerFrontUserTypeService.selectCustomerAll(name);
		return R.data(customers);
	}


	@UsualLog("查询融资平台用户角色信息")
	@GetMapping("/usersRole")
	@ApiOperation(value = "查询平台用户角色信息", notes = "查询平台用户角色信息")
	public R<String> users() {
		String companyId = MyAuthUtil.getCompanyId();
		Long customerId = MyAuthUtil.getCustomerId();
		CustomerFrontUserType one = customerFrontUserTypeService.lambdaQuery().eq(CustomerFrontUserType::getUserId, companyId).eq(CustomerFrontUserType::getCustomerFrontUserId, customerId).one();
		if (StringUtil.isEmpty(one)) {
			throw new ServiceException("未找到对应的数据!");
		}
		String user = CustomerUserCache.getUserById(one.getRoleUserId()).getRoleId();
		return R.data(user);
	}

	@SneakyThrows
	@UsualLog("更换企业logo")
	@PostMapping("/updateCompanyAvatar")
	@PreAuth("hasPermission('front:customerFrontUserType:updateCompanyAvatar') or hasRole('admin') or hasRole('financing_admin')")
	public R<Boolean> updateCompanyAvatar(String corporateAvatar, Long typeId) {
		Boolean customerFrontUserTypeVO = customerFrontUserTypeService.updateCompanyAvatar(corporateAvatar, typeId);
		return R.data(customerFrontUserTypeVO);
	}

	@SneakyThrows
	@UsualLog("查询实名企业详情信息")
	@PostMapping("/getCompanyDetailsBy")
	public R<CustomerInfoVO> getCompanyDetailsBy() {
		CustomerInfoVO customerInfoVO = customerFrontUserTypeService.getCompanyDetailsByCustomerId();
		return R.data(customerInfoVO);
	}

	@UsualLog("查询当前账户企业信息")
	@PostMapping("/selectEnterprise")
	@ApiOperation(value = "查询当前账户企业信息", notes = "customerId登录id")
	public R<List<CustomerFrontUserTypeVO>> selectEnterprise() {
		Long customerId = MyAuthUtil.getId();
		String tenantId = MyAuthUtil.getTentId();
		customerService.isTenanId(tenantId);
		List<CustomerFrontUserTypeVO> customerFrontUserType = customerFrontUserTypeService.selectEnterprise(customerId);
		return R.data(customerFrontUserType);
	}

	@UsualLog("修改角色信息")
	@PostMapping("/updateRole")
	@ApiOperation(value = "修改角色信息", notes = "修改角色信息")
	@PreAuth("hasPermission('front:customerFrontUserType:updateRole') or hasRole('admin') or hasRole('financing_admin')")
	public R<Boolean> updateRole(String roleIds, String typeId) {
		CacheUtil.clear(SYS_CACHE);
		CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
		return R.data(customerFrontUserTypeService.updateRole(roleIds, typeId));
	}

	@UsualLog("查询角色id")
	@GetMapping("/selecRole")
	@ApiOperation(value = "查询角色id", notes = "查询角色id")
	public R<String> selecRole(String typeId) {
		CustomerFrontUserType byId = customerFrontUserTypeService.getById(typeId);
		Long userId = byId.getRoleUserId();
		User byId1 = CustomerUserCache.getUserById(userId);
		return R.data(byId1.getRoleId());
	}

	@PostMapping("/verifyCoreEnterprise")
	@ApiOperation("核心企业实名")
	//@PreAuth("hasPermission('tradeBackground:bindLowerTradeBackground') or hasRole('admin') or hasRole('admin') or hasRole('financing_admin') ")
	public R bindTradeBackground() {
		//参数检查及参数赋值
		//R check = checkLowerBackGroundParams(tradeBackground);
		boolean check = customerFrontUserTypeService.verifyCoreEnterprise();
		return R.data(true);
	}
}
