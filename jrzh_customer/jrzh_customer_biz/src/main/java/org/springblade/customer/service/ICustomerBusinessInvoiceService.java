/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.customer.dto.CustomerBusinessInvoiceDTO;
import org.springblade.customer.entity.CustomerBusinessInvoice;
import org.springblade.customer.vo.CustomerBusinessInvoiceVO;

import java.util.List;

/**
 * 业务发票 服务类
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface ICustomerBusinessInvoiceService extends BaseService<CustomerBusinessInvoice> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customerBusinessInvoice
	 * @return
	 */
	IPage<CustomerBusinessInvoiceVO> selectCustomerBusinessInvoicePage(IPage<CustomerBusinessInvoiceVO> page, CustomerBusinessInvoiceVO customerBusinessInvoice);

	/**
	 * 上传发票
	 */
	CustomerBusinessInvoice uploadInvoice(String picUrl);

	/**
	 * 发票信息验真
	 *
	 * @param customerBusinessInvoiceDTO
	 * @return
	 */
	boolean invoiceVertification(CustomerBusinessInvoiceDTO customerBusinessInvoiceDTO);

	/**
	 * 根据发票号码查找发票信息
	 *
	 * @param invoiceNumber
	 * @return
	 */
	CustomerBusinessInvoice selectByInvoiceNumberAndCode(String invoiceNumber, String code);

	/**
	 * @param code   发票代码
	 * @param number 发票号码
	 */
	CustomerBusinessInvoice getByCodeAndNumber(String code, String number);

	/**
	 * 是否发票未使用
	 *
	 * @param ids
	 * @return
	 */
	boolean uploadBefore(List<Long> ids);

	/**
	 * 改变发票使用状态
	 *
	 * @param ids
	 * @param status 0：未使用 1：使用
	 * @return
	 */
	boolean changeUploadStatus(List<Long> ids, Integer status);

	/**
	 * 发票识别
	 *
	 * @param picUrl
	 * @return
	 */
	CustomerBusinessInvoice requestInvoiceOcr(String picUrl);

	/**
	 * 发票识别
	 *
	 * @param picUrl
	 * @param userId 发票所属人
	 * @return
	 */
	CustomerBusinessInvoice requestInvoiceOcr(String picUrl, String userId);

	/**
	 * 查询未上传的发票
	 *
	 * @param companyId 公司id
	 */
	List<CustomerBusinessInvoice> listUnUploadByCompanyId(String companyId);

	/**
	 * 查询未上传的发票
	 *
	 * @param companyId 公司id
	 * @param sellerId  销售方社会统一代码
	 * @return
	 */
	List<CustomerBusinessInvoice> listUnUploadByCompanyIdAndSellerId(String companyId, String sellerId);

	/**
	 * 假删除 省一次发票识别
	 *
	 * @param id
	 * @return
	 */
	Boolean deleteNoRealById(Long id);

	/**
	 * 处理发票金额
	 *
	 * @param customerBusinessInvoice
	 */
	void calInvoiceMoney(CustomerBusinessInvoice customerBusinessInvoice);
}
