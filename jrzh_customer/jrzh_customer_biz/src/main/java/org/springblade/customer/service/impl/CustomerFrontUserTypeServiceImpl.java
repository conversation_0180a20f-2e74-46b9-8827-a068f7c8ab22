/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.auth.utils.TokenUtil;
import org.springblade.common.enums.*;
import org.springblade.common.utils.ChineseUtil;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.*;
import org.springblade.customer.businessinfo.entity.CustomerBusinessInfo;
import org.springblade.customer.constant.AuthStatus;
import org.springblade.customer.entity.*;
import org.springblade.customer.mapper.CustomerFrontUserTypeMapper;
import org.springblade.customer.mapper.CustomerMapper;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerUserCache;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.*;
import org.springblade.customer.wrapper.CustomerFrontUserTypeWrapper;
import org.springblade.customer.wrapper.CustomerInfoWrapper;
import org.springblade.customer.wrapper.CustomerWrapper;
import org.springblade.resource.entity.Param;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserSearchService;
import org.springblade.system.feign.RemoteUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 融资企业用户类型 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Service
@AllArgsConstructor
public class CustomerFrontUserTypeServiceImpl extends BaseServiceImpl<CustomerFrontUserTypeMapper, CustomerFrontUserType> implements ICustomerFrontUserTypeService {

    private final static Pattern EMAIL = Pattern.compile("^\\s*\\w+(?:\\.?[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$");

    private final static Pattern PAR = Pattern.compile("^[1][3,4,5,7,8,9][0-9]{9}$");

    private final ICustomerVerificationCodeService customerVerificationCodeService;

    private final JwtProperties jwtProperties;

    private final CustomerMapper customerMapper;

    private final RemoteDeptSearchService remoteDeptSearchService;

    private final RemoteUserService remoteUserService;

    private final RemoteUserSearchService remoteUserSearchService;

    private final ICustomerInfoService customerInfoService;
    private final IProcessCoreEnterService enterService;
    private final IParamService paramService;


    @Override
    public IPage<CustomerFrontUserTypeVO> selectCustomerFrontUserTypePage(IPage<CustomerFrontUserTypeVO> page, CustomerFrontUserTypeVO customerFrontUserType) {
        return page.setRecords(baseMapper.selectCustomerFrontUserTypePage(page, customerFrontUserType));
    }

    @Override
    public List<CustomerFrontUserTypeVO> selectByCustomerId(Long customerId) {
        List<CustomerFrontUserType> list = this.lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, customerId).list();

        return toCustomerFrontUserName(list, true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void customerRegister(Customer customer) {
        if (!isPhone(customer.getPhone())) {
            throw new ServiceException("手机号输入不正确,请检查!");
        }
        checkCustomer(customer);
        String password = customer.getPassword();

        customer.setPassword(SecureUtil.md5(password));

        if (customer.getType().equals(CustomerEnum.ZERO_TYPE.getCode())) {
            //进行同步
            User user = new User();
            //租户ID
            String tenantId = customer.getTenantId();
            user.setTenantId(tenantId);
            //账号
            user.setAccount(getByAccount(UUID.randomUUID().toString().substring(0, 5)));
            //密码
            user.setPassword(UUID.randomUUID().toString());
            //个人名称
            //user.setName(customer.getName());
            //真名
            //user.setRealName("");
            //设置类型
            user.setUserType(UserTypeEnum.FINANCING.getCode());
            Dept dept = getDeptByFinancingName(tenantId);
            if (Func.isEmpty(dept)) {
                throw new ServiceException("融资企业部门不存在，请联系管理员");
            }
            user.setDeptId(String.valueOf(dept.getId()));
            //TODO 计划进行调整
            user = remoteUserService.userSubmit(user, FeignConstants.FROM_IN).getData();
            if (ObjectUtil.isEmpty(user)) {
                throw new ServiceException("未知错误!");
            }

            FrontFinancingList frontFinancingList = new FrontFinancingList();
            frontFinancingList.setTenantId(tenantId);
            frontFinancingList.setStatus(0);
            //编码
            frontFinancingList.setCustomerCode(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
            //设置 企业ID
            frontFinancingList.setCompanyId(user.getId());
            SpringUtil.getBean(IFrontFinancingListService.class).save(frontFinancingList);
            //设置 企业ID
            customer.setCompanyId(user.getId());
            //userID
            customer.setUserId(user.getId());
            //设置默认头像
            String logoSrc = getSysLog(tenantId);
            customer.setLogoSrc(logoSrc);
            long id = IdWorker.getId();
            customer.setId(id);
            customerType(customer, user);
            SpringUtil.getBean(ICustomerService.class).save(customer);
        } else {
            SpringUtil.getBean(ICustomerService.class).save(customer);
        }
    }

    /***
     * 	创建 子用户中间表,Type 类型  企业邀请用户时 类型为1 个人  用户实名企业时类型为2  企业
     */
    private void customerType(Customer customer, User user) {
        CustomerFrontUserType customerFrontUserType = new CustomerFrontUserType();
        customerFrontUserType.setType(CustomerTypeEnum.PERSONAL.getCode());
        //注册ID
        customerFrontUserType.setCustomerFrontUserId(customer.getId());
        String tenantId = customer.getTenantId();
        customerFrontUserType.setTenantId(tenantId);
        Long id = user.getId();
        //中间表设置企业ID  个人
        customerFrontUserType.setUserId(id);
        //设置用户Id  在企业用户中   用户ID 与企业ID 不同
        customerFrontUserType.setRoleUserId(id);
        save(customerFrontUserType);
        saveEnterpriseadopt(CustomerEnum.LABEL_PERSONAL.getCode(), null, id, 1);
    }

    private boolean isPhone(String phone) {
        Matcher matcher = PAR.matcher(phone);
        return matcher.matches();
    }

    @Override
    public void checkCustomer(Customer customer) {
        if (!StringUtil.isEmpty(customer.getEmail())) {
            isnotNullEmail(customer.getEmail());
        }

        LambdaQueryWrapper<Customer> and = null;
        LambdaQueryWrapper<Customer> and1 = null;
        if (customer.getType() != 1) {
            //融资企业
            and = Wrappers.<Customer>lambdaQuery()
                    .eq(Customer::getPhone, customer.getPhone())
                    .eq(Customer::getTenantId, customer.getTenantId())
                    .and(wrapper -> wrapper.eq(Customer::getType, 0).or().eq(Customer::getType, 2));
            and1 = Wrappers.<Customer>lambdaQuery()
                    .eq(Customer::getAccountUser, customer.getAccountUser())
                    .eq(Customer::getTenantId, customer.getTenantId())
                    .and(wrapper -> wrapper.eq(Customer::getType, 0).or().eq(Customer::getType, 2));
        }
        Customer customer1 = customerMapper.selectOne(and);
        Customer customer2 = customerMapper.selectOne(and1);
        if (StringUtil.isEmpty(customer.getId())) {
            if (ObjectUtil.isNotEmpty(customer1)) {
                throw new ServiceException("手机号已被注册,请去登录!");
            }
            if (ObjectUtil.isNotEmpty(customer2)) {
                throw new ServiceException("账号已被注册,请重新输入!");
            }
        }
        isChineseCustomer(customer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void isnotNullEmail(String email) {
        if (ObjectUtil.isEmpty(email)) {
            throw new ServiceException("邮箱未输入!");
        }
        Matcher matcher = EMAIL.matcher(email);
        if (!matcher.matches()) {
            throw new ServiceException("邮箱格式不符合!");
        }
    }

    @Override
    public boolean customerUnbound(Long id) {
        CustomerFrontUserType byId = getById(id);
        if (StringUtil.isEmpty(byId)) {
            return false;
        }
        byId.setCustomerFrontUserId(null);
        return updateById(byId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean customerBinding(Long customerId, String name, Long userId, Integer entAuthType) {
        // 企业实名认证的时候需要 在User表中填加一条数据  准备添加角色 与用户
        CustomerFrontUserType customerFrontUserType = new CustomerFrontUserType();
        //用户绑定企业
        customerFrontUserType.setTenantId(AuthUtil.getTenantId());
        customerFrontUserType.setType(CustomerTypeEnum.ENTERPRISE.getCode());
        customerFrontUserType.setCustomerFrontUserId(customerId);
        //企业 实名认证 填写name  实名认证填写 企业名称
        User user = user(name, "", userId, entAuthType);
        Long companyId1 = user.getId();
        //设置UserId 从user表获取 主要是获取角色信息,方便控制权限
        customerFrontUserType.setRoleUserId(companyId1);
        //企业ID  一个账号不能有多个相同的企业信息
        customerFrontUserType.setUserId(companyId1);
        //新增企业信息
        boolean save = save(customerFrontUserType);
        //创建客户经理数据
        FrontFinancingList financingList = new FrontFinancingList();
        financingList.setCompanyId(companyId1);
        financingList.setStatus(0);
        //编码
        financingList.setCustomerCode(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
        SpringUtil.getBean(IFrontFinancingListService.class).save(financingList);
        //赋予管理员权限
        give(user);
        return save;
    }

    @Override
    public List<CustomerFrontUserTypeVO> queryTypeCustomer(Long companyId) {
        List<CustomerFrontUserType> customerFrontUserTypes = baseMapper.selectList(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getUserId, companyId).orderByDesc(CustomerFrontUserType::getType));
        return toCustomerFrontUserName(customerFrontUserTypes, false);
    }

    @Override
    public IPage<CustomerFrontUserTypeBackVO> customerFrontUserList(CustomerFrontUserType customerFrontUserTypeDTO, Query query, Integer isCoreOrFront) {
        IPage<CustomerFrontUserType> page = Condition.getPage(query);
        IPage<CustomerFrontUserTypeBackVO> page5 = Condition.getPage(query);

        boolean equals = isCoreOrFront.equals(CustomerEnum.LABEL_FINANCING.getCode());
        LambdaQueryChainWrapper<EnterpriseAdopt> enterpriseAdoptLambdaQueryChainWrapper = SpringUtil.getBean(IEnterpriseAdoptService.class).lambdaQuery();
        ICustomerInfoService customerInfoService = SpringUtil.getBean(ICustomerInfoService.class);
        //1成功 2失敗 3开通中
        List<Integer> status = Func.toIntList("1,2,3");
        if (equals) {
            enterpriseAdoptLambdaQueryChainWrapper.eq(EnterpriseAdopt::getLabel, CustomerEnum.LABEL_FINANCING.getCode())
                    .in(EnterpriseAdopt::getProcessStatus, status);
        } else {

            enterpriseAdoptLambdaQueryChainWrapper.eq(EnterpriseAdopt::getLabel, CustomerEnum.LABEL_CORE.getCode())
                    .in(EnterpriseAdopt::getProcessStatus, status);
        }
        //查询所有融资或核心企业
        List<EnterpriseAdopt> list1 = enterpriseAdoptLambdaQueryChainWrapper.list();
        List<Long> collect6 = null;
        if (ObjectUtil.isNotEmpty(list1)) {
            collect6 = list1.stream().map(EnterpriseAdopt::getUserId).collect(Collectors.toList());
        } else {
            return page5;
        }


        IPage<CustomerFrontUserType> page1 = this.page(page, Condition.getQueryWrapper(customerFrontUserTypeDTO)
                .lambda().eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode())
                .in(CustomerFrontUserType::getUserId, collect6)
                .orderByDesc(BaseEntity::getCreateTime));

        List<CustomerFrontUserType> list = page1.getRecords();
        List<CustomerFrontUserTypeVO> customerFrontUserTypeVos = CustomerFrontUserTypeWrapper.build().listVO(list);


        List<Long> collect = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getCustomerFrontUserId).distinct().collect(Collectors.toList());
        //查询已开通的企业实名信息
        if (ObjectUtil.isEmpty(collect)) {
            return page5;
        }
        //查询企业的法人等信息
        Map<Long, CustomerInfo> collect1 = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().in(CustomerInfo::getCustomerId, collect).list().stream().collect(Collectors.toMap(CustomerInfo::getCompanyId, e -> e));
        //查询父企业信息
        List<Long> collect3 = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getRoleUserId).collect(Collectors.toList());


        Map<Long, User> collect4 = remoteUserService.getMapInId(collect3).getData();

        //客户经理
        //Map<Long, User> userMap = SpringUtil.getBean(IUserService.class).lambdaQuery().eq(User::getUserType, 1).list().stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));

        List<FrontFinancingList> frontFinancingLists = SpringUtil.getBean(IFrontFinancingListService.class).lambdaQuery().in(FrontFinancingList::getCompanyId, collect3).list();
        Map<Long, FrontFinancingList> frontFinancingListMap = null;
        if (ObjectUtil.isNotEmpty(frontFinancingLists)) {
            frontFinancingListMap = frontFinancingLists.stream().collect(Collectors.toMap(FrontFinancingList::getCompanyId, e -> e));
        }


        //查询所有所属者
        List<Long> collect2 = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getCustomerFrontUserId).collect(Collectors.toList());
        Map<Long, Customer> customerMap = SpringUtil.getBean(ICustomerService.class).lambdaQuery().in(BaseEntity::getId, collect2).list().stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

        Map<Long, FrontFinancingList> finalFrontFinancingListMap = frontFinancingListMap;
        List<CustomerFrontUserTypeBackVO> collect5 = customerFrontUserTypeVos.stream().map(customerFrontUserTypeVo -> {
            CustomerFrontUserTypeBackVO customerFrontUserTypeBackVO = BeanUtil.copy(customerFrontUserTypeVo, CustomerFrontUserTypeBackVO.class);
            CustomerInfo customerInfo = collect1.get(customerFrontUserTypeVo.getUserId());
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                customerFrontUserTypeBackVO.setCreditCode(customerInfo.getBusinessLicenceNumber());
                customerFrontUserTypeBackVO.setLegalPersonName(customerInfo.getCorporationName());
            }
            //后续进行拼接 头像
            User user = collect4.get(customerFrontUserTypeBackVO.getRoleUserId());
            if (ObjectUtil.isNotEmpty(user)) {
                customerFrontUserTypeBackVO.setCorporateAvatar(user.getAvatar());
                customerFrontUserTypeBackVO.setName(user.getName());
            }
            Customer customer = customerMap.get(customerFrontUserTypeBackVO.getCustomerFrontUserId());
            if (Objects.nonNull(customer)) {
                customerFrontUserTypeBackVO.setPersonalName(customer.getName());
            }
            if (ObjectUtil.isNotEmpty(finalFrontFinancingListMap)) {
                String invitationCode = finalFrontFinancingListMap.get(customerFrontUserTypeVo.getUserId()).getInvitationCode();
                if (ObjectUtil.isNotEmpty(invitationCode)) {
                    Long aLong = Long.valueOf(invitationCode);
                    User user1 = CustomerUserCache.getUserById(aLong);
                    customerFrontUserTypeBackVO.setInvitationName(user1.getRealName());
                }
            }
            //后续增加客户经理名称
            return customerFrontUserTypeBackVO;
        }).collect(Collectors.toList());
        page5.setTotal(collect6.size());
        page5.setRecords(collect5);
        //设置公司状态
        if (!equals) {
            setEntStatus(page5.getRecords());
        }
        return page5;
    }

    /**
     * 设置公司状态
     *
     * @param voList
     */
    private void setEntStatus(List<CustomerFrontUserTypeBackVO> voList) {
        List<Long> companyIds = StreamUtil.map(voList, CustomerFrontUserType::getUserId);
        HashMap<Long, CustomerFrontUserTypeBackVO> voListMap = voList.stream().collect(HashMap::new, (m, v) -> m.put(v.getUserId(), v), HashMap::putAll);
        // 获取业务（除已终止）
        HashMap<Long, ProcessCoreEnter> processMap = enterService.listByCompanyIds(companyIds).stream().collect(HashMap::new, (m, v) -> m.put(v.getCompanyId(), v), HashMap::putAll);
        List<CustomerInfo> customerInfoList = customerInfoService.getByCompanyIds(companyIds);
        // 获取以终止的业务
        HashMap<Long, ProcessCoreEnter> processMapTerminal = enterService.listByCompanyIdsTerminal(companyIds).stream().collect(HashMap::new, (m, v) -> m.put(v.getCompanyId(), v), HashMap::putAll);
        for (CustomerInfo e : customerInfoList) {
            Long companyId = e.getCompanyId();
            CustomerFrontUserTypeBackVO vo = voListMap.get(companyId);

            if (!AuthStatus.CompanyAuthStatus.AUTH_PASS.getStatus().equals(e.getAuthStatus())) {
                vo.setCoryStatus(0);
                continue;
            }
            ProcessCoreEnter processCoreEnter = processMap.get(companyId);
            if (ObjectUtil.isNotEmpty(processCoreEnter)) {
                Integer status = processCoreEnter.getStatus();
                if (ProcessStatusEnum.APPROVING.getCode() == status) {
                    vo.setCoryStatus(2);
                    continue;
                }
                if (ProcessStatusEnum.FINISH.getCode() == status) {
                    vo.setCoryStatus(3);
                    continue;
                }
            }
            if(ProcessStatusEnum.TERMINAL.getCode() == processMapTerminal.get(companyId).getStatus()){
                vo.setCoryStatus(4);
                continue;
            }
            vo.setCoryStatus(1);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enterpriseDeleteCustomer(List<Long> toLongList) {

        List<CustomerFrontUserType> customerFrontUserTypes = baseMapper.selectBatchIds(toLongList);
        List<Long> collect2 = customerFrontUserTypes.stream().map(CustomerFrontUserType::getCustomerFrontUserId).collect(Collectors.toList());
        List<Integer> collect1 = customerFrontUserTypes.stream().map(CustomerFrontUserType::getType).collect(Collectors.toList());
        if (collect1.contains(VerificationCodeSupertube.ENTERPRISE.getCode())) {
            throw new ServiceException("不能删除超级管理员!");
        }
        Long typeId = MyAuthUtil.getTypeId();
        if (toLongList.contains(typeId)) {
            throw new ServiceException("不能刪除自己账户!");
        }
        List<Long> collect = customerFrontUserTypes.stream().map(CustomerFrontUserType::getRoleUserId).collect(Collectors.toList());
        //先删除用户信息
        //TODO 需要重新考虑
        // boolean b1 = SpringUtil.getBean(IUserService.class).deleteLogic(collect);
        //只是删除了企业下的绑定的用户
        boolean b = deleteLogic(toLongList);
        boolean remove = SpringUtil.getBean(ICustomerVerificationCodeService.class).remove(Wrappers.<CustomerVerificationCode>lambdaQuery().in(CustomerVerificationCode::getCoverCodeId, collect2).eq(CustomerVerificationCode::getCompanyId, MyAuthUtil.getCompanyId()));
        // return b && b1;
        return b;
    }

    /***
     * 企业邀请用户进行绑定,当前用户填写编码进行校验
     * @param  code 通过获取编码 获得角色等信息，编码只能使用一次 frontName邀请企业名称
     */
    @Override
    public boolean enterpriseInvitesCustomer(String code, String frontName) {
        boolean b = SpringUtil.getBean(ICustomerVerificationCodeService.class).checkInviteCodeIsValid(code, frontName);
        if (!b) {
            throw new ServiceException("邀请码已过期或不存在!");
        }
        verifyCode(code);
        //删除邀请码
        return SpringUtil.getBean(ICustomerVerificationCodeService.class).removeInviteCode(code, frontName);
    }

    @Override
    public boolean enterpriseInvitCode(String emil) {
        return false;
    }

    @Override
    public List<Customer> selectCustomerAll(String name) {
        //查询所有用户
        List<Customer> customers = customerMapper.selectList(Wrappers.<Customer>lambdaQuery().like(Customer::getName, name));
        //通过用户ID 去分组
        Map<Long, List<CustomerFrontUserType>> map = list().stream().collect(Collectors.groupingBy(CustomerFrontUserType::getCustomerFrontUserId));

        //企业ID
        String companyId = MyAuthUtil.getCompanyId();
        //组装数据
        return customers.stream().filter(customer -> {
            Long id = customer.getId();
            List<CustomerFrontUserType> customerFrontUserTypes = map.get(id);
            if (ObjectUtil.isNotEmpty(customerFrontUserTypes)) {
                //获取一个用户的所有企业ID
                List<Long> collect1 = customerFrontUserTypes.stream().map(CustomerFrontUserType::getUserId).collect(Collectors.toList());
                //如果这个用户存在在当前企业里面则返回false
                boolean contains = collect1.contains(Long.valueOf(companyId));
                return !contains;
            }
            return true;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean customerTransfer(Long typeId, Long customerId) {
        String companyId = MyAuthUtil.getCompanyId();
        ICustomerService bean = SpringUtil.getBean(ICustomerService.class);
        //判断转移的那个人是否已成功实名企业信息
        Customer byId2 = bean.getById(customerId);


        CustomerFrontUserType one = getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getCustomerFrontUserId, customerId)
                .eq(CustomerFrontUserType::getUserId, companyId));


        //获取当前企业的超级管理员账号
        CustomerFrontUserType byId = getById(typeId);
        if (byId.getCustomerFrontUserId().equals(byId2.getId())) {
            throw new ServiceException("不能转移给自己!");
        }
        if (!byId.getType().equals(VerificationCodeSupertube.ENTERPRISE.getCode())) {
            throw new ServiceException("您不是超级管理员不能转移账号!");
        }
        //给予前超级管理员一个 管理员角色或其它角色信息
        one.setCustomerFrontUserId(byId.getCustomerFrontUserId());
        Long userId = one.getRoleUserId();
        //TODO 接口需要重新考虑
        User byId1 = CustomerUserCache.getUserById(userId);
        //设置前超级管理员账号信息
        FinancingRole financingAdmin = SpringUtil.getBean(IFinancingRoleService.class).getOne(Wrappers.<FinancingRole>lambdaQuery().eq(FinancingRole::getCompanyId, MyAuthUtil.getCompanyId()).eq(FinancingRole::getRoleAlias, "financing_admin"));
        if (ObjectUtil.isEmpty(financingAdmin)) {
            financingAdmin = new FinancingRole();
        }
        //TODO 接口需要重新考虑
        byId1.setRoleId(String.valueOf(financingAdmin.getId()));
        remoteUserService.updateById(byId1);
        // SpringUtil.getBean(IUserService.class).updateById(byId1);
        //首先获取 设置为已绑定超级管理员
        Long customerId1 = byId.getCustomerFrontUserId();

        //绑定需要绑定的人,从而实现转移超级管理员
        byId.setCustomerFrontUserId(customerId);
        //将需要转移的设置为企业用户
        //byId2.setCompanyStatus(2);
        bean.updateById(byId2);

        //前超级管理员信息
        Customer customer3 = bean.getById(customerId1);
        //设置为当前企业实名认证状态
        //customer3.setCompanyStatus(1);
        bean.updateById(customer3);

        // customerId1 原管理员   customerId现管理员
        updateCustomerInfo(customerId1, customerId, Long.valueOf(companyId));
        return updateById(byId) && updateById(one);
    }

    @Override
    public List<Role> selectFinancingRoleAll() {
        return null;
    }

    /****
     *查询所有融资角色信息
     */
    //TODO 接口需要重新考虑
//    @Override
//    public List<Role> selectFinancingRoleAll() {
//        IRoleService bean = SpringUtil.getBean(IRoleService.class);
//        Role financingUsers = bean.lambdaQuery().eq(Role::getRoleAlias, "financing_users").one();
//        if (StringUtils.isEmpty(financingUsers)) {
//            return null;
//        }
//        return bean.lambdaQuery().eq(Role::getParentId, financingUsers.getId()).list();
//    }

    /***
     *  根据角色id  进行获取角色的名称信息，从而控制权限等信息
     */
    @Override
    public String selectRoleName(String roleId) {
        List<FinancingRole> list = selectRole(roleId);
        if (ObjectUtil.isEmpty(list)) {
            return "";
        }
        //获取当前用户的角色名称
        return StringUtil.join(list.stream().map(FinancingRole::getRoleName).toArray(), ",");
    }

    public String selectRoleAlias(String roleId) {
        List<FinancingRole> list = selectRole(roleId);
        if (ObjectUtil.isEmpty(list)) {
            return "";
        }
        //获取当前用户的角色名称
        return StringUtil.join(list.stream().map(FinancingRole::getRoleAlias).toArray(), ",");
    }

    public List<FinancingRole> selectRole(String roleId) {
        if (StringUtil.isEmpty(roleId)) {
            return new ArrayList<>();
        }
        String[] split = roleId.split(",");
        List<FinancingRole> list = SpringUtil.getBean(IFinancingRoleService.class).lambdaQuery().in(FinancingRole::getId,
                (Object[]) split).list();
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }


    /***
     * 根据typeId 获取CustomerFrontUserType 数据
     * @param roleIds 角色ids
     * @param typeId  选择的企业ID
     */
    @Override
    public Boolean changeRole(String roleIds, String typeId) {
        //TODO 接口需要重新考虑
        CustomerFrontUserType byId = getById(typeId);
        Long userId = byId.getRoleUserId();

        User byId1 = CustomerUserCache.getUserById(userId);
        byId1.setRoleId(roleIds);
        return remoteUserService.updateById(byId1).getData();

    }

    /***
     * 默认创建一个超级管理员信息
     */
    @Override
    public FinancingRole initialization(Long companyId) {

        FinancingRole financingRole = new FinancingRole();
        financingRole.setRoleName("超级管理员");
        financingRole.setRoleAlias(RoleEnum.ADMIN.getName());
        financingRole.setParentId(0L);
        financingRole.setCompanyId(companyId);
        financingRole.setIsDeleted(0);
        financingRole.setSort(1);
        financingRole.setCreateTime(new Date());
        IFinancingRoleService bean = SpringUtil.getBean(IFinancingRoleService.class);
        bean.save(financingRole);

        FinancingRole financingRole2 = new FinancingRole();
        financingRole2.setRoleName("管理员");
        financingRole2.setRoleAlias(RoleEnum.FINANCING_ADMIN.getName());
        financingRole2.setParentId(0L);
        financingRole2.setIsDeleted(0);
        financingRole2.setCompanyId(companyId);
        financingRole2.setSort(2);
        financingRole2.setCreateTime(new Date());
        bean.save(financingRole2);
        return financingRole;
    }


    /***
     * 赋予管理员权限
     */
    private void give(User user) {
        FinancingRole role = initialization(user.getId());
        Long id = role.getId();

        // List<Long> longs = Func.toLongList(id.toString());
        // IFinancingRoleService bean = SpringUtil.getBean(IFinancingRoleService.class);
        /*IMenuService bean1 = SpringUtil.getBean(IMenuService.class);
        List<TreeNode> treeNodes = bean1.granFinancing();
        if (ObjectUtil.isEmpty(treeNodes)) {
            treeNodes = new ArrayList<>();
        }*/
        //查询所有的菜单接口
        // List<Long> collect = treeNodes.stream().map(BaseNode::getId).collect(Collectors.toList());
        user.setRoleId(String.valueOf(id));

        //TODO 接口需要重新考虑
        remoteUserService.updateById(user);
        //SpringUtil.getBean(IUserService.class).updateById(user);
        //赋予权限信息
        //bean.grant(longs, collect, 1);

    }


    /**
     * 是否填写中文
     */
    private void isChineseCustomer(Customer customer) {
        boolean isPassword = ChineseUtil.isChinese(customer.getPassword());
        boolean isAccount = ChineseUtil.isChinese(customer.getAccountUser());
        if (isPassword || isAccount) {
            throw new ServiceException("不能输入中文!");
        }
    }

    private List<CustomerFrontUserTypeVO> toCustomerFrontUserName(List<CustomerFrontUserType> list, Boolean isUpdateLogoSrc) {
        List<CustomerFrontUserTypeVO> customerFrontUserType = CustomerFrontUserTypeWrapper.build().listVO(list);
        //查询父企业userId
        List<Long> collect1 = customerFrontUserType.stream().distinct().map(CustomerFrontUserType::getUserId).collect(Collectors.toList());
        //查询企业类型
        List<Long> finalCollect1 = collect1;
        List<EnterpriseAdopt> enterpriseAdopts = SpringUtil.getBean(IEnterpriseAdoptService.class).lambdaQuery()
                .in(EnterpriseAdopt::getUserId, collect1).eq(EnterpriseAdopt::getProcessStatus, 1)
                .or(Wrappers -> Wrappers.eq(EnterpriseAdopt::getLabel, CustomerEnum.LABEL_PERSONAL.getCode()).eq(EnterpriseAdopt::getProcessStatus, 3)
                        .in(EnterpriseAdopt::getUserId, finalCollect1)).list();
        //获取开通成功数据
        collect1 = enterpriseAdopts.stream().map(EnterpriseAdopt::getUserId).collect(Collectors.toList());
        Map<Long, Integer> longIntegerMap = StreamUtil.toMap(enterpriseAdopts, EnterpriseAdopt::getUserId, EnterpriseAdopt::getLabel);
        List<Long> finalCollect = collect1;

        customerFrontUserType = customerFrontUserType.stream().filter(customerFrontUserTypeVO -> finalCollect.contains(customerFrontUserTypeVO.getUserId())).collect(Collectors.toList());

        //查询customerId
        List<Long> customerIds = customerFrontUserType.stream().distinct().map(CustomerFrontUserType::getCustomerFrontUserId).collect(Collectors.toList());

        if (ObjectUtil.isEmpty(customerIds)) {
            return Collections.emptyList();
        }
        Map<Long, Customer> collect = SpringUtil.getBean(ICustomerService.class).lambdaQuery().in(BaseEntity::getId, customerIds).list().stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

        //查询所有父企业ID
        List<Long> companyIds = customerFrontUserType.stream().map(CustomerFrontUserType::getUserId).distinct().collect(Collectors.toList());
        //查找所有父企业信息
        List<CustomerFrontUserType> list2 = lambdaQuery().eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).in(CustomerFrontUserType::getUserId, companyIds).list();
        //获取所有父企业的customerId
        List<Long> collect6 = list2.stream().map(CustomerFrontUserType::getCustomerFrontUserId).collect(Collectors.toList());
        customerIds.addAll(collect6);
        //查询所有的邀请信息
        List<CustomerVerificationCode> customerCode = SpringUtil.getBean(ICustomerVerificationCodeService.class).lambdaQuery().in(CustomerVerificationCode::getCoverCodeId, customerIds).list();
        Map<Long, CustomerVerificationCode> collect3 = new HashMap<>();
        Map<Long, List<CustomerVerificationCode>> customerVerificationCodeMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(customerCode)) {
            customerVerificationCodeMap = customerCode.stream().collect(Collectors.groupingBy(CustomerVerificationCode::getCompanyId));
        }
        List<CustomerInfo> cus = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().in(CustomerInfo::getCustomerId, customerIds).list();
        Map<Long, CustomerInfo> customerInfoMap = null;
        if (ObjectUtil.isNotEmpty(cus)) {
            //获取统一社会代码
            customerInfoMap = cus.stream().collect(Collectors.toMap(CustomerInfo::getCompanyId, e -> e));
        }

        //userId
        List<Long> userIds = customerFrontUserType.stream().distinct().map(CustomerFrontUserType::getRoleUserId).collect(Collectors.toList());
        //获得角色ID
        //TODO 接口需要重新考虑
        List<User> list1 = remoteUserSearchService.listByUserForInner(userIds, FeignConstants.FROM_IN).getData();

        Map<Long, User> collect4 = list1.stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

        //获取统一社会代码
        Map<Long, CustomerInfo> finalCustomerInfoMap = customerInfoMap;

        Map<Long, List<CustomerVerificationCode>> finalCustomerVerificationCodeMap = customerVerificationCodeMap;
        return customerFrontUserType.stream().peek(customerFrontUser -> {
            Customer customer = collect.get(customerFrontUser.getCustomerFrontUserId());
            //设置个人名称
            customerFrontUser.setPersonalName(customer.getName());
            //设置个人手机号
            customerFrontUser.setPersonalPhone(customer.getPhone());
            //设置个人头像
            customerFrontUser.setLogoSrc(customer.getLogoSrc());
            //设置个人用户
            customerFrontUser.setAccountUser(customer.getAccountUser());

            customerFrontUser.setLabel(longIntegerMap.get(customerFrontUser.getUserId()));
            //设置企业名称
            //TODO 接口需要重新考虑
            List<User> userList = remoteUserSearchService.listByUserForInner(finalCollect, FeignConstants.FROM_IN).getData();
            if (ObjectUtil.isNotEmpty(userList)) {
                Map<Long, User> collect2 = userList.stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
                User user = collect2.get(customerFrontUser.getUserId());
                if (ObjectUtil.isNotEmpty(user)) {
                    customerFrontUser.setName(user.getName());
                    customerFrontUser.setCorporateAvatar(user.getAvatar());
                }
            }


            String roleId = collect4.get(customerFrontUser.getRoleUserId()).getRoleId();
            if (ObjectUtil.isNotEmpty(roleId)) {
                String roleName = selectRoleName(roleId);
                customerFrontUser.setRoleName(roleName);
                String s = selectRoleAlias(roleId);
                if (ObjectUtil.isNotEmpty(s)) {
                    customerFrontUser.setRoleAlias(s);
                }
            }
            if (ObjectUtil.isNotEmpty(finalCustomerInfoMap)) {
                CustomerInfo customerInfo = finalCustomerInfoMap.get(customerFrontUser.getUserId());
                if (customerFrontUser.getType().equals(VerificationCodeSupertube.UNDER.getCode())) {
                    customerFrontUser.setCreditCode(finalCustomerInfoMap.get(customerFrontUser.getUserId()).getBusinessLicenceNumber());
                } else if (customerFrontUser.getType().equals(VerificationCodeSupertube.ENTERPRISE.getCode())) {
                    customerFrontUser.setCreditCode(finalCustomerInfoMap.get(customerFrontUser.getUserId()).getBusinessLicenceNumber());
                }
                customerFrontUser.setName(Func.isEmpty(customerInfo) ? "" : customerInfo.getCorpName());
                customerFrontUser.setCorporateAvatar(Func.isEmpty(customerInfo) ? "" : customerInfo.getLogo());
                if (ObjectUtil.isNotEmpty(customerInfo) && isUpdateLogoSrc) {
                    User user = collect4.get(customerInfo.getCompanyId());
                    if (ObjectUtil.isNotEmpty(user)) {
                        if (customerFrontUser.getType().equals(VerificationCodeSupertube.PERSONAL.getCode())) {
                            customerFrontUser.setLogoSrc(StringUtil.isEmpty(customer.getLogoSrc()) ?  user.getAvatar() :customer.getLogoSrc());
                        } else if (customerFrontUser.getType().equals(VerificationCodeSupertube.ENTERPRISE.getCode())) {
                            customerFrontUser.setLogoSrc(StringUtil.isEmpty(user.getAvatar()) ?  customer.getLogoSrc() :user.getAvatar());
                        }
                    }
                }
            }
            if (VerificationCodeSupertube.UNDER.getCode().equals(customerFrontUser.getType())) {
                List<CustomerVerificationCode> customerVerificationCodes = finalCustomerVerificationCodeMap.get(customerFrontUser.getUserId());
                Map<Long, CustomerVerificationCode> collect7 = customerVerificationCodes.stream().collect(Collectors.toMap(CustomerVerificationCode::getCoverCodeId, e -> e));

                CustomerVerificationCode customerVerificationCode = collect7.get(customerFrontUser.getCustomerFrontUserId());
                if (ObjectUtil.isNotEmpty(customerVerificationCode)) {
                    customerFrontUser.setInviteeName(customerVerificationCode.getInviteeName());
                    customerFrontUser.setBindingTime(customerVerificationCode.getCreateTime());
                }
            }

        }).collect(Collectors.toList());
    }

    /***
     * 创建企业下的用户
     * @param name  企业名称
     * @param roleId  角色id
     * @return
     */
    private User user(String name, String roleId, Long userId, Integer entAuthType) {

        //进行同步
        User user = new User();
        //设置当前租户
        String tentId = MyAuthUtil.getTentId();
        user.setTenantId(tentId);

        if (ObjectUtil.isNotEmpty(userId)) {
            user.setId(userId);
        }
        String substring = UUID.randomUUID().toString().substring(0, 10);
        String byAccount = getByAccount(substring);
        //账号
        user.setAccount(byAccount);
        //密码
        user.setPassword(UUID.randomUUID().toString());
        //企业名称
        user.setName(name);
        //设置企业默认头像
        String logoSrc = getSysLog(tentId);
        user.setAvatar(logoSrc);
        //设置部门id
        Dept dept = getDeptByFinancingName(tentId);
        if (Func.isNotEmpty(dept)) {
            user.setDeptId(String.valueOf(dept.getId()));
        }
        //设置 用户类型 融资企业
        user.setUserType(entAuthType.equals(CustomerEnum.LABEL_FINANCING.getCode()) ? UserTypeEnum.FINANCING.getCode() : UserTypeEnum.CORE.getCode());
        //设置角色 角色ID
        user.setRoleId(roleId);
        //TODO 接口需要重新考虑
        R<User> rUser = remoteUserService.userSubmit(user, FeignConstants.FROM_IN);
        if (!rUser.isSuccess()) {
            throw new ServiceException("新增用户失败");
        }
        if(ObjectUtil.isEmpty(rUser.getData())) {
            throw new ServiceException("新增用户失败");
        }
        user.setId(rUser.getData().getId());
        return user;
    }

    /**
     * 获取系统默认头像
     *
     * @param tentId
     * @return
     */
    private String getSysLog(String tentId) {
        String logoSrc = paramService.getValue("LogoSrc");
        if (StringUtil.isBlank(logoSrc)) {
            return "";
        }
        Map<String, String> map = JSONUtil.toBean(logoSrc, Map.class);
        logoSrc = map.get(BladeConstant.ADMIN_TENANT_ID);
        if (StringUtil.isNotBlank(tentId)) {
            logoSrc = map.get(tentId);
        }
        return StringUtil.isNotBlank(tentId) ? logoSrc : "";
//        // 旧格式为000000:https://jingruiit.com:9000/supplychain/upload/20230105/cad853f0e5c1e04bc6b408dfbc902f62.png
//        if (StringUtil.isNotBlank(logoSrc)) {
//            String[] split = logoSrc.split(",");
//            //设置默认000000
//            //TODO 接口需要重新考虑
//            for (String str : split) {
//                if (str.contains(TokenUtil.DEFAULT_TENANT_ID)) {
//                    logoSrc = str.replaceAll(tentId + ":", "");
//                    break;
//                }
//            }
//            //匹配系统自带
//            if (StringUtil.isNotBlank(tentId)) {
//                for (String str : split) {
//                    if (str.contains(tentId)) {
//                        logoSrc = str.replaceAll(tentId + ":", "");
//                        break;
//                    }
//                }
//            } else {
//                logoSrc = "";
//            }
//        }
//        return logoSrc;
    }


    private Dept getDeptByFinancingName(String tenantId) {
        R<Dept> resultDept = remoteDeptSearchService.getDeptByName("融资企业", tenantId);
        return resultDept.getData();

    }

    /**
     * 当校验成功邀请码之后 根据Code 查询邀请人信息，以及角色信息等，
     */
    private void verifyCode(String code) {
        CustomerVerificationCode one = customerVerificationCodeService.lambdaQuery().eq(CustomerVerificationCode::getCode, code).one();
        //获取企业id
        Long companyId = one.getCompanyId();
        //获取当前人的的customerId
        one.setCoverCodeId(MyAuthUtil.getCustomerId());

        customerVerificationCodeService.updateById(one);

        CustomerFrontUserType customerFrontUserType = new CustomerFrontUserType();
        //添加企业ID
        customerFrontUserType.setUserId(companyId);
        //添加当前人的
        customerFrontUserType.setCustomerFrontUserId(MyAuthUtil.getCustomerId());
        //只有企业第一个实名认证的账号 才能是 2 不是初始创建人是类型是3   个人实名是1
        customerFrontUserType.setType(VerificationCodeSupertube.UNDER.getCode());
        //获取企业类型
        EnterpriseAdopt one1 = SpringUtil.getBean(IEnterpriseAdoptService.class).lambdaQuery().eq(EnterpriseAdopt::getUserId, companyId).one();
        //获取企业创建人的企业信息
        //User one2 = SpringUtil.getBean(IUserService.class).lambdaQuery().eq(BaseEntity::getId, userId).one();
        //存入企业名称
        User user = user("", one.getRole(), one.getCoverCodeId(), one1.getLabel());
        //存入当前人的User id 方便找到角色 控制权限和工作流
        customerFrontUserType.setRoleUserId(user.getId());
        save(customerFrontUserType);
    }

    /***
     * 根据企业id查询数据 与当前企业下的人员查询
     */
    private CustomerFrontUserType getByCompanyId(Long companyId) {
        return getOne(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getUserId, companyId).eq(CustomerFrontUserType::getType, CustomerTypeEnum.ENTERPRISE.getCode()));
    }

    /***
     * 生成一个假账号信息，为避免账号重复，选择递归查找， 方便融资企业进行工作流操作
     */
    private String getByAccount(String account) {

        User one = remoteUserService.getUserByAccount(account, FeignConstants.FROM_IN).getData();
        if (ObjectUtil.isNotEmpty(one)) {
            String substring = UUID.randomUUID().toString().substring(0, 5);
            return getByAccount(substring);
        }
        return account;
    }

    /***
     * 个人实名成功时
     * @param companyId
     * @param name
     */
    private void updateByCompanyIdAndPersonal(Long companyId, String name) {
        //TODO 接口需要重新考虑
        User byId = CustomerUserCache.getUserById(companyId);
        byId.setName(name);
        remoteUserService.updateById(byId);


        Customer one = SpringUtil.getBean(ICustomerService.class).getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getCompanyId, companyId));
        //添加状态实名成功
        one.setStatus(1);
        //个人信息
        one.setName(name);
        SpringUtil.getBean(ICustomerService.class).updateById(one);
    }

    @Override
    public Boolean updateCompanyAvatar(String corporateAvatar, Long typeId) {
        //TODO 接口需要重新考虑
        Long typeId1 = MyAuthUtil.getTypeId();
        CustomerFrontUserType byId = SpringUtil.getBean(ICustomerFrontUserTypeService.class).getById(typeId1);
        User byId1 = CustomerUserCache.getUserById(byId.getUserId());
        byId1.setAvatar(corporateAvatar);
        return remoteUserService.updateById(byId1).getData();
    }

    @Override
    public CustomerInfoVO getCompanyDetailsByCustomerId() {
        String companyId = MyAuthUtil.getCompanyId();
        CustomerInfo one = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().eq(CustomerInfo::getCompanyId, companyId).one();
        CustomerBusinessInfo customerBusinessInfo = null;
        if (ObjectUtil.isEmpty(one)) {
            Long customerId = MyAuthUtil.getCustomerId();
            CustomerFrontUserType frontUserType = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery().eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).eq(CustomerFrontUserType::getCustomerFrontUserId, customerId).eq(CustomerFrontUserType::getUserId, companyId).one();
            if (ObjectUtil.isEmpty(frontUserType)) {
                return new CustomerInfoVO();
            }
            one = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().eq(CustomerInfo::getCompanyId, frontUserType.getUserId()).one();
            if (ObjectUtil.isEmpty(one)) {
                return new CustomerInfoVO();
            }
            customerBusinessInfo = customerInfoService.getCustomerBusinessInfoByCompanyId(frontUserType.getUserId().toString());
        } else {
            customerBusinessInfo = customerInfoService.getCustomerBusinessInfoByCompanyId(companyId);
        }

        CustomerInfoVO customerInfoVO = CustomerInfoWrapper.build().entityVO(one);
        customerInfoVO.setCreditCode(customerInfoVO.getBusinessLicenceNumber());
        customerInfoVO.setCorporationIdCardNumber(DesensitizedUtil.idCardNum(customerInfoVO.getCorporationIdCardNumber(), 3, 2));
        customerInfoVO.setMobile(DesensitizedUtil.mobilePhone(customerInfoVO.getMobile()));
        if (ObjectUtil.isNotEmpty(customerBusinessInfo)) {
            customerInfoVO.setEstiblishTime(customerBusinessInfo.getEstiblishTime());
        }
        return customerInfoVO;
    }

    @Override
    public CustomerVO customerSignin(Long typeId, Long id) {
        CustomerVO customerVO = personalCustomer(typeId, id);
        CustomerFrontUserType one1 = lambdaQuery().eq(BaseEntity::getId, typeId).one();
        Long userId = one1.getUserId();
        //获取企业基础信息
        User one = CustomerUserCache.getUserById(userId);
        //获取个人 信息
        EnterpriseAdopt enterpriseAdopt = SpringUtil.getBean(IEnterpriseAdoptService.class)
                .lambdaQuery().eq(EnterpriseAdopt::getUserId, userId).one();
        Integer label = enterpriseAdopt.getLabel();
        customerVO.setLabel(label);
        customerVO.setCompanyName(one.getName());
        //获取当前企业 当前人的角色信息
        //TODO 接口需要重新考虑
        User user = CustomerUserCache.getUserById(one1.getRoleUserId());
        if (ObjectUtil.isNotEmpty(user)) {
            String roleId = user.getRoleId();
            String list = selectRoleName(roleId);
            customerVO.setRoleName(list);
            customerVO.setRoleId(user.getRoleId());
            String s = selectRoleAlias(roleId);
            customerVO.setRoleAlias(s);
        }
        //企业头像
        customerVO.setCorporationName(one.getAvatar());

        BladeUser user1 = AuthUtil.getUser();

        if (user1 != null && jwtProperties.getState()) {
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.removeAccessToken(user1.getTenantId(), String.valueOf(user1.getUserId()), token);
        }
        return customerVO;
    }

    /**
     *  获取当前登陆用户信息
     * <AUTHOR>
     * @date 2024/10/15 17:24
     * @return org.springblade.customer.vo.CustomerVO
     */
    @Override
    public CustomerVO getCustomerInfo(){
        CustomerVO customerVO = personalCustomer(MyAuthUtil.getTypeId(), MyAuthUtil.getCustomerId());
        CustomerFrontUserType one1 = lambdaQuery().eq(BaseEntity::getId, MyAuthUtil.getTypeId()).one();
        Long userId = one1.getUserId();
        //获取企业基础信息
        User one = CustomerUserCache.getUserById(userId);

        CustomerInfo customerInfo =customerInfoService.getByCompanyId(userId);

        //获取个人 信息
        EnterpriseAdopt enterpriseAdopt = SpringUtil.getBean(IEnterpriseAdoptService.class)
                .lambdaQuery().eq(EnterpriseAdopt::getUserId, userId).one();
        Integer label = enterpriseAdopt.getLabel();
        customerVO.setLabel(label);
        customerVO.setCompanyName(one.getName());

        User user = CustomerUserCache.getUserById(one1.getRoleUserId());
        if (ObjectUtil.isNotEmpty(user)) {
            String roleId = user.getRoleId();
            String list = selectRoleName(roleId);
            customerVO.setRoleName(list);
            customerVO.setRoleId(user.getRoleId());
            String s = selectRoleAlias(roleId);
            customerVO.setRoleAlias(s);
        }
        //企业头像
        if(ObjectUtil.isNotEmpty(customerInfo)){
            customerVO.setCorporationName(Func.toStr(customerInfo.getLogo(),one.getAvatar()));
        }else{
            customerVO.setCorporationName(one.getAvatar());
        }
        return customerVO;
    }

    /**
     * 转移原企业信息
     *
     * @param primaryCustomerId 原来的customerId
     * @param primaryCustomerId 转移后 customerId
     */
    private void updateCustomerInfo(Long primaryCustomerId, Long presentCustomerId, Long companyId) {
        ICustomerInfoService bean = SpringUtil.getBean(ICustomerInfoService.class);
        CustomerInfo customerInfo = bean.lambdaQuery().eq(CustomerInfo::getCustomerId, primaryCustomerId).eq(CustomerInfo::getCompanyId, companyId).one();
        customerInfo.setCustomerId(presentCustomerId);
        bean.updateById(customerInfo);
    }

    @Override
    public CustomerVO personalCustomer(Long typeId, Long id) {
        Long id1 = MyAuthUtil.getId();
        Map<String, String> parameter = SpringUtil.getBean(IParamService.class).list().stream()
                .filter(o -> o.getParamKey().equals("toDemo"))
                .collect(Collectors.toMap(Param::getParamKey, Param::getParamValue, (oldVal, newVal) -> oldVal));

        if (ObjectUtil.isNotEmpty(id1)) {
            CustomerFrontUserType one = lambdaQuery().eq(CustomerFrontUserType::getId, typeId).one();
            //判断切换个人用户，以及当id为null时
            if (!one.getType().equals(VerificationCodeSupertube.PERSONAL.getCode()) || ObjectUtil.isEmpty(id)) {
                id = id1;
            }
        }
        CustomerFrontUserType one = lambdaQuery()
                .eq(CustomerFrontUserType::getId, typeId)
                .eq(CustomerFrontUserType::getCustomerFrontUserId, id)
                .one();
        if (ObjectUtil.isEmpty(one)) {
            throw new ServiceException("非法操作!");
        }
        Long customerId = one.getCustomerFrontUserId();
        Customer customer = SpringUtil.getBean(ICustomerService.class).getById(customerId);
        //设置企业ID
        customer.setCompanyId(one.getUserId());
        CustomerVO customerVO = CustomerWrapper.build().entityVO(customer);
        //查询个人信息
        //TODO 接口需要重新考虑
        User user = CustomerUserCache.getUserById(customer.getUserId());

        customerVO.setUser(user);
        customerVO.setToDemo(parameter.get("toDemo"));
        Integer type = one.getType();
        if (CustomerTypeEnum.UNDER_ENTERPRISE.getCode().equals(type)) {
            //当为企业下的人员时 则为父企业userId 数据库的user_id 还是本来的不变 只是token 发生了改变
            customerVO.setUserId(one.getUserId());
        } else {
            //为个人或者企业时 user 为本身的
            customerVO.setUserId(one.getRoleUserId());
        }
        customerVO.setSubUserId(one.getRoleUserId());
        customerVO.setType(type);
        //设置个人信息userId
        customerVO.setPersonalUserId(customer.getUserId());

        String phone = customerVO.getPhone();
        String s = DesensitizedUtil.mobilePhone(phone);
        customerVO.setPhone(s);
        //存入tyepId
        customerVO.setTypeId(one.getId());
        CustomerPersonInfo one1 = SpringUtil.getBean(ICustomerPersonInfoService.class)
                .getOne(Wrappers.<CustomerPersonInfo>lambdaQuery().eq(CustomerPersonInfo::getCustomerId, user.getId()));
        if (org.springblade.core.tool.utils.ObjectUtil.isEmpty(one1)) {
            return customerVO;
        }
        String identity = one1.getIdentity();

        String iden = DesensitizedUtil.idCardNum(identity, 3, 2);
        customerVO.setIdentityCard(iden);
        return customerVO;
    }

    @Override
    public List<CustomerFrontUserTypeVO> selectEnterprise(Long customerId) {
        List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = selectByCustomerId(customerId);
        if (ObjectUtil.isEmpty(customerFrontUserTypeVOS)) {
            return null;
        }
        List<Long> collect3 = customerFrontUserTypeVOS.stream().filter(customerFrontUserTypeVo -> !customerFrontUserTypeVo.getType().equals(1)).map(CustomerFrontUserType::getUserId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(collect3)) {
            return new ArrayList<>();
        }
        //TODO 接口需要重新考虑
        List<User> userList = remoteUserService.listByUser(collect3, FeignConstants.FROM_IN).getData();
        if (ObjectUtil.isNotEmpty(userList)) {
            Map<Long, User> collect2 = userList.stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));

            List<CustomerFrontUserTypeVO> collect = customerFrontUserTypeVOS.stream().filter(customerFrontUserTypeVo -> {
                //只有为3 企业下的成员时返回true
                //return customerFrontUserTypeVo.getType().equals(VerificationCodeSupertube.UNDER.getCode());
                return !customerFrontUserTypeVo.getType().equals(1);
            }).map(customerFrontUserTypeVO -> {
                if(StringUtil.isBlank(customerFrontUserTypeVO.getCorporateAvatar())) {
                    User user = collect2.get(customerFrontUserTypeVO.getUserId());
                    customerFrontUserTypeVO.setCorporateAvatar(user.getAvatar());
                    return customerFrontUserTypeVO;
                }
                return customerFrontUserTypeVO;
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    @Override
    public Boolean updateRole(String roleIds, String typeId) {
        CustomerFrontUserType one = getById(typeId);
        if (VerificationCodeSupertube.ENTERPRISE.getCode().equals(one.getType())) {
            throw new ServiceException("无法修改管理员权限!");
        }
        if (VerificationCodeSupertube.PERSONAL.getCode().equals(one.getType())) {
            throw new ServiceException("无法修改个人账号角色信息!");
        }
        List<Long> longs = Func.toLongList(roleIds);

        List<FinancingRole> list = SpringUtil.getBean(IFinancingRoleService.class).lambdaQuery().in(FinancingRole::getId, longs).list();
        List<String> collect = list.stream().map(FinancingRole::getRoleAlias).collect(Collectors.toList());
        if (collect.contains(RoleEnum.ADMIN.getName())) {
            throw new ServiceException("非法操作,不可以给超级员权限!");
        }

        Long userId = one.getRoleUserId();
        //TODO 接口需要重新考虑
        User byId = CustomerUserCache.getUserById(userId);
        byId.setRoleId(roleIds);

        return remoteUserService.updateById(byId).getData();

    }

    @Override
    public CustomerFrontUserTypePersonalVO personalDetails(Long id) {
        FrontFinancingList one = SpringUtil.getBean(IFrontFinancingListService.class).getById(id);
        CustomerFrontUserType byId = null;
        Customer byId1 = null;
        if (ObjectUtils.isEmpty(one)) {
            throw new ServiceException("未找到对应的数据!");
        } else {
            byId1 = SpringUtil.getBean(ICustomerService.class).lambdaQuery().eq(Customer::getCompanyId, one.getCompanyId()).one();
            byId = lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, byId1.getId()).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.PERSONAL.getCode()).one();
        }

        CustomerFrontUserTypePersonalVO customerFrontUserTypeVO = BeanUtil.copy(byId, CustomerFrontUserTypePersonalVO.class);
        customerFrontUserTypeVO.setPersonalName(byId1.getName());
        customerFrontUserTypeVO.setLogoSrc(byId1.getLogoSrc());
        customerFrontUserTypeVO.setPersonalPhone(byId1.getPhone());
        customerFrontUserTypeVO.setLastVisitTime(byId1.getLastVisitTime());
        customerFrontUserTypeVO.setComprehensiveScore(one.getCustomerScore());

        String invitationCode = one.getInvitationCode();
        User byId2 = CustomerUserCache.getUserById(Func.toLong(invitationCode));
        if (ObjectUtil.isNotEmpty(byId2)) {
            customerFrontUserTypeVO.setInvitationName(byId2.getRealName());
        }
        customerFrontUserTypeVO.setCustomerStatus(byId1.getStatus());
        customerFrontUserTypeVO.setAccountUser(byId1.getAccountUser());
        return customerFrontUserTypeVO;
    }

    @Override
    public IPage<CustomerFrontUserBackTypeVO> queryTypeCustomerToPage(Long valueOf, IPage<CustomerFrontUserType> page) {
        IPage<CustomerFrontUserType> page1 = this.lambdaQuery().eq(CustomerFrontUserType::getUserId, valueOf).page(page);
        List<CustomerFrontUserType> records = page1.getRecords();
        IPage<CustomerFrontUserTypeVO> customerFrontUserTypeVOPage = CustomerFrontUserTypeWrapper.build().pageVO(page1);
        List<CustomerFrontUserBackTypeVO> copy = BeanUtil.copy(records, CustomerFrontUserBackTypeVO.class);
        Page<CustomerFrontUserBackTypeVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(copy);
        pageVO.setPages(customerFrontUserTypeVOPage.getPages());

        if (ObjectUtil.isEmpty(records)) {
            return pageVO;
        }
        List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = toCustomerFrontUserName(records, true);

        List<CustomerFrontUserBackTypeVO> copy1 = BeanUtil.copy(customerFrontUserTypeVOS, CustomerFrontUserBackTypeVO.class);

        List<Long> collect = customerFrontUserTypeVOS.stream().map(CustomerFrontUserType::getCustomerFrontUserId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(collect)) {
            List<Customer> list = SpringUtil.getBean(ICustomerService.class).lambdaQuery().in(BaseEntity::getId, collect).list();
            Map<Long, Customer> collect2 = list.stream().collect(Collectors.toMap(Customer::getId, e -> e));
            List<Long> collect1 = list.stream().map(Customer::getCompanyId).distinct().collect(Collectors.toList());
            Map<Long, FrontFinancingList> frontFinancingMap = SpringUtil.getBean(IFrontFinancingListService.class).lambdaQuery().in(FrontFinancingList::getCompanyId, collect1).list().stream().collect(Collectors.toMap(FrontFinancingList::getCompanyId, e -> e));
            List<CustomerFrontUserBackTypeVO> CustomerFrontUserBackTypeVoList = customerFrontUserTypeVOS.stream().map(customerFrontUserTypeVO -> {
                CustomerFrontUserBackTypeVO customerFrontUser = BeanUtil.copy(customerFrontUserTypeVO, CustomerFrontUserBackTypeVO.class);
                Customer customer = collect2.get(customerFrontUserTypeVO.getCustomerFrontUserId());
                customerFrontUser.setFrontId(frontFinancingMap.get(customer.getCompanyId()).getId());
                return customerFrontUser;
            }).collect(Collectors.toList());
            IPage<CustomerFrontUserBackTypeVO> customerFrontUserBackTypeVOIPage = pageVO.setRecords(CustomerFrontUserBackTypeVoList);
            return customerFrontUserBackTypeVOIPage;
        }
        pageVO.setRecords(copy1);
        return pageVO;
    }

    @Override
    public List<CustomerFrontUserTypeVO> selectByCustomerIdAndEncryptionPhone(Long customerId) {
        List<CustomerFrontUserTypeVO> customerFrontUserTypeVOS = this.selectByCustomerId(customerId);
        List<CustomerFrontUserTypeVO> collect = customerFrontUserTypeVOS.stream().map(customerFrontUserTypeVO -> {
            String personalPhone = customerFrontUserTypeVO.getPersonalPhone();
            if (ObjectUtil.isNotEmpty(personalPhone)) {
                customerFrontUserTypeVO.setPersonalPhone(DesensitizedUtil.mobilePhone(personalPhone));
            }
            String creditCode = customerFrontUserTypeVO.getCreditCode();
            if (ObjectUtil.isNotEmpty(creditCode) && creditCode.length() > 5) {
                customerFrontUserTypeVO.setCreditCode(StrUtil.hide(creditCode, 3, creditCode.length() - 1));
            }
            return customerFrontUserTypeVO;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public boolean verifyCoreEnterprise() {
        //流程变量
        //	com.alibaba.fastjson.JSONObject variables = new com.alibaba.fastjson.JSONObject();
        //	String simpleProcessId = businessProcessService.startProcessByDefinitionKey(WfProcessConstant.PROCESS_LOWER_SALE, variables);
        return false;
    }

    @Override
    public boolean enterpriseBinding(Long customerId, String name, Long userId, Integer enterpriseType, Integer entAuthType) {
        customerBinding(customerId, name, userId, entAuthType);
        return saveEnterpriseadopt(CustomerEnum.LABEL_FINANCING.getCode(), enterpriseType, userId, entAuthType);
    }

    @Override
    public boolean saveEnterpriseadopt(Integer label, Integer enterpriseType, Long userId, Integer entAuthType) {
        EnterpriseAdopt enterpriseAdopt = new EnterpriseAdopt();
        enterpriseAdopt.setEnterpriseType(enterpriseType);
        enterpriseAdopt.setUserId(userId);
        enterpriseAdopt.setLabel(entAuthType);
        //1-开通成功  2-失败 3 -開通中
        enterpriseAdopt.setProcessStatus(3);
        return SpringUtil.getBean(IEnterpriseAdoptService.class).save(enterpriseAdopt);
    }


    /***
     *  取消开通操作
     * @param userId
     * @return
     */
    @Override
    public boolean removeFrontUserType(Long userId) {
        CustomerFrontUserType byId = getOne(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode()).eq(CustomerFrontUserType::getUserId, userId));
        //避免未实名成功进行取消开通操作
        if (ObjectUtil.isEmpty(byId)) {
            return true;
        }
        Long typeId = byId.getId();
        SpringUtil.getBean(IEnterpriseAdoptService.class).remove(Wrappers.<EnterpriseAdopt>lambdaQuery().eq(EnterpriseAdopt::getUserId, userId));
        SpringUtil.getBean(IFrontFinancingListService.class).remove(Wrappers.<FrontFinancingList>lambdaQuery().eq(FrontFinancingList::getCompanyId, userId));
        //TODO 接口需要重新考虑
        //SpringUtil.getBean(IUserService.class).remove(Wrappers.<User>lambdaQuery().eq(BaseEntity::getId, userId));
    /*	SpringUtil.getBean(ICustomerBusinessInfoService.class).remove(Wrappers.<CustomerBusinessInfo>lambdaQuery().eq(CustomerBusinessInfo::getCompanyId, userId));
        SpringUtil.getBean(ICustomerInfoService.class).remove(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCompanyId, userId));
    */
        return remove(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(BaseEntity::getId, typeId));
    }


    /***
     * 成功时
     * @param userId
     */
    @Override
    public void success(Long userId) {
        IEnterpriseAdoptService bean = SpringUtil.getBean(IEnterpriseAdoptService.class);
        EnterpriseAdopt one = bean.lambdaQuery().eq(EnterpriseAdopt::getUserId, userId).one();
        one.setProcessStatus(1);
        bean.updateById(one);
    }

    /***
     * 失败时时
     * @param userId
     */
    @Override
    public void fail(Long userId) {
        IEnterpriseAdoptService bean = SpringUtil.getBean(IEnterpriseAdoptService.class);
        EnterpriseAdopt one = bean.lambdaQuery().eq(EnterpriseAdopt::getUserId, userId).one();
        if (ObjectUtil.isNotEmpty(one)) {
            one.setProcessStatus(2);
            bean.updateById(one);
        }
    }

    @Override
    public List<CustomerFrontUserType> listByRoleIds(List<String> rolesIds) {
        return list(Wrappers.<CustomerFrontUserType>lambdaQuery().in(CustomerFrontUserType::getRoleUserId, rolesIds));
    }

    @Override
    public CustomerFrontUserType getByRoleId(String roleId) {
        return getOne(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getRoleUserId, roleId));
    }

    @Override
    public CustomerPersonInfo getCustomerPersonInfo(Long companyId) {
        CustomerPersonInfo one = SpringUtil.getBean(ICustomerPersonInfoService.class)
                .lambdaQuery().eq(CustomerPersonInfo::getCustomerId, companyId).one();
        return one;
    }

    @Override
    public void deleteByRoleId(Long rolesId) {
        remove(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getRoleUserId, rolesId));
    }

    @Override
    public void deleteByUserId(Long userId) {
        remove(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getUserId, userId));
    }

    @Override
    public List<CustomerFrontUserType> listByUserId(Long userId) {
        return list(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getUserId, userId));
    }

    @Override
    public List<CustomerFrontUserType> listByFornUserTypeId(Long customerId) {
        return list(Wrappers.<CustomerFrontUserType>lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, customerId));
    }

    @Override
    public List<CustomerFrontUserTypeBackVO> selectCoreEnterpriseQuarterRanking() {
        //1成功 2失敗 3开通中
        List<Integer> status = Func.toIntList("1,2,3");
        //查询实名用户
        List<EnterpriseAdopt> enterpriseAdoptList = SpringUtil.getBean(IEnterpriseAdoptService.class).lambdaQuery().eq(EnterpriseAdopt::getLabel, CustomerEnum.LABEL_CORE.getCode())
                .in(EnterpriseAdopt::getProcessStatus, status).list();
        if (CollectionUtil.isEmpty(enterpriseAdoptList)) {
            return Collections.emptyList();
        }
        List<Long> userIds = enterpriseAdoptList.stream().map(EnterpriseAdopt::getUserId).collect(Collectors.toList());
        //查询核心企业用户类型
        List<CustomerFrontUserType> customerFrontUserTypeList = SpringUtil.getBean(ICustomerFrontUserTypeService.class).lambdaQuery()
                .eq(CustomerFrontUserType::getType, VerificationCodeSupertube.ENTERPRISE.getCode())
                .in(CustomerFrontUserType::getUserId, userIds)
                .orderByDesc(CustomerFrontUserType::getCreateTime)
                .last("limit 6").list();
        List<CustomerFrontUserTypeVO> customerFrontUserTypeVos = CustomerFrontUserTypeWrapper.build().listVO(customerFrontUserTypeList);
        //查询所有核心企业用户id
        List<Long> frontUserIds = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getCustomerFrontUserId).distinct().collect(Collectors.toList());
        //查询已开通的企业实名信息
        if (CollectionUtil.isEmpty(frontUserIds)) {
            return Collections.emptyList();
        }
        //TODO 接口需要重新考虑
//        //查询企业的法人等信息
//        Map<Long, CustomerInfo> customerInfoMap = SpringUtil.getBean(ICustomerInfoService.class).lambdaQuery().in(CustomerInfo::getCustomerId, frontUserIds).list().stream().collect(Collectors.toMap(CustomerInfo::getCompanyId, e -> e));
//        //查询父企业信息
//        List<Long> roleUserIds = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getRoleUserId).collect(Collectors.toList());
//        Map<Long, User> mapInId = SpringUtil.getBean(IUserService.class).getMapInId(roleUserIds);
//        //客户经理
//        Map<Long, User> userMap = SpringUtil.getBean(IUserService.class).lambdaQuery().eq(User::getUserType, 1).list().stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
//
//        List<FrontFinancingList> frontFinancingLists = SpringUtil.getBean(IFrontFinancingListService.class).lambdaQuery().in(FrontFinancingList::getCompanyId, roleUserIds).list();
//        Map<Long, FrontFinancingList> frontFinancingListMap = null;
//        if (ObjectUtil.isNotEmpty(frontFinancingLists)) {
//            frontFinancingListMap = frontFinancingLists.stream().collect(Collectors.toMap(FrontFinancingList::getCompanyId, e -> e));
//        }
//        //查询所有所属者
//        List<Long> possessIds = customerFrontUserTypeVos.stream().map(CustomerFrontUserType::getCustomerFrontUserId).collect(Collectors.toList());
//        Map<Long, Customer> customerMap = SpringUtil.getBean(ICustomerService.class).lambdaQuery().in(BaseEntity::getId, possessIds).list().stream().collect(Collectors.toMap(BaseEntity::getId, e -> e));
//        Map<Long, FrontFinancingList> finalFrontFinancingListMap = frontFinancingListMap;
//        List<CustomerFrontUserTypeBackVO> customerFrontUserTypeBackVOList = customerFrontUserTypeVos.stream().map(customerFrontUserTypeVo -> {
//                    CustomerFrontUserTypeBackVO customerFrontUserTypeBackVO = BeanUtil.copy(customerFrontUserTypeVo, CustomerFrontUserTypeBackVO.class);
//                    CustomerInfo customerInfo = customerInfoMap.get(customerFrontUserTypeVo.getUserId());
//                    if (ObjectUtil.isNotEmpty(customerInfo)) {
//                        customerFrontUserTypeBackVO.setCreditCode(customerInfo.getBusinessLicenceNumber());
//                        customerFrontUserTypeBackVO.setLegalPersonName(customerInfo.getCorporationName());
//                    }
//                    //后续进行拼接 头像
//                    User user = mapInId.get(customerFrontUserTypeBackVO.getRoleUserId());
//                    if (ObjectUtil.isNotEmpty(user)) {
//                        customerFrontUserTypeBackVO.setCorporateAvatar(user.getAvatar());
//                        customerFrontUserTypeBackVO.setName(user.getName());
//                    }
//                    Customer customer = customerMap.get(customerFrontUserTypeBackVO.getCustomerFrontUserId());
//                    if (Objects.nonNull(customer)) {
//                        customerFrontUserTypeBackVO.setPersonalName(customer.getName());
//                        RatingRecord ratingRecord = SpringUtil.getBean(IRatingRecordService.class).getOne(Wrappers.<RatingRecord>lambdaQuery()
//                                .eq(RatingRecord::getClientId, customer.getId()).last("limit 1"));
//                        if (ratingRecord != null) {
//                            customerFrontUserTypeBackVO.setComprehensiveScore(ratingRecord.getFinalScore());
//                        }
//                    }
//                    if (ObjectUtil.isNotEmpty(finalFrontFinancingListMap)) {
//                        String invitationCode = finalFrontFinancingListMap.get(customerFrontUserTypeVo.getUserId()).getInvitationCode();
//                        if (ObjectUtil.isNotEmpty(invitationCode)) {
//                            Long aLong = Long.valueOf(invitationCode);
//                            User user1 = userMap.get(aLong);
//                            customerFrontUserTypeBackVO.setInvitationName(user1.getRealName());
//                        }
//                    }
//                    //后续增加客户经理名称
//                    return customerFrontUserTypeBackVO;
//                }).sorted(Comparator.comparing(CustomerFrontUserTypeBackVO::getComprehensiveScore, Comparator.nullsFirst(BigDecimal::compareTo)).reversed())
//                .collect(Collectors.toList());
//        return customerFrontUserTypeBackVOList;
        return null;
    }

}
