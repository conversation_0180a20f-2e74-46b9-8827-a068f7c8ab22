<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.EnterpriseQuotaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="coreEnterpriseQuotaResultMap" type="org.springblade.customer.entity.EnterpriseQuota">
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="id" property="id"/>
        <result column="quota_no" property="quotaNo"/>
        <result column="task_no" property="taskNo"/>
        <result column="capital_id" property="capitalId"/>
        <result column="credit_amount" property="creditAmount"/>
        <result column="annual_interest_rate" property="annualInterestRate"/>
        <result column="used_amount" property="usedAmount"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="expire_time" property="expireTime"/>
    </resultMap>

    <sql id="enterpriseQuotaColumn">
        jeq.id, jeq.quota_no, jeq.task_no, jeq.capital_id, jeq.credit_amount,jeq.goods_id,jeq.enterprise_id,
        jeq.goods_name,bd.dept_name as capital_name,jeq.credit_amount,jeq.available_amount,jeq.used_amount,
        jeq.frozen_amount,jeq.annual_interest_rate,jeq.service_rate,jeq.quota_type,jeq.bank,jeq.bank_card_no,
        jeq.effective_time,jeq.expire_time,jeq.status,jeq.process_instance_id,jeq.enterprise_type,jeq.bond_proportion,
        jeq.frozen_reason,jeq.thaw_reason,jeq.disable_reason,jeq.loanable,jeq.apply_amount,jeq.product_type,
    </sql>

    <select id="selectCoreEnterpriseQuotaPage" resultType="org.springblade.customer.vo.EnterpriseQuotaVO">
        select <include refid="enterpriseQuotaColumn"/> bu.name as enterprise_name,bu.avatar as avatar
        from jrzh_enterprise_quota jeq
        inner join blade_dept bd on jeq.capital_id = bd.id
        inner join blade_user bu on jeq.enterprise_id = bu.id
        <where>
            jeq.enterprise_type = 2 and jeq.is_deleted = 0
            <if test="enterpriseQuota.taskNoEqual != null and enterpriseQuota.taskNoEqual != ''">
                and jeq.task_no = #{enterpriseQuota.taskNoEqual}
            </if>
            <if test="enterpriseQuota.statusEqual != null and enterpriseQuota.statusEqual != ''">
                and jeq.status = #{enterpriseQuota.statusEqual}
            </if>
            <if test="enterpriseQuota.productType != null and enterpriseQuota.productType != ''">
                and jeq.product_type = #{enterpriseQuota.productType}
            </if>
            <if test="enterpriseQuota.enterpriseName != null and enterpriseQuota.enterpriseName != ''">
                and bu.name like concat('%',#{enterpriseQuota.enterpriseName},'%')
            </if>
            <if test="enterpriseQuota.capitalName != null and enterpriseQuota.capitalName != ''">
                and bd.dept_name like concat('%',#{enterpriseQuota.capitalName},'%')
            </if>
        </where>
        order by jeq.update_time desc
    </select>
    <select id="selectFinancingEnterpriseQuotaPage" resultType="org.springblade.customer.vo.EnterpriseQuotaVO">
        select <include refid="enterpriseQuotaColumn"/> bu.name as enterprise_name,bu.avatar
        from jrzh_enterprise_quota jeq
        inner join blade_dept bd on jeq.capital_id = bd.id
        inner join blade_user bu on jeq.enterprise_id = bu.id
        <where>
            jeq.enterprise_type = 1 and jeq.is_deleted = 0
            <if test="enterpriseQuota.taskNoEqual != null and enterpriseQuota.taskNoEqual != ''">
                and jeq.task_no = #{enterpriseQuota.taskNoEqual}
            </if>
            <if test="enterpriseQuota.statusEqual != null and enterpriseQuota.statusEqual != ''">
                and jeq.status = #{enterpriseQuota.statusEqual}
            </if>
            <if test="enterpriseQuota.enterpriseName != null and enterpriseQuota.enterpriseName != ''">
                and bu.name like concat('%',#{enterpriseQuota.enterpriseName},'%')
            </if>
            <if test="enterpriseQuota.capitalName != null and enterpriseQuota.capitalName != ''">
                and bd.dept_name like concat('%',#{enterpriseQuota.capitalName},'%')
            </if>
        </where>
        order by jeq.update_time desc
    </select>

    <select id="selectFinancingEnterpriseQuotaSum" resultType="org.springblade.customer.vo.EnterpriseQuotaVO">
        select
        sum(jeq.credit_amount) as credit_amount ,sum(jeq.used_amount) as  used_amount
        from jrzh_enterprise_quota jeq
        inner join blade_dept bd on jeq.capital_id = bd.id
        inner join jrzh_customer_info bu on jeq.enterprise_id = bu.company_id
        <where>
            jeq.enterprise_type = 1 and jeq.is_deleted = 0
            <if test="enterpriseQuota.taskNoEqual != null and enterpriseQuota.taskNoEqual != ''">
                and jeq.task_no = #{enterpriseQuota.taskNoEqual}
            </if>
            <if test="enterpriseQuota.statusEqual != null and enterpriseQuota.statusEqual != ''">
                and jeq.status = #{enterpriseQuota.statusEqual}
            </if>
            <if test="enterpriseQuota.enterpriseName != null and enterpriseQuota.enterpriseName != ''">
                and bu.corp_name like concat('%',#{enterpriseQuota.enterpriseName},'%')
            </if>
            <if test="enterpriseQuota.capitalName != null and enterpriseQuota.capitalName != ''">
                and bd.dept_name like concat('%',#{enterpriseQuota.capitalName},'%')
            </if>
        </where>
    </select>

    <select id="selectExportList" resultType="org.springblade.customer.vo.EnterpriseQuotaVO">
        select
        <include refid="enterpriseQuotaColumn"/>
        bu.name as enterprise_name,bu.avatar
        from jrzh_enterprise_quota jeq
        inner join blade_dept bd on jeq.capital_id = bd.id
        inner join blade_user bu on jeq.enterprise_id = bu.id
        <where>
            jeq.enterprise_type = 1 and jeq.is_deleted = 0
            <if test="enterpriseQuota.taskNoEqual != null and enterpriseQuota.taskNoEqual != ''">
                and jeq.task_no = #{enterpriseQuota.taskNoEqual}
            </if>
            <if test="enterpriseQuota.statusEqual != null and enterpriseQuota.statusEqual != ''">
                and jeq.status = #{enterpriseQuota.statusEqual}
            </if>
            <if test="enterpriseQuota.enterpriseName != null and enterpriseQuota.enterpriseName != ''">
                and bu.name like concat('%',#{enterpriseQuota.enterpriseName},'%')
            </if>
            <if test="enterpriseQuota.capitalName != null and enterpriseQuota.capitalName != ''">
                and bd.dept_name like concat('%',#{enterpriseQuota.capitalName},'%')
            </if>
        </where>
        order by jeq.update_time desc
    </select>

    <select id="selectEnterpriseQuotaNewList" resultType="org.springblade.customer.vo.EnterpriseQuotaNewVo">
        select credit_amount, effective_time, cus.corp_name as user_name
        from jrzh_enterprise_quota quota
                 left join jrzh_customer_info cus on quota.enterprise_id = cus.company_id
        where quota.is_deleted = 0
          and quota.status = 2
        order by effective_time DESC LIMIT 0, 10
    </select>

</mapper>
