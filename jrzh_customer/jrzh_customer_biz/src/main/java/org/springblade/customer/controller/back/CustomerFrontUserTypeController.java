/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.controller.back;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.annotation.UsualLog;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.dto.FinanceCustomerDto;
import org.springblade.customer.entity.CustomerFrontUserType;
import org.springblade.customer.entity.CustomerPersonInfo;
import org.springblade.customer.excel.FinanceCustomerExcel;
import org.springblade.customer.service.ICustomerFrontUserTypeService;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.vo.*;
import org.springblade.customer.wrapper.CustomerFrontUserTypeWrapper;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


/**
 * 融资企业用户类型 控制器
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_BACK + "/customer/customerFrontUserType")
@Api(value = "融资企业用户类型", tags = "融资企业用户类型接口")
public class CustomerFrontUserTypeController extends BladeController {

	private final ICustomerFrontUserTypeService customerFrontUserTypeService;

	private final ICustomerInfoService customerInfoService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:detail') or hasRole('administrator')")
	public R<CustomerFrontUserTypeVO> detail(CustomerFrontUserType customerFrontUserType) {
		CustomerFrontUserType detail = customerFrontUserTypeService.getOne(Condition.getQueryWrapper(customerFrontUserType));
		return R.data(CustomerFrontUserTypeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 融资企业用户类型
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:list') or hasRole('administrator')")
	public R<IPage<CustomerFrontUserTypeVO>> list(CustomerFrontUserType customerFrontUserType, Query query) {
		IPage<CustomerFrontUserType> pages = customerFrontUserTypeService.page(Condition.getPage(query), Condition.getQueryWrapper(customerFrontUserType));
		return R.data(CustomerFrontUserTypeWrapper.build().pageVO(pages));
	}

	@GetMapping("/customerFrontUserList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "")
	@PreAuth("hasPermission('customer:customerFrontUserType:CustomerFrontUserList') or hasRole('administrator')")
	public R<IPage<CustomerFrontUserTypeBackVO>> customerFrontUserList(CustomerFrontUserType customerFrontUserTypeDTO, Query query,Integer isCoreOrFront) {
		IPage<CustomerFrontUserTypeBackVO> pages = customerFrontUserTypeService.customerFrontUserList(customerFrontUserTypeDTO,query,isCoreOrFront);
		return R.data(pages);
	}

	@GetMapping("/financeCustomerList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "")
	@PreAuth("hasPermission('customer:customerFrontUserType:CustomerFrontUserList') or hasRole('administrator')")
	public R<IPage<FinanceCustomerVo>> financeCustomerList(FinanceCustomerDto financeCustomerDto, Query query, Integer isCoreOrFront) {
		return R.data(customerInfoService.selectFinanceCustomerPage(Condition.getPage(query),financeCustomerDto));
	}

	/**
	 * 自定义分页 融资企业用户类型
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:page') or hasRole('administrator')")
	public R<IPage<CustomerFrontUserTypeVO>> page(CustomerFrontUserTypeVO customerFrontUserType, Query query) {
		IPage<CustomerFrontUserTypeVO> pages = customerFrontUserTypeService.selectCustomerFrontUserTypePage(Condition.getPage(query), customerFrontUserType);
		return R.data(pages);
	}

	/**
	 * 新增 融资企业用户类型
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:save') or hasRole('administrator')")
	public R<Boolean> save(@Valid @RequestBody CustomerFrontUserType customerFrontUserType) {
		return R.status(customerFrontUserTypeService.save(customerFrontUserType));
	}

	/**
	 * 修改 融资企业用户类型
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:update') or hasRole('administrator')")
	public R<Boolean> update(@Valid @RequestBody CustomerFrontUserType customerFrontUserType) {
		return R.status(customerFrontUserTypeService.updateById(customerFrontUserType));
	}

	/**
	 * 新增或修改 融资企业用户类型
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入customerFrontUserType")
   @PreAuth( "hasPermission('customer:customerFrontUserType:submit') or hasRole('administrator')")
	public R<Boolean> submit(@Valid @RequestBody CustomerFrontUserType customerFrontUserType) {
		return R.status(customerFrontUserTypeService.saveOrUpdate(customerFrontUserType));
	}


	/**
	 * 删除 融资企业用户类型
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
   @PreAuth( "hasPermission('customer:customerFrontUserType:remove') or hasRole('administrator')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(customerFrontUserTypeService.deleteLogic(Func.toLongList(ids)));
	}


	@UsualLog("查询当前企业的所有用户")
	@GetMapping("/currentSelectCustomer")
	@ApiOperation(value = "查询当前企业的所有用户",notes = "companyId 企业ID")
	@PreAuth("hasPermission('customer:customerFrontUserType:currentSelectCustomer') or hasRole('administrator')")
	public  R<IPage<CustomerFrontUserBackTypeVO>>  queryTypeCustomer(String companyId, Query query){
		IPage<CustomerFrontUserBackTypeVO> pages = customerFrontUserTypeService.queryTypeCustomerToPage(Long.valueOf(companyId),Condition.getPage(query));
		return R.data(pages);
	}


	@UsualLog("查询当前企业的个人用户")
	@GetMapping("/personalDetails")
	@ApiOperation(value = "查询当前企业的所有用户",notes = "companyId 企业ID")
	@PreAuth("hasPermission('customer:customerFrontUserType:personalDetails') or hasRole('administrator')")
	public  R<CustomerFrontUserTypePersonalVO>   personalDetails(Long id){
		CustomerFrontUserTypePersonalVO customerFrontUserTypeVO = customerFrontUserTypeService.personalDetails(id);
		return R.data(customerFrontUserTypeVO);
	}

	@UsualLog("查询当前企业的个人用户")
	@GetMapping("/getCustomerPersonInfo")
	@ApiOperation(value = "查询当前企业的所有用户",notes = "companyId 企业ID")
	@PreAuth("hasPermission('customer:customerFrontUserType:personalDetails') or hasRole('administrator')")
	public  R<CustomerPersonInfo>   getCustomerPersonInfo(Long companyId){
		//CustomerFrontUserTypePersonalVO customerFrontUserTypeVO = customerFrontUserTypeService.personalDetails(id);
		CustomerPersonInfo customerPersonInfo=	customerFrontUserTypeService.getCustomerPersonInfo(companyId);

		return R.data(customerPersonInfo);
	}

	/**
	 * 融资企业导出
	 * @param response
	 * @param financeCustomerDto
	 */
	@GetMapping("/export")
	@PreAuth("hasPermission('customer:customerFrontUserType:export') or hasRole('administrator')")
	public void export(HttpServletResponse response, FinanceCustomerDto financeCustomerDto) {
		List<FinanceCustomerVo> voList = customerInfoService.selectFinanceCustomerList(financeCustomerDto);
		List<FinanceCustomerExcel> list = BeanUtil.copy(voList, FinanceCustomerExcel.class);
		ExcelUtil.export(response, "融资企业" + DateUtil.time(), "融资企业", list, FinanceCustomerExcel.class);
	}

}
