/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springblade.customer.dto.FinanceCustomerDto;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.vo.CustomerInfoVO;
import org.springblade.customer.vo.FinanceCustomerVo;

import java.util.List;


/**
 * 企业信息名称 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
public interface CustomerInfoMapper extends BaseMapper<CustomerInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customerInfo
	 * @return
	 */
	List<CustomerInfoVO> selectCustomerInfoPage(IPage page, CustomerInfoVO customerInfo);

	/**
	 * 物理删除
	 *
	 * @param customerId 客户实名信息
	 * @return
	 */
	@Delete("delete from jrzh_customer_info where customer_id=#{customerId} and enter_status=1")
	Boolean deleteRealByCustomerId(Long customerId);
	/**
	 *
	 */
	@Delete("delete from jrzh_customer_info where company_id=#{companyId}")
	Boolean deleteRealByCompanyId(Long companyId);

	List<FinanceCustomerVo> selectFinanceCustomerList(@Param("financeCustomer") FinanceCustomerDto financeCustomerDto);

	/**
	 * 融资企业信息分页查询
	 * <AUTHOR>
	 * @date 2025/4/25 10:54
	 * @param page
	 * @param financeCustomerDto
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<org.springblade.customer.vo.FinanceCustomerVo>
	 */
	IPage<FinanceCustomerVo> selectFinanceCustomerPage(IPage page, @Param("financeCustomer") FinanceCustomerDto financeCustomerDto);

}
