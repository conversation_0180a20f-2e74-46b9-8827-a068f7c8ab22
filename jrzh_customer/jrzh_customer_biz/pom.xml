<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh_customer</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>jrzh_customer_biz</artifactId>
    <version>3.3.0-SNAPSHOT</version>
    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_system_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_resource</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_other_api_huawei</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_auth</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade.customer</groupId>
            <artifactId>jrzh_customer_base</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springblade</groupId>-->
        <!--            <artifactId>jrzh_flow_process_core</artifactId>-->
        <!--            <version>3.3.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_product_process</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_business_info</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_auth</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_auth</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_other_api_risk_order_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_flow_process_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_external_interface</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_message</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_contract_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_riskmana_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_contract_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_feign_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_loan_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_api</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade.ledger</groupId>
            <artifactId>jrzh_expense_ledger</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_other_paymanager</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <!-- 报表 -->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-report</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_jimu</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_aigc_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_other_api_ali</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springblade</groupId>-->
        <!--            <artifactId>jrzh_procurement_product</artifactId>-->
        <!--            <version>3.3.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.springblade</groupId>-->
        <!--            <artifactId>jrzh_receivable_product</artifactId>-->
        <!--            <version>3.3.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.springblade</groupId>-->
        <!--            <artifactId>jrzh_procurement_redeem_interface</artifactId>-->
        <!--            <version>3.3.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
    </dependencies>

</project>