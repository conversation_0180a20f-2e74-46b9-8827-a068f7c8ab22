<template>
  <div class="VFormRenderP-box">
    <basic-container v-if="arrData.length" style="padding-top: 10px">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="collapseVform">
          <!-- title的solt -->
          <template slot="title">
            <div class="fromHeader">
              <div class="fromLeft">
                <i
                  class="el-icon-caret-bottom"
                  :class="{
                    'i-active': activeType,
                  }"
                />
                <h1 class="fromLeft-title">
                  <span>{{ titleText }}</span>
                </h1>
              </div>
            </div>
          </template>
          <div class="father-box">
            <div v-for="item in arrData" :key="item.id" class="my-vform-box">
              <div class="table-name-box">
                <SvgIcon
                  icon-class="icon-a-1"
                  style="fill: #000; font-size: 20px"
                />
                {{ item.tableName }}
              </div>
              <v-form-render
                ref="vFormRef"
                :form-json="item.formJson"
                :form-data="item.formData"
                :option-data="item.optionData"
              />
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </basic-container>
  </div>
</template>

<script>
export default {
  name: 'VFormRenderP',
  components: {},
  props: {
    vFormDataObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    titleText: {
      type: String,
      required: false,
      default: '融资客户提交信息',
    },
    isDefaultExpansion: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      arrData: [],
      lock: false,
      vFormRef: [],
      activeNames: [],
      activeType: false,
    }
  },
  watch: {
    // 渲染数据处理
    vFormDataObj: {
      handler(objs) {
        if (objs.jsonData && !this.lock) {
          // if (JSON.stringify(objs) !== '{}') {
          this.lock = true
          // 清空vform表单的el存储
          this.vFormRef = []
          const backfillArr = []
          const renderArr = []
          // vform填写信息提取
          for (const item of objs.valueData.businessProcessTaskData) {
            // 不等于基础页进入判断
            if (item.nodeType !== -1) {
              backfillArr.push(item)
            }
          }
          for (const item of objs.jsonData) {
            // 不等于基础页进入判断
            if (item.nodeType !== 1) {
              const fIndex = backfillArr.findIndex(
                fItem => fItem.nodeKey === item.keyName
              )
              if (fIndex !== -1) {
                renderArr.push({
                  id: item.id,
                  tableName: item.tableName,
                  formJson: JSON.parse(item.content),
                  formData: backfillArr[fIndex].variable,
                  optionData: {},
                })
                backfillArr.splice(fIndex, 1)
              } else {
                renderArr.push({
                  id: item.id,
                  tableName: item.tableName,
                  formJson: JSON.parse(item.content),
                  formData: {},
                  optionData: {},
                })
              }
            }
          }
          this.arrData = renderArr
        }
      },
      immediate: true,
      deep: false,
    },
    // 折叠面板是否默认展开watch
    isDefaultExpansion: {
      handler(val) {
        if (val) {
          this.activeType = true
          this.activeNames = ['collapseVform']
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 循环将vform表单禁止编辑
    const vFormRefArr = this.$refs.vFormRef
    if (vFormRefArr && vFormRefArr.length) {
      for (const item of this.$refs.vFormRef) {
        item.disableForm()
      }
    }
  },
  methods: {
    // 折叠面板收缩控制
    handleChange() {
      this.activeType = !this.activeType
    },
  },
}
</script>

<style lang="scss" scoped>
.VFormRenderP-box {
  .fromHeader {
    width: 100%;
    font-size: 16px;
    font-family: SourceHanSansSC-bold;
    background: #fff;
    color: rgba(16, 16, 16, 100);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fromLeft {
      display: flex;
      align-items: center;
      width: 60%;

      .fromLeft-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        display: flex;
        align-items: center;
        // .long-string {
        //   display: inline-block;
        //   width: 1px;
        //   height: 16px;
        //   line-height: 20px;
        //   background-color: rgba(215, 215, 215, 100);
        //   text-align: center;
        //   margin: 0 8px;
        // }

        // .interest-rate {
        //   color: rgba(125, 125, 125, 100);
        //   font-size: 14px;
        //   text-align: left;
        // }
      }

      .i-active {
        transform: rotate(0deg) !important;
      }
    }
  }

  .father-box {
    margin-top: 25px;

    .my-vform-box {
      margin-top: 37px;
      border-top: 1px dashed #000;
      padding-top: 30px;

      &:first-child {
        border-top: none;
        margin-top: 0;
        padding-top: 0;
      }

      .table-name-box {
        font-size: 22px;
        line-height: 32px;
        color: #0a1f44;
        font-weight: 500;
        margin-bottom: 16px;
      }
    }
  }

  // 更改折叠组件样式
  ::v-deep {
    i.el-icon-caret-bottom {
      font-size: 150%;
      transform: rotate(-90deg);
      transition: transform 0.4s;
    }
    i.el-collapse-item__arrow {
      display: none;
    }
    div.el-collapse-item__header {
      height: 15px;
      border-bottom: none;
    }
    div.el-collapse-item__content {
      padding-bottom: 0 !important;
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-card {
      border-radius: 8px;
    }
    .el-collapse-item__wrap {
      border-bottom: none;
    }
  }
}
</style>
