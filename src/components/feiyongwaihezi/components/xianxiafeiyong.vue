<template>
  <div class="xianxiafeiyong-box">
    <p class="parent-feiyong-name">
      <span>{{ chuanruObj.expenseTypeName }}</span>
      <template v-if="shifouzhanshibenjLixiC">
        <span class="baohanbenjinlixi-sp-t">(包含本金+利息)</span>
        <span class="baohanbenjinlixi-sp-j">{{ chuanruObj.exAmount | formatMoney }}</span>
      </template>
    </p>
    <template
      v-if="
        chuanruObj.expenseOrderDetail && chuanruObj.expenseOrderDetail.length
      "
    >
      <Feiyongliest :chuanruObj="chuanruObj" />
    </template>
    <!-- 费用控件 -->
    <Xianxiafukuankongjian
      ref="XianxiafukuankongjianRef"
      :chuanruObj="chuanruObj"
      :isLook="isLook"
    />
  </div>
</template>

<script>
import Feiyongliest from './feiyongliest.vue'
import Xianxiafukuankongjian from './xianxia-fukuan-kongjian.vue'
export default {
  name: 'xianxiafeiyong',
  components: { Feiyongliest, Xianxiafukuankongjian },
  props: {
    chuanruObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    isLook: {
      type: Boolean,
    },
  },
  data() {
    return {}
  },
  computed: {
    shifouzhanshibenjLixiC() {
      return (
        this.chuanruObj.expenseInfoType === 1 &&
        Number(this.chuanruObj.exAmount) > 0
      )
    },
  },
  methods: {
    jiaoyanfeiyongFun() {
      return this.$refs.XianxiafukuankongjianRef.checkFun()
    },
  },
}
</script>

<style lang="scss" scoped>
.xianxiafeiyong-box {
  .parent-feiyong-name {
    height: 22px;
    color: #7d7d7d;
    font-size: 14px;
    text-align: left;
    font-weight: bold;
  }

  .baohanbenjinlixi-sp-t {
    color: #031222;
    font-size: 16px;
    font-weight: bold;
    margin-left: 8px;
  }

  .baohanbenjinlixi-sp-j {
    color: #031222;
    font-size: 25px;
    font-weight: bold;
    margin-left: 8px;
  }
}
</style>
