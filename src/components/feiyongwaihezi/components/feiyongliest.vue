<template>
  <div class="feiyongliest-box">
    <div class="table-top refund">
      <el-table
        ref="gongyongfeiyongTableRef"
        :data="chuanruObj.expenseOrderDetail"
        show-summary
        :summary-method="gongyongfeiyongTableSummaries"
        class="table-border-style"
        style="width: 100%; margin-top: 13px"
      >
        <el-table-column
          prop="name"
          label="费用名称"
          v-if="quanxianObj.fei_name"
        >
        </el-table-column>
        <el-table-column
          prop="feeNameType"
          label="费用类型"
          v-if="quanxianObj.fei_feeNameType"
        >
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.feeNameType }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="feeNodeStr"
          label="支付节点"
          v-if="quanxianObj.fei_feeNodeStr"
        >
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.feeNodeStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="collectFeesNodeStr"
          label="收费节点"
          v-if="quanxianObj.fei_collectFeesNodeStr"
        >
          <template slot-scope="scope">
            <span class="border-box">
              {{ scope.row.collectFeesNodeStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="feeFormulaName"
          label="计费方式"
          v-if="quanxianObj.fei_feeFormulaName"
        >
        </el-table-column>
        <el-table-column
          prop="amount"
          label="应付金额"
          v-if="quanxianObj.fei_amount"
        >
          <template slot-scope="scope">
            <span>￥{{ scope.row.amount | formatMoney }} </span>
            <!-- <div v-else style="width: 80%">
                      <el-input
                        placeholder="请输入金额"
                        v-model="scope.row.amount"
                        type="number"
                        :disabled="
                          !costPlatformList.costPlatform_amountPayable_w
                        "
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </div> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { formatMoney } from '@/util/filter.js'

export default {
  name: 'feiyongliest',
  components: {},
  inject: ['quanxianObj'],
  props: {
    chuanruObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    // el-table表格合计功能
    gongyongfeiyongTableSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 1) return
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
.feiyongliest-box {
  .border-box {
    color: #000000b5;
    background-color: #e9ebf0;
    border-radius: 100px;
    font-size: 14px;
    padding: 3px 12px;
    box-sizing: border-box;
  }

  // 更改表格组件样式
  ::v-deep {
    .table-top {
      .el-table th.el-table__cell {
        background-color: #f7f7f7;
      }

      .el-table th.el-table__cell > .cell {
        color: rgba(0, 0, 0, 100);
        font-size: 14px;
      }

      .el-table__footer-wrapper tbody td.el-table__cell {
        font-size: 20px;
        background-color: #f7f7f7;
        font-weight: 600;
      }

      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: transparent;
      }
    }

    .refund {
      .el-table th.el-table__cell {
        background-color: #fff1f1;
      }
    }

    // .el-input-group__append {
    //   color: #000;
    // }
  }
}
</style>
