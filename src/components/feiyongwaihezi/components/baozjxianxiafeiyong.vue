<template>
  <div class="baozj-xianxiafeiyong-box">
    <p class="parent-feiyong-name">{{ chuanruObj.expenseTypeName }}</p>
    <BaozjList :chuanruObj="chuanruObj" />
    <!-- 费用控件 -->
    <Xianxiafukuankongjian
      ref="XianxiafukuankongjianRef"
      :chuanruObj="chuanruObj"
      :isLook="isLook"
    />
  </div>
</template>

<script>
import BaozjList from './baozjList.vue'
import Xianxiafukuankongjian from './xianxia-fukuan-kongjian.vue'
export default {
  name: 'baozjxianxiafeiyong',
  components: { BaozjList, Xianxiafukuankongjian },
  props: {
    chuanruObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    isLook: {
      type: Boolean,
    },
  },
  data() {
    return {}
  },
  methods: {
    jiaoyanfeiyongFun() {
      return this.$refs.XianxiafukuankongjianRef.checkFun()
    },
  },
}
</script>

<style lang="scss" scoped>
.baozj-xianxiafeiyong-box {
  .parent-feiyong-name {
    height: 22px;
    color: #7d7d7d;
    font-size: 14px;
    text-align: left;
    font-weight: bold;
  }
}
</style>
