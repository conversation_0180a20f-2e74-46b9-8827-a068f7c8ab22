<template>
  <div class="xianshangfeiyong-box">
    <p class="parent-feiyong-name">
      <span>{{ chuanruObj.expenseTypeName }}</span>
      <template v-if="shifouzhanshibenjLixiC">
        <span class="baohanbenjinlixi-sp-t">(包含本金+利息)</span>
        <span class="baohanbenjinlixi-sp-j">{{
          chuanruObj.exAmount | formatMoney
        }}</span>
      </template>
    </p>
    <template
      v-if="
        chuanruObj.expenseOrderDetail && chuanruObj.expenseOrderDetail.length
      "
    >
      <Feiyongliest :chuanruObj="chuanruObj" />
    </template>
    <!-- 订单状态 -->
    <div class="pay-box">
      <div v-if="quanxianObj.fei_orderStatus" class="order-box">
        <span class="order-status">订单状态</span>
        <span class="order-text">
          {{ chuanruObj.paymentStatus === 2 ? '已付款' : '未付款' }}
        </span>

        <div class="informition-box">
          <span>
            支付方式：
            <span class="informition-box_bold">
              {{ chuanruObj.paymentMethodStr }}
            </span>
          </span>
          <span>
            付款金额：
            <span class="informition-box_bold">
              ￥{{ chuanruObj.totalAmount | formatMoney }}
            </span>
          </span>
          <span>
            支付流水号：
            <span class="informition-box_bold">
              {{ chuanruObj.billPaySerialNo }}
            </span>
          </span>
          <span>
            支付时间：
            <span class="informition-box_bold">
              {{ chuanruObj.payTime }}
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Feiyongliest from './feiyongliest.vue'
export default {
  name: 'xianshangfeiyong',
  components: { Feiyongliest },
  inject: ['quanxianObj'],
  props: {
    chuanruObj: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    shifouzhanshibenjLixiC() {
      return (
        this.chuanruObj.expenseInfoType === 1 &&
        Number(this.chuanruObj.exAmount) > 0
      )
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.xianshangfeiyong-box {
  margin-bottom: 75px;

  .parent-feiyong-name {
    height: 22px;
    color: #7d7d7d;
    font-size: 14px;
    text-align: left;
    font-weight: bold;

    .baohanbenjinlixi-sp-t {
      color: #031222;
      font-size: 16px;
      font-weight: bold;
      margin-left: 8px;
    }

    .baohanbenjinlixi-sp-j {
      color: #031222;
      font-size: 25px;
      font-weight: bold;
      margin-left: 8px;
    }
  }

  .pay-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;

    .order-box {
      display: flex;
      align-items: center;
      cursor: context-menu;

      .order-status {
        width: 56px;
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
        margin-right: 8px;
      }

      .order-text {
        height: 30px;
        border-radius: 28px;
        font-size: 14px;
        font-family: Microsoft Yahei;
        padding: 4px 8px;
        box-sizing: border-box;
        margin-right: 12px;

        color: #09c067;
        background-color: #ddf0e7;
      }

      .order-modification-btn {
        height: 22px;
        color: rgba(105, 124, 255, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-bold;
        cursor: pointer;
      }

      .order-btn {
        width: 28px;
        height: 22px;
        color: rgba(105, 124, 255, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-bold;
        cursor: pointer;
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }

      .informition-box {
        margin-left: 10px;
        color: #000000b5;
        font-size: 16px;

        & > * {
          margin-left: 70px;

          &:first-child {
            margin-left: 0;
          }
        }

        &_bold {
          font-weight: bold;
        }
      }
    }

    .payOrder-box {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      span {
        width: 103px;
        height: 30px;
        border-radius: 4px;
        color: rgba(105, 124, 255, 100);
        font-size: 14px;
        text-align: center;
        border: 1px solid rgba(105, 124, 255, 100);
        margin-right: 10px;
        padding: 2px 4px;
        box-sizing: border-box;
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }
      }

      .payOrder-left-box {
        margin-right: 10px;
      }
    }
  }
}
</style>
