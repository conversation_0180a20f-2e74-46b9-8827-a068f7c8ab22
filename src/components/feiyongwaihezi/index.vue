<template>
  <div class="feiyongwaihezi-box">
    <template v-if="erGaiShuJuArr && erGaiShuJuArr.length">
      <div v-for="item in erGaiShuJuArr" :key="item.billExpenseNo">
        <!-- 线上支付 -->
        <Xianshangfeiyong
          v-if="item.expenseInfoType !== 3 && item.payMode === 2"
          :chuanruObj="item"
        />
        <!-- 线下支付-不需要银行清分 -->
        <Xianxiafeiyong
          v-else-if="
            item.expenseInfoType !== 3 &&
            item.payMode === 1 &&
            item.expenseChargeMethod === 2
          "
          :ref="`${item.billExpenseNo}_xianxiaRef`"
          :chuanruObj="item"
          :isLook="isLook"
        />
        <!-- 保证金线下支付 -->
        <Baozjxianxiafeiyong
          v-else-if="item.expenseInfoType === 3 && item.payMode === 1"
          :ref="`${item.billExpenseNo}_xianxiaRef`"
          :chuanruObj="item"
          :isLook="isLook"
        />
      </div>
    </template>
    <template v-else>
      <p class="wuxujiaofei-p">无需缴纳费用</p>
    </template>
  </div>
</template>

<script>
import Xianshangfeiyong from './components/xianshangfeiyong.vue'
import Xianxiafeiyong from './components/xianxiafeiyong.vue'
import Baozjxianxiafeiyong from './components/baozjxianxiafeiyong.vue'
import func from '@/util/func'
export default {
  name: 'feiyongwaihezi',
  components: {
    Xianshangfeiyong,
    Xianxiafeiyong,
    Baozjxianxiafeiyong,
  },
  props: {
    // 支付方式 1线下 2 线上 3银联 4 银联线下
    feiyongList: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    quanxianObj: {
      type: Object,
      required: true,
      default: () => {
        return {
          fei_name: true,
          fei_feeNameType: true,
          fei_feeNodeStr: true,
          fei_collectFeesNodeStr: true,
          fei_feeFormulaName: true,
          fei_amount: true,
          fei_orderStatus: true,
          fei_refundTypeStr: true,
        }
      },
    },
    isLook: {
      type: Boolean,
      default: false,
    },
  },
  // 将权限传入孙子组件使用
  provide() {
    return {
      quanxianObj: this.quanxianObj,
    }
  },
  data() {
    return {
      cunChuXianXiaShuju: {},
      erGaiShuJuArr: [],
      // getPayType: {
      //   1: '待支付',
      //   2: '已支付',
      //   3: '已关闭',
      //   4: '已退款',
      //   5: '待清分',
      //   6: '已取消',
      //   7: '待退款',
      // },
    }
  },
  watch: {
    feiyongList: {
      handler(val) {
        if (val && val.length) {
          for (const item of val) {
            // 保证金处理
            let expenseDepositData = undefined

            if (
              item.expenseInfoType === 3 &&
              item.expenseDeposit &&
              !func.isEmptyObject(item.expenseDeposit)
            ) {
              expenseDepositData = [
                {
                  feeNodeStr: item.feeNodeStr,
                  amount: item.totalAmount,
                  name:
                    item.expenseDeposit.cashDepositType === '1'
                      ? '初始保证金'
                      : '追加保证金',
                  feeFormulaName:
                    '融资金额*保证金比例(' +
                    item.expenseDeposit.cashDepositRate +
                    '%)',
                  refundTypeStr:
                    item.expenseDeposit.refundType === '1'
                      ? '同还款比例等比释放'
                      : '信贷结清后退还',
                },
              ]
            }
            item.expenseDepositData = expenseDepositData
            if (item.expenseInfoType === 3) {
              item.paymentStatus = item.paymentStatus || 1
            }
          }
          this.erGaiShuJuArr = val
        }
      },
      // deep: true,
      immediate: true,
    },
  },
  methods: {
    // 无异常就进行存储对应线下数据
    tongyicunshujuObj(jId, valObj) {
      switch (valObj.paymentStatus) {
        case 1:
          // 待支付
          this.cunChuXianXiaShuju[jId + '_shu'] = {
            paymentStatus: valObj.paymentStatus,
          }
          break
        case 3:
          // 支付失败
          this.cunChuXianXiaShuju[jId + '_shu'] = {
            failReason: valObj.failReason,
            paymentStatus: valObj.paymentStatus,
          }
          break
        case 2:
          // 已付款
          this.cunChuXianXiaShuju[jId + '_shu'] = {
            payAccountNo: valObj.payAccountNo,
            payOpenBank: valObj.payOpenBank,
            payTime: valObj.payTime,
            paymentStatus: valObj.paymentStatus,
            totalAmount: valObj.ziAmount,
            payAmount: valObj.ziAmount,
          }
          break
      }
    },
    // 同步处理多个线下费用返回的promise,进行校验
    tongyideProFun(chuanruArr, msg) {
      return Promise.all(chuanruArr)
        .then(() => {
          // 将数据融入原数组
          this.feiyongList.forEach(item => {
            const cunzaishujuma =
              this.cunChuXianXiaShuju[item.billExpenseNo + '_shu']
            if (cunzaishujuma) {
              item = Object.assign(item, cunzaishujuma)
            }
          })
          return true
        })
        .catch(err => {
          if (err === 'shibai1Msg') {
            this.$message.warning(msg)
          } else {
            this.$message.warning('请检查表单是否完成填写')
          }
          return false
        })
    },
    // 异步处理线下费用的校验
    chufajiaoyanFun(thisItem, faIsTr) {
      const sunzigeidedaijiaoyanshuju =
        this.$refs[
          thisItem.billExpenseNo + '_xianxiaRef'
        ][0].jiaoyanfeiyongFun()

      return new Promise((resolve, reject) => {
        // 能直接判断失败的条件
        // 待付款并且属于通过与终止 或者 已付款并且属于驳回 或者 支付失败属于通过
        const changPanDuan =
          (!sunzigeidedaijiaoyanshuju.jiaoyanF && faIsTr !== 'B') ||
          (sunzigeidedaijiaoyanshuju.shuju.paymentStatus === 2 &&
            faIsTr === 'B') ||
          (sunzigeidedaijiaoyanshuju.shuju.paymentStatus === 3 &&
            faIsTr === 'T')

        // 待付款并且属于驳回
        if (!sunzigeidedaijiaoyanshuju.jiaoyanF && faIsTr === 'B') {
          this.tongyicunshujuObj(
            thisItem.billExpenseNo,
            sunzigeidedaijiaoyanshuju.shuju
          )
          resolve()
          return
        } else if (changPanDuan) {
          reject('shibai1Msg')
          return
        }
        sunzigeidedaijiaoyanshuju.jiaoyanF.validate((valid, done) => {
          done()
          if (valid) {
            this.tongyicunshujuObj(
              thisItem.billExpenseNo,
              sunzigeidedaijiaoyanshuju.shuju
            )
            resolve()
          } else {
            reject('shibai2Msg')
          }
        })
      })
    },
    // 通过
    async tongguojiaoyanFun() {
      const proCunChu = []
      for (const item of this.feiyongList) {
        if (item.payMode === 1) {
          proCunChu.push(this.chufajiaoyanFun(item, 'T'))
        } else if (item.payMode === 2 && item.paymentStatus === 1) {
          this.$message.warning('请检查线上费用状态是否正常')
          return false
        }
      }

      const callBFun = await this.tongyideProFun(
        proCunChu,
        '请检查线下费用状态是否为已付款'
      )
      if (callBFun) {
        return this.feiyongList
      } else {
        return false
      }
    },
    // 驳回
    async bohuijiaoyanFun() {
      const proCunChu = []
      for (const item of this.feiyongList) {
        if (item.payMode === 1) {
          proCunChu.push(this.chufajiaoyanFun(item, 'B'))
        } else if (item.payMode === 2 && item.paymentStatus === 2) {
          this.$message.warning('存在已付款的线上费用,只能进行完成或终止')
          return false
        }
      }

      const callBFun = await this.tongyideProFun(
        proCunChu,
        '存在已付款的线下费用,只能进行完成或终止'
      )
      if (callBFun) {
        return this.feiyongList
      } else {
        return false
      }
    },
    // 终止
    async zhongzhijiaoyanFun() {
      const proCunChu = []
      for (const item of this.feiyongList) {
        if (item.payMode === 1) {
          proCunChu.push(this.chufajiaoyanFun(item, 'Z'))
        } else if (item.payMode === 2 && item.paymentStatus === 1) {
          this.$message.warning('请检查线上费用状态是否正常')
          return false
        }
      }

      const callBFun = await this.tongyideProFun(
        proCunChu,
        '存在待付款的线下费用,请修改状态后在进行终止'
      )
      if (callBFun) {
        return this.feiyongList
      } else {
        return false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.feiyongwaihezi-box {
  .wuxujiaofei-p {
    margin: 15px 10px 0;
    font-size: 15px;
  }
}
</style>
