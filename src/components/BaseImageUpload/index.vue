<template>
  <div class="base-image-upload">
    <div class="base-image-item" v-for="item in allImgData" :key="item.id">
      <img :src="item.imgUrl" />
      <div class="base-image-item-disabled" v-if="disabled"></div>
      <div class="base-image-item-mask">
        <div class="base-image-item-icon">
          <i
            @click="handleZoom(item.link)"
            class="el-icon-zoom-in"
            style="margin-right: 10px"
          ></i>
          <i
            v-if="!disabled"
            @click="handleDelete(item.link)"
            class="el-icon-delete"
          ></i>
        </div>
      </div>
    </div>
    <!-- 设置一个当没有图片的时候，默认样式 -->
    <div class="base-image-item" v-if="allImgData.length === 0 && disabled">
      <div class="base-image-item-disabled" v-if="disabled">
        <i class="el-icon-picture-outline"></i>
      </div>
    </div>

    <el-upload
      ref="upload"
      class="avatar-uploader"
      v-if="allImgData.length < length && !disabled"
      :headers="{ 'Blade-Auth': Basic }"
      action="/api/blade-resource/oss/endpoint/put-file-kv"
      list-type="picture"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
    >
      <i class="el-icon-plus avatar-uploader-icon"></i
    ></el-upload>
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import FilePreview from '@/components/file-preview'
import { getToken } from '@/util/auth'

export default {
  components: { FilePreview },
  data() {
    return {
      Basic: '',
      pdfSrc: '', // pdf文件入径
      allImgData: [],
    }
  },
  props: {
    imgData: {
      // 由父组件传递的图片数据
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    length: {
      type: Number,
      default: 6,
    },
  },
  watch: {
    imgData: {
      handler(val) {
        this.getBasicImgBg(val)
      },
      immediate: true,
    },
  },
  created() {
    this.Basic = 'bearer ' + getToken()
  },
  methods: {
    // 预览图片
    handleZoom(imgUrl) {
      if (imgUrl.includes('pdf')) {
        this.pdfSrc = imgUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: imgUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    handleDelete(imgUrl) {
      let imgObj = this.allImgData.find(item => item.link === imgUrl) // 扎到当前删除的对象元素
      this.allImgData.splice(this.allImgData.indexOf(imgObj), 1)
      this.$emit('update:imgData', this.allImgData)
    },
    // 处理pdf的背景
    getBasicImgBg(data) {
      this.allImgData = []
      if (data.length) {
        data.forEach(item => {
          item.imgUrl = this.isImagePdf(item.link)
          this.allImgData.push(item)
        })
      }
    },
    // 上传成功图片操作
    handleAvatarSuccess(res) {
      const list = {
        id: res.data.attachId,
        link: res.data.url,
      }
      this.allImgData.push(list)
      this.$emit('update:imgData', this.allImgData)
    },
    // 限制格式、大小
    beforeAvatarUpload(file) {
      const type = file.type
      const islt500kb = file.size / 1024 > 1024 * 2
      if (['image/png', 'image/jpeg', 'application/pdf'].includes(type)) {
        if (islt500kb) {
          this.$message.error('文件大小不能超过2M')
          return false
        }
      } else {
        this.$message.error('文件格式错误')
        return false
      }
    },
    // 判断图片是否为pdf

    isImagePdf(imgUrl) {
      return imgUrl.includes('pdf')
        ? require('@/assets/pdf_default.png')
        : imgUrl
    },
  },
}
</script>

<style lang="scss" scoped>
.base-image-upload {
  display: flex;
  align-items: center;
  flex-flow: row wrap;
  .base-image-item {
    width: 120px;
    height: 120px;
    border: 1px solid #d7d7d7;
    position: relative;
    border-radius: 8px;
    margin-right: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    &:hover {
      .base-image-item-mask {
        display: block;
      }
    }
    img {
      width: 100%;
      height: 100%;
    }
    .base-image-item-mask {
      display: none;
      position: absolute;
      top: -1px;
      left: -1px;
      width: 100%;
      height: 100%;
      background: rgba(24, 44, 79, 0.5);
      box-sizing: border-box;
      .base-image-item-icon {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          cursor: pointer;
          font-size: 24px;
          color: #fff;
        }
      }
    }
    .base-image-item-disabled {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      opacity: 0.5;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  ::v-deep {
    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
    }
  }

  // ::v-deep {
  //   .el-upload--picture-card {
  //     width: 120px !important;
  //     height: 120px !important;
  //     line-height: 120px !important;
  //   }
  //   .el-upload-list__item {
  //     width: 120px;
  //     height: 120px;
  //     line-height: 120px;
  //   }
  // }
}
</style>
