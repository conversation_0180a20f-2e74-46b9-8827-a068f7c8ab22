<template>
  <div class="container-footer">
    <el-button
      v-for="item in btnOptions"
      :key="item.btnName"
      :type="item.type || ''"
      @click="handleEvent(item)"
    >
      {{ item.btnName }}
    </el-button>
  </div>
</template>

<script>
export default {
  props: {
    btnOptions: {
      // 按钮数组
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handleEvent(item) {
      this.$emit('click', item.funName)
    },
  },
}
</script>

<style lang="scss" scoped>
@media screen and(max-width: 992px) {
  .container-footer {
    width: 100%;
  }
}
@media screen and(min-width: 993px) {
  .container-footer {
    width: calc(100% - 240px);
  }
}

.container-footer {
  position: fixed;
  background: #fff;
  bottom: 0;
  height: 68px;
  line-height: 68px;
  z-index: 99999999;
  margin-left: -10px;
  border-radius: 4px 4px 0 0;
  box-shadow: 0px -2px 6px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  .el-button {
    padding: 7px 20px !important;
  }
}
</style>
