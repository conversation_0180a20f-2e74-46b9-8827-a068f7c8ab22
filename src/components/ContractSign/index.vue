<template>
  <div class="contract-sign-box">
    <el-dialog
      :visible.sync="dialogVisible"
      :modal="true"
      :fullscreen="isFullscreen"
      :modal-append-to-body="false"
      custom-class="pre-contract-dialog"
    >
      <div slot="title">
        <span class="el-dialog__title">合同签署</span>
        <div class="dialog__menu">
          <i @click="handleFullScreen" class="el-icon-full-screen"></i>
        </div>
      </div>

      <iframe
        v-if="dialogVisible && pdfUrl"
        :src="pdfUrl"
        frameborder="no"
        class="pdf-iframe"
      ></iframe>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="dialogLoading">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="signContractFun"
          :loading="dialogLoading"
        >
          签 署
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { skipToSign, skipToSignByDeptId } from '@/api/goods/pcontrol/workflow/productConfirmation'
export default {
  name: 'VueFilePreview',
  props: {
    signObj: {
      type: Object,
      required: true,
    },
    // 是否动产质押
    isPledgeMovables: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      pdfUrl: undefined,
      dialogLoading: false,
    }
  },
  watch: {
    signObj: {
      handler(newVal) {
        this.dialogVisible = true
        this.pdfUrl = 'cdn/pdfjs/web/viewer.html?file=' + newVal.pdfUrl
      },
      deep: true,
    },
  },
  methods: {
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen
    },
    // 签署合同
    signContractFun() {
      this.dialogLoading = true
      if (this.isPledgeMovables) {
        this.pledgeMovablessignContract()
      } else {
        this.signContract()
      }
    },
    // 云信合同签署
    signContract() {
      const dataD = {
        contractId: this.signObj.contractId,
        customizeWriteBase64: '',
        verifyType: '',
        code: {
          phone: '',
          value: '',
        },
        deptId: this.signObj.deptId,
      }
      skipToSign(dataD)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('合同签署成功')
            this.dialogLoading = false
            this.$emit('resetList')
            this.dialogVisible = false
            // console.log('data', data)
          }
        })
        .catch(() => {
          this.dialogLoading = false
        })
    },
    // 动产质押合同签署
    pledgeMovablessignContract() {
      const dataD = {
        contractId: this.signObj.contractId,
      }
      skipToSignByDeptId(dataD)
        .then(({ data }) => {
          if (data.success) {
            this.$message.success('合同签署成功')
            this.dialogLoading = false
            this.$emit('resetList')
            this.dialogVisible = false
          }
        })
        .catch(() => {
          this.dialogLoading = false
        })
    },
  },
}
</script>

<style lang="scss">
// ::v-deep .contract-sign-box {
.pre-contract-dialog {
  height: 84vh;
  margin-top: 8vh !important;
  border-radius: 4px;
}

.pre-contract-dialog .dialog__menu {
  float: right;
  padding-right: 24px;
  cursor: pointer;
}

.pre-contract-dialog .dialog__menu:hover {
  color: #409eff;
}

.pre-contract-dialog .el-dialog__body {
  height: calc(100% - 135px);
  padding: 0 10px 10px;
}

.pre-contract-dialog.is-fullscreen {
  margin-top: 0 !important;
  border-radius: 0 !important;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.pdf-iframe body,
.pdf-iframe .toolbarContainer {
  background-color: white;
}
// }
</style>
