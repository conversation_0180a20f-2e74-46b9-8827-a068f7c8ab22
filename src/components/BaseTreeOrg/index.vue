<template>
  <div style="width: 100%">
    <VueOkrTree
      :data="cloudList"
      show-collapsable
      default-expand-all
      :render-content="renderContent"
    ></VueOkrTree>
  </div>
</template>
<script>
import { formatMoney } from '@/util/filter'
import { VueOkrTree } from 'vue-okr-tree'
import Clipboard from 'clipboard'
import 'vue-okr-tree/dist/vue-okr-tree.css'
export default {
  components: { VueOkrTree },
  props: {
    cloudList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {}
  },
  methods: {
    renderContent(h, node) {
      const cls = ['diy-wrapper']
      if (node.isCurrent) {
        cls.push('current-select')
      }

      return (
        <div
          class={[
            'diy-wrapper',
            node.data.nextCloudCode && node.data.type != 0
              ? 'border-other'
              : 'border-init',
          ]}
        >
          <div
            class={['tree-content', node.data.content ? '' : 'display-none']}
          >
            <span>{node.data.content}</span>
          </div>
          <div class="cloud-item">
            <span>云信编号</span>
            <span class="cloud-num">
              {node.data.nextCloudCode || node.data.cloudCode}
            </span>
          </div>
          <div class="cloud-item">
            <span>
              {node.data.nextCloudCode
                ? node.data.type == 0
                  ? '转单方'
                  : '融资方'
                : '开单方'}
            </span>
            <span class="cloud-name">{node.data.billName}</span>
          </div>
          <div class="cloud-item">
            <span>收单方</span>
            <span class="cloud-name">{node.data.holdName}</span>
          </div>
          <div class="cloud-item">
            <span>金额</span>
            <span class="cloud-amount">{formatMoney(node.data.amount)}元</span>
          </div>
          <div class="cloud-item">
            <span>承诺付款日</span>
            <span class="cloud-date">{node.data.endDate}</span>
          </div>
          <div class="cloud-item">
            <span>上链哈希值</span>
            <span
              class="cloud-date"
              onClick={event =>
                this.handleCopy(event, node.data.transactionAddress)
              }
            >
              {node.data.transactionAddress}
            </span>
          </div>
        </div>
      )
    },

    handleCopy(event, value) {
      const copyValue = value
      const clipboard = new Clipboard(event.target, { text: () => copyValue })
      clipboard.on('success', () => {
        this.$message({ type: 'success', message: '复制成功' })
        // 释放内存
        clipboard.off('error')
        clipboard.off('success')
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        console.error(e)
        // 不支持复制
        this.$message({ type: 'waning', message: '该浏览器不支持自动复制' })
        // 释放内存
        clipboard.off('error')
        clipboard.off('success')
        clipboard.destroy()
      })
      clipboard.onClick(event)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .org-chart-node-children {
    display: flex;
    justify-content: space-between;
    .org-chart-node {
      width: 100%;
    }
  }
  .org-chart-node-label-inner {
    width: 346px !important;
    max-width: 346px !important;
    height: 290px !important;
    border-radius: 8px;
    overflow: hidden;
    padding: unset !important;
    box-sizing: border-box;
  }
  .diy-wrapper {
    width: 100%;
    height: 100%;
    border: 2px solid #e1e4e8;
    padding: 20px;
    border-radius: 8px;
    box-sizing: border-box;
  }

  .tree-content {
    text-align: center;
    width: 100%;
    height: 56px;
    box-sizing: border-box;
    border: 1px solid #efefef;
    background-color: #f8f9fb;
    border-radius: 6px;
    padding: 8px 12px;
    color: #8a94a6;
    font-weight: 400;
    font-size: 14px;
    & span {
      display: block;
      width: 100%;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }

  .display-none {
    display: none;
  }
  .border-init {
    border: 2px solid #e1e4e8;
  }
  .border-other {
    border: 2px solid #ffad0d;
  }

  .cloud-item {
    margin-top: 12px;
    display: flex;
    & span:first-child {
      display: block;
      width: 80px;
      line-height: 20px;
      text-align: left;
      color: #8a94a6;
      font-weight: 400;
      font-size: 14px;
      margin-right: 12px;
      white-space: nowrap;
    }
    & span:last-child {
      display: block;
      max-width: 180px;
      line-height: 20px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 14px;
    }
  }
  .cloud-num {
    font-weight: 400;
    font-family: SFProText-Regular, SFProText;
    color: #0d55cf;
  }
  .cloud-name {
    color: #0a1f44;
    font-weight: 600;
    font-family: PingFangSC-Semibold, PingFang SC;
  }
  .cloud-amount {
    color: #dd2727;
    font-size: bold;
    font-family: SFProText-Regular, SFProText;
  }
  .cloud-date {
    font-family: SFProText-Regular, SFProText;
    color: #0a1f44;
  }
}
</style>
