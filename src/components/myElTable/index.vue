<template>
  <div class="tableB-my-box">
    <div class="tableB-my-box_table-box">
      <!-- 解决el的折叠面板与table表格冲突的例子: this.$refs.tableB4.$children[0].$ready = false -->
      <el-table
        ref="tableMO"
        :data="tableData"
        style="width: 100%"
        :empty-text="emptyText"
        sum-text="合计:"
        class="table-border-style"
        :show-summary="showSummaryF"
        :summary-method="showSummaryF === 'trues' ? isGetSummaries : ''"
        :row-class-name="rowClassNameCondition ? tableRowClassName : ''"
      >
        <el-table-column
          v-for="item in columns"
          :key="item.id"
          :prop="item.iProp"
          :label="item.iLabel"
          :width="item.iWidth"
          :align="item.iAlign"
          :type="item.isType ? 'expand' : ''"
        >
          <template slot-scope="scope">
            <template v-if="item.isSlot">
              <!-- 插槽示例 -->
              <!-- <template #refundTime="{ iItem: { iProp }, iScope: { row } }">
                {{ row[iProp] }}
              </template> -->
              <slot
                :name="item.iProp"
                :iItem="item"
                :iScope="{
                  row: scope.row,
                  index: scope.$index,
                  column: scope.column,
                }"
              >
              </slot>
            </template>
            <!-- 将值使用过滤器进行转换 -->
            <div v-else-if="item.isMoneyFilter">
              {{ scope.row[item.iProp] | formatMoney }}
            </div>
            <div v-else>
              {{ scope.row[item.iProp] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import func from '@/util/func'

export default {
  name: 'tableB',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 表格列
    // isSlot: true, 可开启插槽
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 不使用组件自身合计栏，需要提共一个条件判断自定义总计栏
    // :rowClassNameCondition="{ rKey: 'refundTime', rValue: '总计:' }"
    rowClassNameCondition: {
      type: Boolean | Object,
      required: false,
      default: false,
    },
    // 是否启用组件自带合计（传入一个函数或任意有效值）
    // :isGetSummaries="getSummaries"
    isGetSummaries: {
      type: Boolean | Function,
      required: false,
      default: false,
    },
    emptyText: {
      type: String,
      required: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    // 是否启用组件自带合计（传入一个函数或任意有效值）
    showSummaryF() {
      // 没传：不启用
      if (!this.isGetSummaries) return false
      // 传了方法：启用传入的方法进行自定义合计
      if (func.isFunction(this.isGetSummaries)) {
        return 'trues'
      }
      // 传了有效值：启用组件自身合计
      return true
    },
  },
  methods: {
    tableRowClassName({ row }) {
      if (
        row[this.rowClassNameCondition.rKey] ===
        this.rowClassNameCondition.rValue
      ) {
        return 'tableB-total-column-row'
      }
      return ''
    },
  },
}
</script>

<style lang="scss" scoped>
$table-border-color: #e9ebf0; // 表格边框颜色
$table-header-bg: #f7f7f7; // 表头背景色
$table-header-color: rgba(0, 0, 0, 100); // 表头文字颜色
$table-header-font-size: 14px; // 表头文字大小
$table-hover-bg: #fbfbfb; // 鼠标hover时的背景色
$table-total-row-bg: #f5f7fa; // 总计行背景色
$table-total-row-color: #606266; // 总计行文字颜色
$table-total-row-font-size: 18px; // 总计行文字大小
$table-total-row-font-weight: bold; // 总计行文字加粗

.tableB-my-box {
  // 更改表格组件样式
  &_table-box {
    // 整个表格添加边框
    .table-border-style {
      border: 1px solid $table-border-color;
      border-bottom: none;
    }

    ::v-deep {
      .el-table {
        // 覆盖表头背景
        th.el-table__cell {
          background-color: $table-header-bg;
        }
        // 覆盖表头文字样式
        th.el-table__cell > .cell {
          color: $table-header-color;
          font-size: $table-header-font-size;
        }
        // 数字输入框样式覆盖
        .el-input-number .el-input__inner {
          text-align: left;
        }
        // 输入框禁用样式颜色覆盖
        .el-input.is-disabled .el-input__inner {
          color: #606266;
        }
      }

      // 鼠标hove时样式覆盖
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        // background-color: transparent;
        background-color: $table-hover-bg;
      }

      // 不使用组件自身合计栏，自定义总计栏样式
      .tableB-total-column-row {
        background-color: $table-total-row-bg;
        color: $table-total-row-color;
        font-weight: $table-total-row-font-weight;
        font-size: $table-total-row-font-size;

        // 避免被hove样式修改掉
        td.el-table__cell {
          background-color: $table-total-row-bg !important;
        }
      }

      // 覆盖自带默认的总计样式
      .el-table__footer-wrapper tbody td.el-table__cell {
        font-size: $table-total-row-font-size;
        font-weight: $table-total-row-font-weight;
      }
    }
  }
  // 覆盖表头背景（橘红色）
  .lightOrangeRed {
    ::v-deep .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }
}
</style>
