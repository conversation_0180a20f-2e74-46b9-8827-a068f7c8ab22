<template>
  <div class="redeem-detail-container" style="margin-bottom: 116px">
    <basic-container>
      <avue-affix id="avue-view" :offset-top="114">
        <div class="header">
          <avue-title value="赎货单详情"></avue-title>
        </div>
      </avue-affix>

      <div class="apply-container">
        <div class="left">
          <div class="form-item">
            <span class="title">融资编号：</span>
            <span class="value">{{ redeemData.financeNo || '--' }}</span>
          </div>
          <div class="form-item">
            <span class="title">融资用户：</span>
            <span class="value">
              <span style="color: rgba(105, 124, 255, 100)">{{
                pageData.createUser || '--'
              }}</span>
            </span>
          </div>
          <div class="form-item">
            <span class="title">创建时间：</span>
            <span class="value">{{ pageData.createTime || '--' }}</span>
          </div>
        </div>
        <div class="right">
          <template v-if="redeemData.status">
            <div class="right-icon-box">
              <svg-icon
                :icon-class="statusArr[redeemData.status].iconClass"
                :style="{ fill: statusArr[redeemData.status].iconColor }"
                style="font-size: 40px"
              />
              <span class="status">{{
                statusArr[redeemData.status].title
              }}</span>
            </div>
            <div class="desc">
              <template v-if="redeemData.status === 2">
                <span>拒绝原因：{{ redeemData.comment }}</span>
              </template>
              <template v-else-if="redeemData.status === 3">
                <span
                  >赎货申请已通过，用户在
                  <span style="color: red">{{ timeDown }}</span>
                  内未确认赎货，订单将自动关闭</span
                >
              </template>
              <template v-else-if="redeemData.status === 6">
                <span>拒绝原因：{{ redeemData.comment }}</span>
              </template>
              <span>{{ statusArr[redeemData.status].desc }}</span>
            </div>
          </template>
        </div>
      </div>
    </basic-container>

    <!-- 赎货信息 -->
    <LayoutCard title="赎货信息" style="margin: 10px 6px">
      <div class="foreclosure-note-container" v-loading="pageDataLoading">
        <!-- 加载动画 -->
        <template v-if="pageDataLoading">
          <div style="height: 200px" />
        </template>
        <!-- 内容主体 -->
        <template v-else>
          <!-- <div class="goods-container">
            <div class="goods-wrapper">
              <el-image
                style="
                  width: 72px;
                  height: 72px;
                  flex-shrink: 0;
                  border-radius: 4px;
                "
                :src="redeemData.goodLogo"
                fit="contain"
              />
              <div class="goods-content">
                <span class="goods-name">{{ redeemData.goodsName }}</span>
                <div class="goods-type-container">
                  <span class="goods-type">规格型号：</span>
                  <span class="goods-type-value">{{
                    redeemData.goodsSpec
                  }}</span>
                </div>
              </div>
            </div>
            <div class="goods-desc-container">
              <span class="goods-desc-title">采购单价(元)</span>
              <span class="goods-desc-value">{{
                redeemData.purchasePrice | formatMoney
              }}</span>
            </div>

            <div class="goods-desc-container">
              <span class="goods-desc-title">融资单价(元)</span>
              <span class="goods-desc-value">{{
                redeemData.financingPrice | formatMoney
              }}</span>
            </div>
            <div class="goods-desc-container">
              <span class="goods-desc-title">赎货数量</span>
              <span class="goods-desc-value">{{ redeemData.num }}</span>
            </div>
            <div class="goods-desc-container">
              <span class="goods-desc-title">单位</span>
              <span class="goods-desc-value">{{
                redeemData.goodsUnitValue
              }}</span>
            </div>
          </div> -->
          <div class="goodstitleName">
            <span class="titleBasic">产品名称：</span>
            <span class="titleReal">{{redeemData.goodsName}}</span>
          </div>
          <el-table
              :data="redeemData.redeemCommodityVOS"
              style="width: 100%"
              border>
              <el-table-column
                type="index"
                width="50">
              </el-table-column>
              <el-table-column
                prop="spec"
                label="商品规格"
                width="180">
              </el-table-column>
              <el-table-column label="商品图片">
                <template>
                  <el-image
                    style="
                      width: 72px;
                      height: 72px;
                      flex-shrink: 0;
                      border-radius: 4px;
                    "
                    :src="redeemData.goodLogo"
                    fit="contain"/>
                </template>
              </el-table-column>
              <el-table-column
                prop="purchasePrice"
                label="采购单价">
              </el-table-column>
              <el-table-column
                prop="financingPrice"
                label="融资单价">
              </el-table-column>
              <el-table-column
                prop="num"
                label="赎货数量">
              </el-table-column>
          </el-table>

          <div class="descriptions-container">
            <el-descriptions labelStyle="width: 136px" :column="3" border>
              <!-- <template v-if="true">
                <el-descriptions-item label="库存编号">{{
                  redeemData.warehouseNo || '--'
                }}</el-descriptions-item>
              </template> -->
              <el-descriptions-item label="融资编号">{{
                redeemData.financeNo || '--'
              }}</el-descriptions-item>
              <!-- <el-descriptions-item label="供应商">{{
                redeemData.supplierName || '--'
              }}</el-descriptions-item> -->
              <!-- <el-descriptions-item label="仓储公司">{{
                pageData.storageCompany || '--'
              }}</el-descriptions-item> -->
              <!-- <el-descriptions-item label="仓库">{{
                pageData.warehouseName || '--'
              }}</el-descriptions-item> -->
              <!-- <el-descriptions-item label="入库日期">{{
                redeemData.warehouseInDate || '--'
              }}</el-descriptions-item> -->
              <!-- <el-descriptions-item label="库龄(天)">{{
                redeemData.warehouseAge === null
                  ? '--'
                  : redeemData.warehouseAge
              }}</el-descriptions-item> -->
              <el-descriptions-item label="约定赎货日">{{
                redeemData.redemptionDate || '--'
              }}</el-descriptions-item>
              <el-descriptions-item label="提货方式">{{
                redeemData.extractType || '--'
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
          <!-- 收货地址 -->
          <div
            v-if="redeemData.extractType === '第三方物流'"
            class="delivery-address-container"
          >
            <span class="title">收货地址</span>
            <div class="form-item">
              <span class="label">收货对象</span>
              <span class="value">{{
                redeemData.financingAddress.enterpriseName
              }}</span>
            </div>
            <div class="form-item">
              <span class="label">收货地址</span>
              <span class="value">{{
                redeemData.financingAddress.addressTarget ||
                redeemData.financingAddress.urbanAreas
              }}</span>
            </div>
            <div class="form-item">
              <span class="label">联系人</span>
              <span class="value">{{
                redeemData.financingAddress.contacts
              }}</span>
            </div>
            <div class="form-item">
              <span class="label">联系方式</span>
              <span class="value">{{
                redeemData.financingAddress.addressPhone
              }}</span>
            </div>
          </div>
          <!-- 自提信息 -->
          <div v-else class="delivery-address-container">
            <span class="title">自提信息</span>
            <div class="form-item">
              <span class="label">赎货人</span>
              <span class="value">{{
                `${redeemData.redeemUser.username}｜${redeemData.redeemUser.idCard}｜${redeemData.redeemUser.phone}`
              }}</span>
            </div>
            <div class="form-item">
              <span class="label">车牌号</span>
              <span class="value">{{
                redeemData.redeemUser.licensePlate
              }}</span>
            </div>
            <div class="form-item">
              <span class="label">提货日期</span>
              <span class="value">{{ redeemData.redeemUser.arriveTime }}</span>
            </div>
          </div>
        </template>
      </div>
    </LayoutCard>
    <!-- 还款单 -->
    <LayoutCard title="还款单" style="margin: 10px 6px">
      <div class="repayment-note-container" v-loading="pageDataLoading">
        <!-- 加载动画 -->
        <template v-if="pageDataLoading">
          <div style="height: 200px" />
        </template>
        <!-- 内容主体 -->
        <template v-else>
          <div class="form-container">
            <!-- 银行还款单 -->
            <div class="table-top refund">
              <div class="table-title-box">
                <div class="title-left-box">
                  <span>银行还款单</span>
                  <!-- <span />
                  <span>年化利率{{ pageData.yearRate || '--' }}%</span> -->
                </div>

                <!-- <div class="title-right-box">
                  应还本金(元)
                  <span style="color: black">{{
                    repayFunds | formatMoney
                  }}</span>
                </div> -->
              </div>
              <div class="zifangfeiyongzhanshi-d">
                <p>年利率: <span>{{ yinhangDataObj.yearRate }}</span> %</p>
                <p>日利率: <span>{{ yinhangDataObj.dayRate }}</span> %</p>
                <p>本金: <span>{{ yinhangDataObj.principal | formatMoney }}</span> (元)</p>
                <p>利息: <span>{{ yinhangDataObj.interest | formatMoney }}</span> (元)</p>
                <p>总计: <span>{{ yinhangDataObj.total | formatMoney }}</span> (元)</p>
              </div>
              <!-- <el-table
                ref="table2"
                :data="tableData2"
                style="width: 100%; margin-top: 13px"
                class="table-border-style"
                :summary-method="getSummaries"
                show-summary
              >
                <el-table-column prop="name" label="费用名称">
                </el-table-column>
                <el-table-column prop="expenseType" label="费用类型">
                  <template slot-scope="{ row }">
                    <Tag
                      :name="row.expenseType"
                      color="#00072A"
                      backgroundColor="#EAECF1"
                      borderColor="transparent"
                      :radius="true"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="node" label="支付节点">
                  <template>
                    <Tag
                      name="确认赎货"
                      color="#00072A"
                      backgroundColor="#EAECF1"
                      borderColor="transparent"
                      :radius="true"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="feeFormula" label="计费方式" />
                <el-table-column prop="money" label="应付金额(元)">
                  <template slot-scope="{ row }">
                    <span> ￥{{ row.money | formatMoney }} </span>
                  </template>
                </el-table-column>
              </el-table> -->
              <!-- <div
                v-if="redeemData.status >= 2"
                class="repayment-status-container"
              >
                <div class="left-box">
                  <span class="title">还款状态</span>
                  <Tag
                    class="tag"
                    :name="payStatusMap[pageData.repaymentStatus] || '--'"
                    color="#697CFF"
                    backgroundColor="#EBF1FF"
                    borderColor="transparent"
                    :radius="true"
                  />
                </div>

                <div class="right">
                  <el-button size="small" @click="handleViewBalanceProof"
                    >查看余额凭证</el-button
                  >
                </div>
              </div> -->
            </div>

            <!-- <div class="chain-line" /> -->

            <!-- 平台费用单 -->
            <div class="table-top refund">
              <template v-for="item in tableData3" >
                <div class="table-title-box" :key="item.billExpenseNo">
                  <div class="title-left-box">
                    <span>{{ item.parenExpenseName }}</span>
                    <!-- <span>平台费用单</span> -->
                  </div>
                </div>
                <el-table
                  :key="item.billExpenseNo"
                  ref="table3"
                  :data="item.expenseOrderDetailList"
                  style="width: 100%; margin-top: 13px"
                  class="table-border-style"
                  :summary-method="getSummaries"
                  show-summary
                >
                  <el-table-column prop="name" label="费用名称">
                  </el-table-column>
                  <el-table-column prop="expenseTypeStr" label="费用类型">
                    <template slot-scope="{ row }">
                      <Tag
                        :name="row.expenseTypeStr"
                        color="#00072A"
                        backgroundColor="#EAECF1"
                        borderColor="transparent"
                        :radius="true"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeNodeStr" label="计算节点">
                    <template slot-scope="{ row }">
                      <Tag
                        :name="row.feeNodeStr"
                        color="#00072A"
                        backgroundColor="#EAECF1"
                        borderColor="transparent"
                        :radius="true"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="collectFeesNodeStr" label="收费节点">
                    <template slot-scope="{ row }">
                      <Tag
                        :name="row.collectFeesNodeStr"
                        color="#00072A"
                        backgroundColor="#EAECF1"
                        borderColor="transparent"
                        :radius="true"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="feeFormulaName" label="计费方式" />
                  <el-table-column prop="amount" label="应付金额(元)">
                    <template slot-scope="{ row }">
                      <template v-if="!row.amount">
                        <span>人工核算</span>
                      </template>
                      <template v-else>
                        <span>￥{{ row.amount | formatMoney }}</span>
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="repayment-status-container" :key="item.billExpenseNo">
                  <div class="left-box">
                    <span class="title">还款状态</span>
                    <Tag
                      class="tag"
                      :name="orderStatusMap[item.paymentStatus] || '--'"
                      color="#697CFF"
                      backgroundColor="#EBF1FF"
                      borderColor="transparent"
                      :radius="true"
                    />
                  </div>

                  <div class="right">
                    <div class="pay-box">
                      <div class="payImg">
                        <div class="table-bottom">
                          <el-button
                            v-if="item.platformImg && item.platformImg.length"
                            @click="handlePreviewImage(item.platformImg)"
                            >查看支付凭证</el-button
                          >
                          <span
                            class="table-bottom-pdf"
                            v-for="(hCitem, index) in item.platformPdf"
                            :key="hCitem.url"
                            @click="handlePreviewFile(hCitem.url)"
                          >
                            附件{{ index + 1 }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </LayoutCard>
    <template
      v-if="redeemData.status >= 9 && redeemData.extractType === '第三方物流'"
    >
      <!-- 出库信息 -->
      <LayoutCard title="出库信息" style="margin: 10px 6px">
        <el-descriptions labelStyle="width: 136px" :column="3" border>
          <el-descriptions-item label="出库日期">{{
            redeemSend.outWarehouseTime || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="批号">{{
            redeemSend.batchNumber || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="出库单">
            <el-button
              :loading="loadingBtn.deliveryDocument"
              type="text"
              @click="handleViewDeliveryDocument"
              >查看文件</el-button
            >
          </el-descriptions-item>
        </el-descriptions>
      </LayoutCard>
      <!-- 物流信息 -->
      <LayoutCard title="物流信息" style="margin: 10px 6px">
        <el-descriptions labelStyle="width: 136px" :column="3" border>
          <el-descriptions-item label="物流公司">{{
            redeemSend.logisticsCompany || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="运单编号">{{
            redeemSend.transportNumber || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="发货时间">{{
            redeemSend.sendGoodsTime || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="送货人">{{
            redeemSend.givePeople || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="送货人手机号">{{
            redeemSend.givePeoplePhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{
            redeemSend.carNumber || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="物流单">
            <el-button
              :loading="loadingBtn.logisticsDocument"
              type="text"
              @click="handleViewLogisticsDocument"
              >查看文件</el-button
            >
          </el-descriptions-item>
          <template v-if="redeemData.status >= 10">
            <el-descriptions-item label="到货地址">{{
              redeemSend.arrivalAddress || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="到货时间">{{
              redeemSend.arrivalTime || '--'
            }}</el-descriptions-item>
          </template>
          <template v-else>
            <el-descriptions-item label="" />
            <el-descriptions-item label="" />
          </template>
          <template v-if="redeemData.status >= 10">
            <el-descriptions-item label="签收人">{{
              redeemSend.receivedPeople || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="签收时间">{{
              redeemSend.receivedTime || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="签收凭证">
              <el-button type="text">查看文件</el-button>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </LayoutCard>
    </template>
    <!-- 签收信息 -->
    <template
      v-if="redeemData.extractType !== '第三方物流' && redeemData.status > 11"
    >
      <LayoutCard title="签收信息" style="margin: 10px 6px">
        <el-descriptions labelStyle="width: 136px" :column="3" border>
          <template v-if="true">
            <el-descriptions-item label="签收人">{{
              redeemSend.receivedPeople || '--'
            }}</el-descriptions-item>
          </template>
          <el-descriptions-item label="签收时间">{{
            redeemSend.receivedTime || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="签收凭证">
            <el-button type="text">查看文件</el-button>
          </el-descriptions-item>
        </el-descriptions>
      </LayoutCard>
    </template>
    <!-- 处理记录 -->
    <LayoutCard
      v-if="pageData.redeemObjectionVoList.length > 0"
      title="处理记录"
      style="margin: 10px 6px"
    >
      <div class="log-records-container">
        <div
          v-for="item of pageData.redeemObjectionVoList"
          :key="item.id"
          class="card-container clear-fix"
        >
          <div class="progress-bar-wrapper">
            <span class="point" />
            <span class="line" />
          </div>
          <div class="date-content">
            <div class="date">
              <span>{{ item.createTime }}</span>
            </div>
          </div>
          <div class="card-content">
            <div class="title">
              <span>{{ item.userName }}</span>
            </div>
            <div class="card-msg-container">
              <div class="form-item">
                <span class="form-title">处理状态：</span>
                <span class="form-value">{{ item.statusStr }}</span>
              </div>
              <div class="form-item" style="align-items: flex-start">
                <span class="form-title">事件备注：</span>
                <span class="form-value">{{ item.remarks }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </LayoutCard>
    <template v-if="redeemData.status === 11">
      <LayoutCard title="变更信息" style="margin: 10px 6px">
        <el-descriptions labelStyle="width: 136px" :column="3" border>
          <el-descriptions-item label="变更类型">{{
            getType(changeInfo.changeType)
          }}</el-descriptions-item>

          <el-descriptions-item label="赎货单号">{{
            changeInfo.redeemNo || '--'
          }}</el-descriptions-item>
          <el-descriptions-item
            :label="changeInfo.changeType == 1 ? '退货数量' : '换货数量'"
            v-show="changeInfo.changeType != 2"
            >{{ changeInfo.goodsNum || '--' }}</el-descriptions-item
          >
          <el-descriptions-item label="联系人">{{
            changeInfo.linkMan || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{
            changeInfo.linkPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="变更附件">
            <!--  -->
            <div
              @click="see(changeInfo.adjunctProofList)"
              style="color: #007fff"
            >
              查看附件
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="变更原因">{{
            changeInfo.reason
          }}</el-descriptions-item>
        </el-descriptions>
      </LayoutCard>
    </template>
    <div class="footer-container">
      <span class="backBtn" @click="handleBackBtn">返 回</span>
      <span
        class="nextBtn"
        v-if="redeemData.status === 8"
        @click="handleDelivery"
        >发货</span
      >
      <span
        class="nextBtn"
        v-if="redeemData.status === 9"
        @click="handleArrivalInfo"
        >填写到货信息</span
      >
      <!-- <span
        class="nextBtn"
        v-if="redeemData.status === 11"
        @click="handleProcess"
        >处理</span
      > -->
      <span
        class="nextBtn"
        v-if="redeemData.status === 12"
        @click="handleSignReceiptRegistration"
        >填写提货信息</span
      >
    </div>
    <ArrivalDialog ref="arrivalDialogRef" @handleRefreshTable="initData" />
    <SignDialog ref="signDialogRef" @handleRefreshTable="initData" />
    <ProcessDialog ref="processDialogRef" @handleRefreshTable="initData" />
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import Tag from '@/views/customer/archives/components/Tag/index.vue'
import Dialog from '@/views/customer/archives/components/CommonDialog/index.vue'
import LayoutCard from '@/views/customer/archives/components/LayoutCard/index.vue'
import { formatMoney } from '@/util/filter'
import { getRedeemPledgeDetail } from '@/api/plugin/workflow/custom/redeem'
import { getDictionary } from '@/api/system/dictbiz'
import { getDetail, getByRedeemNo } from '@/api/resource/attach'
import ArrivalDialog from '../arrivalRegistrationDialog/index.vue'
import SignDialog from '../signReceiptRegistrationDialog/index.vue'
import ProcessDialog from '../processDialog/index.vue'
import { statusArr } from './config'

export default {
  components: {
    Tag,
    Dialog,
    LayoutCard,
    ArrivalDialog,
    SignDialog,
    ProcessDialog,
  },
  computed: {
    // 应还本金
    repayFunds() {
      return (
        this.$numChengFun(this.redeemData.financingPrice, this.redeemData.num)
      )
    },
    id() {
      return this.$route.params.id
    },
    redeemNo() {
      return this.$route.params.redeemNo
    },
  },
  data() {
    return {
      statusArr,
      pageDataLoading: true,
      pageData: { redeemObjectionVoList: [] },
      redeemSend: {},
      redeemData: { financingAddress: {}, redeemUser: {} },
      tableData2: [{ money: '123.213' }],
      tableData3: [{ money: '' }, { money: '' }],
      pdfSrc: '',
      // 字典
      payStatusArr: [],
      payStatusMap: {},
      orderStatusArr: [],
      orderStatusMap: {},
      // 顶部右侧倒计时变量
      timeDown: '',
      // 保存修改后的银行还款单支付状态
      bankRepaymentData: {},
      // 保存修改后的平台费用单支付状态
      platformFeeData: {},
      recordsData: [],
      loadingBtn: {
        deliveryDocument: false,
        logisticsDocument: false,
      },
      changeInfo: {},
      yinhangDataObj: {},
    }
  },
  created() {
    getDictionary({ code: 'loan_repayment_status' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          // 处理字典数据
          const statusArr = []
          const statusMap = {}
          for (const item of data) {
            item.dictKey = Number(item.dictKey)
            statusArr.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
            statusMap[item.dictKey] = item.dictValue
          }
          this.payStatusArr = statusArr
          this.payStatusMap = statusMap
        }
      })
      .catch(() => {})
    getDictionary({ code: 'bill_pay_status' })
      .then(({ data }) => {
        if (data.success) {
          data = data.data
          // 处理字典数据
          const orderArr = []
          const orderMap = {}
          for (const item of data) {
            item.dictKey = Number(item.dictKey)
            orderArr.push({
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            })
            orderMap[item.dictKey] = item.dictValue
          }
          this.orderStatusArr = orderArr
          this.orderStatusMap = orderMap
        }
      })
      .catch(() => {})
    this.initData()
  },
  methods: {
    getType(changeType) {
      if (changeType == 1) {
        return '退货退款'
      } else if (changeType == 2) {
        return '退款'
      } else if (changeType == 3) {
        return '换货'
      } else {
        return '--'
      }
    },
    see(phoneList) {
      const imgSrcArr = []
      const pdfSrcArr = []
      for (const item of phoneList) {
        if (item.extension != 'pdf') {
          imgSrcArr.push({ name: item.name, url: item.link })
        } else {
          pdfSrcArr.push({ name: item.name, url: item.link })
        }
      }
      if (pdfSrcArr.length == 0) {
        this.handlePreviewImage(imgSrcArr)
      } else {
        this.handlePreviewImage(pdfSrcArr, 'pdf')
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总价:'
          return
        } else if (index !== columns.length - 2) return

        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          const money = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return this.$numJiaFun(prev, curr)
            } else {
              return prev
            }
          }, 0)
          sums[index] = `￥${formatMoney(money)}`
        } else {
          sums[index] = ''
        }
      })

      return sums
    },
    initData() {
      const requestObj = {
        redeemNo: this.redeemNo,
      }
      getRedeemPledgeDetail(requestObj)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            data.redeemObjectionVoList = data.redeemObjectionVoList || []
            const redeemData = data.pledgeRedeemDetailCargoCurrencyVO || {}
            const redeemSend = redeemData.redeemSend || {}
            redeemData.financingAddress = redeemData.financingAddress || {}
            redeemData.redeemUser = redeemData.redeemUser || {}
            redeemData.manAttachList = redeemData.manAttachList || []
            redeemData.plaAttachList = redeemData.plaAttachList || []

            // 银行还款单
            // const tableData2 = redeemData.manExpenseCulationVoList || []
            // 平台费用单
            // const tableData3 = redeemData.plaExpenseCulationVoList || []
            this.costCalculusVOCunChu = redeemData.costCalculusVO || {}
            const costCalculusVO = this.costCalculusVOCunChu
            if (costCalculusVO) {
              // 资方数据
              this.yinhangDataObj = costCalculusVO.showRepaymentPlan || {}
              // 动态费用
              const expenseOrderDetailFinanceVos = costCalculusVO.expenseOrderDetailFinanceVos
              if (expenseOrderDetailFinanceVos && expenseOrderDetailFinanceVos.length) {
                for (const item of expenseOrderDetailFinanceVos) {
                  // 附件信息处理
                  if (item.attachInfoDTOList && item.attachInfoDTOList.length) {
                    item.platformPdf = []
                    item.platformImg = []
                    for (const cItem of item.attachInfoDTOList) {
                      if (cItem.url.indexOf('pdf') !== -1) {
                        item.platformPdf.push({
                          name: cItem.name,
                          url: cItem.url,
                        })
                      } else {
                        item.platformImg.push({
                          name: cItem.name,
                          url: cItem.url,
                        })
                      }
                    }
                  }
                }
                this.tableData3 = expenseOrderDetailFinanceVos
              }
            }

            switch (redeemData) {
              case 3:
                // 待确认倒计时

                break
              default:
            }
            if (data.pledgeRedeemDetailCargoCurrencyVO.status == 11) {
              getByRedeemNo(requestObj.redeemNo).then(res => {
                const resData = res.data.data
                this.changeInfo = resData || {}
              })
            }

            this.pageDataLoading = false
            this.pageData = data
            this.redeemSend = redeemSend
            this.redeemData = redeemData
            // this.tableData2 = tableData2
            // this.tableData3 = tableData3
          }
        })
        .catch(() => {})
    },
    // 查看余额凭证
    handleViewBalanceProof() {
      const imgSrcArr = []

      for (const item of this.redeemData.manAttachList) {
        imgSrcArr.push({ name: item.name, url: item.link })
      }

      this.handlePreviewImage(imgSrcArr)
    },
    // 查看支付凭证
    handleViewPayProof() {
      const imgSrcArr = []

      for (const item of this.redeemData.plaAttachList) {
        imgSrcArr.push({ name: item.name, url: item.link })
      }

      this.handlePreviewImage(imgSrcArr)
    },
    // 预览图片
    handlePreviewImage(imgSrcArr = []) {
      this.$ImagePreview(imgSrcArr, 0, {
        closeOnClickModal: true,
      })
    },
    handlePreviewFile(preUrl) {
      const targetUrl = preUrl
      if (targetUrl.endsWith('.pdf')) {
        this.pdfSrc = targetUrl + '?time=' + new Date().getMilliseconds()
      } else {
        const imgSrcArr = []
        imgSrcArr.push({ url: targetUrl })
        this.$ImagePreview(imgSrcArr, 0, {
          closeOnClickModal: true,
        })
      }
    },
    // 出库单 - 查看文件
    handleViewDeliveryDocument() {
      this.loadingBtn.deliveryDocument = true
      getDetail(this.redeemSend.deliveryDocument)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.handlePreviewFile(data.link)
          }
        })
        .finally(() => {
          this.loadingBtn.deliveryDocument = false
        })
    },
    // 物流单 - 查看文件
    handleViewLogisticsDocument() {
      this.loadingBtn.logisticsDocument = true
      getDetail(this.redeemSend.logisticsDocument)
        .then(({ data }) => {
          if (data.success) {
            data = data.data
            this.handlePreviewFile(data.link)
          }
        })
        .finally(() => {
          this.loadingBtn.logisticsDocument = false
        })
    },
    // 返回按钮
    handleBackBtn() {
      this.$router.$avueRouter.closeTag()
      this.$router.push('/redeemcargo/redeemPledgeCargo')
    },
    // 底部操作按钮 - 发货
    handleDelivery() {
      this.$router.push(`/redeemcargo/toShipments?id=${this.redeemNo}`)
    },
    // 底部操作按钮 - 填写到货信息
    handleArrivalInfo() {
      this.$refs.arrivalDialogRef.handleOpen(this.id)
    },
    // 底部操作按钮 - 处理
    handleProcess() {
      this.$refs.processDialogRef.handleOpen(this.redeemNo)
    },
    // 底部操作按钮 - 填写提货信息
    handleSignReceiptRegistration() {
      this.$refs.signDialogRef.handleOpen(this.id)
    },
  },
}
</script>

<style lang="scss" scoped>
.redeem-detail-container {
  .log-records-container {
    $pointWidthHeight: 12px;
    $cardMarginBottom: 32px;

    .card-container {
      position: relative;
      margin-bottom: $cardMarginBottom;

      &:last-child {
        margin-bottom: 0;

        .progress-bar-wrapper {
          .line {
            display: none;
          }
        }
      }

      .progress-bar-wrapper {
        position: absolute;
        top: 10px - $pointWidthHeight / 2;
        left: 145px;
        transform: translateX(-$pointWidthHeight / 2);
        bottom: -($cardMarginBottom + 10px - $pointWidthHeight / 2);

        .point {
          position: absolute;
          top: 0;
          display: inline-block;
          width: $pointWidthHeight;
          height: $pointWidthHeight;
          background-color: rgba(215, 215, 215, 100);
          border-radius: 50%;
        }

        .line {
          position: absolute;
          left: $pointWidthHeight / 2 - 1px;
          display: inline-block;
          width: 2px;
          height: 100%;
          background-color: rgba(215, 215, 215, 100);
        }
      }

      .date-content {
        width: 120px;
        float: left;
        text-align: right;

        .date {
          margin-bottom: 12px;
          height: 20px;
          color: rgba(106, 106, 106, 100);
          font-size: 14px;
          text-align: right;
          font-family: SourceHanSansSC-regular;
        }
      }

      .card-content {
        float: left;
        margin-left: 50px;
        max-width: 50%;

        .title {
          margin-bottom: 12px;
          height: 20px;
          color: rgba(16, 16, 16, 100);
          font-size: 14px;
          text-align: left;
          font-family: SourceHanSansSC-bold;
        }

        .card-msg-container {
          padding: 24px 32px;
          text-align: center;
          border-radius: 6px;
          // border: 1px solid rgba(215, 215, 215, 100);
          background-color: rgba(247, 247, 247, 100);

          .form-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .form-title {
              width: 70px;
              height: 20px;
              color: rgba(106, 106, 106, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
              flex-shrink: 0;
            }

            .form-value {
              color: rgba(77, 0, 0, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
    }
  }

  .el-button--text {
    padding: 0;
  }

  .footer-container {
    position: fixed;
    height: 68px;
    width: calc(100% - 272px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    bottom: 0;
    right: 15px;
    color: #000;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 #0000001a;

    .backBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      color: rgba(0, 7, 42, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      border: 1px solid rgba(187, 187, 187, 100);
      margin-right: 18px;
      cursor: pointer;
    }

    .nextBtn {
      width: 80px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      background-color: rgba(18, 119, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: center;
      font-family: Microsoft Yahei;
      cursor: pointer;
    }
  }
}

.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: right;
      font-family: SourceHanSansSC-regular;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
      align-items: center;

      .status {
        padding: 0 6px;
      }
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

// 自定义主体样式 - 开始
.foreclosure-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .divider {
      display: inline-block;
      margin: 0 12px;
      width: 1px;
      height: 16px;
      background: rgba(215, 215, 215, 100);
    }

    .number-container {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;

      .number-type-value {
        color: rgba(105, 124, 255, 100);
      }
    }
  }

  .goods-container {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-top: 16px;
    border-top: 1px solid rgba(233, 235, 239, 100);

    .goods-wrapper {
      display: flex;
      align-items: center;
      max-width: 40%;

      .goods-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 6px 0 6px 12px;
        width: 100%;
        height: 72px;

        .goods-name {
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #0a1f44;
          line-height: 20px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .goods-type-container {
          margin-top: 12px;

          .goods-type {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8a94a6;
            line-height: 20px;
          }

          .goods-type-value {
            font-size: 14px;
            font-family: SFProText-Medium, SFProText;
            font-weight: 500;
            color: #53627c;
            line-height: 20px;
          }
        }
      }
    }

    .goods-desc-container {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 20px;

      .goods-desc-title {
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        line-height: 20px;
      }

      .goods-desc-value {
        margin-top: 8px;
        color: rgba(16, 16, 16, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-bold;
        font-weight: 600;
        line-height: 20px;
      }
    }
  }

  .delivery-address-container {
    margin-top: 12px;

    .title {
      color: rgba(112, 112, 112, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      font-weight: 600;
    }

    .form-item {
      margin-top: 14px;

      .label {
        display: inline-block;
        width: 96px;
        color: rgba(112, 112, 112, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }

      .value {
        color: rgba(36, 36, 36, 100);
        font-size: 14px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
      }
    }
  }
}

.repayment-note-container {
  .title-container {
    display: flex;
    align-items: center;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.repayment-status-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;

  .left-box {
    .title {
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }

    .tag {
      margin-left: 8px;
    }

    .button {
      margin-left: 8px;
    }

    .pay-box {
      width: 100%;
      border-radius: 6px;
      box-sizing: border-box;
      line-height: 20px;
      & span:first-child {
        display: block;
        color: rgba(153, 153, 153, 100);
        font-size: 14px;
        font-family: SourceHanSansSC-regular;
        font-weight: 500;
        margin-right: 8px;
      }
      & span:last-child {
        color: #00072a;
        font-weight: 600;
        font-size: 14px;
      }

      .form-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 150px;
          // text-align: right;
        }
      }
    }
  }
}
// 自定义主体样式 - 结束
// 更改表格组件样式
::v-deep {
  .table-top {
    .table-title-box {
      height: 22px;
      margin-top: 23px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left-box {
        display: flex;
        align-items: center;

        & span:first-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
          font-weight: 600;
        }
        & span:nth-child(2) {
          display: inline-block;
          width: 1px;
          height: 16px;
          line-height: 20px;
          background-color: rgba(215, 215, 215, 100);
          text-align: center;
          margin: 0 8px;
        }
        & span:last-child {
          color: rgba(125, 125, 125, 100);
          font-size: 14px;
          text-align: left;
        }
      }

      .title-right-box {
        height: 22px;
        color: rgba(125, 125, 125, 100);
        font-size: 14px;
        text-align: right;
      }
    }
    .contract-no {
      color: #697cff;
    }
    .tag-box {
      height: 30px;
      padding: 2px 6px;
      box-sizing: border-box;
      border-radius: 4px;
      color: rgba(16, 16, 16, 100);
      font-size: 13px;
      text-align: center;
      border: 1px solid rgba(187, 187, 187, 100);
    }
    .el-table th.el-table__cell {
      background-color: #f7f7f7;
    }
    .el-table th.el-table__cell > .cell {
      color: rgba(0, 0, 0, 100);
      font-size: 14px;
    }
    .el-table__footer-wrapper tbody td.el-table__cell {
      font-size: 20px;
      background-color: #f7f7f7;
      font-weight: 600;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background-color: transparent;
    }
  }

  .refund {
    .el-table th.el-table__cell {
      background-color: #fff1f1;
    }
  }

  .el-input-group__append {
    color: #000;
  }
}
::v-deep .el-table .aggregate-row {
  background-color: #f7f7f7;
  font-size: 20px;
  font-weight: 600;
}
.chain-line {
  width: 100%;
  height: 1px;
  border-top: 1px dashed #000;
  margin: 20px 0;
}

.table-border-style {
  border-top: 1px solid #e9ebf0;
  border-left: 1px solid #e9ebf0;
  border-right: 1px solid #e9ebf0;
}

.border-box {
  color: #000000b5;
  background-color: #e9ebf0;
  border-radius: 100px;
  font-size: 14px;
  padding: 3px 12px;
  box-sizing: border-box;
}

.fees-at-box {
  margin-top: 23px;
  display: flex;
  // justify-content: space-between;
  justify-content: right;
  align-items: center;

  .fees-left-at {
    height: 22px;
    color: rgba(125, 125, 125, 100);
    font-size: 14px;
    text-align: left;
  }

  .fees-right-at {
    height: 29px;
    color: #242424;
    font-size: 16px;
    text-align: right;
    font-weight: 600;
    display: flex;
    align-items: center;

    & span {
      color: #ff2929;
      font-size: 22px;
    }
  }
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goodstitleName{
  margin: 10px;

  .titleBasic{
    font-size: 15px
  }
  .titleReal{
    font-weight: bold;
    font-size: 15px
  }
}
.zifangfeiyongzhanshi-d {
  display: flex;
  font-size: 16px;
  margin-top: 10px;

  & > * {
    margin-right: 30px;
    color: #808080;

    span {
      color: #000;
    }
  }
}
</style>
